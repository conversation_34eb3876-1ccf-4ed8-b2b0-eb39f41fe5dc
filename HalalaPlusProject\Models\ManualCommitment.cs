﻿using System;
using System.Collections.Generic;

namespace HalalaPlusProject.Models;

public partial class ManualCommitment
{
    public int ManualCommitmentsId { get; set; }

    public decimal? Amount { get; set; }

    public DateOnly? ExpenseDate { get; set; }

    public string? Notes { get; set; }

    public int? MainTermsId { get; set; }

    public long? SubTermsId { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? CreatedAt { get; set; }

    public string? UpdatedBy { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public bool? Deleted { get; set; }

    public string? DeletedBy { get; set; }

    public DateTime? DeletedAt { get; set; }

    public virtual ICollection<ExpensesPlan> ExpensesPlans { get; set; } = new List<ExpensesPlan>();

    public virtual MainTerm? MainTerms { get; set; }

    public virtual SubTerm? SubTerms { get; set; }
}
