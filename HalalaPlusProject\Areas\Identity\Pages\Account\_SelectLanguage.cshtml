﻿@using Microsoft.AspNetCore.Builder;
@using Microsoft.AspNetCore.Localization;
@using Microsoft.AspNetCore.Http.Features;
@using Microsoft.AspNetCore.Mvc.Localization;
@using Microsoft.Extensions.Options;

@inject IViewLocalizer localizer
@inject IOptions<RequestLocalizationOptions> options

@{
    var requestCulture = Context.Features.Get<IRequestCultureFeature>();
    var cultures = options.Value.SupportedUICultures
        .Select(c => new SelectListItem { Value = c.Name, Text = c.NativeName })
        .ToList();
    var returnUrl = string.IsNullOrEmpty(Context.Request.Path) ? "~/" : $"~{Context.Request.Path.Value}";
}

<div>
    <form method="post" asp-controller="Home" asp-action="SetLanguage" asp-route-returnUrl="@returnUrl">
        @* <select name="culture" class="form-select "
            asp-for="@requestCulture.RequestCulture.UICulture.Name"
                asp-items="cultures"
                onchange="this.form.submit();">
           
        </select> *@
        <select name="culture" class="form-select" asp-for="@requestCulture.RequestCulture.UICulture.Name" onchange="this.form.submit();" data-val="true" data-val-required="The Name field is required.">
        <option value="en-US">English</option>
        <option  value="ar-EG">العربيـة</option>
        </select>
        
    </form>
</div>