﻿@using System.Globalization
@model IEnumerable<HalalaPlusProject.Models.vw_MonthlyGrowthRevenue>

@{
    ViewData["Title"] = "تقرير نمو الإيرادات الشهري والاشتراكات";
}

<h2>تقرير نمو الإيرادات الشهري والاشتراكات </h2>
<div class="row">
    <div class="col-12">
        <div class="card mb-4">

            <div class="card-body px-0 pt-0 pb-2">
                <div class="table-responsive p-0">
<table id="tbl1" class="table">
                        <thead class="table-light">
                            <tr>
                                <th>الشهر</th>
                                <th>عدد الطلبات</th>
                                <th>إجمالي الإيرادات</th>
                                <th>متوسط قيمة الطلب</th>
                                <th>الاشتراكات الجديدة</th>
                                <th>العملاء الجدد</th>
                                <th>مزودي الخدمة الجدد</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var row in Model)
                            {
                                <tr>
                                    <td>@row.YearMonth</td>
                                    <td>@row.OrdersCount</td>
                                    <td>@row.TotalRevenue.ToString("C2", new CultureInfo("ar-SA"))</td>
                                    <td>@row.AvgOrderValue.ToString("C2", new CultureInfo("ar-SA"))</td>
                                    <td>@row.NewSubscriptions</td>
                                    <td>@row.NewCustomers</td>
                                    <td>@row.NewProviders</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@section Scripts{
        <script>
            let table = new DataTable('#tbl1');

        </script>
}
