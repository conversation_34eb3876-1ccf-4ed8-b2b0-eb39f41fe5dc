﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class SystemUser
{
    [Key]
    public long Id { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    public int? Nationality { get; set; }

    [StringLength(100)]
    public string? IdentityNo { get; set; }

    public int? City { get; set; }

    public int? Activity { get; set; }

    public string? ServiceProviderRepresent { get; set; }

    public DateOnly? BirthDate { get; set; }

    [StringLength(50)]
    public string? PhoneNo { get; set; }

    [StringLength(50)]
    public string? Email { get; set; }

    [StringLength(10)]
    public string? DiscountCode { get; set; }

    public double? Discount { get; set; }

    public double? Precentage { get; set; }

    public double? Salary { get; set; }

    [StringLength(450)]
    public string? BusinessNo { get; set; }

    [StringLength(450)]
    public string? EnterPrisePhoneNo { get; set; }

    [StringLength(250)]
    public string? Location { get; set; }

    [StringLength(450)]
    public string? Logo { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? DateofJoin { get; set; }

    [StringLength(450)]
    public string? AddUser { get; set; }

    [Column("ASP_Id")]
    [StringLength(450)]
    public string AspId { get; set; } = null!;

    [Column("masterId")]
    [StringLength(450)]
    public string? MasterId { get; set; }

    [StringLength(50)]
    public string? AccountNo { get; set; }

    public double? Amount { get; set; }

    [StringLength(1)]
    [Unicode(false)]
    public string? Status { get; set; }

    [StringLength(20)]
    public string? AccountType { get; set; }

    [Column("contractDate")]
    public DateOnly? ContractDate { get; set; }

    public DateOnly? ContractEndDate { get; set; }

    [Column("cashBack")]
    public double? CashBack { get; set; }

    public bool? Deleted { get; set; }

    [Column("orderstate")]
    [StringLength(50)]
    public string? Orderstate { get; set; }

    [Column("orderdate", TypeName = "datetime")]
    public DateTime Orderdate { get; set; }

    public bool? IsOrder { get; set; }

    [StringLength(20)]
    public string? Gender { get; set; }

    [StringLength(1500)]
    public string? OverView { get; set; }

    [StringLength(1500)]
    public string? ContractNo { get; set; }

    [StringLength(50)]
    public string? Lat { get; set; }

    [Column("lng")]
    [StringLength(50)]
    public string? Lng { get; set; }

    [StringLength(50)]
    public string? RegisterProgress { get; set; }

    [StringLength(1500)]
    public string? EnName { get; set; }

    public string? EnServiceProviderRepresent { get; set; }

    [StringLength(1500)]
    public string? EnOverView { get; set; }

    [Column("providerimages")]
    public string? Providerimages { get; set; }

    [StringLength(250)]
    public string? StoreLink { get; set; }

    [StringLength(50)]
    public string? CradActivationCode { get; set; }

    [StringLength(500)]
    public string? Benefitfrompoints { get; set; }

    [StringLength(500)]
    public string? EnBenefitfrompoints { get; set; }

    [StringLength(50)]
    public string? FirstColor { get; set; }

    [StringLength(50)]
    public string? SecondColor { get; set; }

    [StringLength(250)]
    public string? OffesLogo { get; set; }

    public bool HasCustomBrand { get; set; }

    public long? OrgId { get; set; }

    public int EmployeesCount { get; set; }

    [StringLength(50)]
    public string OrderStatus { get; set; } = null!;

    [Column("target")]
    public int Target { get; set; }

    [StringLength(20)]
    public string? DealingWay { get; set; }

    public int BranchesNo { get; set; }

    [StringLength(100)]
    public string? ActorDescription { get; set; }

    [ForeignKey("AspId")]
    [InverseProperty("SystemUsers")]
    public virtual AspNetUser Asp { get; set; } = null!;

    [InverseProperty("User")]
    public virtual ICollection<Cart> Carts { get; set; } = new List<Cart>();

    [InverseProperty("User")]
    public virtual ICollection<ConnectBanksOtpCode> ConnectBanksOtpCodes { get; set; } = new List<ConnectBanksOtpCode>();

    [InverseProperty("User")]
    public virtual ICollection<ConsentHistory> ConsentHistories { get; set; } = new List<ConsentHistory>();

    [InverseProperty("User")]
    public virtual ICollection<CustomerDiscount> CustomerDiscounts { get; set; } = new List<CustomerDiscount>();

    [InverseProperty("Customer")]
    public virtual ICollection<CustomerPlan> CustomerPlans { get; set; } = new List<CustomerPlan>();

    [InverseProperty("Master")]
    public virtual ICollection<DiscountsTable> DiscountsTableMasters { get; set; } = new List<DiscountsTable>();

    [InverseProperty("User")]
    public virtual ICollection<DiscountsTable> DiscountsTableUsers { get; set; } = new List<DiscountsTable>();

    [InverseProperty("User")]
    public virtual ICollection<GrantDiscount> GrantDiscounts { get; set; } = new List<GrantDiscount>();

    [InverseProperty("Provider")]
    public virtual ICollection<GrantedDiscount> GrantedDiscountProviders { get; set; } = new List<GrantedDiscount>();

    [InverseProperty("User")]
    public virtual ICollection<GrantedDiscount> GrantedDiscountUsers { get; set; } = new List<GrantedDiscount>();

    [InverseProperty("User")]
    public virtual ICollection<MonyBoxAccount> MonyBoxAccounts { get; set; } = new List<MonyBoxAccount>();

    [InverseProperty("User")]
    public virtual ICollection<MonyBoxTransaction> MonyBoxTransactions { get; set; } = new List<MonyBoxTransaction>();

    [InverseProperty("User")]
    public virtual ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();

    [InverseProperty("User")]
    public virtual ICollection<Order> Orders { get; set; } = new List<Order>();

    [InverseProperty("User")]
    public virtual ICollection<PaymentsRequest> PaymentsRequests { get; set; } = new List<PaymentsRequest>();

    [InverseProperty("User")]
    public virtual ICollection<PlanPersonalAccount> PlanPersonalAccounts { get; set; } = new List<PlanPersonalAccount>();

    [InverseProperty("Provider")]
    public virtual ICollection<PointReplaceOrder> PointReplaceOrderProviders { get; set; } = new List<PointReplaceOrder>();

    [InverseProperty("User")]
    public virtual ICollection<PointReplaceOrder> PointReplaceOrderUsers { get; set; } = new List<PointReplaceOrder>();

    [InverseProperty("User")]
    public virtual ICollection<PointsTable> PointsTables { get; set; } = new List<PointsTable>();

    [InverseProperty("User")]
    public virtual ICollection<RetriveMonyBoxAmount> RetriveMonyBoxAmounts { get; set; } = new List<RetriveMonyBoxAmount>();

    [InverseProperty("User")]
    public virtual ICollection<Shipment> Shipments { get; set; } = new List<Shipment>();

    [InverseProperty("User")]
    public virtual ICollection<SocialMediaAccount> SocialMediaAccounts { get; set; } = new List<SocialMediaAccount>();

    [InverseProperty("User")]
    public virtual ICollection<Subscription> Subscriptions { get; set; } = new List<Subscription>();

    [InverseProperty("User")]
    public virtual ICollection<UserBanksAccount> UserBanksAccounts { get; set; } = new List<UserBanksAccount>();

    [InverseProperty("User")]
    public virtual ICollection<UsersMonyBox> UsersMonyBoxes { get; set; } = new List<UsersMonyBox>();

    [InverseProperty("DistUserNavigation")]
    public virtual ICollection<WaletTransction> WaletTransctionDistUserNavigations { get; set; } = new List<WaletTransction>();

    [InverseProperty("User")]
    public virtual ICollection<WaletTransction> WaletTransctionUsers { get; set; } = new List<WaletTransction>();

    [InverseProperty("User")]
    public virtual ICollection<Walet> Walets { get; set; } = new List<Walet>();
}
