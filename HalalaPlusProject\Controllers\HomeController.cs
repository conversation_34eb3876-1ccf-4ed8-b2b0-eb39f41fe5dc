﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Models;
using HalalaPlusProject.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;
//using System.Collections.Generic;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة الصفحات الرئيسية والتفاعلات العامة مثل لوحة التحكم، الخصوصية، وتغيير اللغة.
    /// </summary>
    [Authorize]
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly HalalaPlusdbContext _context;
        private readonly IDashboardActivityService _activityService;

        /// <summary>
        /// إنشاء نسخة جديدة من وحدة التحكم الرئيسية مع حقن الخدمات المطلوبة.
        /// </summary>
        /// <param name="logger">خدمة تسجيل الأحداث.</param>
        /// <param name="context">سياق قاعدة البيانات.</param>
        /// <param name="activityService">خدمة توفير بيانات لوحة التحكم.</param>
        public HomeController(ILogger<HomeController> logger, HalalaPlusdbContext context, IDashboardActivityService activityService)
        {
            _logger = logger;
            _context = context;
            _activityService = activityService;
        }

        //[Authorize(Roles ="Admin,Provider,Marketer,Employee,Customer")]
        //[AllowAnonymous]
        /// <summary>
        /// إعداد وعرض لوحة التحكم الرئيسية مع إحصائيات النظام وبيانات الرسوم البيانية.
        /// </summary>
        /// <returns>عرض يحتوي على إحصائيات النظام وبيانات النشاط.</returns>
        [Authorize]
        public IActionResult Index()
        {
            var totalAmount = _context.UsersMonyBoxs.Sum(op => op.Amount);
            var totalDiscounts = _context.DiscountsTables.Sum(op => op.Discount);
            var stats = new UserStatistic
            {
                TotalUsersMonyBoxs = _context.UsersMonyBoxs.Count(),
                TotalAmount = (totalAmount != null && totalAmount.Value > 0) ? totalAmount.Value : 00,
                TotalDiscount = (totalDiscounts != null && totalDiscounts.Value > 0) ? totalDiscounts.Value : 00,
                visitors = _context.AnonymousUserActivities.Count(),
                ActiveUsers = _context.SystemUsers.Where(o => o.AccountType == "Customer").Count(),
                Providers = _context.SystemUsers.Where(o => o.AccountType == "Provider").Count(),
                //ActiveUsers = _context.Users.Count(u => u.IsActive),
                //InactiveUsers = _context.Users.Count(u => !u.IsActive)
                AllUsers = _context.SystemUsers.Count()
            };
            //var users=_context.SystemUsers.Count();
            // Fetch your data here. This is just an example.
            var data = new List<int> { 10, 20, 40, 15, 7, 3, 30 };

            var liveData = new List<int>();
            for (int i = 7; i > 0; i--)
            {
                liveData.Add(_activityService.GetActivityCount(i));
                // liveData.Add(_activityService.GetActivityCountForDay(i));
            }
            ViewBag.ChartData = liveData;

            var liveTotalDiscountData = new List<double>();
            for (int i = 7; i > 0; i--)
            {
                liveTotalDiscountData.Add(_activityService.GetActivityTotalDiscountsByMonth(i));
            }
            ViewBag.TotalDiscountData = liveTotalDiscountData;

            var myLast7Days = _activityService.GetDateOfLast7Days();
            ViewBag.Last7Days = myLast7Days;
            var myLast7Months = _activityService.GetDateOfLast7Months();
            ViewBag.Last7Months = myLast7Months;

            //ViewBag.CurrentDateTime = DateTime.Now;
            //ViewBag.CurrentDateTimeUtc = DateTime.UtcNow;
            //ViewBag.CurrentMyDateTimeUtc =_activityService.GetTheRightDateTime();
            ViewBag.CurrentMyDateTimeUtcZ = _activityService.GetTheRightDateTime(1);
            return View(stats);
        }

        /// <summary>
        /// عرض صفحة سياسة الخصوصية.
        /// </summary>
        /// <returns>عرض صفحة الخصوصية.</returns>
        public IActionResult Privacy()
        {
            return View();
        }

        /// <summary>
        /// تعيين لغة العرض المفضلة للمستخدم وتخزينها في ملف تعريف الارتباط (cookie).
        /// </summary>
        /// <param name="culture">رمز اللغة المطلوب (مثل "ar" أو "en").</param>
        /// <param name="returnUrl">الرابط الذي سيتم العودة إليه بعد تغيير اللغة.</param>
        /// <returns>إعادة توجيه محلي إلى الرابط الأصلي.</returns>
        public IActionResult SetLanguage(string culture, string returnUrl)
        {
            Response.Cookies.Append(
                CookieRequestCultureProvider.DefaultCookieName,
                CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture)),
                new CookieOptions { Expires = DateTimeOffset.UtcNow.AddYears(1) }
                );

            return LocalRedirect(returnUrl);
        }

        /// <summary>
        /// عرض صفحة الخطأ العامة.
        /// </summary>
        /// <returns>عرض صفحة الخطأ مع معرف الطلب.</returns>
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = System.Diagnostics.Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}