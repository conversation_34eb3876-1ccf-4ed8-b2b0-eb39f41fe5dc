﻿using System;

namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل رسالة يتم إرسالها داخل غرفة دردشة معينة.
    /// </summary>
    public class ChatMessageViewModel
    {
        /// <summary>
        /// معرف غرفة الدردشة.
        /// </summary>
        public string RoomId { get; set; }

        /// <summary>
        /// نص الرسالة.
        /// </summary>
        public string Text { get; set; }
    }

    /// <summary>
    /// يمثل رسالة داخل الدردشة تحتوي على معلومات المرسل والتوقيت.
    /// </summary>
    public class MessageViewModel
    {
        /// <summary>
        /// معرف المرسل.
        /// </summary>
        public string SenderId { get; set; }

        /// <summary>
        /// اسم المرسل.
        /// </summary>
        public string SenderName { get; set; }

        /// <summary>
        /// نص الرسالة.
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// توقيت إرسال الرسالة.
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// معرف غرفة الدردشة المرتبطة بالرسالة.
        /// </summary>
        public string? RoomId { get; set; }
    }

    /// <summary>
    /// يمثل غرفة دردشة تحتوي على معرف واسم.
    /// </summary>
    public class ChatRoomViewModel
    {
        /// <summary>
        /// معرف غرفة الدردشة.
        /// </summary>
        public string RoomId { get; set; }

        /// <summary>
        /// اسم غرفة الدردشة.
        /// </summary>
        public string RoomName { get; set; }
    }
}
