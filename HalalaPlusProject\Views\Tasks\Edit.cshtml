﻿@model HalalaPlusProject.CModels.TasksDetails
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["edit"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["edit"]</h2>

<hr />
<div class="row">
    <div class="col-md-8">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
             
         <div class="row">
        <div class="col-md-5">
          <div class="form-group">
                        <label asp-for="TaskName" class="control-label">@localizer["taskname"]</label>
                <input asp-for="TaskName" class="form-control" />
                <span asp-validation-for="TaskName" required class="text-danger"></span>
            </div>            
            <div class="form-group">
                        <label asp-for="TaskDescribe" class="control-label">@localizer["details"]</label>
                 <input asp-for="Employee" class="form-control" />
                  <span asp-validation-for="TaskDescribe" required class="text-danger"></span>
            </div>
                    <div class="form-group">
                        <label asp-for="EnTaskName" class="control-label">الاسم انجليزي </label>
                        <input asp-for="EnTaskName" class="form-control" />
                        <span asp-validation-for="EnTaskName" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="EnTaskDescribe" class="control-label">الوصف انجليزي</label>
                        <input asp-for="EnTaskDescribe" class="form-control" />
                        <span asp-validation-for="EnTaskDescribe" class="text-danger"></span>
                    </div>
              <div class="form-group">
                        <label asp-for="StartDate" class="control-label"> @localizer["offerstartdate"]</label>
                <input asp-for="StartDate" class="form-control" />
                <span asp-validation-for="StartDate" class="text-danger"></span>
            </div>
            <div class="form-group">
                        <label asp-for="EndDate" class="control-label">@localizer["offerenddate"]</label>
                <input asp-for="EndDate" type="date" class="form-control" />
                <span asp-validation-for="EndDate" class="text-danger"></span>
            </div>
                     
          </div>

            <div class="col-md-5">
         <div class="form-group">
                        <label asp-for="Files" class="control-label"> @localizer["attatchments"]</label>
                <input asp-for="Files" class="form-control" />
                <span asp-validation-for="Files" class="text-danger"></span>
            </div>
            <div class="form-group">
                        <label asp-for="Notes" class="control-label">@localizer["note"]</label>
                <textarea asp-for="Notes" class="form-control" ></textarea>
                <span asp-validation-for="Notes" class="text-danger"></span>
            </div>
              <div class="form-group">
                        <label asp-for="Employee" class="control-label">@localizer["empname"] </label>
               
                 <select asp-for="Employee" required class ="form-select" asp-items="ViewBag.Emloyee"></select>
                <span asp-validation-for="Employee" class="text-danger"></span>
            </div>
             
          </div>
          </div>
 
        
         
        
         
     
           
            <div class="form-group">
                <button type="submit" value="Save" class="btn btn-primary">@localizer["save"]</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
