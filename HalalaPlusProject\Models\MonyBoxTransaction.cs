﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class MonyBoxTransaction
{
    [Key]
    [Column(TypeName = "decimal(18, 0)")]
    public decimal Id { get; set; }

    public long? MonyBoxId { get; set; }

    public long? AccountId { get; set; }

    public double? Deduction { get; set; }

    [Column("operationDate", TypeName = "datetime")]
    public DateTime? OperationDate { get; set; }

    public long? UserId { get; set; }

    [Column("temp1")]
    [StringLength(50)]
    public string? Temp1 { get; set; }

    [Column("temp2")]
    [StringLength(50)]
    public string? Temp2 { get; set; }

    public double? Credit { get; set; }

    public double? Debit { get; set; }

    [StringLength(50)]
    public string? PaymentId { get; set; }

    public bool IsVerified { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? VerifayDate { get; set; }

    [Column("status")]
    [StringLength(100)]
    public string? Status { get; set; }

    [ForeignKey("AccountId")]
    [InverseProperty("MonyBoxTransactions")]
    public virtual MonyBoxAccount? Account { get; set; }

    [ForeignKey("MonyBoxId")]
    [InverseProperty("MonyBoxTransactions")]
    public virtual UsersMonyBox? MonyBox { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("MonyBoxTransactions")]
    public virtual SystemUser? User { get; set; }
}
