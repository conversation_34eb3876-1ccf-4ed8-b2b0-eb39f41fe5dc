﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class VwSubscriptionsOverview
{
    public int PackageId { get; set; }

    [StringLength(1500)]
    public string? PackageName { get; set; }

    public int? ActiveSubscribers { get; set; }

    public int? NewSubscribersThisMonth { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TotalRevenueFromPackage { get; set; }

    [Column(TypeName = "decimal(5, 2)")]
    public decimal? ChurnRate { get; set; }
}
