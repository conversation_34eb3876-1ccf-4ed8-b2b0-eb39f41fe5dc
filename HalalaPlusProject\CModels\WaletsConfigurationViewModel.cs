﻿namespace HalalaPlusProject.CModels
{


        public class WaletsConfigurationViewModel
        {
            public int Id { get; set; }
            public double? MaxLimitForWallets { get; set; } = 0;
            public double? MinLimitForWalet { get; set; } = 0;
            public bool GrantMonyBoxsChargePoints { get; set; } = false;
            public double? MaxlimtForAllMonyBoxs { get; set; } = 0;
            public double? CashBackPrecent { get; set; } = 0;
            public double? MaxCachBackAmount { get; set; } = 0;
            public bool GrantCachBack { get; set; } = false;
            public DateTime? CachBackStartDate { get; set; }
            public DateTime? CachBackEndDate { get; set; }
            public int? WelcomPointsNo { get; set; } = 0;
            public int? AmountForWelcomePint { get; set; } = 0;
            public bool IsWecomePointsActive { get; set; } = false;
        }
 



}
