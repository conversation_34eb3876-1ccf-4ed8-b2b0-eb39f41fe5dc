﻿@model HalalaPlusProject.Models.CitiesTable
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["details"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["details"]</h1>

<div>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @localizer["thecity"]
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.City)
        </dd> 
        <dt class = "col-sm-2">
            @localizer["thecity"]
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnCity)
        </dd>
        <dt class = "col-sm-2">
            @localizer["thecountry"]
        </dt>
        
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CIdNavigation.Country)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id">@localizer["edit"]</a> |
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>
