﻿namespace HalalaPlusProject.CustomClasses
{
    public class MarketerClass
    {

        public bool InsertProvider(CModels.MUserCreateProvider model, Models.HalalaPlusdbContext _context,string userid,string masterId)
        {
            try
            {
                var ob = new Models.SystemUser();
                ob.Name = model.Name;
                ob.ServiceProviderRepresent = model.ServiceProviderRepresent;
                ob.PhoneNo = model.PhoneNumber;
                ob.Activity = model.Activity;
                ob.Email = model.Email;
                ob.City = model.City;
                ob.AspId = userid;
                ob.AccountType = "Provider";
                ob.IsOrder = true;
                ob.Orderstate = "new";
                ob.MasterId = masterId;
                ob.Deleted = false;
                _context.Add(ob);
                _context.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {

                return false;
            }

        }


    }
}
