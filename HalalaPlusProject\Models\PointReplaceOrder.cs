﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class PointReplaceOrder
{
    [Key]
    public long Id { get; set; }

    public long? UserId { get; set; }

    public long? ProviderId { get; set; }

    public double? PointCount { get; set; }

    [StringLength(500)]
    public string? AgentName { get; set; }

    public double? DesrveAmount { get; set; }

    public bool? Accept { get; set; }

    [Column("state")]
    [StringLength(500)]
    public string State { get; set; } = null!;

    [Column(TypeName = "datetime")]
    public DateTime OrderDate { get; set; }

    [StringLength(500)]
    public string? EnAgentName { get; set; }

    public long? AcceptUserId { get; set; }

    public long? RejectUserId { get; set; }

    [ForeignKey("ProviderId")]
    [InverseProperty("PointReplaceOrderProviders")]
    public virtual SystemUser? Provider { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("PointReplaceOrderUsers")]
    public virtual SystemUser? User { get; set; }
}
