﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class UsersMonyBox
{
    [Key]
    public long Id { get; set; }

    public long? UserId { get; set; }

    public string Name { get; set; } = null!;

    public double Target { get; set; }

    public string? Icon { get; set; }

    [Column("startDate", TypeName = "datetime")]
    public DateTime? StartDate { get; set; }

    [Column("endDate", TypeName = "datetime")]
    public DateTime? EndDate { get; set; }

    [Column("isActive")]
    public bool? IsActive { get; set; }

    [Column("status")]
    [StringLength(50)]
    public string? Status { get; set; }

    [StringLength(500)]
    public string? Note { get; set; }

    public double? Amount { get; set; }

    [StringLength(50)]
    public string? DiscountMethod { get; set; }

    public string? EnName { get; set; }

    public bool Closed { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CloseDate { get; set; }

    public bool IsMainMonyBox { get; set; }

    [InverseProperty("MonyBox")]
    public virtual ICollection<GrantedDiscount> GrantedDiscounts { get; set; } = new List<GrantedDiscount>();

    [InverseProperty("MonyBox")]
    public virtual ICollection<MonyBoxAccount> MonyBoxAccounts { get; set; } = new List<MonyBoxAccount>();

    [InverseProperty("MonyBox")]
    public virtual ICollection<MonyBoxTransaction> MonyBoxTransactions { get; set; } = new List<MonyBoxTransaction>();

    [ForeignKey("UserId")]
    [InverseProperty("UsersMonyBoxes")]
    public virtual SystemUser? User { get; set; }
}
