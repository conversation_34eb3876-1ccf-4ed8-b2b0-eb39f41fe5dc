﻿@model HalalaPlusProject.CModels.RegisterProvider

   @{
    ViewData["Title"] = "تسجيل مقدم خدمة";
    Layout = "~/Views/Shared/_sitelayout.cshtml";
}

    <section>
      <div class="page-header min-vh-75">
        <div class="container">
          <div class="row">
            <div class="col-xl-6 col-lg-6 col-md-6 d-flex flex-column mx-auto">
              <div class="card ">
                <div class="card-header pb-0 text-left bg-transparent  text-center">

          
                  <h3 class="font-weight-bolder text-info text-gradient">تسجيل مقدم خدمة</h3>
                  <hr style="    height: 4px; color: #7b68ee; opacity: 1;">
                </div>
                <div class="card-body">
           <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="City" class="control-label"></label>
                <input asp-for="City" class="form-control" />
                <span asp-validation-for="City" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Activity" class="control-label"></label>
                <select asp-for="Activity" class ="form-control" asp-items="ViewBag.Activity"></select>
            </div>
            <div class="form-group">
                <label asp-for="ServiceProviderRepresent" class="control-label"></label>
                <input asp-for="ServiceProviderRepresent" class="form-control" />
                <span asp-validation-for="ServiceProviderRepresent" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="PhoneNumber" class="control-label"></label>
                <input asp-for="PhoneNumber" class="form-control" />
                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Email" class="control-label"></label>
                <input asp-for="Email" class="form-control" />
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>
               <div class="form-group">
                <button type="submit" style="width: 100%; margin-top: 15px; margin-bottom:0;" value="Create" class="btn btn-rigester mb-0 btn-block" >تسجيل</button>
            </div>
        </form>
                </div>
                
              </div>
            </div>
            
          </div>
        </div>
      </div>
    </section>