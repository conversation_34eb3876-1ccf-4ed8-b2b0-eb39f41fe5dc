﻿@model IEnumerable<HalalaPlusProject.Models.CustomerGrantedDiscountsView>

@{
    ViewData["Title"] = "Index";
}

<h1>العملاء و سجلات النقاط </h1>


<table class="table">
    <thead>
        <tr>
            <th>
                الاسم
            </th>
            <th>
               الجوال
            </th>
            <th>
                الحصالة
            </th>
            <th>
                اجمالي عدد النقاط
            </th>
            <th>
                اجمالي المبالغ 
            </th>

            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.CustomerName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.CustomerPhone)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.MonyBoxName)
            </td>
            <td>
                    @Html.DisplayFor(modelItem => item.CustomerGrantedDiscountsCount)
           </td>
            <td>
                    @Html.DisplayFor(modelItem => item.CustomerTotalAmount)
            </td>

            <td>
                    <a asp-action="Details" class="btn btn-outline-info tablebtn" asp-route-id="@item.MonyBoxId">استعراض السجلات</a>
            </td>
        </tr>
}
    </tbody>
</table>

