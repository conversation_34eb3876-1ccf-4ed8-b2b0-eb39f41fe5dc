﻿@model List<HalalaPlusProject.CModels.GrantDiscountModel>
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer localizer

@{
    ViewData["Title"] = localizer["offers"];
}
<style>
    .modal-backdrop.show {
       
        z-index: auto;
    }
</style>
<div class="row">
    <h3>@localizer["DiscountsOrder"]</h3>
</div>
<button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#addDiscountModal">
    + إضافة خصم
</button>
<div class="row mb-3">
    <div class="col-md-4">
        <input type="text" id="phoneSearch" class="form-control" placeholder="ادخل رقم الجوال للبحث..." />
    </div>
    <div class="col-md-2">
        <button class="btn btn-primary" onclick="searchByPhone()">@localizer["search"]</button>
    </div>
</div>

<div>
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table id="tbl1" class="table table-striped text-center">
                            <thead>
                                <tr>
                                    <th>@localizer["customersname"]</th>
                                    
                                    <th>@localizer["phoneno"]</th>
                                    <th>@localizer["thecity"]</th>
                                    <th>@localizer["OperationDate"]</th>
                                    <th>@localizer["amount"]</th>
                                    <th>@localizer["discounts"]</th>
                                    <th>@localizer["rate"]</th>
                                     
                                   
                     
                                </tr>
                            </thead>
                            <tbody id="discountTableBody">
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>@item.CustomerName</td>
                                        <td>@item.phoneNo</td>
                                        <td>@item.City</td>
                                        <td>@item.OperationDate</td>
                                   
                                        <td>@item.amount</td>
                                        <td>@item.discount</td>
                                        <td>@item.rate</td>
                                       
                                       
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 🔹 Modal لإضافة الخصم -->
<div class="modal fade" id="addDiscountModal" tabindex="-1" aria-labelledby="addDiscountModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addDiscountModalLabel">إضافة خصم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="addDiscountForm">
                    <div class="mb-3">
                        <label class="form-label">رقم الجوال</label>
                        <input type="text" id="phoneInput" class="form-control" required />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المبلغ (Amount)</label>
                        <input type="number" id="amountInput" class="form-control" required />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">نسبة الخصم (%)</label>
                        <input type="number" id="rateInput" class="form-control" required />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">مبلغ الخصم</label>
                        <input type="text" id="discountAmount" class="form-control" readonly />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المبلغ بعد الخصم</label>
                        <input type="text" id="finalAmount" class="form-control" readonly />
                    </div>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let table = new DataTable('#tbl1');

      
        document.getElementById("rateInput").addEventListener("input", calculateDiscount);
        document.getElementById("amountInput").addEventListener("input", calculateDiscount);

        function calculateDiscount() {
            let amount = parseFloat(document.getElementById("amountInput").value) || 0;
            let rate = parseFloat(document.getElementById("rateInput").value) || 0;

            let discount = (amount * rate) / 100;
            let finalAmount = amount - discount;

            document.getElementById("discountAmount").value = discount.toFixed(2);
            document.getElementById("finalAmount").value = finalAmount.toFixed(2);
        }
         
        document.getElementById("addDiscountForm").addEventListener("submit", function (e) {
            e.preventDefault();

            let phone = document.getElementById("phoneInput").value;
            let amount = document.getElementById("amountInput").value;
            let rate = document.getElementById("rateInput").value;
            let discount = document.getElementById("discountAmount").value;

            fetch("/CustomDiscount/AddDiscount", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    phone: phone,
                    amount: amount,
                    rate: rate,
                    discount: discount
                })
            })
            .then(response => response.json())
            .then(data => {
                
                if (data.state === 7) {

                     Swal.fire("نجاح", data.message, "success").then(() => {
                     $("#addDiscountModal").modal("hide");
                         $(".show").modal("hide");
                    window.location.reload(); document.getElementById("addDiscountForm").reset();
                });
                      
                       
                   
                   
                }
            })
            .catch(error => {
                console.error(error);
                alert("حصل خطأ أثناء الإضافة");
            });
        });  
      
 
         
        function searchByPhone() {
            let phone = document.getElementById("phoneSearch").value;

            fetch(`/CustomDiscount/SearchDiscounts?phone=${phone}`)
                .then(response => response.json())
                .then(data => {
                    let tbody = document.getElementById("discountTableBody");
                    tbody.innerHTML = "";

                    if (data.length === 0) {
                        tbody.innerHTML = "<tr><td colspan='5'>لا توجد خصومات لهذا الرقم</td></tr>";
                    } else {
                        data.forEach(item => {
                            tbody.innerHTML += `
                                <tr>
                                    <td>${item.CustomerName}</td>

                                    <td>${item.phoneNo}</td><td>${item.city}</td>
                                     <td>${item.OperationDate}</td>
                                    <td>${item.amount}</td>
                                   <td>${item.discount}</td>
                                     <td>${item.rate}</td>
                                </tr>`;
                        });
                    }
                })
                .catch(error => console.error(error));
        }
    </script>
}
