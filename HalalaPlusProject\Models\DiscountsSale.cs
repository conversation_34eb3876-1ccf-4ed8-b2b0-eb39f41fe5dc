﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class DiscountsSale
{
    public long? Id { get; set; }

    public int? Count { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? SoldDate { get; set; }

    public long? DiscountId { get; set; }

    public long? UserId { get; set; }

    [ForeignKey("DiscountId")]
    public virtual DiscountsTable? Discount { get; set; }

    [ForeignKey("UserId")]
    public virtual SystemUser? User { get; set; }
}
