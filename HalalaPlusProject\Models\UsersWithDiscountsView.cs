﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class UsersWithDiscountsView
{
    public long Id { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    [StringLength(20)]
    public string? AccountType { get; set; }

    public int? Activity { get; set; }

    [StringLength(250)]
    public string? StoreLink { get; set; }

    [StringLength(1500)]
    public string? EnOverView { get; set; }

    [StringLength(1500)]
    public string? EnName { get; set; }

    [StringLength(1500)]
    public string? OverView { get; set; }

    [StringLength(500)]
    public string? EnBenefitfrompoints { get; set; }

    [StringLength(500)]
    public string? Benefitfrompoints { get; set; }

    [Column("lng")]
    [StringLength(50)]
    public string? Lng { get; set; }

    [StringLength(50)]
    public string? Lat { get; set; }

    [StringLength(450)]
    public string? Logo { get; set; }

    public long DiscountId { get; set; }

    [StringLength(250)]
    public string? DisCountName { get; set; }

    public double? Discount { get; set; }

    public string? Conditions { get; set; }

    public DateOnly? EndDate { get; set; }

    public DateOnly? StartDate { get; set; }

    public double? CashBack { get; set; }

    public bool? Deleted { get; set; }

    [Column("isActive")]
    public bool? IsActive { get; set; }

    [StringLength(250)]
    public string? EnDiscountName { get; set; }

    [StringLength(250)]
    public string? DiscountCode { get; set; }

    [StringLength(1)]
    [Unicode(false)]
    public string? Status { get; set; }

    public bool? UserDeleted { get; set; }

    [StringLength(250)]
    public string? EnCity { get; set; }

    [StringLength(500)]
    public string? ActivityName { get; set; }

    [StringLength(250)]
    public string? ActivityEnName { get; set; }

    public int CityId { get; set; }

    [StringLength(250)]
    public string? City { get; set; }
}
