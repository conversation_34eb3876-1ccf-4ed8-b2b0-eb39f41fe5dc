﻿@model IOrderedEnumerable<HalalaPlusProject.Models.SystemUser>
 @using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer localizer

@{
	ViewData["Title"] = localizer["Business"];
	// int Id = ViewBag.id;
}

<div class="row">
	<h3>@localizer["Business"]</h3>

	@*<a href="add-consol.html" class="btn btn-primary px-5">إضافة مستشار</a>*@
</div>


<div class="row">
	<div class="col-12">
		<div class="card mb-4">
			<div class="card-header pb-0">
				@*  <div  style="display:10px">
                <h3 style="display:inline;">الأنشطة</h3>*@
				@*   <a asp-controller="Activities" asp-action="Index" style="float:left;     margin-right: 10px;" class="btn btn-primary  px-5"> @localizer["displayactivities"]</a>
                <a asp-controller="Activities" asp-action="Create" style="float:left;     margin-right: 10px;" class="btn btn-primary  px-5">@localizer["addactivity"]</a>
               *@ 

					<a asp-action="Create" asp-route-id="@ViewBag.id" style="float:left; margin-right: 10px; " class="btn btn-primary px-5">@localizer["addnew"]</a>
				<button onclick="SendBulkMessages(@ViewBag.id)" class="btn btn-primary tablebtn">ارسال للجميع الصيغة المختصرة</button>
				<button onclick="SendBulklongMessages(@ViewBag.id)" class="btn btn-primary tablebtn"> ارسال للجميع الصيغة المفصلة </button>
				<button onclick="displaymesage(0)" class="btn btn-primary tablebtn"> عرض الصيغة المختصرة </button>
				<button onclick="displaymesage(1 )" class="btn btn-primary tablebtn"> عرض الصيغة المفصلة </button>
				
				@*</div>*@
				@*<h3>مقدمي الخدمات</h3>*@
				@*<a asp-action="Create">اضافة مقدم خدمة</a>*@
			</div>
			<div class="card-body px-0 pt-0 pb-2">
				<div class="table-responsive p-0">

					<table id="tbl1" class="table table-striped text-center">
						<thead>
							<tr>

								<th scope="col">#</th>
								<th scope="col">@localizer["name"]</th>
								@* <th scope="col">@localizer["activity"]</th> *@
								@* <th scope="col">@localizer["busnissno"]</th> *@
								@* <th scope="col">@localizer["mail"]</th> *@
								<th scope="col">@localizer["phoneno"]</th>
								<th scope="col">@localizer["options"]</th>
							</tr>
						</thead>
						<tbody>
							@foreach (var item in Model.OrderByDescending(o => o.Id))
							{
								<tr>
									<td>
										@Html.DisplayFor(modelItem => item.Id)
									</td>
									<td>
										@Html.DisplayFor(modelItem => item.Name)
									</td>
									@*  <td>
                                        @Html.DisplayFor(modelItem => item.Activity)
                                        </td> *@
									@* <td>
                                        @Html.DisplayFor(modelItem => item.BusnissNo)
                                        </td> *@
									
									<td>
										@Html.DisplayFor(modelItem => item.PhoneNo)
									</td>


									<td>

										<a class="btn btn-primary tablebtn" href="http://mimassoft-001-site25.jtempurl.com/api/CardReportsApi/DownloadCard/@item.Id">البطاقة الافتراضية</a>

										<button onclick="SendMessage('@item.Id')" class="btn btn-primary tablebtn">اعادة الارسال-صيغة مختصرة</button>
										<button onclick="SendlongMessage('@item.Id')" class="btn btn-primary tablebtn">اعادة الارسال-صيغة مفصلة</button>
										<a asp-action="Details" class="btn btn-primary tablebtn" asp-route-id="@item.Id">@localizer["more"]..</a>






										<a onclick="setField('/BusinessEmp/Disable?id='+@item.Id)" class="btn btn-primary tablebtn">@item.Status</a>
									</td>
								</tr>
							}








						</tbody>

					</table>

				</div>

			</div>
		</div>
	</div>
</div>

@section Scripts {
	<script>
			let table = new DataTable('#tbl1');
			table.order([0, 'desc']).draw();
		   function SendMessage(id){

		var obj = new FormData();
		obj.append("Id", id);
		$.ajax({
			url: "/BusinessEmp/SendSingleMessage",
			type: "POST",
			//dataType: "JSON",
			data: obj,
			processData: false,
			contentType: false,
			success: function (data) {
				if (data.state == 1) {
					Swal.fire({
						title: 'نجـــاح',
						type: 'success',
						text: data.message,
						confirmButtonText: "تم"
					});

				} else {
					Swal.fire({
						type: 'error',
						title: 'خطاء...',
						text: data.message,
					})
				}

			},
			error: function (xhr, desc, err) {
				Swal.fire({
					type: 'error',
					title: 'خطاء...',
					text: "هنالك خطاء تاكد من صحة البيانات",
				})
			}
		});

			}
		  function SendBulkMessages(id){

				var obj = new FormData();
				obj.append("Id", id);
				$.ajax({
					url: "/BusinessEmp/SendBulkMessage",
					type: "POST",
					//dataType: "JSON",
					data: obj,
					processData: false,
					contentType: false,
					success: function (data) {
						if (data.state == 1) {
							Swal.fire({
								title: 'نجـــاح',
								type: 'success',
								text: data.message,
								confirmButtonText: "تم"
							});

						} else {
							Swal.fire({
								type: 'error',
								title: 'خطاء...',
								text: data.message,
							})
						}

					},
					error: function (xhr, desc, err) {
						Swal.fire({
							type: 'error',
							title: 'خطاء...',
							text: "هنالك خطاء تاكد من صحة البيانات",
						})
					}
				});

			}
		  function SendlongMessage(id){

					var obj = new FormData();
					obj.append("Id", id);
					$.ajax({
						url: "/BusinessEmp/SendSinglelongMessage",
						type: "POST",
						//dataType: "JSON",
						data: obj,
						processData: false,
						contentType: false,
						success: function (data) {
							if (data.state == 1) {
								Swal.fire({
									title: 'نجـــاح',
									type: 'success',
									text: data.message,
									confirmButtonText: "تم"
								});

							} else {
								Swal.fire({
									type: 'error',
									title: 'خطاء...',
									text: data.message,
								})
							}

						},
						error: function (xhr, desc, err) {
							Swal.fire({
								type: 'error',
								title: 'خطاء...',
								text: "هنالك خطاء تاكد من صحة البيانات",
							})
						}
					});

			}
		  function SendBulklongMessages(id){

				var obj = new FormData();
				obj.append("Id", id);
				$.ajax({
					url: "/BusinessEmp/SendBulklongMessage",
					type: "POST",
					//dataType: "JSON",
					data: obj,
					processData: false,
					contentType: false,
					success: function (data) {
						if (data.state == 1) {
							Swal.fire({
								title: 'نجـــاح',
								type: 'success',
								text: data.message,
								confirmButtonText: "تم"
							});

						} else {
							Swal.fire({
								type: 'error',
								title: 'خطاء...',
								text: data.message,
							})
						}

					},
					error: function (xhr, desc, err) {
						Swal.fire({
							type: 'error',
							title: 'خطاء...',
							text: "هنالك خطاء تاكد من صحة البيانات",
						})
					}
				});

			}
		 function displaymesage(id){

				var obj = new FormData();
				obj.append("Id", id);
				$.ajax({
					url: "/BusinessEmp/Displaymessage",
					type: "POST",
					//dataType: "JSON",
					data: obj,
					processData: false,
					contentType: false,
					success: function (data) {
						if (data.state == 1) {
							Swal.fire({
								title: 'نجـــاح',
								type: 'success',
								text: data.message,
								confirmButtonText: "تم"
							});

						} else {
							Swal.fire({
								type: 'error',
								title: 'خطاء...',
								text: data.message,
							})
						}

					},
					error: function (xhr, desc, err) {
						Swal.fire({
							type: 'error',
							title: 'خطاء...',
							text: "هنالك خطاء تاكد من صحة البيانات",
						})
					}
				});

			}
	</script>
}