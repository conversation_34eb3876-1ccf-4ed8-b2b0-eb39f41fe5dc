﻿using DocumentFormat.OpenXml.Wordprocessing;
using HalalaPlusProject.CModels;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using Microsoft.ReportingServices.ReportProcessing.ReportObjectModel;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة عملية منح الخصومات المخصصة للعملاء من قبل مقدمي الخدمة.
    /// </summary>
    [Authorize]
    public class CustomDiscountController : Controller
    {
        Models.HalalaPlusdbContext _context;
        IWebHostEnvironment _hosting;
        private readonly IStringLocalizer<CustomDiscountController> _localization;
        public CustomDiscountController(Models.HalalaPlusdbContext context, IStringLocalizer<CustomDiscountController> _localization)
        {
            this._context = context;
            this._localization = _localization;

        }
        public async Task<IActionResult> Granted(long? id)
        {

             
                var ob = new CustomClasses.GrantingDiscountsClass().retrive(_context, true, false, _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id);


            return View(ob);
            


            
        }
        public async Task<IActionResult> DiscountsOrder()
        {
            var ob = new CustomClasses.GrantingDiscountsClass()
                .GetDiscount(_context, true, false,
                _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id);

            return View(ob);
        }
        [HttpPost]
        public IActionResult AddDiscount([FromBody] AddDiscount dto)
        {
            if (string.IsNullOrEmpty(dto.Phone) || dto.Amount <= 0 || dto.Rate <= 0)
            {
                return Json(new { state = 0, message = "البيانات غير صحيحة" });
            }

            var customer = _context.SystemUsers.FirstOrDefault(c => c.PhoneNo == dto.Phone);
            if (customer == null)
            {
                return Json(new { state = 0, message = "لم يتم العثور على العميل" });
            }

            var discountValue = (dto.Amount * dto.Rate) / 100;

            var discount = new CustomerDiscount
            {
                UserId = customer.Id,
                Amount = dto.Amount,
                DiscountRate = discountValue,
                DiscountAmount = dto.Discount,
                ProviderId = _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id,
                CreateOn = DateTime.Now
            };

            _context.CustomerDiscounts.Add(discount);
            _context.SaveChanges();
            return Ok(new { state = 7, message = "تمت العملية بنجاح", url = "../DiscountsOrder" });
            
        }

        public async Task<IActionResult> Discounts()
        {
            var ob = new CustomClasses.GrantingDiscountsClass()
                .GetDiscount(_context, true, false,
                _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id);

            return View(ob);
        }
        [HttpGet]
        public IActionResult SearchDiscounts(string phone)
        {
            var userId = _context.SystemUsers
                .FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value)?.Id;

            var query = from ob in _context.CustomerDiscounts
                        join city in _context.CitiesTables
                        on ob.User.City equals city.Id into cityJoin
                        from city in cityJoin.DefaultIfEmpty()
                        where ob.ProviderId == userId
                        select new GrantDiscountModel
                        {
                            OperationDate = ob.CreateOn,
                            CustomerName = ob.User.Name,
                            City = city.City,         
                            discount = ob.DiscountAmount,
                            rate = ob.DiscountRate,
                            Id = ob.Id,
                            amount = ob.Amount,
                            phoneNo = ob.User.PhoneNo
                        };

            if (!string.IsNullOrEmpty(phone))
            {
                query = query.Where(x => x.phoneNo.Contains(phone));
            }

            var result = query
                .OrderByDescending(f => f.discount)
                .ToList();

            return Json(result);
        }

        /// <summary>
        /// عرض صفحة منح الخصم المخصص، مع تعبئة قائمة بالخصومات المتاحة لمقدم الخدمة الحالي.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج منح الخصم.</returns>
        public async Task<IActionResult> Index()
        {
            ViewData["Products"] = new SelectList(_context.DiscountsTables.Where(p => p.IsActive == true && p.Deleted != true && p.UserId == _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id), "Id", "DisCountName");
            return View();
        }
     
        /// <summary>
        /// البحث عن العملاء عن طريق رقم الهاتف (للإكمال التلقائي).
        /// </summary>
        /// <param name="search">النص المدخل للبحث عن رقم هاتف العميل.</param>
        /// <returns>نتيجة JSON تحتوي على قائمة بالعملاء المتطابقين.</returns>
        public JsonResult GetSearchValue(string search)
        {
            var allsearch = _context.SystemUsers.Where(z => z.PhoneNo.Contains(search) && z.AccountType == "Customer").Select(x => new
            {
                Id = x.Id,
                PhoneNo = x.PhoneNo
            }).ToList();
            return new JsonResult(new { Data = allsearch });
        }

        /// <summary>
        /// البحث عن عميل معين باستخدام رقم هاتفه الكامل أو بريده الإلكتروني.
        /// </summary>
        /// <param name="search">رقم الهاتف أو البريد الإلكتروني للعميل.</param>
        /// <returns>نتيجة JSON تحتوي على بيانات العميل إذا تم العثور عليه.</returns>
        public JsonResult SearchValue(string search)
        {
            if (search.Length == 9)
            {
                var all = _context.SystemUsers.Where(z => (z.PhoneNo == search || z.Email == search) && z.AccountType == "Customer").Select(x => new
                {
                    Id = x.Id,
                    PhoneNo = (x.PhoneNo != null) ? x.PhoneNo : x.Email
                }).FirstOrDefault();
                return new JsonResult(new { Data = all });
            }
            else if (search.Length == 10 && search.StartsWith("0"))
            {
                var all = _context.SystemUsers.Where(z => (z.PhoneNo == search || z.PhoneNo.Contains(search)) && z.AccountType == "Customer").Select(x => new
                {
                    Id = x.Id,
                    PhoneNo = x.PhoneNo
                }).FirstOrDefault();
                return new JsonResult(new { Data = all });
            }
            else
            {


            }
            return new JsonResult(null);
        }

        /// <summary>
        /// استرداد قيمة الخصم لمنتج خصم معين.
        /// </summary>
        /// <param name="id">معرف منتج الخصم.</param>
        /// <returns>نتيجة JSON تحتوي على قيمة الخصم.</returns>
        public async Task<IActionResult> getDiscount(long? id)
        {
            if (id != null)
            {
                var all = _context.DiscountsTables.Where(z => z.Id == id).Select(x => new
                {
                    Id = x.Id,
                    Discount = x.Discount
                }).FirstOrDefault();
                return new JsonResult(new { Data = all });
            }

            return new JsonResult(null);
        }

        /// <summary>
        /// معالجة عملية منح خصم معين لعميل محدد.
        /// </summary>
        /// <param name="model">البيانات التي تحتوي على تفاصيل عملية المنح (معرف العميل، معرف الخصم، إلخ).</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        public async Task<IActionResult> GrantDiscount(CModels.GrantDiscountModel model)
        {
            if (ModelState.IsValid)
            {
                GrantingDiscountsClass obj = new GrantingDiscountsClass();
                return Ok(await obj.grantDiscount(model, _context, _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }
        [HttpPost]
        public async Task<IActionResult> Activate(long? id)
        {
            try
            {

                if (id == null) return Ok(new { state = 0, message = _localization["selectoffer"].Value });

                if (await new CustomClasses.GrantingDiscountsClass().Activate(_context, id ?? 0, true)) 
                    return Ok(new { state = 1, message = _localization["opsuccess"].Value });
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }

        }
    }
}