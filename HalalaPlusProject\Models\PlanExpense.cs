﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class PlanExpense
{
    [Key]
    [Column("ExpensesPlanID")]
    public int ExpensesPlanId { get; set; }

    public int? ManualCommitmentsId { get; set; }

    public int? PlanAutoMonthlyCommitmentsId { get; set; }

    [StringLength(100)]
    public string? CreatedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [StringLength(100)]
    public string? UpdatedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(10)]
    public string? Deleted { get; set; }

    [StringLength(100)]
    public string? DeletedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? DeletedAt { get; set; }

    public bool? IsManualPlan { get; set; }

    public double? Expenses { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? ExpensesDate { get; set; }

    public int? PlanId { get; set; }

    [ForeignKey("ManualCommitmentsId")]
    [InverseProperty("PlanExpenses")]
    public virtual PlanManualCommitment? ManualCommitments { get; set; }

    [ForeignKey("PlanId")]
    [InverseProperty("PlanExpenses")]
    public virtual Plan? Plan { get; set; }

    [ForeignKey("PlanAutoMonthlyCommitmentsId")]
    [InverseProperty("PlanExpenses")]
    public virtual PlanAutoMonthlyCommitment? PlanAutoMonthlyCommitments { get; set; }
}
