﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Controllers;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace HalalaPlusProject.CustomClasses
{
    public class PointsClass
    {
        public async Task<Object> insert(CModels.Points model, HalalaPlusdbContext _context,long userId, IStringLocalizer<ServiceProviderController> _localization) {

            try
            {
                if (model.Id == null || model.Id == 0)
                {

                    var ob = new PointsTable();
                   
                    ob.Prize = model.Prize;
                    ob.Conditions = model.PointsConditions;
                    ob.PointCount = model.PointNo;
                    ob.UserId = userId;
                    _context.Add(ob);
                    await _context.SaveChangesAsync();
                    //return RedirectToAction("Edit", new { id = model.userId });
                    return new { state = 5, message = _localization["savedsuccessfuly"].Value };
                }
                else
                {
                    var ob = _context.PointsTables.Find(model.Id);
                    ob.Prize = model.Prize;
                    ob.Conditions = model.PointsConditions;
                    ob.PointCount = model.PointNo;
                    _context.Update(ob);
                    await _context.SaveChangesAsync();
                    //return RedirectToAction("Edit", new { id = model.userId });
                    return new { state = 5, message = _localization["modefiedsuccessfuly"].Value };
                }
            }
            catch (Exception)
            {

                return new { state = 0, message = _localization["anerroroccured"].Value };
            }
 
        }

           public async Task<Object> insert(CModels.Points model, HalalaPlusdbContext _context,long userId, IStringLocalizer<ProviderPointsController> _localization) {

            try
            {
                if (model.Id == null || model.Id == 0)
                {

                    var ob = new PointsTable();
                   
                    ob.Prize = model.Prize;
                    ob.Conditions = model.PointsConditions;
                    ob.PointCount = model.PointNo;
                    ob.UserId = userId;
                    _context.Add(ob);
                    await _context.SaveChangesAsync();
                    //return RedirectToAction("Edit", new { id = model.userId });
                    return new { state = 5, message = _localization["savedsuccessfuly"].Value };
                }
                else
                {
                    var ob = _context.PointsTables.Find(model.Id);
                    ob.Prize = model.Prize;
                    ob.Conditions = model.PointsConditions;
                    ob.PointCount = model.PointNo;
                    _context.Update(ob);
                    await _context.SaveChangesAsync();
                    //return RedirectToAction("Edit", new { id = model.userId });
                    return new { state = 5, message = _localization["modefiedsuccessfuly"].Value };
                }
            }
            catch (Exception)
            {

                return new { state = 0, message = _localization["anerroroccured"].Value };
            }
 
        }


        public async Task<Object> insertSales(CModels.Sales model, HalalaPlusdbContext _context, long userId, IStringLocalizer<ServiceProviderController> _localization)
        {

            try
            {
                if (model.Id == null || model.Id == 0)
                {

                    var ob = new PointSetting();
                    ob.Sales = model.SalesNo;
                    ob.Points = model.DeservePoints;                  
                    ob.UserId = userId;
                    _context.Add(ob);
                    await _context.SaveChangesAsync();
                    //return RedirectToAction("Edit", new { id = model.userId });
                    return new { state = 1, message = _localization["savedsuccessfuly"].Value };
                }
                else
                {
                    var ob = _context.PointSettings.Find(model.Id);
                    ob.Sales = model.SalesNo;                    
                    ob.Points = model.DeservePoints;
                    _context.Update(ob);
                    await _context.SaveChangesAsync();
                    //return RedirectToAction("Edit", new { id = model.userId });
                    return new { state = 1, message = _localization["modefiedsuccessfuly"].Value };
                }
            }
            catch (Exception)
            {

                return new { state = 0, message = _localization["anerroroccured"].Value };
            }

        }
       

         public async Task<Object> insertSales(CModels.Sales model, HalalaPlusdbContext _context, long userId, IStringLocalizer<ProviderPointsController> _localization)
        {

            try
            {
                if (model.Id == null || model.Id == 0)
                {

                    var ob = new PointSetting();
                    ob.Sales = model.SalesNo;
                    ob.Points = model.DeservePoints;                  
                    ob.UserId = userId;
                    _context.Add(ob);
                    await _context.SaveChangesAsync();
                    //return RedirectToAction("Edit", new { id = model.userId });
                    return new { state = 1, message = _localization["savedsuccessfuly"].Value };
                }
                else
                {
                    var ob = _context.PointSettings.Find(model.Id);
                    ob.Sales = model.SalesNo;                    
                    ob.Points = model.DeservePoints;
                    _context.Update(ob);
                    await _context.SaveChangesAsync();
                    //return RedirectToAction("Edit", new { id = model.userId });
                    return new { state = 1, message = _localization["modefiedsuccessfuly"].Value };
                }
            }
            catch (Exception)
            {

                return new { state = 0, message = _localization["anerroroccured"].Value };
            }

        }
       


        public async Task<List<Points>> retrive(long userId, HalalaPlusdbContext _context)
        {
            try
            {
                if ( userId != 0)               
                  return  _context.PointsTables.Where(p => p.UserId == userId&&p.Deleted!=true).Select(l => new Points { Id = l.Id,userId=l.UserId??0, PointNo = l.PointCount, PointsConditions = l.Conditions, Prize = l.Prize }).ToList();                      
            }
            catch (Exception)
            {
                return new List<Points>();
            }
            return new List<Points>();
        }
        public async Task<Sales> retriveSales(long userId, HalalaPlusdbContext _context)
        {
            try
            {
                if (userId != 0)
                    return _context.PointSettings.Where(p => p.UserId == userId).Select(l => new Sales { Id = l.Id, DeservePoints=l.Points,userId=l.UserId??0, SalesNo = l.Sales }).FirstOrDefault();
            }
            catch (Exception)
            {
                return new Sales();
            }
            return new Sales();
        }


        public async Task<Object> DeletePoint(long Id, HalalaPlusdbContext _context, IStringLocalizer<ServiceProviderController> _localization)
        {

            try
            {
                if ( Id != 0)
                {
                    var ob = _context.PointsTables.Find(Id);
                   ob.Deleted=true;
                    _context.Update(ob);
                    await _context.SaveChangesAsync();
                    //return RedirectToAction("Edit", new { id = model.userId });
                    return new { state = 1, message = _localization["deletessuccessfuly"].Value };
                }
                return new { state = 0, message = _localization["anerroroccured"].Value };
            }
            catch (Exception)
            {

                return new { state = 0, message = _localization["anerroroccured"].Value };
            }

        }
        public async Task<Object> DeletePoint(long Id, HalalaPlusdbContext _context, IStringLocalizer<ProviderPointsController> _localization)
        {

            try
            {
                if ( Id != 0)
                {
                    var ob = _context.PointsTables.Find(Id);
                   ob.Deleted=true;
                    _context.Update(ob);
                    await _context.SaveChangesAsync();
                    //return RedirectToAction("Edit", new { id = model.userId });
                    return new { state = 1, message = _localization["deletessuccessfuly"].Value };
                }
                return new { state = 0, message = _localization["anerroroccured"].Value };
            }
            catch (Exception)
            {

                return new { state = 0, message = _localization["anerroroccured"].Value };
            }

        }

    }
}
