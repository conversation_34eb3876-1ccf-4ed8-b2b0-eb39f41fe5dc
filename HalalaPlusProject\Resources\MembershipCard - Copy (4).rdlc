<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="Model">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>7adcd0b0-0a7d-49e5-97b9-068d2de8af43</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="MembershipCardData">
      <Query>
        <DataSourceName>Model</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Organization">
          <DataField>Organization</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MemberNo">
          <DataField>MemberNo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Name">
          <DataField>Name</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>Model</rd:DataSetName>
        <rd:TableName>MembershipCard</rd:TableName>
        <rd:ObjectDataSourceType>YourProjectName.Models.MembershipCard, YourProjectName, Version=*******, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Rectangle Name="CardBackground">
            <ReportItems>
              <Rectangle Name="LogoRightPlaceholder">
                <ReportItems>
                  <Image Name="LogoHalalaPlus">
                    <Source>External</Source>
                    <Value>=Parameters!IconPath.Value</Value>
                    <Sizing>FitProportional</Sizing>
                    <Top>0cm</Top>
                    <Left>0.1905cm</Left>
                    <Height>2.89148cm</Height>
                    <Width>4.8641cm</Width>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <PaddingLeft>5pt</PaddingLeft>
                      <PaddingRight>15pt</PaddingRight>
                      <PaddingTop>15pt</PaddingTop>
                      <PaddingBottom>5pt</PaddingBottom>
                    </Style>
                  </Image>
                </ReportItems>
                <KeepTogether>true</KeepTogether>
                <Top>0.01319cm</Top>
                <Left>15.9004cm</Left>
                <Height>11.67376cm</Height>
                <Width>5.0996cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Rectangle>
              <Rectangle Name="LogoLeftPlaceholder">
                <ReportItems>
                  <Image Name="LogoKhotwa">
                    <Source>External</Source>
                    <Value>=Parameters!CompanyIconPath.Value</Value>
                    <Sizing>FitProportional</Sizing>
                    <Top>0cm</Top>
                    <Left>0cm</Left>
                    <Height>2.89148cm</Height>
                    <Width>4.8641cm</Width>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <PaddingLeft>15pt</PaddingLeft>
                      <PaddingRight>5pt</PaddingRight>
                      <PaddingTop>15pt</PaddingTop>
                      <PaddingBottom>5pt</PaddingBottom>
                    </Style>
                  </Image>
                </ReportItems>
                <KeepTogether>true</KeepTogether>
                <Top>0cm</Top>
                <Left>0cm</Left>
                <Height>3.05153cm</Height>
                <Width>4.8641cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Rectangle>
              <Textbox Name="Title">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>بطاقة عضوية خصومات هلة بلس</Value>
                        <Style>
                          <FontFamily>Segoe UI</FontFamily>
                          <FontSize>18pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>White</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Title</rd:DefaultName>
                <Top>3.12209cm</Top>
                <Left>0.8382cm</Left>
                <Height>1.05833cm</Height>
                <Width>18.8341cm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="FooterText">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>الخصومات محدثة باستمرار في تطبيق هلة بلس</Value>
                        <Style>
                          <FontFamily>Segoe UI</FontFamily>
                          <FontSize>11pt</FontSize>
                          <Color>White</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <Top>9.91875cm</Top>
                <Left>0.8382cm</Left>
                <Height>0.635cm</Height>
                <Width>18.8341cm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="SocialHandle">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>@halalaplus</Value>
                        <Style>
                          <FontFamily>Segoe UI</FontFamily>
                          <Color>White</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <Top>10.6425cm</Top>
                <Left>0.8382cm</Left>
                <Height>0.635cm</Height>
                <Width>18.8341cm</Width>
                <ZIndex>4</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Rectangle Name="DataFieldsContainer">
                <ReportItems>
                  <Textbox Name="OrganizationEn">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Organization</Value>
                            <Style>
                              <FontFamily>Segoe UI</FontFamily>
                              <FontSize>12pt</FontSize>
                              <Color>White</Color>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Left</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <Top>0.27163cm</Top>
                    <Left>0.4191cm</Left>
                    <Height>0.762cm</Height>
                    <Width>3.175cm</Width>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="OrganizationData">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=First(Fields!Organization.Value, "MembershipCardData")</Value>
                            <Style>
                              <FontFamily>Segoe UI</FontFamily>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                              <Color>White</Color>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <Top>0.2032cm</Top>
                    <Left>3.8608cm</Left>
                    <Height>0.889cm</Height>
                    <Width>10.414cm</Width>
                    <ZIndex>1</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <BackgroundColor>#9991b9</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="OrganizationAr">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>اسم الجهة</Value>
                            <Style>
                              <FontFamily>Segoe UI</FontFamily>
                              <FontSize>14pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                              <Color>White</Color>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Right</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <Top>0.27163cm</Top>
                    <Left>14.6304cm</Left>
                    <Height>0.762cm</Height>
                    <Width>3.175cm</Width>
                    <ZIndex>2</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="MemberNoEn">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Member No</Value>
                            <Style>
                              <FontFamily>Segoe UI</FontFamily>
                              <FontSize>12pt</FontSize>
                              <Color>White</Color>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Left</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <Top>1.79563cm</Top>
                    <Left>0.4191cm</Left>
                    <Height>0.762cm</Height>
                    <Width>3.175cm</Width>
                    <ZIndex>3</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="MemberNoData">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=First(Fields!MemberNo.Value, "MembershipCardData")</Value>
                            <Style>
                              <FontFamily>Segoe UI</FontFamily>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                              <Color>White</Color>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <Top>1.7272cm</Top>
                    <Left>3.8608cm</Left>
                    <Height>0.889cm</Height>
                    <Width>10.414cm</Width>
                    <ZIndex>4</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <BackgroundColor>#9991b9</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="MemberNoAr">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>رقم العضوية</Value>
                            <Style>
                              <FontFamily>Segoe UI</FontFamily>
                              <FontSize>14pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                              <Color>White</Color>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Right</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <Top>1.79563cm</Top>
                    <Left>14.6304cm</Left>
                    <Height>0.762cm</Height>
                    <Width>3.175cm</Width>
                    <ZIndex>5</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="NameEn">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Name</Value>
                            <Style>
                              <FontFamily>Segoe UI</FontFamily>
                              <FontSize>12pt</FontSize>
                              <Color>White</Color>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Left</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <Top>3.31963cm</Top>
                    <Left>0.4191cm</Left>
                    <Height>0.762cm</Height>
                    <Width>3.175cm</Width>
                    <ZIndex>6</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="NameData">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=First(Fields!Name.Value, "MembershipCardData")</Value>
                            <Style>
                              <FontFamily>Segoe UI</FontFamily>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                              <Color>White</Color>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <Top>3.2512cm</Top>
                    <Left>3.8608cm</Left>
                    <Height>0.889cm</Height>
                    <Width>10.414cm</Width>
                    <ZIndex>7</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <BackgroundColor>#9991b9</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="NameAr">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>الاسم</Value>
                            <Style>
                              <FontFamily>Segoe UI</FontFamily>
                              <FontSize>14pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                              <Color>White</Color>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Right</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <Top>3.31963cm</Top>
                    <Left>14.6304cm</Left>
                    <Height>0.762cm</Height>
                    <Width>3.175cm</Width>
                    <ZIndex>8</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </ReportItems>
                <KeepTogether>true</KeepTogether>
                <Top>4.318cm</Top>
                <Left>1.27cm</Left>
                <Height>4.445cm</Height>
                <Width>18.161cm</Width>
                <ZIndex>5</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Rectangle>
            </ReportItems>
            <Height>12.68695cm</Height>
            <Width>21cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <BackgroundColor>#666eb3</BackgroundColor>
            </Style>
          </Rectangle>
        </ReportItems>
        <Height>12.68695cm</Height>
        <Style />
      </Body>
      <Width>21cm</Width>
      <Page>
        <PageHeight>12.7cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <LeftMargin>0cm</LeftMargin>
        <RightMargin>0cm</RightMargin>
        <TopMargin>0cm</TopMargin>
        <BottomMargin>0cm</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="IconPath">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="CompanyIconPath">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>IconPath</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>CompanyIconPath</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <EmbeddedImages>
    <EmbeddedImage Name="logoleft">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAASAAAACjCAYAAAAq0MNCAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEbAAABGwAcgn9VQAACdsSURBVHhe7Z0JeBXHle+PJLQLLUggJLEIhNg3A8IsNtgeb3HyxuNszrzY8XMWj+NkkjjvS/zGE0+cOOP5ZiZOZpz4i+3kTYKXOE5ix5l43wGDDUgIEPuOhDa0gEASkgBp6tTt6u7bt9fqe6/ucn58ze3uW6fqVN+qv6qqu6pTRhgQx5xo6oGOrn44cTLw2dl1Hvr6L0AjOyaIhCVF+fSJ32iC7FO8xxZ3AlS7o5WLzb6DXWzrVM4SRJIQJuFBRlt8kJgXIGzV1DHR2cvEpm5nm3KWIJKQMIlPOKJR45AUHkFMClA/60LV7myF1945Sl0pgkBiRHyC7H2KDxJTAoTjOa+9e4S1eNqg//wF5SxBJDmJKj64xYIA4VjOCy8foDEdgjASa+ITJuERjKoAkfAQhA1hEJ+wCQ8SZvFBRkWAcGD5iXX1JDwEYUYYhAeJJfGxso6qAOHg8gsv74fX3z2qnCEIIohYE58ItHr0RE2AsLXz+G/qobO7XzlDEEQQSSY+SMQFiFo9BOGCGBCfINsIdbmMRFSA8Lb6T36xlVo9BGFHGMTHbxSjIT5IxARow4eNfKCZIAgbYkl8otDlMhIRAXp83XbY+GGTckQQhCkJJD6y1mEVIBzv+enjW+n2OkE4McriE2QbTvHxGFXYBAjF50c/2URztwjCCX/1neMnimDB8OeMai0ZTary6QsSH4Jwib/6zvETRZCtD/FBS9Xah0O+BYjEhyBc4qOiCvxEoQkG2/MpPip+HGL4EiASH4JwAVZSnxUV8ROFajuK4z1mSAsQiQ9BuCAMlRSjkI0myDac4z3+olKRFiC820XiQxA2hKGS+okiyDYGxntCSZEToCfWbadb7QRhRxgqqp8ogmxjZLwnmEBk
nm/Db9jcCE8yASIIwpwUn10djo8owqUTwdnwF6uwHjFE46kFhHO7SHwIwprEEx/ckY9Vb20UH8S1AOGg809/sUU5IgjCSCKID2YhWHzk0VubiQ/iWoBwSY3OLprVThBmJIr4aPiLUVij8FiJD+JKgHDA+Y13jihHBEHoGU3xQbMwpB428dH7Yyc8AkcBwq7XE7+hcR+CMGO0xSccaFnAHflY9ZZuxAdxFCDqehGEOYknPvLord2KD2IrQPj2Cup6EUQoJD4awtppvMcMWwF6krpeBBFCvIsPuh8O8UFLYe1VeASWAoQDz/S0M0FEAMnKKi8VGsHaKR+j3lJGfNAEN0sBevEv+5U9giAEvls/kuY+U+XEkvgITAWIWj8EEQqJj2YpM96DaCa4ZzEZlVo/BBFMYohPoNLLIiz9dLkCaHshAoTzvaj1QxAaiSM+cqClsPbb5TIehQjQ63TbnSBUSHw0wtHlMhIkQPjUc92OVuWIIJIbEh8N/+JjTpAA1TLx6T9/QTkiiOSFxCcACo9X8cHgmom9cZAAUfeLIFiVIfHhhHu8xwxVgHDaBa3xTCQ78So+6Ha8iQ+iChCN/RDJTjyLj4Z8bMLS/3iP+whIgAiCkczig1bC0r/4eEMVIHr2hyAkkai0iKSZSrjERxDJwWYruABR64dIZny1fiRNfaTIiQXx0ZBLH+EChE8/E0QyQuIzeuKD+eACRN0vIhlJdvFB4ZEXH9zzaKxD5IMEiEhKSHyUHQ9oJnJpC/T5SKXuF5Fs+L7jJYHfFBNBfDAPxkufSgvOE8mEb/GRMPeZ4qiKDwbXTOTSRqwuO7WAiKSBxEfZcUlwcLm0EbvLnopTMAiCcECi/slX2QCJLj4IdcGIpCDag84+UuMkg/ggqX39tPwGkdiQ+LgnOLhc2ojbS55KM+CJRCba4uOX2BAf3JNMm5l5ueTqXDCCSDRGQ3x8pBhD4iOHzOUmASISEt93vCTwk2Iyig9CAkQQRiQq
k3zVNSIXk7CKJ/FBSICIhCN+B53lYhJW8SY+SMptd/1pRNkniLjHn/b4MpZCNfNr7zECEdqraOlRTX1cdGoBEQkDiY87ROjRFh+0JAEiEoJREx9JtAqsfHpEM/MWgQgdC+KDkAARhB8k6qBWgZVPj2hm3iIQoWXFB81U0zCID0ICRMQ98dT1Uk0kk9XMvEUgQvsRH5UwiQ9CAkTENSQ+zojQsSY+CAkQEbfEpfhIotl7i0mEHk3xQSsrSxIgIukYVfHxZe/NWIQebfGxgwSIiEv8tH6k8ZumhL1m4s1YhI5l8UFIgDxg9jtI/r6ED/yIT7RvuaupSSSrmXgzFqFjXXwQehLahlR28S+bWwoLF5ZB+cQ8yMhIgx/++3q4NBy4ZPm5GXDfvVfA2Z5BONlyDj7a2ghHT56BEbqiESPS4nPv3SsgJzcdOjr6Yf+BTthc2wgXL7EfVCJd1cSPLcd9BCKkb/HxcaG9WJIAWVBROha+/IUlUFVVyI9xftHg4CW4+95XWIEc5ucKx2bCo/9+I/9uhKkOCs+22lZ4+nc7oKdviIdJFrC84nUYVsQ5UsjWC7ctn4cfuAYqKsby3xPp7DoP657aAQ2HOvixW9TUJPwNNnEfgQgZL+KDUBfMhIklufDQA1dz8cFK5WZyI4ZJTU2B5TVl8L3vroVx+VnKN4kLthCrpxbBJz82B/7twetg7fKpyjeRQbZeyHS7xO9eUpwN3/7WSli1ZLLyjTNqahL+Bpu4j0CEjCfxQUiATPhfN86GMWPcCY8RtCktzYG/u3MZpKcl7uWtmJAH3/n6KnjgH9bA39w8k+d56MIl5dvw46Ne+CIgRABfvnMxTJ8UaA3bobrp21/3EYiQ8SY+CAmQgZKibLhi9SRe8GRB2zlziqFmUblyJnHAy7Jsfhn84HtXw7x5JTyv4lpdiJAA+fgpWMXwYayA+UtNS4FbPz3f1he/KWn27mMSIeNRfBASIAOXzS3zVeD1XLVmmrKX
OMydXgL33F0DGRmpvGLqGRi4qOzFBuEQHwHmdWb1OHdda4lkNRP3xiJkvIoPQgJkYMmSMmXPH1hgp00r4OMkiUJudjp8+YtLLLunA4PhFyDZyxdO8RGgL5PLCpSjYNTUJJLVTNwbSyQThOavfEx+fUBIgHSgWOBAcnNLL5xsPheytbX1wgj7JxgZHmHn+kzD4tbVdR6yM9OU0PHPdWuqoLg421R8kIGh8AqQj7ohDd71ama/ndnWwsrF0IUBJaSGVpmVTw9oJu6N9SFlWj8SJiH4jQPt+Ua34Qk3pKWlwI8fuh6Kx2WZChDetr7ve29Da2efcsY/sgIk3fqRMFNN/Nh6MNaH9C0+khdYzkpDb08tIMIV0yoKLcVHEM4uWNTFRwI1JYkk/XqZCOKDByRAhCuKCrKVPXOGL43AYJjugo2K+PitWdK4T1iETBTxQUiACFc4DabjA9DheApaVnx8IZGmauLH1oOxCJlI4oOQALkAB6bHF2bDjClFsGjWBFg6r4xvuF9ZXgAFuRmjU3FiCBSf4VGcBJcM4z6JJj4IDUJbkMZEZwETmLlzJkDNsnI+/mFHY+NZqKtvg7372uHA8W7lbOQxlqlIacDlCyvga/csY+mZF8fz5y/CPf8X58nJOeBHwMPZ9cKWXmF+JhTlZ0NOdjqMSUuFS8PDMDh4Ec71DUHn6X64cHE4xM4Nmol7YxFSRnwQ1Wy0xcciIhIgE2ZPL4Y7Pr8YKirylDP4+9n/FGLyInLo0Gl45bWDUL+vLSKCgJViclk+zJhWDOOKsiArK5230nAqRF/vEOw7cAoOnejm3aJw4SRA/f0XuAAp83QdwVjSxqTCtIoCqKoshqJxrMLnBCo8gg81njlzHo43noFDx7tgYPBS0CMQAr/ig9nJzc6AmoXlMH/eBJg1qxjy8tKVAKHghORjx3qg6eRZOHiwExoOtrsa+9K8dO+vCJmo4oOQAOnAv35raqbAbbct4EtvOImOFUKM6ra3
w6+eroO+8xf4sV+wq3fDNdWwds0UGDs2QzlrTlf3AGzd1gLvbzwKbV19voXQSYDOnRuCr3/3NcdxoCx2XS9j3dfVK6dAdXURZGePUb6xZmjoEhw9egbe33Actu9pg0Hd80ayAoTiV8261DdeX81auSX8yW7E7W8ufmNs+dXvaId33j8Kx1t6WP5DFTg4Rnfx60P56nrFsPggJEAKKD6fu2U+K5DT+bGs+OjBQrqttg0e+6+tvgZoM9PTYO2KSrj1s3MhPd1dRREVBO9OvfH2MXj1zQO+lghxEqCenkH4xn2vW44DodncqhK47W8XqS1LL9dY5Ofs2SF49rcNsK2hmV1TfsozVUx4PvupeTBr5jh+7Pe3Rt/QvT17OuGFP+/hQqRHi91dOvpQiSw+SNrCpZ97UNlPau64dTFcd21g7lY4xAfBeMrL81jLJRt27G5TznqjrCQXvvHVFXDV2in8YUCM041/IhwGrZ5RBMuXTYLDh7rg9NnQJ3ndMKk0H5bXlFum3cu6fm+8e0Q5CmZqeT585QtL4ZO3zIb8fBywd5cHPcImK2sMLFtWBlPKC2HXnna4iOMxLslmtp/563nwxTsW8WU2ZPwwQ8QzYUIOrL2yEtJGUuHg0W4uTFrs7tMRIRNdfBASIMbVrHXxyVtm8f1wFEgj0yoL4NIgwIGjXcoZd0yeOJavuIgiJltZhF0O6+qsWD4ZDh7ogq4z55Vv3eMkQNgCeot1Q/TgeM7NN86Gu760FMrKcvk5mTyYUVaWB8WFuVC3q0U5Y0/5+Fz41j0rYemSifw4XH7oEXHOmlkM2RkZsHt/Oz9m3yifzoiQySA+SNLfhi8bnwe337aQ71sVykATe4SPRbS398HevZ2we3cHnwd2if0FFt0DM0Scn/ybWVA12Xk9GQHe9r/v21dAYVFmWCoLxpGVlQbfuXcVTCs3n1DpBxyc1VPC/P/u36+GW26eqU5eDUc+EBHXyhXl8PG/mqmctQYXmLv/
u2tg6tT8sPphhoj/umsr4RPXsj9qHtISIZNFfJCkbgHhb3XH5xbD5MljeaExA8UFK9d/v3wInvh1Hbz85kHY+FEjbNraBG+vPwoffdQEqSljWBz5kMrk3CyeQKFkrQjWbdi0pcnxeRl8BODv77ocJk2y9ksGjAu7cQvml8IHm08Ebie7xKkFdOpUP7suJ/g+3tm6/ztX8rGeQN7DlwcjOIDc0ngOWk6dU84Eg/m950s1qi/RZPbsEhjouwRHTpxWzlgjPPMlPohEHv1eFdXeY0QYPKkFaNa0Yrj1U3MhhVV4s8KJ4rN7dyc88rPNsH033n0Jvd3ae/4C7NzTBjt2tPLb4gUF1i2WceOyoLmpF062nVXOmPOZT8yD1avcLYpm1vqys8PvsDtWWpIP2+qblbPOOAkQzhav29nKWiTVcNeXl/K7W07+633H3aGhYbh0aZg/UiBwyguycEEp7N/XCd09oeNbGG9+TibMnzeeH9vFJ/zBj97eC9DfdwEGzl/kzzalj9E6C075QkQYTHewfxiONDIR0rIbhIgt2cSHfybzXbBv3rUCli4pNS1QWBixEP7D9992ffdowrgc+PHD1/F9qzhxqY9/fOhdy1YQjvv884PXOBZyjOvixRH48KNmaGrqgfMDF6CEdTUWLpjAx5zc2P/w4Q1wGCuGC5zugmGXFIUDV4JErMKJSn7hwjBsr2+HQ4c74Mix09B8qle9vZ6dOQaWzi+H66+r4t0mxC4/4rf6zgNvsetgMiGWmdaw+D77mXkwfrz5nDZ0a8u2VqirbYa9zKd+dj31lSorYwz/g7W8ZhLr+lUwfwLn3Vxn5PEnt8PWneaCL2KIdtdLIrkgtLSVT5fogydtCwh/r9s/t4DfVbECu1279p9SjpzB531yMjNhRlWRacHEc9k56fx5lgGT1hRy7ZVVrOlebFuwsVAfPNgNj/znZljPuj2HT3TDieYe2M8qzgbWtSrIy4bKqc4i1Mi6LkddCpBT
C2j8+By+IVZh0G/szj7/+73w62frYSO+xqjxDL8zhy0fAb51pLG1h+WlEU53DsL8+RMsu7cInsfneE53DMCxpjPKWQXFBLtoGzadgJMsz6fPDMLIcAr0sRbOocOnYcPGRnj+jw3w7qbjPJx6Z02XHPrU3tUH23e1wva6VsjMSOfdbsTuOovvcGB6wweh3V5hmYzigyStAOGi6jfdWG1beF58aR909Xi7Y5Q6PAKrVk62jBd7F3gnqrWjVzmjgSZ3fn4x5OUFblWbgZW46eQ5eOTnHzLfzLschw53wpUrKyEz0/phSjw/MHAJtu1w1w1zEiA8LzYjohXQ3t4P//bTTVC/t81SgPWg3fGTZ3j3asXySepjCFZMnlII764/FvzMlS44vs+tuf0cNOw7BRs+PAHvfXActrJuKAr4WaWVqwa3ToaFHYT6XW1whInXggUTISM9dHlaPfgdPtg6fDEF9h3qVM5qSSSr+CBJexesclKRsmcOdhFOGB4ocwMWcCcqLO5CjS/KgYkTc20LM/Lsc7v4vCQr+lg35OlndypH1kyrLGSlXzmIEEJ83nu/Ef7p4ffgpIvrY+TQ8W5Y91QgPyI+I3jNxhVlwpwZJcoZhmwFcWGH00IaDp6Cf/7X9XwVRSu/9Fx9dSVf1hYRSSSz+Iww/5NWgHDukR14u11mfZtu1p3AloUdY/NxoFo50FFcaO8Tgsu87j2i/RW1Ys+hDv4UtB34UKB+wDfcYKXEevn00w2w7vkdUku2sjYP/9xc38RvCDix8vIpgR3ZCuLRDqe5/Pg/NvMntO1ECAUyN2cMrK6ZoiaR7OKDJKUAYd7T0+3Xasa5TTJg8//MGfunjbE5blZW83IzlT1r8NkjN2UPx6MOHjptWymwi5an/EUON5gu3j362WNb4a1NR3mLwQ8jwwB/fnmfcmQOVvJ5c0tgDOuqRQtM6VR3Hzz2+NbACQdWrZzE77rKEL1chaKm7dEJs+BCfJBUXi6ScHOq
xNgFM7NzsxkfyjPCu1gmdhkOoojgrHAzW7OtvcN5febcnAxT25DNA6Ll88tfbofahtZAITTG52IL/ETaiSNNp/n4l52oYqsO53rpzBy3oKJg8r3VliL2Gbj6wJtvHbP1DX/3KVPyYcrEsYFjJQ7XG9pwSwam42FLEfsSSF8f9mEG+iK2pO2CjTh0T8TsaBmcZnhfZF07JwG0Qv88ihP41g4nwt1aEBXwd8/vgY92nJTOp5kdti5ra52nXlRX4QsTlQMHZP0z45U3D0Bfv3M3c9qUwCRYLwg/nX/RUNQsSmRW9vK4TSopBQjrSF+//RIZJeNzpApnJutejXMYX8K1c8xw82bRonFZsn/Iosamzc3w+vuHI+JnS4v9Q5xIaWlexK+RVqmVT8a5/iHYvPmkKsJWLL+8wlPNTlTxQZK2BdTSan+HC2dL43wmr+C8I5z7ZEfHqV7TCtLHCrATpRNy+ZPMsQhWPFwZ8pnf7+D5kxFwxM6uyeHOJHZz8BkoN0j7p3ya1dBNynQUO3AZkJwsd2Nvwsdoi48sXpNKWgE6evKM4/Khc6sDj+97oarSuXnNH8s3AZf7dAK7htdeEVizKJZA8cHr+ctf4wJs3u92CZwKMN5ldPrdKsrzIMvhhZCREB+kpaMXztrcwODjf4xxBc6veBY++hIfSWTsZa4pf9lBMm74yP4HG5tsm8tf/OJlsGQuTtUwj0O/4Y2NK2smw/+5Y5FiHQqmVV/fDi3t50zjONXdD++tb7T1CQvwpz89B+6/9wqonlrIx3CM8YjNLWa2xs2pROLYx/0PvA2NrWd5eLwewiycG3ZTLzo8xJjKrknFBJzIy2xMtkj5htuli5dg69YW298QmTfbuVwJcFd6YxGZnrfZsFXC99l/bjeza+qGlNuTeC4YLvb18A/+ihdY8ZdJDxYifJbmF0/WwbadzbZ/ibBVcvttC/i+VVzIQw+vh8ONhukCOnAm+YMPXGUahx4RH67DU1vbCr19
Q0E/On6L77mfbDOjHuNAwTtn8kS1kbLyfKhZNtEyrhMnemDHDmXRNQff/YArBdx00wzb55cwX1u2tsIp1tUNJ25yhdd9RtU4PifO7jf8aEsL/PKpWuUovKipSvwOmq3y6QKzoHZ1RU9SCxBeuK/eWQOXX249xQDBuy8bP2iC1986CC04fqOcR4tq1uX62PUzWWUvDZyziAcrBS7Z+chjm/mUADu+9Xcr4LLLzCfJmiHEyAynOOxszbDLXzQJd74igZ2PR46chod/slE5Ch9qii7Ljh7NVvl0gVlQL1c+qQUIwVbQQ9+/hq+1bFdgsEBjme7uPg89Z4d4y6ioKBOKiwMD1U62OG7xwx+9DydYF8WJyaVj4fvfu8rRJyJ+6ejsh/t/8E7wvDWfqCUlXsSHbUk7CC1o7eyDpxzmGSEoBNjsLynJgarphfyNDriP553EB/nDH/a6Eh8E50u9+OJ+5YhIRPB5Lg913RE1riiJjxlexQdJegFCNmxrhFdfDyyoHomm+3vvneDPxbgFPXhj/WH+PE0k/CFGHz6G5bPCC/xEI2trtJMRH4QESOH5l3bDn146oByFBxSP7dvb4ak/OM9MN4LjROueq1dFiIQosUhNSw3cgg4nfuLzYOpHfIyQAOn48+v74dGfbeHLcPqp8EIwcPb2r5/ZLt3Px9n4//+ZOnjud3v43DQSocQhXA0gNY4odb2MQb2WSKN92iJ6LU8QuFDYzp1tMKm8wNUAsx4hEC0tvfDb53bDH/+yx9XCW3ZglEeOd0NdXQsU5ufw19EgNDgd3+AflDffOeJ4R9QOtQTEqfig30l/F8wOvEM2uaIQKsrzoaIin0/PGJufoUwITeGFp69vCLo6z0NL6zk4ebIHWtrOwvHmHl/NUjvwDhlOtpw6tRDKJubxhe5xeQ/x8+IEVFz281zvEJ+XZvc2CBTMnh65ZUf0kBZ6B1/p/OC/vOfpzSR61Es+SuKDeCnjIfaK3yRALsHLJS5U
iu7S47sv9d9FE+1HDU4dfcIH9u775hUwa9Y4SwHCv8J3f/MvgOsdyyIvPu4N1ZAe0/Jrh9fRDbLpSBioaGl6i0PGV7OgXsp7iL3OZxoDcon+gmPBFFvgeHTAdAOb5o/w6ZrV07j42DEwcJG34oz2nrYR75un0q+Ai1iZpm+xYRrqsYkPVhsuU8j3eRzOqKF4pfK6yaFayoqPB8xswiU+CAlQnIE/n92WmjIC1zLx+fz/XsB+a/tnlPBhOD/j2tYxW+OxzkilIYualstEtfDR8zIsafowDaf4INQFiyPKx+dBzWUVylEwY9LTYML4XKiqKuLvvnIapMbxn7feOgbPvLBLOeMN2TLspd6IoPolPN0g7Zvy6TYCLbxsit7wk55mq3y6wBjUl/ggJn6TAMURMyvHwT/+vyuVI3OchEeAAvToz7dC3e5W5Yw3PJRjFa/1RgT3IkAyfiGqncsItPCyKXojKBWPaWq+Kp8uMAaNhPgg1AWLI9o7A7O7RdfKbHNLd/cA7D5g/tLFBTPHw+WLKmDVZZNg2fwy5ayGh3LsirS0VJhTpXudDkOkMZG1+lYvnQxXLpvCW4CRQM2Py4xp4QN7+FTz/JkT+L4RfFvuSnYdV7E8hMV/D78x4jVviDFopMQHIQGKI3p6h6ChoYO3XmRBW7z79fiT2yxfO3TgaBfs3NcG2/e0QoOFSHnFrt7gYw2rV1UqRxo3f2wOrKiZAm2nzsGJ5jN8yRE7PNSxsDKGCeg1V4UuEnfDVTNgzeppcKqzD0629Ni+y80ONV8exUcGPymY2jr4TAIUZ2ze3Mg/ZUQIbYaGLsHPH9sCB453K2dDwb/omelpfMvKSAsqWHbFCcvaGCYmxrV6vNYbDD5pYj6/S/fSa/vgaNMZaGo9y9dctsIqCfTFbuF9dJX7Zx0kCDWYQ6bwGTJ89RM+jMrf5sH873Wx5K4Rt+mZodkqnw6YBfM1PuPCZxoDijPSWWX6+ldWwOLFgSa/U7dL
L1SHDp2Gdc/WQ1Ob9dtJsdtVUzOJP1jJjGHuvAnwH49t5q0vZGxOOq/UZ5VjAb5S6NN/PQ9yczP4mtUbPjgO9XsCC5Q5lcOszDFw22cXw6+erlUrwWrW5aqeWcJba3oGBi5AXX0zHGsOXhvaLIkZU4rgphtnwSAT3YaGNthc16R8w/LB/FzO8lpcHHiffXd3P7zzwVG+j2C3ELt+G7ZqazyraRgyhHm/+84aePTJj5QzAMtZF3bevFIYHLwY5NsAO96xq9VyWV4jVmk6odkpny4wBvUiDCHJuPSXBCgOwYcMF80uZd2WqTBnbgl/46YR1J2zZwf5IvF793bAwcMdcJi1JOyYMaWQd3nwzpgoPp//1EL4yxv7uQDhuYWzJzDBSIetO4PfKb/m8qlw+FgXX7ANueWmOXDgYAfs073FFYUGBfM8ExE9ZgKEA89mRRgXc1/Lumu9zB8hDiLcpNKxUMjXWk6BirJ8KJ2YB7/94y7+oOV1a6r4CyO37Wrm4zK3MLF8+dV9fOoNCsgX/vayoBUK08ekMZ8WwW+eq1dFHFuE2MLrZy0zPWYCpEfNB8sTrlV9xfKpfH6gXvDM0Nt5QbNTPl1gDBoN8UGoCxaH4MOD2/e2wc9/tQW+/u1X4Ac/Wg/r1u3kk1afebYBHvnph/C1b78K37jvdb4C46vvHXIUH2TmjPFQuz0gLF7/KmGFxcoswLdDLFmMr5/RCuPSBeW8a2KFCCnueqEPxq2Pidcb7x+Gy5cHP45wJRPOq9dMhx4muqfP9ENeXgZsqz2pPuVdxwRz7twJ3J2VLOz6DUfV1S2Hh53n66FHi+eV8a6hV1T/mZDhWuRvbTgCixaWcjGzQr1qsuLjAaMN+uqWkPQ8+IshSYDiGF552IZv+Hj3w+N8zaG3Nh6FXQdO8VczizBu6ekZgAkGgZjIWhFYafTFClssRnJzg18zk5OdzrtLeopZy+Ns76ByFIzXVw2Jyiv8mjV7PLz0yj7WvTwL
zUxYuliXKgff+qqQzXzuV8ZgcGH7XBevwRarGIg0Cljrquecuf/4mmsvoP9W43jqtfYoPkG4NDUGi6b4ICRAhErDgXZYs6YScrNYV4kdz55ezFsUQ0ETJlNg5crJvAJhecMNJ70uW1YOM6cV8xA4RnTNVVXw4bYm/LPPzyG9THyyTd6Hhd2qOXNKAq0LFiGmbbVh9/P6tTNg5652fixAHy4Oa35ub2iBq9dW8om66OPqlZWwe087d6d2RzPceP0MyM/LZN/hSpcB8cB9sS1gXdy2tuDXQJ9noo7CagS7YNOmFcD0SYXKmQCq37p48dpcvWoa6552wSWH1wt5Rb0e+gtjg8tgpvix1UNjQEQQleX5cNMNs3hl6ezqhz+/to8P4gpwDKisdCyUsq2gINCKwAHbV17bzys53lbC1kxtbTNsYyKgF6DqqeOgqDAHtu48qZwJsHR+OX+b7BjWjZs+vch2oa5hFt++vadg/ZYTQessfe1Ly+G/nt3OW2sCFNBrr5nB76YdONgJG7eeUCvOlPICuOG6ai4eKAooIIePnOHf4/EZJry//1MDDA0p8TGfppTl82Va9IPZyPzq8VBZOQ4GmEBVVxdDCrO3ygFejkOHu+DdTceYAAUPsCOqnc01MEOzUz5dYAzqVghMk/Dgrz4kCRARAhYQUSiCCgs7uPnGObCPdfEOHuvSawuHhxXGekMFFKrbb10Mzzy/Qx2bGV+UA1/9ynL48aOboH/Q/QsNjdGbCRAi6oXwVbVjOyiySHFhNnycie5TzK8gVKNAOBSm2z6zCJ57sYF345Ci/Cy4+0s18LMntkjdZtej+abuuUazVT4dMAYz/JS2hCThwV9jSFqQjLAFC0z5hDxYw1o3SxeX87GODR+y1odNibUqj9id6T83BHfduQyKxmbD4gVlsPbKSnj2dzuh08W7yfQYk1i+pALqG1rhos36OqoN28nPzYBP3DAbprIWzceur4b/fgXv9A3Cpz4xDw4e
6YRbPj4XDrCWCu+CKRnCXXyHGvpfyPxfMG8iXLN2Gjz/wm7o6HZ+q60Tmn/G3Nmjz5cbjMFGS3zwBLWACEtEgcEyVsJaKlgDO8+cVxsGVjiVSRzELi7I5q0gfB01dqX8zvfCW+vom93yt6qdspObnQEFeRn8dc/4fA6C0yXwTSll43OhtaOPnzOC3baSIuY/EzunNN2i+eb+OiDGPDlhFsyt9yG2PsWHf5AAEVa4L14aXuqPCBqN2e6qjUtjLbxMat6QTUuzUz5dYAwqLT6IS3/NbQMfdBeMMMVd0QomCnVVzi/l062xFj4KGYoixtyMtvggJEDEqCDKYDSW2vCCmkaUxEc2Pc1O+XTAGMxXt8fPtTGYkgARIcgUryjVV8+obsWgf5pv3pzzmic/WQ+x9eBrqK3yqYMEiIg6ohwme+tntBjNQWcjKV/4yos0CE1oSFQ+rxYy9VuYRFK0ouUXIpEUR7VzmV5IKJcJmwWLhHBRC4iIKtGs5F7wE3WiiY8ZvgasbSABIjQkaqGPMh1RvPglKz7RzLualh+ldIkxhUgOWJMAEVFDpu4Ik1gdL4pW68crIem4TNgYzIv4hKbpnCgJEBFAQh28W8QeMqIoi2xSqp1LZ0NCuUxY1j8kNE3n2DAECRARFWQqujBJlNaPL2TFxweRGHQWiBAkQIQU4SzodkSykvuJOlpdL9+5dxmBMVikBp0RvQ0JEMFKhO9ibotM9H4LdqSIpl+qncsLGBLKZcLGYJEcdDZ+SwJEeMZlueb40bZYa/0Ik6h2vVwS4lEUXAxN0z5Rs29JgJKdeKhMLvBiE80syyal2kXYWWPskRz3MSPlDnoSOrnxWHC8ljOvxVKElxpjcWni1SfEl18e0ZJwZx0SymWixmCuxcc0fvtErb6lFlAy41VNPBLZ2DW8pBMtn5BopBWShstEjcH8tULsE7X8ln1BAkTEDKKgSo2xSJi4RcYvWXe8tn6iTeglsPfTKRckQMmKRCX3
YuI9djm8pCPjk7CJxsCzV/EJCeXSRWMw+a6XywTNUExJgIiYQBRlqVaGj3oQCUbFHZeJGoNFcgDY0iXdFyRAyYjEX3MvJt5jjzwyPgmbuGj9RJjQS2DvgeW3hi9IgIhRR5RJav24IyQNl4kag0Wq6+XSHQ4JULIh8dfci4n32COPjE/CJhZbPzIYYx7NcR89JEBETJCsrR+v4hMSKl6ugcUXJECELV4aADJ1IdL1x49PUqIYQULScJmoMVjUu142ZiRAyYQXNYkiidD6kUHLduQyY4w5FsZ99JAAEZZ40SuZAihbaN3ix6dEaf2MOg5+kgAlC17UJIrEYuvHi0+yaElIpOXSxBgslrpeCH5NAkT4xqGcmSJj44Vo+SRloxq5s5ZJw0isiY+ABCgZkPiLLmESUVR3IuxXNFo/XgjxxqV7sZWLUIR/JECEL2QKurCJVGX345MXpGxUo8jkHTHGHItdLwEJEBFCrLV+VCLsV6K0fmQYDfFBSIASnRitVFKDzy6QyW3UbFQjCWuXJsZgbls/owUJEBGEF72SqEbyRDixmG/9SBDLXa8AAP8D2HI6eeTLfVQAAAAASUVORK5CYII=</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="logoright">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAAQgAAACjCAYAAAB2WoBmAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEbAAABGwAcgn9VQAAC9KSURBVHhe7Z0HgFTV2f7f7YW2VBGkiCgoTWkSVFBQE7tRY2KJqLGCir3FmPz99EtiiRq7yf9LrEkgiPKZRBQJSgSDAoIFotJ7Z9nd2TJbvvd5z7mzU+7M3HtndnZ25vzg7szccs6997znOe8p99ycS657s4niEHcHNyQ1MIMhPeh7SCdqV1pA3bqWUPeupdSPf8tnn056j7aJI4EARiQMBm8ceUQ3XrqKaIw++mC9tm3gWCAskpa3jUgYshQIxugRPWkUiwW8jHTGtUAAIxIGQ3JA1eS0yQNYMA6mUq6ipBueBAIYkTAYkssJ3+lDp006LK3aLTwLBDAiYTAkH1RBzj9zkHy2NjmXskAkmj+Tkr+NSBgMIaSDUIhA4EtaiAQwQmEwhACB+PEPhrZK1SNv+JiLfoEvOfLTO4keHyBpARkMmcHuPT56f+F68vn8dPihXaigIE9vaXly5W+OypVGJAyG9OWd+WvpngcX0KefbdNrWh4lECBIJBLJn0YkDIaWY/deHz3+/BJ6/qVl4lG0NM0CAbRIgERFIin524iEwWDLwsWbxJvYsKlcr2kZQgUCQCTSqcphRMJgsAXexL0PLaAPF2/Ua5JPpEBYGJEwGNoEL7y0XKocLUHOj69/K3bHYtAoiUR6IJPSe2m6QA2GqKA79JbrxiZ1yHZ0D8Iiie0SCWM8CYMhKqu+3k0P/uajpDZexhcIYETCYGgTbNxcnlSRcCYQIEmNl0YkDIaWJZki4VwgLJIkEgnn8aQEYjBkJskSCfcCAYJEIpE8mpT8bUTCYLAlGSLhTSCAaZcwGNIeiARGXnolN6G8ZUTCYEh70LvxgsdxEnkjxvzoF5K3kjDGIFGRyOFzkM8EluBxGwaDQbFxUzl1
8zDLNlcxJFsFOwMJkVAwSTiHnGRdiMGQYbzIXoTbZzd0G0SzSCQjfyUURDLiNyJhMNjy+HP/dtVoGdRI2ZypjEgYDJkJJp95Zcbn+ld8wnoxki8SnoNJRvxGJAyGCBYu3khLHU46Y9PN2Zytk5W/PAeThPiNSBgMkbzwR2cTztgIhIURCYMhU/FV+2nW26v1r+jEEAhgRMJgyFTmvr8mbq9GHIEAzSKRjDzmOYhkxG1EwmAI4dU4DZYOBAI0Z6y2LhIGg6EZjLLEEg2HAgEyQySMF2EwhPLG/0Zvi3AhEMCIhMGQacTyIlwKBEDmUhnMiITBkBlE8yI8CISFEQmDIVOAB2HXo5HY495GJAyGjOGd99fob82IB5FYFlFHG5EwGNo2GIIdProyt0nnjcSyiDraiITB0LYJfzGweBDJFolE85rnwxON14iEIcsJr2YEGimTKRLAiITB0PbAHJa79vj0ryCBAMEi4T2bNB9pRMJgaHsEPwoe0c1piQTwnk2ajzQiYTC0LWIKBDAioTAiYchGMCbC6s2wFQhgRMJgyF6soddRBQIYkcA5J3jSBkMb5CsnAgEgEpZQeM8qzUcakTAY0h+8RwPEFQgLIxIJnrTB0IZwVMUIJyNEIgGMSBiyCTy85fphrTYvEonGaUTCkCXgHRriQbg1eSMSCQZgMLQBxIPQ341IGAyGELRABGVU/emUbBYJ40UYMh28O0N7EEEZVX86xYiEwZCZ4KGtoF4MGLsyeLdmn04i4YkE4jQiYchUAo2UoSiDd2v26SISjg5tshb+w0tTU6OsaMJ3/c8N6SgSgSuR69OL/pcsAiG2YByG1sVGIIAyeLdm3yZEQmwXxtxADQ311FBfF1gaG/3U1NjAm90berqIBM65ka+t0bq2hqAF18jrsd3NtYXjLI7GhOIwpAc5U6bOiZGKapPbZM7RB3g3Dx2v9wCEiMN5BZu2iAAM2V9fS01szCj5kMFzcvMpL7+A8vIKKZe/
5+SoNlzeogOIDcJpTZAhcW31fG31/hoWPBYC8Y4UuJ7c3DzKLyim/PxCvt48x9dm4SQOhI04cvMKKBf30NBmiSMQQG12a/ppKRIcWGNjvRh2bU0lL1Vs6DVs8I1i2Hls2IVFpWzcJfIJocjJzXWViVpTJFCq1/tr+boqqI4XP38Xj0gDQSgoKKLC4g5UxEs+f8/NydNbnREvDohCEd+7opJOfA/b8W8WWpciZEgfci6f+lZci/ae2dURnrOM53g1QQc2l3y1VFN9gKp95dSuKIfOPnMcTblsguyzb18VzZy1hN6d/yU15ZawobcX0RADd2HjraIRHGkDix8ybnXlXpo0YTDdfuuZemMof3/nS/r/r/5bRCKPPSVWR70lDmFxFBU00D13/oDGjhmgd2jmvl+8TWs2VVE+i6zj8A1phyP/z3vbgjrCs3l4jlcTdiBcYZR+dbU+6lCSR79/YRoLQC5Nu/E5uuSyR+iG6c/SgEN70L13nE35uVyPbvDLMW5prfyAc4XL72cPqb6+Xq+NpLQkP6Jq4JTgOKZee4atOIA+vTuLoOCfoe3iuILYZkVCg+Ph/sO4Gzjjn3P2d+iDD1fTli276ZmnrqfXXr6Dnnrienr4sT+zp9FEI4f1oXoIBFc/vBh5qkVCzhGuC2dgZOKBhx2st9ig93ObgcPjaO02F0PL46oFqU2KRNBBkh3EwJvojNOPoVdfn88ZOYfmvreSbrn1d9SlS3su+XrSS6/Op/HfOUJlBGSKNoS6PqKOHUv1GnsSydxWHIbMx3UTc1sWCWluRNHOS+eyUtqzr1yM/bunDKfHf3M1LflkLa3ZsJVqavyyn7T6J+AKJHBowixd9o3+ZjB4x1MfVNsUCSUO6ObDsm+/j/r16SFVjrnvrqSHfjmTunRuR0WFBdSjexn5quulyxMiIaLikdYUCYMhUTy/vLdNigTGAHCmLywsoT++9CFNu/4sqqtTjXnz5n9CXbu2p5MmHEO333oW
fbRojerm1P34iXTVBY4U11wvhtbHpEVcxPq9mn6riYTG7fHiQeQXUgELxPsffkVVVXV06SWT6L15y6h/vz709LNz6bprTqV/LviGvl6/XzwNuACWOHgSCdgg/5PG0cZ6aSDFp4wdMMbZKlg9MUgHfJq0iE6giuE1s7aKSHg8WRkMlZsvg6BKSsvoiWfeo5deXkhnnTmerrz8NBo79ih6/Mn5NGPOF2r8AwQiEQLigCHJNVRXU0k1GFxUWyVC4aWb0ZAALAJWL1a9v1rSwe/3mbSIASrY+qvnfNeqIuH2WBEJrjpgEFRJuzJataacfvviQnryhYX0h9c/pW8xuCe/SIZay/5hMTj2IixxaFAjNzE4y1e1h3yVu+U7xhHgeQVTcqUGCAA8BhlFW11B1VX7OT32UY3vgKwzaWGPrmCz0WuhcJvhLLyLhMLTcV7iRJUBDZX5BVLVKC5uz0sH+SwoKGHxKAh4DtHEIK5IBImDn0uqGjHIfTSgTxn9189+SK/8/mrq1C7flFqpQnsOShyUUBfm1dKkE46ga66YQAX5nFomLWwJ7cVoFZFo3ttTvJ7i5P3RO8FeAp4dwMNF+BRhgIDof7GQ7ShwIAZsgMELSiOMwoQ44JkPiMNh/brR07+9isaM7k9lZSVU1rFEDBf/DC2IpAcezlPPj/h8+6msQz69+Oy1dNstZ9CZpw+ng7t3NGkRhVCBAFkkEoJcb7Mo4J8dynyC/rEI4BMlU+CxZzzhyIYo7Q11PimtIA5dOhXSgw9crENSbN15QMdtSAjO2JK5kR42i7Q5cLpgeD2qFtRYQzffeK4MirOorvWbtIhCpECAVhYJT3iKUxPjIC0HyghFDPwhQoBGLpRM6gGw/VynLZf6rarj7ufjaunmm84LMchlyzdRnb9Boo0mSIbYBDI/2hX0PBThC567UemDJ3c5jfjzmitPp3HHHqZDIdq/v5q27640aREFe4EArSgSXuO0SPR4ECwMVsu3tCeI
GJQHCcE+qqrYTRXlO9Syfzt/7qRKXldS2EQP3D8lxCDB199s45NkczSllmuUMKjuYvREWOKMNIFAhy77yFe5l6oq94hYn3/OeLrg/GN1SIqPFn3L9oJ0MGlhR3SBAG1NJLyeKAg6VoQBn1oY6uvQ0KiqC8Fi0K0slyaOH0AnnTCILrnwBLrzlu/TnbeeR3fddj7dc/uFNGf2zyLEAVRUVEu1pBEPgrHBG+ITIgycHtLYWIn02EWVIsw7qENxPfXgNOnXq4QmjOvLS3868biBNHnCkfT7Z2+gG6edpkNrZsvWvRJuorNsZSo5V0yLN2EMg3oePuSve7zNJ5FAnJ7i08i1NrEhQhxU3VVKKvSZ11bT4MN705gxg+jsM0eFVBvcsHbtLrrpjteouLST9KTk5uqZlxLwKJCB4Faj5KxkD2byiUPpnrvO01tDWbBgNT3x/AIZCyLjPfRo0XiEx3Hnrd+nU08ZpreG8sLvPqIFize5Ct8WeHBaHBo5bnQP++tqOF2qKC+3gY4adAiNG3ckTTrxKE/p8enSDfSL/54t96KgsFRNcJPI+WYYzu5Egp6EhbvjE4hTH+TlWJQiauwCew1cjfBV7aXKA7tp2OCD6YWnp9IzT11Dl1820bM4gAEDulOXjgVSCqJ+jLYMGV1pvIkQRJD4vlhjF1BNqKpAF2UNXXzheHr1D9Pp0Yen0AXnjfWcHqNH9eMCDD0clSw8PhFAVCkhTAanAhGEp0wXdJC749XeXuK0DnJzrHJj1WQo1mCazh3y6K7bL6BHfj2FBg48SO+ZOE/+5nIac/QhMjMTButAJBC3EQmFlRZqkBmEeo9U7yZxdWHWX+6gKT+ekJBIB/Ozey+ksvY5UiBAKBCnEWyFc4EIcn+9ZNhgkfCCp8P1QU6ODRgk6rfSX76Phh/Vm55+8lo69WR7NzoROnduR/ffdz7dftMZVFzg50xQIV6LEYlgcVBtDRBqjF345X9dRnfdcY7eK3mMHX0o
vfyHaXTi8QNFiBCnjK7kc+CT0XtlJ+48iCSJhLtjvcTkjghxYIM854wx9PCvL0taKRWNyZOH0HNP/YQ6t88NlF7ZbJih4qAGNnXm6tizv72Wjh0b2eCbTDBwCiNdmxqq5LkZqW5kuVi7b41pRZHwEp91UNRjOSPKZLaoVgTEYTRNve5UvUPLA2/i8Ud/LCKR1YaJBkkrLbQ4dOlYSM89fX2LC7UFRrred/cFfC5oDEVvU3Y/yOVeIEAGiQQyIjIkxAEzXY8YcogrccDEM3//x+f0zHP/lOXJp96lRx6bQ3v3Vuo9nGGJRJeO+VlrmOFp4VUc/rlgFc3/5yqa9/4XMhmQW0Qk7jqP8nLUgLhs9ug8TxgTLBJeSAeRUO5sPdVJv3qFDIl++FeX6a2x+XDhN3T3T2fRlde/Qv/z2hJasHgDzf/XtzR3/kpatPhLTyUeROK+e87NSsO00gLiCIHACNSHHnBWxcNoyJmzltMtt8+gc3/0PD3BQv3YU+/Qrx59gx75zV/0Xu5A78a0ayZLdyqe48jWqoZ4EJ4yHNAi4fX41hYJGCVKLHRv1db6ZIx+POAxQBiefP4DWrfFRyWlneT9EnghDUKGkQ8bZj8VPIjnWaALdMrFx5GfzyebqhoiEJIW1VzN8tFFF57kqNcIwjD1lj/TzDlf0LY9DVRcgvRoLy8FwkuPRgwbqPd0z4kTB9Gk4w/nc0K7UHb2agSqGJ4yHGgVkVB4PmdGDFIex8agGx8dP+5IGhunEWzZ8o10/U2v0NrNlVRUwqJQWEp5MneEejwcRtRQ76czTxstv8N54Xfz6errn6Mln67Ta+w584wR1PugUvEismG2o9C0qKK+vbvQFVNO1FvtgVDfc99smvHmCrahEhlwBlHAwCx5bSL/wysLvjPuKH1EKKiG3HTzH+RlSbGYfuMp1L6EhZ/FK6sFAnjOcAmKhIXz4xOIyToUDWIYEMUlFkbkTb/xDL3BHojDI0/M5TtWSkUQ
Bnl/Zx5fOhsib2/EyEs2oo7tiqIKzRdfbqUafz49+MuZcUXiyitO4jDhQWT+EGB5RF6qF6ph8Prr7N8IZiFe3H2zaM2mCiooatcsCjICUjU6Y0h2QUFOxLMXFn97ZyWt2bCPpt/2UlyRuPD8MSJesJdsE4mIRkrPWS8BkbC8CHckEJ82IpTQMMgJxw2JWdddt263iENTTjEbHV5KC2NEGaVih9HgZbYIa8iQfrIuHNSTN++oouLSjpSTV0qPPv5WTMMcdUxf6ndwJznPjDZKFgfVi6SevBw5YkDc7kyIw76KJnmHqhJpbcYSVlBaDO6r1tuwaUu5eIH7Kxq4yvgnvdYezBnRviRXVzOyq7EyQiCAp/wKkiAS7o71EB/SFyUWl8zoKYBhnnfuOLUtCk89O1/EIZ/d2BwWB8QoLiz+aQOXh4jYMKNVLz5esl7c4EL2PvDy3OraHPr1I2/prfacespQ8UrEKDPUMHEPZUIXnRannjJSb7Hn4Ufn0t7yhkhxYELCYvEfd+xgvSUUNDD7G3Olioi02MrC/djjf9Nb7Tn9u8MlfUWss0gkbAUCeMnkQhsQCculhREd1K1DzMawv76xnLbsrBZxCBhk0DXCYGCQaOnuwWFFq14sW7ZBptxH1QR15YKiUvrsi43SFReN008bRoXsJrMCifFnIrh/Tey6S/WsfVHUh78AMvbHyzby/WMvLkwcAMJCNaCBhQZhRatefLxkrcw7iqoJPEKkxYeLVtO3a3bqPSL57qlDVBUDHl2GpoUdUQUCuMl0dng5vqVFAokLQ6qvx/wONTR82KF6iz1z531lb5ASGYcFoWGDxAxSp54cvfT76pudaoq7XMysXaAMk0XnvXkr9B72HHpIZzlfd/ejjQDvixe8AxVzbQw9qr/eYM8bs5cGVfHsxQGDrNBtPfQo+6oeWPX1DgkDT9DiE+nb2JhPr732od4jEryJrX/vMjnfbCKmQABPhqlLWODl+ER6NpwgpZbu
cTjmmOj13XfeXUUVvsaojwBjPgdVTammvJxGOusMe4GY+94qzgSYQJzDQNsFL3jEG57E0hVrYnZ99upVltF130Cpz2kxfHh0scYsXBu3V3BaqLedhaPCYaHRDZ2XXXqS3hLK8s8204FKP4uDEnwsSN88TotPln+r97Jn4GF4E1t2NVTGFQiQqEikBhWf01iR4ZDQI0dGN8qlS9fHNkg2FmtuglEsNNEaOpcu3RAiMuifhychE+ayYS74YJWst2PY0N6SeZRRZpZIwJvjC1Pf+PqOHh691J/3Pjy5opD7aCFpwSJjdZMOOrxX1GqjFU7wBMUQC8xmXt+QE7PK179fV6lOilhniSfhSCBAIiLh5diUtEdwGuN9nNHYun0/X4J9aDBKGAtcYymxLrHvt0fvhapeoMRSBgnwG7Npw2Veu26HrLNDvJSgUitZuhvtuloDleG4hI7RFrRte7l4XVHFOsh7+P454/WWSFZzWqguUX39SBNU+3RafLZivVpvQ7t2hSJEVlpkA44FAngyKZ0QXo5taZEoK4s9jHfbrgP6WyihJZaPjhk+IEaJtbq5ehEEfsNQ0Wi5adNuvTYSjOZLevVC0oQXZA6Xd7e1WLd5L59u5LmGpoUaZBWtofOjRWuporopwgux0gLvStkYJy1EyTg9xPvJAlwJBPBkTikXCWd06eT+eQkxDmsMBYsDGievvfp7emMk8z9cHWmQ8g+3BaVXDtXU1KkNDrHJJ65Q1ZtIV701OfigLvqbO8K9h4svmqS3RLLgg6+jXrekBa+vqY6dFpnaFhQNTxbiyT5TKhLO4lq7Ybv+5hw8GwFxqKupkoeKjj/2yKjeA7rldu6pjqheNJc/ujTyYHSJiERenqpzS4mcSEBJZNuOvfqbc9x4D+vW76FlX2xRjZM2loE0QHhWyhgUrVKEpMYkVSyJxCUGE5R5g0sriENOUx1Nvyn6sOAFH/yH8gsKpTstHITVwMaNdozOnWN7MnaudSLk
cnjNQ5PbBuFpwT9I5o4I8uTuu/dHemMk/3jni5DGyWAkXRsxKa5fwoxFstMi3fFmIZw4OUgstwuD2ysL/3SzKDheUXini/qQ4wML/5EFG2PTD12MnIExfJf/UFODGhKM0qqWPYirrjwjas8FSqwVq7bJmIfmjKhPAvFj9KUeEty1a3SBgBcSkpl1EFjEVK3fITRRu3bF+rsNbOQBOw8KL+4SQsSK6ISHYy0auxI9nP4haaFKewyIgieHd5V8b/LIqJ4cGor/9fFafR85Liv9ZeGwOExJC/ZEjji8lxxjx9JlG/mv8gTlnANhZO7ibT4IbV1ejkW8wJsQe4/XwnKr8YnW61jdWocd2k0bZUOgtKqrrZS5IwYN7CWzKUfjxd99QHgZMOq8Ep9er65flX7ovoTgnDhhqKy1w+fzB6oDzaE0g8uxY/CgPvpbNBK5i8lBXZGVHrm0ZMlavSWSngd11F2M8CSae5DgybUrzqWrrzpZ7xnJ639aQg1NGKTGAoH4ZAGqeidD5XVaHDbgYL0tkqqqWg4j0gPJZKRY8nS5+iZ5OTalIqF3DN4fxghjQcbbsnWfXhvJcccNkpIF7icMCKU95o3MoTp68IFL9F6RLORS/5v1e2VodbAbL9fNBqnEBq+Gq5EhwbEeM1+/YW+oB5FpsBEg0+Xxvdq0ZY9eGcmQoYeotGBhgDjg3knVgr25e+78YUxP7l//Xiu9RSgQwrHEBkPlEf6kk4boLZF8/sUW6ZrO2LSwIbErbTWRUDg+VO9onS1KYxhkQWExrVoFt9Gekcf0pUN6dmBhUHVcq2rxwP2xZzr608wlVCjPboR6D0AMEu6shFVJY0ej6yw6n3+5WXo6YpHIPWxtkNms8SBffL5Br43k7DOPlicqm9NBvXLvvHPG07E2by+zePW1f7P3UKB6LyQlrJulvAeIv3iGLP6jjh4YM123bsVYjAwWaxsCV+rZxlpFJDzEpg/BBxI4jw0GD+x89Z/NakMU
zjt3tEy7XlO1T+ZJvOziyTFL/JdeXkx7yhtYgIokHutMlfeARk41xR1eJJtD/piuMerOO/b4OBynbi3Hgv/WjW0RmuOI9WCVU3CPxJvj+/VFDLEG3zt1mLzVC69A9PEyYkgfumFq9C5mjHtYuXqXapwMydQsDvxPxEF7IigErrs28tV8wazfvD/QIxUwqAwHDdpie7hc3ELr0r0sXo63CJyH40WVB17iVK34qquvoSGHZr/5Ka+1Z+LEQXRo3zKqqtxLl/5oIk25bILeEgnc2XkL/sOloZplyjpHLJY4wCDruQREW8bVPzk9Zon11pwVVMClqzxUxKEEX0P4IuYvyoAHu2IrxLbtB5qP5T+OFg6zuWFaNdrGAoO/+LA4cWCYs0oLVMcqq/y05JPo7RCXXjyeytrlytu1RhzVhx595HK9JRKI6x9eXqTSQro2VXxWI7c8QcppgVnE0aaEGcVijeScOWsZNTRyOmjRb76GzF6aZZV/BH24AyG1Bjpex7EHrpH/sdHAeFAVeGfuUrUhCg/8/CKadt3ZLA4T9Rp7Hv3NXM6eheKdqFKG4QwljZyBNgy4xhUy+vKC8+wfR7ZYuOhbPbAn9hVKecgZVj2AVseiV0+jR0V/xmTXrgr9zSG4Bv5ntZ2gvn5Qj056oz3VNfX6W3ysYedIi9mzP9Zr7Zl+0zk0ku/dY49eodfY8/Qz/6TKGjZwa7wHwHXwYgm1vJQHs2eXFdHN089S+0Th44/XZF31AoRebSADecBtZk0yjuPlHWHsqNZLyVVYTJu37af353+ld4ikc5d2dMH5sSeVefChv9GeA/VikIhE1W/V5CVKGA5QjbjH+2n4UC79Ho5e+oFZb3xGlRgWHGaUKqvC0FVrvhIG1WVaJw+O+ahHt44xPZM9u20Egs83eAkJnxcrU6HERfUo3mPy+8t9/FekWK0Ix4qLwR5SzSgopmUr18ecl2HcsQPp8cev1r/sQWmPqoU0TGq7bE4PdC37
pP3CB3HoVEjPxplaH43OG7ay16W9QsvWs4FIOdTX7ukWtIZIuEgsZSTK2KXLjL/Dk0BbxMsvz9N7uWfmX5fSilXbKY+NHJlJtYpj6K82RK4vwy2urNxDkyYOlfd8xgLu8ey3P+N6eXOLOURBzYJleQkoydX0+BIPXGX2TiBGP7kydl16y/b9/Dco8+qMap278hKspVY34qlrQYmLXpxrrjpFjrED519eWcfnHiVtwuLDgmvD/mg8fvyJ2DNtxQJzh86eg3unepBkjIOkB98nv095cHwNEGon4gBmz1kujagyyCrLsPeXdLp6yuitKBLx4lTupV8be4UYCyatRcmybec+eunlD/Seznnt9UX08usfcLgowXUVgg0Qb+gSYajczcKwm3p2L6FfPjCF7roz/tT6zzy3gDMMZp9q9h5UKY5zrwpk1ECDXeVeXvaI0V92yeSYjYdoJ9mxu0oyYzBWZoXYKG9Hh68zExppcT1449QDP58SM1O99/4qqTLEcsdD4+O04PSAuKE7+fOv1tDcd2NPpGPHJ5+uowd/9QbV+tU8nuItaFGorubrqFRC3eCvoEknDqE/v35HXHH448uLafP2ahFruxGxmU7OVTfMUXIeTtBa+x1ioEsI4PrYRLBKJvkbCQwSXWQw+q6d0P6QJ4YEr6KwMJdGjTqcrphiP9FINC6+9FHauadCvBBkaIiNLFzSFxbk0VGD+9Cpp46iUxy+AHjW7M/or299TmoWKzX/pXgPHB7OHW+6RjUC14LrLSrKp549OlOvXl1kkpRYDW3gpZc/pn+8/w1XrUqVwUMoOBy5N8isfG9qOR7EhzuJe4lSGG0OqFbAc4iXqe67/y1au8knPRO2LrltfFzKS1VJDViaeMLR9N8POXuJkcVFlzxCe8v98l4MNEBbBQJGSCJMGXMyZpD0HMW7BrB8+SZ69LfzCNPqB6or4deS4UQXCKC3RN8hBnEya4sQI04YC1xmlL6VFbvp72/drbck
zrff7qCVX2ykNd9ulbhHHnMYdexQEvc9G+Gs/HwrPfToeyxWnHkhDmyMMMfGoHPv37uUTv/e0ZyZGj11M1513avkq2PvBC44QofBc/ioVsBzqDywiy7+4fHyen0vYGDX7ffOlpcJIZPaZqiw+P78ynR5q1iiYGaut/+2nNav30mbt+yWePCcS9++3Wn0qIExx0uEg6n1b71jBtX4+V5BrC0xzTJiCwTQW2PvFAVOIPmQvykiSpyWQMBtrijfQe+8/TO9JT1YsWILl1bv83kXybwEcM8tc8S5ox0Ans8Jx/alW26O3cYQjedfWCivCJQZoYMMXtxxCBCHj3tzx63f9zzG4dY7ZtK2XXUxM1V4fO/94369JT1QL+V5g/ZVNJIMlxdPjslCgYhfqdL3xNOt0Tc0pbfVQZwpFSwHLFq0VsQBXaTWFHe256/FzwsrVm6h+f9SszlHq0vrCoX64YFXXl0iM4A7ra8nGl9LgAbWe382W8QhPz+oYTILxQHET0Wg742nW5SmIpEuPP/iQnqSS3Z4DsHdcuFE7S50wGefbaZHnpinRxS2jMHPemMFvf3u6jadqTDyctotf6b9FaQ8BzQS4763setIJs4EAuh7lMitSpfb7PY88B7HNWt36V/JYfHidXTTLTPow8UbVZsDqhV4mIiNMer5eTDUv85arryTJgzggnfCYUQJR8XsPg5UXWa8tUI1rLrIVF7ii/eaPC/Aa3js8Xn05HMs1GiQ1A3E2S4OwNPj3q6PaY2brOMMjxnuuxrMFB8MkLnltj/RU8/Np3vv/ys99fR78hq+RMAAqDvvmU1PvfiRfn2cNkZdrYh6p3iDTBXnsC8e545GtplvreSqi3pJTLRuRyujymzbnMHzbJ56tGPRonV0w/S/0Acscm5K3PD4nDJj5lK6eur/0JVXvUALFqzWa70DYXj2uQ/Ea/hkxXYqLNLvXLXuU5aLA8i5+oY57mq2emdPNccoDYgtSlCcwd2cb864
SdYHgzECX3+9k1at2kZfrt5K5VV+ytdtAmhYQ8s7us2OGNCdjh7Rl8aM7k+HHtpNH20Pwvzmm5306dINMqNyfSMeFOPMyhndasTDucUyRemua1TdnBO/05+mXm8/gzbiWrp0Iy3++FvauK1CqhRi8CIqOgY7o+fwg7sdb5t+Gk2ccITeGAq6/lZ+vkWGgctIT4TPmdxVpgqLb86s6XpDJJgwZ8mSdbSc4/Q3IOwcamj0Szdsjy4lNHb0AJp00pFx08ECooBxGitWbKLVa3bLeA1P15AliEDgi6tMq3d2dYxFqkUiKD7rRTfoF+/epZiKClF6NdH6TXtEBJBpIQhSquG3LFzSIRNzOBAJGdbMxom3QeHZB6w/tE/XwAUFX9f2nQeo1o/GLu3eQxSkGgFvQRmhZYqx7ge2YSwCzr1b5wIacuTB8tviP99sp/IDavQi4hFj57jQ3oBS2oorahz62pBpMZx60IBudMghnWWTr6qWNm/dS9U1ftq+q0LCDWQqxBEUPkeoPuMRFt9xY/pTcYny6jAMfM++SulJ2F9RExQfXxd/B5IWMt6E0wGizd/LOhRTr56dqE8fe6FAuBhBikFiEh68Njl/NXza9TVkCQGBALGMNAK9s6tjACeuhetjvaLjxHgCldEbQjKYhRh7UOZVxhJsMNbxeD5BfYZchXzFekSp9hE3mo1RZVhdQgFtiPKEJBMUSgQqWCtuNUoQ30Phsw4ImvJMAtehiRqHdQ4cLjKbCttub4TNS6KZKtH4GHUv1HMi0Y8PR4cn90ndqwBuryFLCBEI4OQ2B9A7uzoGWAYif1MEDCrwNXrMAaPRu0TfU22JCIt/qxGBeF7CL4aIiWtVD4XKuOE4Fglg7Rserw43OBPZETWOsPCa71YzEWEnkqkSjc/B8eEk9fyzhKAiTZGSWxYw5tRixadKEfvFOdgXx1ilkVrEVFkY1LBormLgeQ9/rbjE0WhyEK+KrfncUVUJiVv/8wzOIWgJ
CVsv/Cd0SYSwsFzHF7bN7vjwhf+ELoa45NrpruNbp3f0dKt1AqUsmdzGp3d0d3544rKJ/CwIeCx66OCDaPZfptHJE46ILPFtcHsvvNi4h0MMWYx4EPFNNwaeMlIr4VEk3KDq1Wg8q6WTJw+Xdf37dxUXOJYbbHkRbeI+GrIGEQgQbrquDFXv7Nq420imcHp+VmMZ/jU3vhG1b18kny2B8SIMLUlAIEBCIqFxfUyqRcKjFxF7fyUMzV/5Dy+htQqsw4faFliCMF6EId0IEQgQarLuM5In0l0kAuDuWAuACOhZnhoweAcvdlGNkdb7RC1kHEOjnj1Jxk/obtYwkfCCvhxXpOxeG9o0EQJhh2Nj0jumu/HB9Q/O5vGQSkOIENRLFQIDrzDBCSY8wZRvGBlYz78l8wcHzvHhOEz0gl4N9HDgd0AkgjBehCGdsBWIhMo0ryKRgowhwhBYuKSXjBx5taH7qQWjJ5Gx8bIbNQMSRvCpUZnozsS0bxjCjZGB4YOwcLzsx9uxX43vgEyvFq3r00m3ZzjGizC0BFE9iPBs48WYXB+TCpGQQUyY6LVGhumq0l5dLTKyDMfmKgCqC1ZVAEOq8R5ISwjwiRmkxaPgMOAZnH3aMXTbTadRj67tqFE8g+Y7KCIDD4I9jGuunExvzryRrrn8OFkfi1Rk4FTEYWi7xKxieBaJNLQ6yaTsNaipzio5k5fLJ7ojRQRku5rDEBkeb4yWuR9lrD+EwE+5OfX0/+67gN6ccSP16FLM69WwZ2wfOLAnTTjhCBo2BC/Njcz41mzaVo9G+3aFvJf6Z4eXqoYXL8JgiIWjNohg3IqEa5ttQS8Crr+4+pz5R484hCaOHyDve0Qml6lhWUDgKfTsVkgnjOtHo4cfJNUJtCtABA7q3pFGjuwnYWGyWCUs8DDwKatVlofYqJ9B6PWB/dKHlrjXhswgrkAkZMhpJhJWaY92grvvOoumXn8SHTmop6xHxkVGh6cwoH9XumHq
ZLrt1u/KiEi0F1hPDVo0NuCpTtVgieMtVBWFPQv+tJBJZ8XbiD7c2g7jRRhaG0ceRLhItGUbFJHgzG1RWqKmR5erlG3oimx+bVxTI9oe1FTz8CQs8A6Mupoq3XOh2zKY4qJcJSgcjrUO4VnvfOCVsq4l8SISRlcMdjiuYngWCb2jawNsqaoGdCDoalDii2hwCY9MLV6Bztjgp3dfSI31PumhOOLwXnot0cmTh1JV5R7y+Q5Qj64d5DcYMaIfC0oFFeQ1Uf9+3WVdaWmhrMvPJxo1sr+sq6yslc94mG5PQ2uSN2rsRb/Q3+MSbqSOjVbv6NrIk5w50IPhr8f7K6vo0ovVex/+/claWrd+N4sEb6tTXZb9+3aj8eMHyfbevTvT4QP70OBB/UJe1T94cC86uGd3Oqh7Z7rnrh9QSUmhrO/dqzMdNfhQmnTSSBo2DA2Wat3x44fRGaePo14cHvjfv31O23b4ZK6IeEW+l+s3XoQhGYgH4dQwWt45bnnw2C+mFtu/Hy+XJSrrVCTvyoCHII9nc1WiW/fQN1fjBTjnnzdWvu8vrw5MYIu3ZU2b+j15uW8w2D/8pTl445X11ivMMr1sxSY+F2d33ngRhtYi5xovM0qF7ej8OLWn4/3DcHtc8EQsWNC+gB4MX8VeuvLyE+ncs0fJ9nt++hotXfa1NCSOHDGQHtFv3oYQfPXVFurYUb1oZseuSvr7O59xtaKGvsvicNhhPWR9lc9P7767kvbsq6CJJxxJhx/ek3bsOECrVm2h3bvL5WnOYUP78Dk00fz5K2nV17uptF0Xyi8sUbMlSWzOcNuE4faeCSloJzG0DbwJBAjb2fGxrSASOEYWtDPokY+d2ufQ71+8VvYBa/Qr55HpATyMm297jSp99Wr+wrz80AlsWWzQqMk5nPJRTeCMjvCtng41xVyONGzKy2PZS/nxxZPpzDOOoaumvsJVkk4y0zSOd+sZuMm/nu6zEQiDJqSRMiUubCu4y4hL
Fs6MyJSFxe2pvLKJ7r53hlQZAITBEgdMAHvfz9+kuoZCKikto+LSTlRc3IEKuMQvKCyWT/y2loKiUioswPpSeXEsFnzHK+4Ki3jh38UsCF27dqKyslJq3659QEBa+j54Ct9h1ceQ+ciclOHm4Lj8CNvR+XFqT6/llNvjrKqG9E3wdzUyso789bVUlJ9DI4/uT+1KVSPjnr1VtPrrHUHT00ND+Q5JZm6+U8E9IWq9vqawk0PjJ3pGMMsU4qqtb6ICFhP1EhvlPbi9HuCmkPcSvqsIDBmLrUAAx+YRtqPz49SeXs3QzXHhbRGIG9UEawkJTLQgaC5D/ucmrpB9rXgDcTVxmByiDhukQiCAlzhcR2LIOMRKEzKDMHWxExtbUljVCO4FkG/IpOzio20BXgKqHYFF3veg35mAvV2eYMjuVrwsBiouvNtBvUVLb1Af8tcd+lCDoUUJtEGEi0S625/b87NEAuCbLBCKaIu1Iz6aD3UPDo62MMHn5RY3h3qKJYFzM2QGAYEAnkUibEfnx6k9vZqhl+O8xuWGdD0vg8EtIQKREGEWno4Gn8iAo5YsTNPaizBkNRECkfKqRit4EV5p6cyYymtxTEsqoyHtkdf/hy8g+DdUJPh31IX/BP92fhzq/epkQta3wIKI8On43MIX/uNosTs2xoKD8OnlvOS+OVxScY/NkjlLhAdhkfL2iARIRRwWbuJK5Xm1KFAWQ1YSVSBSjzJCr7bo5rBE2iIc4yHwlJwXY/K7wSkxBSL1XoTaMxUGnFCDpf50QgouRWjxeIyqZCVxPYhkiUQqSDsT1ifk5rxS5UUYDE5IWRXDucHrDJKCHJIqLyJVuDknT/fXeBFZhyOBMA2WCaBPyM15GS/CkC7kOi0UUjs+QmeQFOQQ40W4JBWJYkgbWr6KEWZPzs1L7enFHtPOhPUJuTkv40UY0gERCKeZMFOrGsaLMBjsCXgQLS4SnlChZ4QR62twcymWcKUdRlWyhpT1YoTn
jFSYmJfM6OW8WvpaWvqcTH43RCNEIJwaSmrHRqiDUmHELV5i6+DdxGK8CENrwgIRmtBeRcILzk1M7enFJj0ckrJj3JCO52TIfJJWxXBsjGE7ppsRZ6sX4SmKFJyXoXXRAhGa0E7Tva00WHo5r1Qd44Z0PCdDZhPkQYSaUosWDmFhp5sRp8qLSDeMQ2AIJ+EqRltpsPQSXUsf42ZfS7S8nFOLYlQlowkTiNDEdpr2yahqpJuZZZIX4SYqk98NzRD9H9fNtzhacrgSAAAAAElFTkSuQmCC</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>a83c783c-1b6c-4b61-9c17-d576a3e142e0</rd:ReportID>
</Report>