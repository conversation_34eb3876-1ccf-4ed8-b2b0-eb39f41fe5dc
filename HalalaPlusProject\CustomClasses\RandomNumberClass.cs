﻿namespace HalalaPlusProject.CustomClasses
{
    public class RandomNumberClass
    {
        public string GenerateUniqueRandomNumber(Models.HalalaPlusdbContext _dbContext)
        {
            Random random = new Random();
            string randomNumber = "01" + random.Next(1000000, 9999999).ToString();

            // Check if the generated number already exists in the database
            while (_dbContext.AspNetUsers.Any(n => n.MemberNo == randomNumber))
            {
                randomNumber = random.Next(1000000, 9999999).ToString();
            }

            return randomNumber;
        }
    }
}
