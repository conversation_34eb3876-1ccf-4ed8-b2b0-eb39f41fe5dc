﻿using DocumentFormat.OpenXml.ExtendedProperties;
using HalalaPlusProject.Areas.Identity.Data;
using HalalaPlusProject.CModels;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.HalalaClass;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Localization;
using NPOI.SS.Formula.Functions;
using System.Text;
using System.Text.Encodings.Web;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة البيانات الخاصة بالمسوق، مثل عرض ملفه الشخصي، إنشاء مقدمي خدمة، وعرض المهام والبيانات المالية.
    /// </summary>
    [Authorize]
    public class MarketerUserController : Controller
    {
        Models.HalalaPlusdbContext _context;
        private readonly IWebHostEnvironment _hosting;
        private readonly UserManager<HalalaPlusProjectUser> _userManager;
        private readonly IStringLocalizer<MarketerUserController> _localization;
        private readonly IConfiguration _configuration;
        public MarketerUserController(IConfiguration _configuration,Models.HalalaPlusdbContext context, IStringLocalizer<MarketerUserController> _localization, UserManager<HalalaPlusProjectUser> userManager,
            IWebHostEnvironment hosting)
        {
            this._context = context;
            this._configuration = _configuration;
            this._localization = _localization;
            this._hosting = hosting;
            this._userManager = userManager;
        }

        /// <summary>
        /// عرض التفاصيل الشخصية للمسوق الذي قام بتسجيل الدخول حاليًا.
        /// </summary>
        /// <returns>عرض يحتوي على تفاصيل المسوق.</returns>
        public IActionResult Index()
        {
            var systemUser = new UsersClass().retriveUserDetails(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value, _context);
            return View(systemUser);
        }

        /// <summary>
        /// عرض نموذج إنشاء مقدم خدمة جديد من قبل المسوق.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج إنشاء مقدم الخدمة.</returns>
        [HttpGet]
        public IActionResult CreateProvider()
        {
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name");
            ViewData["City"] = new SelectList(_context.CitiesTables.Where(p => p.CId == 1), "Id", "City");
            return View(new ServiceProvidersCreate());
        }
        /// <summary>
        /// يعرض قائمة بجميع مقدمي الخدمات.
        /// </summary>
        public IActionResult Providers()
        {
            var temp = new UsersClass().retriveForMarketer("Provider", User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value, _context);
            return View(temp);
        }
        /// <summary>
        /// معالجة عملية إنشاء مقدم خدمة جديد، بما في ذلك إنشاء حساب له في نظام الهوية.
        /// </summary>
        /// <param name="model">البيانات الخاصة بمقدم الخدمة الجديد.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        public async Task<IActionResult> CreateProvider(CModels.ServiceProvidersCreate model)
        {
            if (ModelState.IsValid)
            {
                var userExists = await _userManager.FindByEmailAsync(model.Email);
                if (userExists != null) return Ok(new { state = 0, message = _localization["emailused"].Value });
                userExists = await _userManager.FindByNameAsync(model.PhoneNumber);
                if (userExists != null) return Ok(new { state = 0, message = _localization["usedusername"].Value });
                string url = HandleImages.SaveImage(model.Logo, "Images", _hosting);
                HalalaPlusProjectUser user = new()
                {
                    Email = model.Email,
                    SecurityStamp = Guid.NewGuid().ToString(),
                    PhoneNumber = model.PhoneNumber,
                    Image = url,
                    UserName = model.PhoneNumber
                };
                var result = await _userManager.CreateAsync(user, model.Password);
                if (result.Succeeded)
                {
                    try
                    {
                        await _userManager.AddToRoleAsync(user, "Provider");
                        ProvidersClass ob = new ProvidersClass(_context, _hosting);
                        model.ContractDate =DateOnly.Parse( DateTime.Now.ToShortDateString());
                        model.ContractEndDate =DateOnly.Parse( DateTime.Now.AddYears(1).ToShortDateString());


                        if (! await ob.Insert(model, user, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value))
                        {
                            await _userManager.DeleteAsync(user);
                            return Ok(new { state = 0, message = _localization["errorwillsaving"].Value });

                        }

                        user.EmailConfirmed = true;
                        await _userManager.UpdateAsync(user);
                        var emp = _context.SystemUsers.Where(o => o.AspId == user.Id).FirstOrDefault();
                        var mem = await _userManager.FindByIdAsync(User.Identity.Name);
                       var key = _configuration["SmsToken"];
                        string msg = string.Format(_localization["addprovidermessag"].Value, emp.Id, model.Name);
                        new SMSHandler().SendSingleSMSwithoutasync(key, mem.PhoneNumber,msg, "", -1);
                         return Ok(new { state = 7, message = _localization["addedsuccessfuly"].Value, Url = "/MarketerUser/Providers" });
                    }
                    catch (Exception ex)
                    {
                        await _userManager.DeleteAsync(user);
                        return Ok(new { state = 0, message = _localization["errorwillsaving"].Value });

                    }
                }


            }

            return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });
        }

        /// <summary>
        /// عرض العملاء (مقدمي الخدمة) المرتبطين بالمسوق الحالي.
        /// </summary>
        /// <returns>عرض يحتوي على تفاصيل المسوق وعملائه.</returns>
        public IActionResult Customers()
        {
            var systemUser = new UsersClass().retriveUserDetails(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value, _context);
            return View(systemUser);
        }

        /// <summary>
        /// عرض قائمة المهام الموكلة إلى المسوق الحالي.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة المهام.</returns>
        public async Task<IActionResult> Tasks()
        {
            var halalaPlusdbContext = new TaskClass().retrive(_context, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value);
            return View(halalaPlusdbContext);
        }

        /// <summary>
        /// عرض التفاصيل الكاملة لمهمة معينة.
        /// </summary>
        /// <param name="id">معرف المهمة المراد عرض تفاصيلها.</param>
        /// <returns>عرض يحتوي على تفاصيل المهمة، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Details(long? id)
        {
            if (id == null || _context.TaskesTables == null)
            {
                return NotFound();
            }

            var Taskdata = new TaskClass().retriveDetails(id ?? 0, _context);
            if (Taskdata == null)
            {
                return NotFound();
            }

            return View(Taskdata);
        }

        /// <summary>
        /// عرض البيانات المالية الخاصة بالمسوق.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة بالمعاملات المالية.</returns>
        public async Task<IActionResult> Finance()
        {

            return View(new List<MarketerFinanceModel>());
        }

        /// <summary>
        /// إغلاق مهمة معينة وتحديث حالتها.
        /// </summary>
        /// <param name="id">معرف المهمة المراد إغلاقها.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        public async Task<IActionResult> CloseTask(int? id)
        {
            if (id == null || _context.TaskesTables == null) return Ok(new { state = 0, message = _localization["nodata"].Value });
            var Taskdata = new TaskClass().close(id ?? 0, _context).Result;
            if (Taskdata != true) return Ok(new { state = 0, message = _localization["tasknotstoped"].Value });
            return Ok(new { state = 7, message = _localization["taskstoped"].Value, Url = "Tasks" });
        }
    }
}