﻿@model List<HalalaPlusProject.CModels.GrantDiscountModel>
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["offers"];
}
<div class="row">
    <h3> @localizer["coupons"] </h3>
 
                </div>
 
<div   >
<div class="row">
        <div class="col-12">
          <div class="card mb-4">
         
            <div class="card-body px-0 pt-0 pb-2">
              <div class="table-responsive p-0">

                        <table id="tbl1" class="table table-striped text-center">
                            <thead>
                                <tr>
                                    <th scope="col">@localizer["serviceprovidername"]</th>
                                    <th scope="col">@localizer["phoneno"]</th>
                                    <th scope="col">@localizer["products"]</th>
                                    <th scope="col">@localizer["discounts"]</th>
                                   
                                    <th scope="col">@localizer["rate"]</th>
                                    <th scope="col">@localizer["amount"]</th>
                                    <th scope="col">@localizer["more"]</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>@Html.DisplayFor(m => item.CustomerName)</td>
                                        <td>@Html.DisplayFor(m => item.phoneNo)</td>
                                        <td>@Html.DisplayFor(m => item.productName)</td>
                                        <td>@Html.DisplayFor(m => item.discount)</td>
                                        <td>@Html.DisplayFor(m => item.rate)</td>
                                        <td>@Html.DisplayFor(m => item.amount)</td>
                                        <td>
                                            <button class="btn btn-success" onclick="confirmActivate(@item.Id)">تفعيل</button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>


              </div>
              
            </div>
          </div>
        </div>
      </div>
      </div> 
    
                @section Scripts{
    <script> 
        let table = new DataTable('#tbl1');
   
        function confirmActivate(id) {
            if (confirm("هل أنت متأكد من تفعيل هذا الكوبون؟")) {
                fetch(`/CustomDiscount/Activate?id=${id}`, {
                    method: "POST"
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    if (data.state === 1) {
                        location.reload();
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء تنفيذ العملية.");
                    console.error(error);
                });
            }
        }
    </script>
}
 