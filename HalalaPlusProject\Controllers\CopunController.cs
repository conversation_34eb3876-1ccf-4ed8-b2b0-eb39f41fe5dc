﻿using HalalaPlusProject.Controllers;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using HalalaPlusProject.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using System.Security.Claims;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة كوبونات الخصم، بما في ذلك إنشاؤها وعرضها وتعديلها وحذفها.
    /// </summary>
    [Authorize]
    public class CopunController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IWebHostEnvironment _hosting;
        private readonly IStringLocalizer<CopunController> _localization;
        //private readonly IFirebaseNotificationHistoryService fire_notification;

        public CopunController(/*IFirebaseNotificationHistoryService firenotification,*/ HalalaPlusdbContext context, IStringLocalizer<CopunController> _localization, IWebHostEnvironment hosting)
        {
            _context = context;
            _hosting = hosting;
            this._localization = _localization;
            //fire_notification = firenotification;
        }


        /// <summary>
        /// عرض جميع الكوبونات، مصنفة إلى كوبونات نشطة وأخرى متوقفة (للمسؤولين).
        /// </summary>
        /// <returns>عرض يحتوي على قوائم الكوبونات النشطة والمتوقفة.</returns>
        public IActionResult Index()
        {
            var ob = new CModels.CoupunModelIndex();
            ob.ActiveCopuns = new CustomClasses.CoupunsClass().retrive(_context, true, false);
            ob.stoppedCopuns = new CustomClasses.CoupunsClass().retriveStoped(_context, true, true);
            return View(ob);
        }

        /// <summary>
        /// عرض الكوبونات الخاصة بمقدم خدمة معين، مصنفة إلى كوبونات نشطة وأخرى متوقفة.
        /// </summary>
        /// <returns>عرض يحتوي على كوبونات مقدم الخدمة الحالي.</returns>
        public IActionResult ProviderIndex()
        {
            var ob = new CModels.CoupunModelIndex();
            var userId = _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id;
            var isAdmin = User.IsInRole("Admin");
            ob.ActiveCopuns = new CustomClasses.CoupunsClass().retrive(_context, true, false, userId, isAdmin);
            ob.stoppedCopuns = new CustomClasses.CoupunsClass().retriveStoped(_context, false, true, userId, isAdmin);
            return View(ob);
        }

        /// <summary>
        /// عرض التفاصيل الكاملة لكوبون معين.
        /// </summary>
        /// <param name="id">معرف الكوبون المراد عرض تفاصيله.</param>
        /// <returns>عرض يحتوي على تفاصيل الكوبون.</returns>
        public IActionResult Details(long? id)
        {
            var lang = Thread.CurrentThread.CurrentCulture.Name;
            var ob = new CustomClasses.CoupunsClass().retriveCoupun(_context, id ?? 0);
            //if(ob.Provider!=null) 
            //ob.ProviderName = (lang.StartsWith("ar")) ? _context.SystemUsers.Where(o => o.Id == ob.Provider).FirstOrDefault().Name : _context.SystemUsers.Where(o => o.Id == ob.Provider).FirstOrDefault().EnName;
            if (ob.Provider != null && ob.Provider >= 1) ob.SysProvider = (lang.StartsWith("ar")) ? _context.SystemUsers.Where(o => o.Id == ob.Provider).FirstOrDefault().Name : _context.SystemUsers.Where(o => o.Id == ob.Provider).FirstOrDefault().EnName;
            return View(ob);
        }


        /// <summary>
        /// عرض نموذج تعديل بيانات كوبون حالي.
        /// </summary>
        /// <param name="id">معرف الكوبون المراد تعديله.</param>
        /// <returns>عرض يحتوي على بيانات الكوبون في نموذج التعديل.</returns>
        public async Task<IActionResult> Edit(long? id)
        {
            var lang = Thread.CurrentThread.CurrentCulture.Name;
            var ob = new CustomClasses.CoupunsClass().retriveCoupun(_context, id ?? 0);
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name", (ob?.ActivityNo != null) ? ob.ActivityNo : 0);
            ViewData["Provider"] = (lang.StartsWith("ar")) ? new SelectList(_context.SystemUsers.Where(o => o.AccountType == "Provider" && o.Status == "A"), "Id", "Name", ob.Provider) : new SelectList(_context.SystemUsers.Where(o => o.AccountType == "Provider" && o.Status == "A"), "Id", "EnName", ob.Provider);
            if (ob.Provider != null && ob.Provider >= 1) ob.SysProvider = (lang.StartsWith("ar")) ? _context.SystemUsers.Where(o => o.Id == ob.Provider).FirstOrDefault().Name : _context.SystemUsers.Where(o => o.Id == ob.Provider).FirstOrDefault().EnName;
            return View(ob);
        }

        /// <summary>
        /// عرض نموذج إنشاء كوبون خصم جديد.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج الإنشاء.</returns>
        public IActionResult Create()
        {
            var lang = Thread.CurrentThread.CurrentCulture.Name;
            ViewData["Provider"] = (lang.StartsWith("ar")) ? new SelectList(_context.SystemUsers.Where(o => o.AccountType == "Provider" && o.Status == "A"), "Id", "Name") : new SelectList(_context.SystemUsers.Where(o => o.AccountType == "Provider" && o.Status == "A"), "Id", "EnName");
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name");
            //ViewData["Emloyee"] = new SelectList(_context.SystemUsers.Where(p => p.AccountType == "Employee"), "AspId", "Name");
            return View();
        }

        /// <summary>
        /// معالجة عملية إنشاء كوبون جديد، بما في ذلك حفظ الصورة وإرسال إشعار.
        /// </summary>
        /// <param name="model">البيانات الخاصة بالكوبون الجديد.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل عملية الإنشاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CModels.CoupunModel model)
        {
            try
            {
                if (!ModelState.IsValid) return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });

                if (model.Provider == null && string.IsNullOrEmpty(model.ProviderName))
                    return Ok(new { state = 0, message = "يجب تحديد مقدم الخصم" });
                if (model.Provider == null)
                    if (string.IsNullOrEmpty(model.ProviderName) || string.IsNullOrEmpty(model.ProviderName))
                        return Ok(new { state = 0, message = "يجب كتابة اسم المتجر عربي وانجليزي " });
                if (model.Img != null)
                {
                    model.imgLink = HandleImages.SaveImage(model.Img, "images", _hosting);
                }
                var userId = _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id;
                var isAdmin = User.IsInRole("Admin");
                if (await new CustomClasses.CoupunsClass().insert(_context, model, userId , isAdmin))
                {
                    try
                    {
                        //await this.fire_notification.SendNotification(model.Name, model.Details, null, "News");
                    }
                    catch (Exception)
                    {
                        //throw;
                    }

                    return Ok(new { state = 7, message = _localization["savedsuccessfuly"].Value, Url = "/Copun/Index" });
                }
                if (model.imgLink != null)
                    HandleImages.RemoveImageRoot(model.imgLink, "images", _hosting);
                return Ok(new { state = 0, message = _localization["errorwillsaving1"].Value });
            }
            catch (Exception ex)
            {
                //HandleImages.RemoveImage(model.imgLink, "Images");
                return Ok(new { state = 0, message = _localization["errorwillsaving1"].Value });
            }
            //string url = HandleImages.SaveImage(model.Logo, "Images", _hosting);


        }

        /// <summary>
        /// معالجة التعديلات المقدمة لكوبون وحفظها.
        /// </summary>
        /// <param name="model">البيانات المحدثة للكوبون.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل عملية التعديل.</returns>
        [HttpPost]
        public async Task<IActionResult> Edit(CModels.CoupunModel model)
        {
            try
            {

                if (model.Id == null) return Ok(new { state = 0, message = _localization["selectoffer"].Value });
                if (!ModelState.IsValid) return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });
                if (await new CustomClasses.CoupunsClass().Edit(_context, model, _hosting)) 
                    return Ok(new { state = 7, message = _localization["modefiedsuccessfuly"].Value, Url = "/Copun/ProviderIndex" });
                return Ok(new { state = 0, message = _localization["errorwillsditing"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorwillsditing"].Value });
            }

        }

        /// <summary>
        /// تعطيل كوبون معين (إيقافه مؤقتًا).
        /// </summary>
        /// <param name="id">معرف الكوبون المراد تعطيله.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        public async Task<IActionResult> Disable(long? id)
        {
            try
            {

                if (id == null) return Ok(new { state = 0, message = _localization["selectoffer"].Value });

                if (await new CustomClasses.CoupunsClass().disable(_context, id ?? 0, false)) return Ok(new { state = 1, message = _localization["opsuccess"].Value });
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }

        }

        /// <summary>
        /// تفعيل كوبون معين تم إيقافه مسبقًا.
        /// </summary>
        /// <param name="id">معرف الكوبون المراد تفعيله.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        public async Task<IActionResult> Activate(long? id)
        {
            try
            {

                if (id == null) return Ok(new { state = 0, message = _localization["selectoffer"].Value });

                if (await new CustomClasses.CoupunsClass().disable(_context, id ?? 0, true)) return Ok(new { state = 1, message = _localization["opsuccess"].Value });
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }

        }

        /// <summary>
        /// حذف كوبون معين بشكل نهائي.
        /// </summary>
        /// <param name="id">معرف الكوبون المراد حذفه.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        public async Task<IActionResult> Delete(long? id)
        {
            try
            {

                if (id == null) return Ok(new { state = 0, message = _localization["selectoffer"].Value });

                if (await new CustomClasses.CoupunsClass().delete(_context, id ?? 0)) return Ok(new { state = 1, message = _localization["opsuccess"].Value });
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }

        }
    }
}