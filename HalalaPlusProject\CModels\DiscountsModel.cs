﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل تفاصيل إعداد الخصم.
    /// </summary>
    public class DiscountsModel
    {
        public bool? IsActive { get; set; }
        /// <summary>
        /// معرف الخصم.
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// معرف المستخدم المرتبط بالخصم.
        /// </summary>
        [Required]
        public long userId { get; set; }

        /// <summary>
        /// نسبة الخصم.
        /// </summary>
        //[Required]
        [Range(0.0, 100.0, ErrorMessage = "الخصم يجب ان يكون بين  0 و 100")]
        //[Display(Name = "نسبة الخصم")]
        public double? Discount { get; set; }

        /// <summary>
        /// اسم الخدمة المرتبطة بالخصم.
        /// </summary>
        [Required]
        //[Display(Name = "اسم الخدمة (الخصم)")]
        public string? DiscountName { get; set; }

        /// <summary>
        /// الاسم الإنجليزي للخصم.
        /// </summary>
        [Required]
        public string? EnDiscountName { get; set; }

        /// <summary>
        /// تاريخ بداية الخصومات.
        /// </summary>
        //[Display(Name = "بداية الخصومات")]
        public DateOnly? StartDate { get; set; }

        /// <summary>
        /// تاريخ بداية الخصومات (كسلسلة).
        /// </summary>
        //[Display(Name = "بداية الخصومات")]
        public string? SStartDate { get; set; }

        /// <summary>
        /// تاريخ نهاية الخصومات.
        /// </summary>
        //[Required]
        //[Display(Name = "نهاية الخصومات")]
        public DateOnly? EndDate { get; set; }

        /// <summary>
        /// تاريخ نهاية الخصومات (كسلسلة).
        /// </summary>
        //[Display(Name = "نهاية الخصومات")]
        public string? SEndDate { get; set; }

        /// <summary>
        /// شروط الخصم.
        /// </summary>
        //[Required]
        //[Display(Name = "ا/*ل*/شروط")]
        public string? Conditions { get; set; } = null!;

        /// <summary>
        /// الشروط باللغة الإنجليزية.
        /// </summary>
        public string? EnConditions { get; set; } = null!;

        /// <summary>
        /// نوع منح الخصم.
        /// </summary>
        [Required]
        //[Display(Name = "النوع للخصم")]
        public string GrantType { get; set; } = null!;

        /// <summary>
        /// صورة مرفقة للخصم.
        /// </summary>
        //[Display(Name = " صورة الخصم")]
        public IFormFile? DiscImage { get; set; } = null!;
    }

    /// <summary>
    /// يمثل تفاصيل طلب الخصم من قبل مقدم الخدمة.
    /// </summary>
    public class DiscountOrderModel : DiscountsModel
    {
        /// <summary>
        /// اسم مقدم الخدمة.
        /// </summary>
        [Required]
        //[Display(Name = "مقدم الخدمة")]
        public string? Provider { get; set; }

        /// <summary>
        /// تاريخ تقديم الطلب.
        /// </summary>
        //[Display(Name = " تاريخ الطلب")]
        public string? Date { get; set; }
    }
}
