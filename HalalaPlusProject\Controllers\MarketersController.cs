﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HalalaPlusProject.Models;
using HalalaPlusProject.CustomClasses;
using Microsoft.AspNetCore.Identity;
using HalalaPlusProject.Areas.Identity.Data;
using Microsoft.AspNetCore.WebUtilities;
using System.Text.Encodings.Web;
using System.Text;
using Microsoft.AspNetCore.Identity.UI.Services;
using HalalaPlusProject.CModels;
using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Authorization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة المسوقين، وتوفير عمليات الإنشاء والقراءة والتحديث والحذف (CRUD) لبياناتهم.
    /// </summary>
    [Authorize]
    public class MarketersController : Controller
    {

        private readonly HalalaPlusdbContext _context;
        private readonly UserManager<HalalaPlusProjectUser> _userManager;
        private readonly IEmailSender _emailSender;
        private readonly IStringLocalizer<MarketersController> _localization;
        public MarketersController(HalalaPlusdbContext context, IStringLocalizer<MarketersController> _localization, UserManager<HalalaPlusProjectUser> userManager, IEmailSender _emailSender)
        {
            _context = context;
            _userManager = userManager;
            this._emailSender = _emailSender;
            this._localization = _localization;
        }

        // GET: Marketers
        /// <summary>
        /// عرض قائمة بجميع المسوقين.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة المسوقين.</returns>
        public async Task<IActionResult> Index()
        {

            return View(new UsersClass().retrive("Marketer", _context));
        }

        // GET: Marketers/Details/5
        /// <summary>
        /// عرض التفاصيل الخاصة بمسوق معين.
        /// </summary>
        /// <param name="id">معرف المسوق المراد عرض تفاصيله.</param>
        /// <returns>عرض يحتوي على تفاصيل المسوق، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Details(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }

            var systemUser = new UsersClass().retriveUser(id ?? 0, _context);
            if (systemUser == null)
            {
                return NotFound();
            }

            return View(systemUser);
        }

        // GET: Marketers/Create
        /// <summary>
        /// عرض نموذج إنشاء مسوق جديد.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج الإنشاء.</returns>
        public IActionResult Create()
        {
            ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality");
            return View();
        }

        // POST: Marketers/Create
        /// <summary>
        /// معالجة عملية إنشاء مسوق جديد، بما في ذلك إنشاء حساب له في نظام الهوية.
        /// </summary>
        /// <param name="model">البيانات الخاصة بالمسوق الجديد.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CModels.MarketerModel model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var userExists = await _userManager.FindByEmailAsync(model.Email);
                    if (userExists != null) return Ok(new { state = 0, message = _localization["emailused"].Value });
                    userExists = await _userManager.FindByNameAsync(model.UserName);
                    if (userExists != null) return Ok(new { state = 0, message = _localization["usedusername"].Value });
                    HalalaPlusProjectUser user = new()
                    {
                        Email = model.Email,
                        SecurityStamp = Guid.NewGuid().ToString(),
                        PhoneNumber = model.PhoneNo,
                        UserName = model.UserName

                    };

                    var result = await _userManager.CreateAsync(user, model.Password);
                    if (result.Succeeded)
                    {
                        await _userManager.AddToRoleAsync(user, "Marketer");
                        try
                        {
                            _context.Database.BeginTransaction();
                            var ob = new Models.SystemUser();
                            ob.Name = model.Name;
                            ob.PhoneNo = model.PhoneNo;
                            ob.Email = model.Email;
                            ob.DiscountCode = model.DiscountCode;
                            ob.Amount = model.Amount;
                            ob.BirthDate = model.BirthDate;
                            ob.IdentityNo = model.IdentityNo;
                            ob.Nationality = model.Nationality;
                            ob.Precentage = model.Precentage;
                            ob.AspId = user.Id;
                            ob.Deleted = false;
                            ob.MasterId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value;
                            ob.AccountType = "Marketer";
                            ob.DateofJoin = DateTime.Now;
                            _context.SystemUsers.Add(ob);
                            await _context.SaveChangesAsync();
                            _context.Database.CommitTransaction();
                        }
                        catch (Exception)
                        {
                            _context.Database.RollbackTransaction();
                            await _userManager.DeleteAsync(user);
                            throw;
                        }

                        user.EmailConfirmed = true;
                        await _userManager.UpdateAsync(user);
                        return Ok(new { state = 7, message = _localization["addedsuccessfuly"].Value, Url = "Index" });
                    }

                }


                return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });
            }
            catch (Exception ex)
            {

                return Ok(new { state = 0, message = _localization["errorwillsaving"].Value });
            }
        }



        // GET: Marketers/Edit/5
        /// <summary>
        /// عرض نموذج تعديل بيانات مسوق حالي.
        /// </summary>
        /// <param name="id">معرف المسوق المراد تعديله.</param>
        /// <returns>عرض يحتوي على بيانات المسوق في نموذج التعديل، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Edit(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }

            var systemUser = new UsersClass().retriveUser(id ?? 0, _context);
            if (systemUser == null)
            {
                return NotFound();
            }
            ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality", systemUser.NationalityNo);
            return View(systemUser);
        }

        // POST: Marketers/Edit/5
        /// <summary>
        /// معالجة التعديلات المقدمة لبيانات مسوق وحفظها.
        /// </summary>
        /// <param name="model">البيانات المحدثة للمسوق.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة المسوقين؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(CModels.MarketerUserModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var ob = _context.SystemUsers.Find(model.Id);
                    if (ob != null)
                        if (_context.AspNetUsers.Where(p => (p.Email == model.Email || p.UserName == model.UserName) && p.Id != ob.AspId).Count() > 0)
                            return View(model);


                    ob.Name = model.Name;
                    ob.PhoneNo = model.PhoneNo;
                    ob.Email = model.Email;
                    ob.DiscountCode = model.DiscountCode;

                    ob.BirthDate = DateOnly.Parse(model.BirthDate.ToString());
                    ob.IdentityNo = model.IdentityNo;
                    ob.Precentage = model.Precentage;
                    ob.Amount = model.Amount;
                    ob.Nationality = model.Nationality;
                    _context.Update(ob);
                    var userExists = await _userManager.FindByEmailAsync(model.Email);
                    userExists.Email = model.Email;
                    userExists.UserName = model.UserName;
                    await _userManager.UpdateAsync(userExists);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    return View(model);
                }
                return RedirectToAction(nameof(Index));
            }

            ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality", model.Nationality);
            return View(model);
        }

        // GET: Marketers/Delete/5
        /// <summary>
        /// عرض صفحة تأكيد حذف مسوق.
        /// </summary>
        /// <param name="id">معرف المسوق المراد حذفه.</param>
        /// <returns>عرض يحتوي على تفاصيل المسوق لتأكيد الحذف، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Delete(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }
            var systemUser = new UsersClass().retriveUser(id ?? 0, _context);
            if (systemUser == null)
            {
                return NotFound();
            }
            return View(systemUser);
        }

        /// <summary>
        /// عرض قائمة العملاء (مقدمي الخدمة) المرتبطين بالمسوق الحالي.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة بالعملاء.</returns>
        public async Task<IActionResult> Customers()
        {
            if (_context.SystemUsers.Where(p => p.AccountType == "Provider") == null)
                return View(new List<MarketerIndexModel>());

            var systemUser = new UsersClass().retriveProviderss(_context, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value);
            if (systemUser != null) return View(systemUser);
            return View(new List<MarketerIndexModel>());


        }

        /// <summary>
        /// عرض التفاصيل الخاصة بعميل (مقدم خدمة) معين مرتبط بالمسوق.
        /// </summary>
        /// <param name="id">معرف العميل المراد عرض تفاصيله.</param>
        /// <returns>عرض يحتوي على تفاصيل العميل.</returns>
        public async Task<IActionResult> CustomerDetails(long? id)
        {
            if (id == null && _context.SystemUsers.Where(p => p.Deleted != true && p.Id == id) == null)
                return View(new CustomerModel());

            var systemUser = new UsersClass().retriveCustomerDetails(id ?? 0, _context);
            if (systemUser != null) return View(systemUser);
            return View(new CustomerModel());


        }

        // POST: Marketers/Delete/5
        /// <summary>
        /// تنفيذ عملية حذف منطقي للمسوق عن طريق تعيين علامة 'Deleted' إلى 'true'.
        /// </summary>
        /// <param name="id">معرف المسوق المراد حذفه.</param>
        /// <returns>بعد إتمام العملية بنجاح، يتم عرض صفحة قائمة المسوقين.</returns>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(long id)
        {
            if (_context.SystemUsers == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.SystemUsers'  is null.");
            }
            var systemUser = await _context.SystemUsers.FindAsync(id);
            if (systemUser != null)
            {
                systemUser.Deleted = true;
                _context.Update(systemUser);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// التحقق من وجود مستخدم في النظام بالمعرف المحدد.
        /// </summary>
        /// <param name="id">معرف المستخدم للتحقق منه.</param>
        /// <returns>إرجاع 'true' إذا كان المستخدم موجودًا، وإلا 'false'.</returns>
        private bool SystemUserExists(long id)
        {
            return (_context.SystemUsers?.Any(e => e.Id == id)).GetValueOrDefault();
        }
    }
}