﻿@model IEnumerable<HalalaPlusProject.Models.CountriesTable>
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["countries"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["countries"]</h1>

<p>
    <a asp-action="Create">@localizer["create"]</a>
</p>


<div class="row">
        <div class="col-12">
          <div class="card mb-4">
           
            <div class="card-body px-0 pt-0 pb-2">
              <div class="table-responsive p-0">
<table id="tbl1" class="table">
    <thead>
        <tr>
            <th>
                #
            </th>
             <th>
                                    @localizer["thecountry"]
            </th>
            <th>
                                    @localizer["countrynationality"]
            </th>
                                <th>
                                   اسم الدولة انجليزي
                                </th>
                                <th>
                                   الجنسية انجليزي
                                </th>
                                <th> @localizer["options"]</th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Id)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Country)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Nationality)
            </td> 
            <td>
                @Html.DisplayFor(modelItem => item.EnCountry)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EnNationality)
            </td>
            <td>
                                        <a asp-action="Edit" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id"> @localizer["edit"]</a> |
                                        <a asp-action="Details" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id"> @localizer["details"]</a> |
                                        <a asp-action="Delete" class="btn btn-outline-danger tablebtn" asp-route-id="@item.Id"> @localizer["delete"]</a>
                                        <a asp-controller="Cities" asp-action="Index" class="btn btn-outline-danger tablebtn" asp-route-id="@item.Id"> @localizer["citieslist"]</a>
            </td>
        </tr>
}
    </tbody>
</table>
</div>
              
            </div>
          </div>
        </div>
      </div>
   <div  style="display:10px">
    
    <a asp-controller="Cities" asp-action="Index" style="float:left;     margin-right: 10px;" class="btn  btn-primary px-5"> @localizer["showcities"]</a>
    <a asp-controller="Cities" asp-action="Create" style="float:left;     margin-right: 10px;" class="btn btn-primary px-5"> @localizer["addcity"]</a>
                  
                </div>
@section Scripts{
    <script>
  let table = new DataTable('#tbl1');
 
    </script>
}