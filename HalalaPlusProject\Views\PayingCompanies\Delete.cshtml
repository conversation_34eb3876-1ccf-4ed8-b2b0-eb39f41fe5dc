﻿@model HalalaPlusProject.CModels.PayingCompaniesModel
   @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["delete"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2> @localizer["delete"]</h2>
<h4> @localizer["deletepaycommsg"]</h4>

<div>
  
    <hr />
        <div class="col-md-12">
             <div class="row">

     
             <div class="col-md-5">

                  <div class="form-group">
                    <label class="form-label">@localizer["compname"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Name)</label>               
                    </div> 
                    <div class="form-group">
                    <label class="form-label">اسم الشركة انجليزي</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.EnName)</label>               
                    </div>

                       <div class="form-group">
                    <label class="form-label">   @localizer["connectioncode"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.ConnectionCode)</label>               
                    </div>
             
            </div>
               <div class="col-md-5">         
                       <div class="form-group">
                    <label class="form-label"> @localizer["opdetails"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.OperationDetils)</label>               
                     </div> 
                     <div class="form-group">
                    <label class="form-label"> التفاصيل انجليزي</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.EnOperationDetils)</label>               
                     </div>
            </div>
    </div>
    </div>
   @* <dl class="row">
        <dt class = "col-sm-2">
            
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.ConnectionCode)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.ConnectionCode)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.OperationDetils)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.OperationDetils)
        </dd>
    </dl>
    *@
    <form asp-action="Delete" class="mt-2">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-danger"> @localizer["delete"]</button>|
        <a asp-action="Index">  @localizer["backtolist"]</a>
    </form>
</div>
