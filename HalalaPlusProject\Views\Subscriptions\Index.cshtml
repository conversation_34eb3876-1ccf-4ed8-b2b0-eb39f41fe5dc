﻿@model IEnumerable<HalalaPlusProject.Models.Subscription>

@{
    ViewData["Title"] = "قائمة";
}

<h1>قائمة الاشتراكات</h1>

<p>
    <a asp-action="Create">إنشاء اشتراك جديد</a>
</p>
<table class="table">
    <thead>
        <tr>
@*             <th>
                 مشترك
            </th> *@
            <th>
                 الحالة
            </th>
            <th>
                 تاريخ البدء
            </th>
            <th>
                 تاريخ الانتهاء
            </th>
            <th>
                 السعر
            </th>
            <th>
                 رقم العملية
            </th>
            <th>
                 رقم الباقة
            </th>
            <th>
                المستخدم
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
@*                 <td>
                    @Html.DisplayFor(modelItem => item.IsSubscribed)
                </td> *@
                <td>
                    @Html.DisplayFor(modelItem => item.State)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.StartDate)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.EndDate)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Price)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.ProcessNo)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Pack.Id)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.User.Id)
                </td>
                <td>
                    <a asp-action="Edit" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id">تعديل </a>
                    <a asp-action="Details" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id">استعراض </a>
                    <a asp-action="Delete" class="btn btn-outline-danger tablebtn" asp-route-id="@item.Id">حذف </a>
                </td>
            </tr>
        }
    </tbody>
</table>
