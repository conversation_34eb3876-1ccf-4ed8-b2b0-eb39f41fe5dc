﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("FilesTable")]
public partial class FilesTable
{
    [Key]
    public long Id { get; set; }

    [StringLength(250)]
    public string? FileName { get; set; }

    public int? ProviderId { get; set; }

    [StringLength(250)]
    public string? FileLink { get; set; }

    public byte? FileType { get; set; }

    public int? TaskId { get; set; }

    [Column("userId")]
    public long? UserId { get; set; }

    [ForeignKey("TaskId")]
    [InverseProperty("FilesTables")]
    public virtual TaskesTable? Task { get; set; }
}
