﻿using System;
using System.Collections.Generic;

namespace HalalaPlusProject.Models;

public partial class ExpensesPlan
{
    public int ExpensesPlanId { get; set; }

    public int? ManualCommitmentsId { get; set; }

    public int? CommitmentId { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? CreatedAt { get; set; }

    public string? UpdatedBy { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public string? Deleted { get; set; }

    public string? DeletedBy { get; set; }

    public DateTime? DeletedAt { get; set; }

    public bool? IsManualPlan { get; set; }

    public double? Expenses { get; set; }

    public DateTime? ExpensesDate { get; set; }

    public int? PlanId { get; set; }

    public virtual ManualCommitment? ManualCommitments { get; set; }

    public virtual Plan? Plan { get; set; }
}
