﻿using HalalaPlusProject.Areas.Identity.Data;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// يتحكم في إدارة طلبات استبدال النقاط من قبل المستخدمين.
    /// </summary>
    [Authorize]
    public class ReplacePointsController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IStringLocalizer<ReplacePointsController> _localization;

        public ReplacePointsController(HalalaPlusdbContext context, IStringLocalizer<ReplacePointsController> _localization, UserManager<HalalaPlusProjectUser> userManager)
        {
            _context = context;
            this._localization = _localization;
        }

        /// <summary>
        /// يعرض جميع طلبات استبدال النقاط الخاصة بالمستخدم الحالي.
        /// </summary>
        [Authorize(Roles = "Provider")]
        public async Task<IActionResult> Index()
        {
            return View(new ReplaceOrderClass().retrive(
                _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id,
                _context));
        }

        /// <summary>
        /// يعرض جميع طلبات استبدال النقاط الخاصة بالمستخدم الحالي.
        /// </summary>
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Index1()
        {

            var t = new ReplaceOrderClass().retrive1(_context);
            return View(t);
        }

        /// <summary>
        /// يقبل طلب استبدال نقاط محدد ويحدث حالته في قاعدة البيانات.
        /// </summary>
        /// <param name="id">معرف الطلب.</param>
        public async Task<IActionResult> AcceptOrder(long? id)
        {
            if (id != null)
            {
                try
                {
                    var temp = _context.PointReplaceOrders.Include(o => o.User).Where(o => o.Id == id).FirstOrDefault();
                    if (temp != null)
                    {
                        if (temp.State == "Accepted")
                            return Ok(new { state = 0, message = "لقد تم قبول الطلب مسبقا" });
                        if (temp.State == "Reject")
                            return Ok(new { state = 0, message = "الطلب مرفوض لايمكن قبوله" });
                        _context.Database.BeginTransaction();
                        temp.State = "Accepted";
                        temp.Accept = true;
                        _context.Update(temp);

                        var t = _context.CustomerRewardSettings.FirstOrDefault();
                        double co = temp.PointCount??0.0;
                        double c1 = (double)t.PointValueSar ;
                        var x = co/ c1;
                        //var x = (temp.PointCount ?? 0) / ((double)(t.PointValueSar ?? (decimal)1));

                        var monyBox = _context.UsersMonyBoxs.Where(p => p.IsMainMonyBox).FirstOrDefault();

                        monyBox.Amount += x;

                        _context.MonyBoxTransactions.Add(new MonyBoxTransaction
                        {
                            MonyBoxId = monyBox.Id,
                            Credit = x,
                            OperationDate = DateTime.Now,
                            Temp1 = "نقاط تم استبدالها من قبل العميل " + temp.User.Name,
                            UserId = temp.UserId

                        });
                        //_context.Update(monyBox);
                        _context.Update(monyBox);
                        await _context.SaveChangesAsync();

                        _context.Database.CommitTransaction();
                        return Ok(new { state = 1, message = _localization["requestaccepted"].Value });
                    }
                }
                catch
                {
                    _context.Database.RollbackTransaction();
                    return Ok(new { state = 0, message = _localization["requestnotaccepted"].Value });
                }
            }

            return Ok(new { state = 0, message = _localization["requestnotaccepted"].Value });
        }

        /// <summary>
        /// يرفض طلب استبدال نقاط محدد ويحدث حالته في قاعدة البيانات.
        /// </summary>
        /// <param name="id">معرف الطلب.</param>
        public async Task<IActionResult> rejectOrder(long? id)
        {
            if (id != null)
            {
                try
                {
                    var temp = _context.PointReplaceOrders.Find(id);
                    if (temp != null)
                    {
                        temp.State = "Reject";
                        temp.Accept = true;
                        _context.Update(temp);
                        await _context.SaveChangesAsync();
                        return Ok(new { state = 1, message = _localization["requestrejected"].Value });
                    }
                }
                catch
                {
                    return Ok(new { state = 0, message = _localization["requestnotrejected"].Value });
                }
            }

            return Ok(new { state = 0, message = _localization["requestnotrejected"].Value });
        }
    }
}
