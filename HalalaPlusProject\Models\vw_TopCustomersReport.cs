﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models
{
    [Keyless]

    public partial class vw_TopCustomersReport
    {
        public long CustomerId { get; set; }

        public string? CustomerName { get; set; } 

        public string? Country { get; set; }

        public int? TotalOrders { get; set; }

        public double? TotalPayments { get; set; }

        public DateTime? LastOrderDate { get; set; }

        public double? AvgOrderValue { get; set; }

        public int? DaysSinceLastOrder { get; set; }

    }

    }

