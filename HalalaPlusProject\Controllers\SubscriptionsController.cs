﻿using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    /// <summary>
    /// متحكم لإدارة اشتراكات المستخدمين في الباقات المختلفة.
    /// </summary>
    public class SubscriptionsController : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public SubscriptionsController(HalalaPlusdbContext context)
        {
            _context = context;
        }

        // GET: Subscriptions
        /// <summary>
        /// يعرض قائمة بجميع الاشتراكات.
        /// </summary>
        public async Task<IActionResult> Index()
        {
            var halalaPlusdbContext = _context.Subscriptions.Include(s => s.Pack).Include(s => s.User);
            return View(await halalaPlusdbContext.ToListAsync());
        }

        // GET: Subscriptions/Details/5
        /// <summary>
        /// يعرض تفاصيل اشتراك معين بناءً على المعرّف.
        /// </summary>
        /// <param name="id">معرّف الاشتراك.</param>
        public async Task<IActionResult> Details(long? id)
        {
            if (id == null || _context.Subscriptions == null)
            {
                return NotFound();
            }

            var subscription = await _context.Subscriptions
                .Include(s => s.Pack)
                .Include(s => s.User)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (subscription == null)
            {
                return NotFound();
            }

            return View(subscription);
        }

        // GET: Subscriptions/Create
        /// <summary>
        /// يعرض صفحة إنشاء اشتراك جديد.
        /// </summary>
        public IActionResult Create()
        {
            ViewData["PackId"] = new SelectList(_context.Packages, "Id", "Id");
            ViewData["UserId"] = new SelectList(_context.SystemUsers, "Id", "Id");
            return View();
        }

        // POST: Subscriptions/Create
        /// <summary>
        /// ينشئ اشتراكًا جديدًا ويحفظه في قاعدة البيانات.
        /// </summary>
        /// <param name="subscription">بيانات الاشتراك الجديد.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,UserId,PackId,IsSubscribed,State,StartDate,EndDate,Price,ProcessNo")] Subscription subscription)
        {
            if (ModelState.IsValid)
            {
                var temp = _context.Packages.Find(subscription.PackId);
                subscription.Price = temp.Price;
                _context.Add(subscription);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            ViewData["PackId"] = new SelectList(_context.Packages, "Id", "Id", subscription.PackId);
            ViewData["UserId"] = new SelectList(_context.SystemUsers, "Id", "Id", subscription.UserId);
            return View(subscription);
        }

        // GET: Subscriptions/Edit/5
        /// <summary>
        /// يعرض صفحة تعديل اشتراك موجود.
        /// </summary>
        /// <param name="id">معرّف الاشتراك المراد تعديله.</param>
        public async Task<IActionResult> Edit(long? id)
        {
            if (id == null || _context.Subscriptions == null)
            {
                return NotFound();
            }

            var subscription = await _context.Subscriptions.FindAsync(id);
            var temp = _context.Packages.Find(subscription.PackId);
            subscription.Price = temp.Price;
            if (subscription == null)
            {
                return NotFound();
            }
            ViewData["PackId"] = new SelectList(_context.Packages, "Id", "Id", subscription.PackId);
            ViewData["UserId"] = new SelectList(_context.SystemUsers, "Id", "Id", subscription.UserId);
            return View(subscription);
        }

        // POST: Subscriptions/Edit/5
        /// <summary>
        /// يحفظ التعديلات على اشتراك موجود في قاعدة البيانات.
        /// </summary>
        /// <param name="id">معرّف الاشتراك المستهدف.</param>
        /// <param name="subscription">بيانات الاشتراك المحدثة.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(long id, [Bind("Id,UserId,PackId,IsSubscribed,State,StartDate,EndDate,Price,ProcessNo")] Subscription subscription)
        {
            if (id != subscription.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(subscription);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!SubscriptionExists(subscription.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["PackId"] = new SelectList(_context.Packages, "Id", "Id", subscription.PackId);
            ViewData["UserId"] = new SelectList(_context.SystemUsers, "Id", "Id", subscription.UserId);
            return View(subscription);
        }

        // GET: Subscriptions/Delete/5
        /// <summary>
        /// يعرض صفحة تأكيد حذف اشتراك.
        /// </summary>
        /// <param name="id">معرّف الاشتراك المراد حذفه.</param>
        public async Task<IActionResult> Delete(long? id)
        {
            if (id == null || _context.Subscriptions == null)
            {
                return NotFound();
            }

            var subscription = await _context.Subscriptions
                .Include(s => s.Pack)
                .Include(s => s.User)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (subscription == null)
            {
                return NotFound();
            }

            return View(subscription);
        }

        // POST: Subscriptions/Delete/5
        /// <summary>
        /// يحذف اشتراكًا من قاعدة البيانات بعد التأكيد.
        /// </summary>
        /// <param name="id">معرّف الاشتراك المطلوب حذفه.</param>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(long id)
        {
            if (_context.Subscriptions == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.Subscriptions'  is null.");
            }
            var subscription = await _context.Subscriptions.FindAsync(id);
            if (subscription != null)
            {
                _context.Subscriptions.Remove(subscription);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// يتحقق من وجود اشتراك بالمعرّف المحدد.
        /// </summary>
        /// <param name="id">معرّف الاشتراك.</param>
        /// <returns>`true` إذا كان الاشتراك موجودًا، وإلا `false`.</returns>
        private bool SubscriptionExists(long id)
        {
            return (_context.Subscriptions?.Any(e => e.Id == id)).GetValueOrDefault();
        }
    }
}