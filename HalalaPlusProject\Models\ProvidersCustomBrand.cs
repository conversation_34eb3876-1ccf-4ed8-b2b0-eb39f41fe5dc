﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("ProvidersCustomBrand")]
public partial class ProvidersCustomBrand
{
    [Key]
    public long Id { get; set; }

    [StringLength(450)]
    public string? UserId { get; set; }

    [StringLength(50)]
    public string? FirstColor { get; set; }

    [StringLength(50)]
    public string? SecondColor { get; set; }

    [StringLength(250)]
    public string? Logo { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateAt { get; set; }

    [StringLength(250)]
    public string? OffesLogo { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("ProvidersCustomBrands")]
    public virtual AspNetUser? User { get; set; }
}
