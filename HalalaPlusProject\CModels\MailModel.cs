﻿namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل محتوى رسالة البريد الإلكتروني التي سيتم إرسالها.
    /// </summary>
    public class MailModel
    {
        /// <summary>
        /// عنوان البريد الإلكتروني للمستلم.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// نص الرسالة.
        /// </summary>
        public string body { get; set; }

        /// <summary>
        /// عنوان الموضوع.
        /// </summary>
        public string Subject { get; set; }
    }

    /// <summary>
    /// يحتوي على معلومات البريد الإلكتروني الخاص بالمرسل.
    /// </summary>
    public class SenderMailInfo
    {
        /// <summary>
        /// عنوان البريد الإلكتروني للمرسل.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// كلمة مرور البريد الإلكتروني للمرسل.
        /// </summary>
        public string password { get; set; }
    }
}
