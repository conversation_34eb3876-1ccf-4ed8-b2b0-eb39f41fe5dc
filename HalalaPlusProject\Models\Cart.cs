﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("Cart")]
public partial class Cart
{
    [Key]
    public long Id { get; set; }

    public long? UserId { get; set; }

    public int? ProductId { get; set; }

    [Column("quantity")]
    public int? Quantity { get; set; }

    public double? Price { get; set; }

    [ForeignKey("ProductId")]
    [InverseProperty("Carts")]
    public virtual Product? Product { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("Carts")]
    public virtual SystemUser? User { get; set; }
}
