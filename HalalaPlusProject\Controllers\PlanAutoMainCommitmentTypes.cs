﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace HalalaPlusProject.Controllers
{
    public class PlanAutoMainCommitmentTypes : Controller
    {
        // GET: PlanAutoMainCommitmentTypes
        public ActionResult Index()
        {
            return View();
        }

        // GET: PlanAutoMainCommitmentTypes/Details/5
        public ActionResult Details(int id)
        {
            return View();
        }

        // GET: PlanAutoMainCommitmentTypes/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: PlanAutoMainCommitmentTypes/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(IFormCollection collection)
        {
            try
            {
                return RedirectToAction(nameof(Index));
            }
            catch
            {
                return View();
            }
        }

        // GET: PlanAutoMainCommitmentTypes/Edit/5
        public ActionResult Edit(int id)
        {
            return View();
        }

        // POST: PlanAutoMainCommitmentTypes/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(int id, IFormCollection collection)
        {
            try
            {
                return RedirectToAction(nameof(Index));
            }
            catch
            {
                return View();
            }
        }

        // GET: PlanAutoMainCommitmentTypes/Delete/5
        public ActionResult Delete(int id)
        {
            return View();
        }

        // POST: PlanAutoMainCommitmentTypes/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Delete(int id, IFormCollection collection)
        {
            try
            {
                return RedirectToAction(nameof(Index));
            }
            catch
            {
                return View();
            }
        }
    }
}
