﻿@model HalalaPlusProject.Models.Subscription

@{
    ViewData["Title"] = "تحرير";
}

<h1>تحرير</h1>

<h4>الاشتراك</h4>
<hr />
<div class="row">
    <div class="col-md-12">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="UserId" class="control-label">المستخدم</label>
                        <select asp-for="UserId" class="form-control" asp-items="ViewBag.UserId"></select>
                        <span asp-validation-for="UserId" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="PackId" class="control-label">الباقة</label>
                        <select asp-for="PackId" class="form-control" asp-items="ViewBag.PackId"></select>
                        <span asp-validation-for="PackId" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="IsSubscribed" class="control-label">مشترك</label>
                        <input asp-for="IsSubscribed" class="form-control" />
                        <span asp-validation-for="IsSubscribed" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="State" class="control-label">الحالة</label>
                        <input asp-for="State" class="form-control" />
                        <span asp-validation-for="State" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="StartDate" class="control-label">تاريخ البدء</label>
                        <input asp-for="StartDate" class="form-control" />
                        <span asp-validation-for="StartDate" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="EndDate" class="control-label">تاريخ الانتهاء</label>
                        <input asp-for="EndDate" class="form-control" />
                        <span asp-validation-for="EndDate" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Price" class="control-label">السعر</label>
                        <input asp-for="Price" class="form-control" />
                        <span asp-validation-for="Price" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ProcessNo" class="control-label">رقم العملية</label>
                        <input asp-for="ProcessNo" class="form-control" />
                        <span asp-validation-for="ProcessNo" class="text-danger"></span>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <input type="submit" value="حفظ" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">العودة إلى القائمة</a>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
