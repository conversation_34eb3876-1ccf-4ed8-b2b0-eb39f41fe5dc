﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("Walet")]
public partial class Walet
{
    [Key]
    public long Id { get; set; }

    public long? UserId { get; set; }

    public double? Amount { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("Walets")]
    public virtual SystemUser? User { get; set; }

    [InverseProperty("Walet")]
    public virtual ICollection<WaletTransction> WaletTransctions { get; set; } = new List<WaletTransction>();
}
