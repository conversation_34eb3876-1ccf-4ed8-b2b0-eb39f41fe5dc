﻿using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Entities;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    /// <summary>
    /// متحكم لإدارة المعاملات المالية المختلفة، بما في ذلك معاملات الصناديق المالية، طلبات سحب الأموال، وطلبات الدفع.
    /// </summary>
    public class TranscationsController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly ILogger<TranscationsController> _logger;
        private readonly IWebHostEnvironment _hosting;
        public TranscationsController(HalalaPlusdbContext context, ILogger<TranscationsController> logger, IWebHostEnvironment hosting)
        {
            this._context = context;
            _logger = logger;
            _hosting = hosting;
        }

        /// <summary>
        /// يعرض صفحة تحتوي على قوائم لجميع أنواع المعاملات المالية.
        /// </summary>
        public IActionResult Index()
        {

            var ob = new MonyBoxsTransctionsViewModel();
            ob.MonyBoxsTrans = _context.UserMonyBoxsTransctionViews.ToList();
            ob.retrivemonyorders = _context.RetriveMonyMonyViews.ToList();
            ob.PaymentsOrdersTrans = _context.UsersPaymentsOrdersViews.ToList();
            return View(ob);
        }

        /// <summary>
        /// يعرض صفحة تعديل طلب سحب أموال.
        /// </summary>
        /// <param name="Id">معرّف طلب سحب الأموال.</param>
        [HttpGet]
        public IActionResult Edit(long Id)
        {
            var ob = _context.RetriveMonyMonyViews.Where(o => o.Id == Id).FirstOrDefault();
            if (ob == null) RedirectToAction(nameof(Index));
            if (ob.IsCanceled == true) RedirectToAction(nameof(Index));
            return View(ob);
        }

        /// <summary>
        /// يعالج عملية تعديل طلب سحب الأموال، حيث يتم تحديث حالته إلى "مدفوع" وحفظ إيصال الدفع.
        /// </summary>
        /// <param name="model">البيانات المحدثة لطلب سحب الأموال.</param>
        [HttpPost]
        public IActionResult Edit(EditRetriveMonyOrderModel model)
        {
            var ob = _context.RetriveMonyBoxAmounts.FirstOrDefault(o => o.Id == model.Id);
            if (ob == null)
                return Ok(new { state = 0, message = "الطلب غير موجود" });

            try
            {
                if (ob.Status == "canceled")
                    return Ok(new { state = 0, message = "لقد تم الغاء الطلب لا يمكن تعديله" });
 
                if (model.recept == null && string.IsNullOrEmpty(ob.Recepit))
                {
                    return Ok(new { state = 0, message = "يجب رفع صورة الايصال قبل الحفظ" });
                }

                ob.PayDate = DateTime.Now;
                ob.PaymentId = model.PaymentId;
                ob.Status = "payed";
                ob.Payed = true;

                
                if (model.recept != null)
                {
                    ob.Recepit = HandleImages.SaveImage(model.recept, "images", _hosting);
                }

                _context.Update(ob);
                _context.SaveChanges();

                var monybox = _context.UsersMonyBoxs.Find(ob.MonyBoxId);
                monybox.Amount -= ob.Amount;

                var t = new MonyBoxTransaction
                {
                    OperationDate = DateTime.Now,
                    MonyBoxId = ob.MonyBoxId,
                    
                    Debit = ob.Amount,
                    UserId = ob.UserId,
                    PaymentId = model.PaymentId,
                };
                _context.Add(t);

                _context.Update(monybox);
                _context.SaveChanges();

                return Ok(new { state = 7, message = "تمت العملية بنجاح", url = "../Index" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                return Ok(new { state = 0, message = "حدث خطأ" });
            }
        }
        [HttpGet]
        public IActionResult popup(long Id)
        {
            var ob = _context.RetriveMonyMonyViews.Where(o => o.Id == Id).FirstOrDefault();
            if (ob == null) RedirectToAction(nameof(Index));
            if (ob.IsCanceled == true) RedirectToAction(nameof(Index));
            return View(ob);
        }

        /// <summary>
        /// يعالج عملية تعديل طلب سحب الأموال، حيث يتم تحديث حالته إلى "مدفوع" وحفظ إيصال الدفع.
        /// </summary>
        /// <param name="model">البيانات المحدثة لطلب سحب الأموال.</param>
        [HttpPost]
        public IActionResult popup(EditRetriveMonyOrderModel model)
        {
            var ob = _context.RetriveMonyBoxAmounts.FirstOrDefault(o => o.Id == model.Id);
            if (ob == null)
                return Ok(new { state = 0, message = "الطلب غير موجود" });

            try
            {
                if (ob.Status == "canceled")
                    return Ok(new { state = 0, message = "لقد تم الغاء الطلب لا يمكن تعديله" });

                if (model.recept == null && string.IsNullOrEmpty(ob.Recepit))
                {
                    return Ok(new { state = 0, message = "يجب رفع صورة الايصال قبل الحفظ" });
                }

                ob.PayDate = DateTime.Now;
                ob.PaymentId = model.PaymentId;
                ob.Status = "payed";
                ob.Payed = true;


                if (model.recept != null)
                {
                    ob.Recepit = HandleImages.SaveImage(model.recept, "images", _hosting);
                }

                _context.Update(ob);
                _context.SaveChanges();

                var monybox = _context.UsersMonyBoxs.Find(ob.MonyBoxId);
                monybox.Amount -= ob.Amount;

                var t = new MonyBoxTransaction
                {
                    OperationDate = DateTime.Now,
                    MonyBoxId = ob.MonyBoxId,

                    Debit = ob.Amount,
                    UserId = ob.UserId,
                    PaymentId = model.PaymentId,
                };
                _context.Add(t);

                _context.Update(monybox);
                _context.SaveChanges();

                return Ok(new { state = 7, message = "تمت العملية بنجاح", url = "../Index" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                return Ok(new { state = 0, message = "حدث خطأ" });
            }
        }
        /// <summary>
        /// يقوم بقبول طلب سحب أموال وتغيير حالته إلى "قيد المعالجة".
        /// </summary>
        /// <param name="Id">معرّف طلب سحب الأموال.</param>
        public IActionResult Accept(long Id)
        {

            var ob = _context.RetriveMonyBoxAmounts.Where(o => o.Id == Id).FirstOrDefault();
            if (ob == null)
                return Ok(new { state = 0, message = "الطلب غير موجود" });
            try
            {
                if (ob.Status == "canceled") return Ok(new { state = 0, message = "لقد تم الغاء الطلب لا يمكن قبولة" });
                ob.Status = "processing";
                _context.Update(ob);
                _context.SaveChanges(); return Ok(new { state = 1, message = "تمت العملية بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                return Ok(new { state = 0, message = "حدث خطاء" });
            }
        }

        /// <summary>
        /// يقوم برفض طلب سحب أموال وتغيير حالته إلى "مرفوض".
        /// </summary>
        /// <param name="Id">معرّف طلب سحب الأموال.</param>
         [HttpPost]
        public IActionResult reject(long Id)
        {

            var ob = _context.RetriveMonyBoxAmounts.Where(o => o.Id == Id).FirstOrDefault();
            if (ob == null)
                return Ok(new { state = 0, message = "الطلب غير موجود" });
            try
            {
                if (ob.Status == "canceled") return Ok(new { state = 0, message = "لقد تم الغاء الطلب لا يمكن رفضة" });

                ob.Status = "rejected";
                _context.Update(ob);
                _context.SaveChanges(); return Ok(new { state = 1, message = "تمت العملية بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                return Ok(new { state = 0, message = "حدث خطاء" });
            }
        }
    }
}