﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class Platform
{
    [Key]
    public int Id { get; set; }

    [Column("Platform")]
    [StringLength(50)]
    public string? Platform1 { get; set; }

    public string? ImageLink { get; set; }

    [Column("isActive")]
    public bool? IsActive { get; set; }

    [Column("stopped")]
    public bool? Stopped { get; set; }

    [StringLength(50)]
    public string? EnPlatform { get; set; }

    [InverseProperty("PIdNavigation")]
    public virtual ICollection<SocialMediaAccount> SocialMediaAccounts { get; set; } = new List<SocialMediaAccount>();
}
