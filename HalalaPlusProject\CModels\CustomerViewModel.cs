﻿using HalalaPlusProject.Models;

namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل بيانات العميل الأساسية وعلاقته بالحصالات الخاصة به.
    /// </summary>
    public class CustomerViewModel
    {
        /// <summary>
        /// معرف العميل.
        /// </summary>
        public long CustomerId { get; set; }

        /// <summary>
        /// اسم العميل.
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// رقم جوال العميل.
        /// </summary>
        public string CustomerPhone { get; set; }

        /// <summary>
        /// البريد الإلكتروني للعميل.
        /// </summary>
        public string CustomerEmail { get; set; }

        /// <summary>
        /// عدد الحصالات للعميل.
        /// </summary>
        public int CustomerMonyBoxsCount { get; set; }

        /// <summary>
        /// إجمالي المبلغ الموجود في جميع الحصالات.
        /// </summary>
        public string CustomerTotalAmount { get; set; }

        /// <summary>
        /// قائمة الحصالات المرتبطة بالعميل.
        /// </summary>
        public List<UsersMonyBox> MonyBoxes { get; set; }
    }

    /// <summary>
    /// يمثل بيانات الحصالة التابعة للعميل.
    /// </summary>
    public class MonyBoxViewModel
    {
        /// <summary>
        /// معرف الحصالة.
        /// </summary>
        public long MonyBoxId { get; set; }

        /// <summary>
        /// اسم الحصالة.
        /// </summary>
        public string MonyBoxName { get; set; }

        /// <summary>
        /// الهدف المالي المراد تحقيقه في الحصالة.
        /// </summary>
        public float Target { get; set; }

        /// <summary>
        /// تاريخ بداية الادخار في الحصالة.
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// تاريخ نهاية الادخار في الحصالة.
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// القيمة الحالية الموجودة في الحصالة.
        /// </summary>
        public float Amount { get; set; }

        /// <summary>
        /// قائمة العمليات المالية المرتبطة بهذه الحصالة.
        /// </summary>
        public List<TransactionViewModel> Transactions { get; set; }
    }

    /// <summary>
    /// يمثل تفاصيل العملية المالية .
    /// </summary>
    public class TransactionViewModel
    {
        /// <summary>
        /// معرف العملية.
        /// </summary>
        public long TransactionId { get; set; }

        /// <summary>
        /// القيمة الدائنة في العملية.
        /// </summary>
        public float Credit { get; set; }

        /// <summary>
        /// القيمة المدينة في العملية.
        /// </summary>
        public float Debit { get; set; }

        /// <summary>
        /// تاريخ تنفيذ العملية.
        /// </summary>
        public DateTime OperationDate { get; set; }

        /// <summary>
        /// معرف الدفع المرتبط بالعملية.
        /// </summary>
        public string PaymentId { get; set; }

        /// <summary>
        /// هل تم التحقق من العملية.
        /// </summary>
        public bool IsVerified { get; set; }

        /// <summary>
        /// تاريخ التحقق من العملية إن وجد.
        /// </summary>
        public DateTime? VerifayDate { get; set; }
    }
}
