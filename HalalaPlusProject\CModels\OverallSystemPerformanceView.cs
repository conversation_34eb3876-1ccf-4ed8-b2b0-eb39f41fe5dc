﻿namespace HalalaPlusProject.CModels
{
    public class OverallSystemPerformanceView
    {
        public int ActiveEmployees { get; set; }
        public int ActiveCustomers { get; set; }
        public int ActiveProviders { get; set; }
        public int ActiveMarketers { get; set; }
        public int OrdersThisMonth { get; set; }
        public decimal TotalRevenue { get; set; }

        public string CustomerCountry { get; set; }
        public string ProviderCountry { get; set; }
        public string MarketerCountry { get; set; }
        public string EmployeeCountry { get; set; }

        public string TopPackageName { get; set; }
        public int TopPackageUsageCount { get; set; }
    }

}
