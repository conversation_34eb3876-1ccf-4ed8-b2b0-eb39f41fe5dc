﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    public class PayingCompaniesModel
    {
        public int Id { get; set; }
        [Required]
        //[Display(Name ="اسم الشركة")]
        public string? Name { get; set; }
        public string? EnName { get; set; }
        [Required]
        //[Display(Name = "كود الربط")]
        public string? ConnectionCode { get; set; }
        //[Display(Name = "تفاصيل العملية")]
        public string? OperationDetils { get; set; }
        public string? EnOperationDetils { get; set; }
    }
}
