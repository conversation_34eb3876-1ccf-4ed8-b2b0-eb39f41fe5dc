﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Models;

namespace HalalaPlusProject.CustomClasses
{
    public class GrantingDiscountsClass
    {
        public List<GrantDiscountModel> retrive(HalalaPlusdbContext _context, bool isActive = true, bool isDeleated = true, long? ids = null)
        {
            try
            {
                var t= (from ob in _context.GrantedDiscounts
                        where ob.Provider.Deleted==false&& ob.GrantedByHalalalPlus!=true

                       select new CModels.GrantDiscountModel
                       {
                           discount = ob.Discount,
                           rate = ob.Rate,
                           Id = ob.Id,
                           amount = ob.Amount,
                           CustomerName=ob.Provider.Name,
                           phoneNo =ob.Provider.PhoneNo
                           ,product=ob.ProductId,
                           //productName= ob.Product.EnDiscountName
                       }).OrderByDescending(f=>f.discount).ToList(); ;
                return t;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return new List<GrantDiscountModel>();
            }
        }

        public List<GrantDiscountModel> GetDiscount(HalalaPlusdbContext _context, bool isActive = true, bool isDeleated = true, long? ids = null)
        {
            try
            {
                var t = (from ob in _context.CustomerDiscounts
                         join city in _context.CitiesTables
                         on ob.User.City equals city.Id into cityJoin
                         from city in cityJoin.DefaultIfEmpty()
                         where ob.ProviderId == ids
                         select new CModels.GrantDiscountModel
                         {
                             CustomerName = ob.User.Name,
                             City = city.City,   
                             discount = ob.DiscountAmount,
                             rate = ob.DiscountRate,
                             Id = ob.Id,
                             amount = ob.Amount,
                             phoneNo = ob.User.PhoneNo,
                             OperationDate = ob.CreateOn,
                         })
                         .OrderByDescending(f => f.discount)
                         .ToList();

                return t;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return new List<GrantDiscountModel>();
            }
        }



        public async Task<Object> grantDiscount(CModels.GrantDiscountModel model, HalalaPlusdbContext _context, long userId)
        {
            try
            {
                if (model != null )
                {
                    var temp = new GrantedDiscount();
                    temp.UserId = model.Id;
                    temp.Amount = model.amount;
                    temp.ProductId = model.product;
                    temp.ProviderId = userId;
                    temp.Rate = model.rate;
                    
                    temp.Points=int.Parse( getPointsForOneSale(_context,model.product??0,model.Id??0).ToString());
                    temp.Discount=model.discount;
                    temp.GrantDate = DateTime.Now;
                    _context.Add(temp);
                    await _context.SaveChangesAsync();
                    return new { state = 1, message = "تمت العملية بنجاح" };
                }
                return new { state = 0, message = "ليس هناك بيانات" };
            }
            catch (Exception)
            {
                return new { state = 0, message = "خطاء " };
            }
        }
        private double getPointsForOneSale(HalalaPlusdbContext _context, long id,long userid) {
            var ob = _context.DiscountsTables.Where(o => o.Id ==id).FirstOrDefault();
            if (ob != null && ob.GrantPoints)
            {
                var temp=_context.PointSettings.Where(o=>o.UserId==userid).FirstOrDefault();
                if(temp != null)
                    return temp.Points/temp.Sales??0;

            }
            return 0;
        }

        public async Task<bool> Activate(HalalaPlusdbContext _context, long id, bool activate)
        {
            try
            {
                var temp = _context.CustomerDiscounts.Find(id);
                if (temp == null) return false;
                temp.CustomerStatus = "granted";
                _context.Update(temp);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

    }
}
