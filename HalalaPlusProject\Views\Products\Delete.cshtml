﻿@model HalalaPlusProject.Models.Product

@{
    ViewData["Title"] = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<h3>هل تريد حذف هذا الصنف</h3>
<div >
<div class="row">

        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Name" class="control-label">اسم المنتج</label>
                    <input asp-for="Name" class="form-control" />
                    <span asp-validation-for="Name" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <label asp-for="Description" class="control-label">الوصف</label>
                    <input asp-for="Description" class="form-control" />
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>
             
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Engname" class="control-label">اسم المنتج بالانجليزي</label>
                    <input asp-for="Engname" class="form-control" />
                    <span asp-validation-for="Engname" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <label asp-for="Engdescription" class="control-label">الوصف بالانجليزي</label>
                    <input asp-for="Engdescription" class="form-control" />
                    <span asp-validation-for="Engdescription" class="text-danger"></span>
                </div>


                <div class="form-group">
                    <label asp-for="Catagory" class="control-label">الصنف</label>
                    <select asp-for="Catagory" class="form-control" asp-items="ViewBag.Catagory"></select>
                </div>
                <div class="form-group">
                    <label asp-for="Price" class="control-label">السعر</label>
                    <input asp-for="Price" class="form-control" />
                    <span asp-validation-for="Price" class="text-danger"></span>
                </div> 
                <div class="form-group">
                <img style="width:100%;" src="@Model.Image" />
                </div>
               
            </div>
        </div>

</div>

    
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-danger" >ايقاف</button>|
        <a asp-action="Index">عودة</a>
    </form>
</div>
