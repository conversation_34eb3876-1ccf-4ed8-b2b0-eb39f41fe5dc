﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class CouponsOffersView
{
    [Column("PId")]
    public int? Pid { get; set; }

    [Column("storename")]
    [StringLength(250)]
    public string? Storename { get; set; }

    [Column("enstorename")]
    [StringLength(250)]
    public string? Enstorename { get; set; }

    public long Id { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    [Column("enName")]
    [StringLength(1500)]
    public string? EnName { get; set; }

    public string? EnDetails { get; set; }

    [StringLength(1500)]
    public string? Details { get; set; }

    [Column("overview")]
    [StringLength(500)]
    public string? Overview { get; set; }

    [Column("enoverview")]
    [StringLength(1500)]
    public string? Enoverview { get; set; }

    [Column("discount")]
    public double? Discount { get; set; }

    [Column("code")]
    [StringLength(20)]
    public string? Code { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? StartDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? EndDate { get; set; }

    [StringLength(500)]
    public string? StoreLink { get; set; }

    [Column("recType")]
    [StringLength(20)]
    public string? RecType { get; set; }

    public bool Deleted { get; set; }

    public int? Activity { get; set; }

    [Column("img")]
    [StringLength(500)]
    public string? Img { get; set; }

    [Column("isActive")]
    public bool? IsActive { get; set; }

    [Column("activenName")]
    [StringLength(250)]
    public string? ActivenName { get; set; }

    [Column("actvName")]
    [StringLength(500)]
    public string? ActvName { get; set; }

    [Column("image")]
    [StringLength(500)]
    public string? Image { get; set; }
}
