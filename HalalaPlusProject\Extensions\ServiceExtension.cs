﻿

namespace HalalaPlusProject.Extensions
{
    public static class ServiceExtension
    {
        public static IServiceCollection RegisterService(this IServiceCollection services)
        {
            #region Services

          
            //services.AddScoped<IOffersService, OffersService>();

            #endregion

            #region Repositories


            //services.AddTransient<IProductRepository, ProductRepository>();

            #endregion
         
            return services;
        }
    }
}
