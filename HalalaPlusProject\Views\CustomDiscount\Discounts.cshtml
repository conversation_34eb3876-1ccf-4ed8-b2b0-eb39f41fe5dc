﻿@model List<HalalaPlusProject.CModels.GrantDiscountModel>
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer localizer

@{
    ViewData["Title"] = localizer["offers"];
}

<div class="row">
    <h3>@localizer["grantdiscount"]</h3>
</div>
 
<div class="row mb-3">
    <div class="col-md-4">
        <input type="text" id="phoneSearch" class="form-control" placeholder="ادخل رقم الجوال للبحث..." />
    </div>
    <div class="col-md-2">
        <button class="btn btn-primary" onclick="searchByPhone()">@localizer["search"]</button>
    </div>
</div>

<div>
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table id="tbl1" class="table table-striped text-center">
                            <thead>
                                <tr>
                                    <th>@localizer["phoneno"]</th>
                                    <th>@localizer["discounts"]</th>
                                    <th>@localizer["rate"]</th>
                                    <th>@localizer["amount"]</th>
                                    <th>@localizer["more"]</th>
                                </tr>
                            </thead>
                            <tbody id="discountTableBody">
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>@item.phoneNo</td>
                                        <td>@item.discount</td>
                                        <td>@item.rate</td>
                                        <td>@item.amount</td>
                                        <td>
                                            <button class="btn btn-success" onclick="confirmActivate(@item.Id)">@localizer["Grant"]</button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let table = new DataTable('#tbl1');

        function confirmActivate(id) {
            if (confirm("@localizer["Areyousureyouwanttograntthisdiscount"]؟")) {
                fetch(`/CustomDiscount/Activate?id=${id}`, {
                    method: "POST"
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    if (data.state === 1) {
                        location.reload();
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء تنفيذ العملية.");
                    console.error(error);
                });
            }
        }

        // ✅ البحث بالهاتف
        function searchByPhone() {
            let phone = document.getElementById("phoneSearch").value;

            fetch(`/CustomDiscount/SearchDiscounts?phone=${phone}`)
                .then(response => response.json())
                .then(data => {
                    let tbody = document.getElementById("discountTableBody");
                    tbody.innerHTML = "";

                    if (data.length === 0) {
                        tbody.innerHTML = "<tr><td colspan='5'>لا توجد خصومات لهذا الرقم</td></tr>";
                    } else {
                        data.forEach(item => {
                            tbody.innerHTML += `
                                <tr>
                                    <td>${item.phoneNo}</td>
                                    <td>${item.discount}</td>
                                    <td>${item.rate}</td>
                                    <td>${item.amount}</td>
                                    <td><button class="btn btn-success" onclick="confirmActivate(${item.id})">منح</button></td>
                                </tr>`;
                        });
                    }
                })
                .catch(error => console.error(error));
        }
    </script>
}
