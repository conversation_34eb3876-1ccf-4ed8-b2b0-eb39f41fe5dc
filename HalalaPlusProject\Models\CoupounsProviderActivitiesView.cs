﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class CoupounsProviderActivitiesView
{
    [Column(TypeName = "datetime")]
    public DateTime? StartDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? EndDate { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    public long Id { get; set; }

    [Column("isActive")]
    public bool? IsActive { get; set; }

    [StringLength(500)]
    public string? Img { get; set; }

    public long? MasterId { get; set; }

    [Column("recType")]
    [StringLength(20)]
    public string? RecType { get; set; }

    public long ProviderId { get; set; }

    [StringLength(500)]
    public string? ProviderName { get; set; }

    public int? City { get; set; }

    [StringLength(1)]
    [Unicode(false)]
    public string? Status { get; set; }

    public bool? IsOrder { get; set; }

    [Column("orderstate")]
    [StringLength(50)]
    public string? Orderstate { get; set; }

    [StringLength(1500)]
    public string? ProviderEnName { get; set; }

    public bool? Deleted { get; set; }

    public int Activity { get; set; }

    [StringLength(250)]
    public string? ActivityEnName { get; set; }

    [StringLength(500)]
    public string? ActivityName { get; set; }

    [Column("image")]
    [StringLength(500)]
    public string? Image { get; set; }

    [StringLength(1500)]
    public string? EnName { get; set; }

    [StringLength(50)]
    public string? PhoneNo { get; set; }

    public double? Discount { get; set; }

    [StringLength(20)]
    public string? CopunCode { get; set; }

    public string? EnDetails { get; set; }

    [StringLength(1500)]
    public string? Details { get; set; }

    [Column("couponDeleted")]
    public bool CouponDeleted { get; set; }
}
