﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class ProductOptionalField
{
    [Key]
    [Column("PKId")]
    public long Pkid { get; set; }

    public int? Id { get; set; }

    public string? Required { get; set; }

    public string? DefaultValue { get; set; }

    public string? Hint { get; set; }

    public string? Label { get; set; }

    public string? FieldTypeId { get; set; }

    public string? FieldCode { get; set; }

    [StringLength(20)]
    public string? Lang { get; set; }

    [Column("OptionalFieldID")]
    public string? OptionalFieldId { get; set; }

    public string? Options { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateAt { get; set; }

    public int? ProductId { get; set; }

    [ForeignKey("ProductId")]
    [InverseProperty("ProductOptionalFields")]
    public virtual Product? Product { get; set; }
}
