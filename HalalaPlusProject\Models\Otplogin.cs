﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("OTPLogins")]
public partial class Otplogin
{
    [Key]
    public long Id { get; set; }

    [StringLength(50)]
    public string? PhoneNo { get; set; }

    [Column("OTPCode")]
    public int? Otpcode { get; set; }

    public DateOnly? Date { get; set; }

    public int Count { get; set; }

    public long? UserId { get; set; }

    [Column("isPassChanged")]
    public bool IsPassChanged { get; set; }

    [Column("ASPId")]
    [StringLength(450)]
    public string? Aspid { get; set; }

    [Column("otpTime")]
    public TimeOnly? OtpTime { get; set; }

    [Column("otphash")]
    public string? Otphash { get; set; }

    [Column("isVerify")]
    public bool IsVerify { get; set; }

    [Column("isCurrent")]
    public bool IsCurrent { get; set; }

    [Column("purpose")]
    [StringLength(50)]
    public string Purpose { get; set; } = null!;

    [InverseProperty("Otp")]
    public virtual ICollection<ConnectBanksOtpCode> ConnectBanksOtpCodes { get; set; } = new List<ConnectBanksOtpCode>();
}
