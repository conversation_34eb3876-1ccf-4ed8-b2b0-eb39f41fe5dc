﻿

namespace HalalaPlusProject.CustomClasses
{
    public static class HandleImages
    {
        public static void SaveImagebyte(byte[] ImageName, string ImageFolder, IWebHostEnvironment _hosting)
        {
            string? fileName = "";
            if (ImageName !=null)
            {
                Guid guid = Guid.NewGuid();
                
                string img = "";

                img = Path.Combine(_hosting.ContentRootPath, ImageFolder);
                if (!Directory.Exists(img))
                {
                    Directory.CreateDirectory(img);
                }
                fileName = guid.ToString() + ".png";
                string fullPath = Path.Combine(img, fileName);
                using var writer = new BinaryWriter(File.OpenWrite(fullPath));
                writer.Write(ImageName);
            }
            
        }
        public static string SaveImage(IFormFile ImageName, string ImageFolder, IWebHostEnvironment _hosting)
        {
            string? fileName = "";
            if (ImageName != null)
            {
                Guid guid = Guid.NewGuid();
                string extention = Path.GetExtension(ImageName.FileName);
                string img = Path.Combine(_hosting.WebRootPath, ImageFolder);
                if (!Directory.Exists(img))
                {
                    Directory.CreateDirectory(img);
                }
                fileName = guid.ToString() + extention;
                string fullPath = Path.Combine(img, fileName);
                ImageName.CopyTo(new FileStream(fullPath, FileMode.Create));
            }
            return fileName;
        }

        

        public static string SaveImage1(IFormFile ImageName, string ImageFolder, Microsoft.AspNetCore.Hosting.IWebHostEnvironment _hosting)
        {
            string? fileName = "";
            if (true)
            {
                Guid guid = Guid.NewGuid();
                string extention = Path.GetExtension(ImageName.FileName);
                string img = Path.Combine(_hosting.ContentRootPath, ImageFolder);
                if (!Directory.Exists(img))
                {
                    Directory.CreateDirectory(img);
                }
                fileName = guid.ToString() + extention;
                string fullPath = Path.Combine(ImageFolder,fileName );
                ImageName.CopyTo(new FileStream(fullPath, FileMode.Create));
            }
            return fileName;
        }
     
        public static string? ImageToBase64(string imagePath)
        {
            try
            {
                byte[] imageBytes = File.ReadAllBytes(imagePath);
                string base64String = Convert.ToBase64String(imageBytes);

                return base64String;
            }
            catch (Exception ex)
            {                
                Console.WriteLine(ex.Message);
                return null;
            }
        }
        //public static Response saveBase64Image(IWebHostEnvironment _hosting, UploadeImage img ) {

        //    try
        //    {

        //        byte[] imageBytes = Convert.FromBase64String(img.Image);

        //        string fileName = Guid.NewGuid().ToString() + "." + img.type;


        //        string filePath = Path.Combine(_hosting.ContentRootPath, fileName);

        //        System.IO.File.WriteAllBytes(filePath, imageBytes);

        //        return new Response { error = false, message = filePath };
        //    }
        //    catch (Exception ex)
        //    {

        //        return new Response { error =true, message = ex.Message };
        //    }
        //}
      
        
        public static string SaveImageRoot(IFormFile ImageName, string ImageFolder, Microsoft.AspNetCore.Hosting.IWebHostEnvironment _hosting)
        {
            string? fileName = "";
            if (ImageName != null)
            {
                Guid guid = Guid.NewGuid();
                string extention = Path.GetExtension(ImageName.FileName);
                string img = "";

                img = Path.Combine(_hosting.ContentRootPath, ImageFolder);
                if (!Directory.Exists(img))
                {
                    Directory.CreateDirectory(img);
                }
                fileName = guid.ToString() + extention;
                string fullPath = Path.Combine(img, fileName);
                fileName = fullPath;
                ImageName.CopyTo(new FileStream(fullPath, FileMode.Create));
            }
            return fileName;
        }
        public static void RemoveImage(string ImageName, string path)
        {
            var FilePath = Path.Combine(Directory.GetCurrentDirectory(), path, ImageName);
            if (File.Exists(FilePath))
            {
                File.Delete(FilePath);
            }
        }
        public static void RemoveImageRoot( string ImageName, string path, IWebHostEnvironment _hosting)
        {
            try
            {
                var FilePath = Path.Combine(_hosting.WebRootPath, path, ImageName);
                if (File.Exists(FilePath))
                {
                    File.Delete(FilePath);
                }
            }
            catch (Exception)
            {

                throw;
            }
        }
        public static bool RemoveImage(string ImageName, string path, IWebHostEnvironment _hosting)
        {
            var FilePath = Path.Combine(_hosting.WebRootPath, path, ImageName);
            if (File.Exists(FilePath))
            {
                try
                {
                    File.Delete(FilePath);
                    return true;
                }
                catch (Exception)
                {

                    return false;
                }
            }
            return false;
        }
        public static DownloadImage GetImage(string fileName, IWebHostEnvironment _hosting, string folder)
        {
            string filepath = _hosting.ContentRootPath + "/"+folder+"/";
            var path = filepath + fileName + ".png";
            DownloadImage ob = new DownloadImage();
            if (System.IO.File.Exists(path))
            {
                byte[] b = System.IO.File.ReadAllBytes(path);
                ob.file = b;
                
            }
            return ob;

        }
        public static DownloadImage GetImageRoot(string fileName,IWebHostEnvironment _hosting, string folder)
        {
            string filepath = _hosting.WebRootPath + "\\" + folder + "\\";
            var path = filepath + fileName + ".png";
            DownloadImage ob = new DownloadImage();
            if (System.IO.File.Exists(path))
            {
                byte[] b = System.IO.File.ReadAllBytes(path);
                ob.file = b;
            
            }
            return ob;
        }

    }

    public  class UploadImage
    {
        public IFormFile? file { get; set; } 
    }
    public  class DownloadImage
    {
        public  byte[]? file { get; set; }
    }
}
