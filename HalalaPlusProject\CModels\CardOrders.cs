﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل تفاصيل طلب بطاقة العميل.
    /// </summary>
    public class CardOrders
    {
        /// <summary>
        /// معرف الطلب.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// اسم العميل.
        /// </summary>
        //[Display(Name = "اسم العميل")]
        public string? Name { get; set; }

        /// <summary>
        /// رقم جوال العميل.
        /// </summary>
        //[Display(Name = "رقم العميل")]
        public string? PhoneNo { get; set; }

        /// <summary>
        /// رقم العضوية الخاص بالعميل.
        /// </summary>
        //[Display(Name = "رقم العضوية")]
        public string? MemberNo { get; set; }

        /// <summary>
        /// تاريخ انضمام العميل.
        /// </summary>
        //[Display(Name = "تاريخ التسجيل")]
        public string? JoinDate { get; set; }
    }

    /// <summary>
    /// يمثل إعادة إرسال طلب البطاقة.
    /// </summary>
    public class CardOrdersResend
    {
        /// <summary>
        /// معرف الطلب المعاد إرساله.
        /// </summary>
        public string Id { get; set; }
    } /// <summary>
      /// يمثل إعادة إرسال ؤسالة الترحيب للموظغين.
      /// </summary>
    public class EmpResend
    {
        /// <summary>
        /// معرف الطلب المعاد إرساله.
        /// </summary>
        public long Id { get; set; }
    }
}
