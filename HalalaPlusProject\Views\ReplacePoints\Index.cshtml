﻿@model IEnumerable<HalalaPlusProject.CModels.ReplaceOrderModel>
    @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
	ViewData["Title"] = localizer["pointsreplaceorders"];
	Layout = "~/Views/Shared/_Layout.cshtml";
}

<h4>@localizer["pointsreplaceorders"]</h4>


<table id="tbl1" class="table">
	<thead>
		<tr>
			<th> @localizer["no"]</th>
			<th> @localizer["customername"]</th>
			<th> @localizer["PhoneNo"]</th>
			<th> @localizer["pointsno"]</th>
			<th> @localizer["amountdue"]</th>
			<th> @localizer["proce"]</th>

		</tr>

	</thead>
	<tbody>
		@foreach (var item in Model)
		{
			<tr>
				<td>
					@Html.DisplayFor(modelItem => item.Id)
				</td>
				<td>
					@Html.DisplayFor(modelItem => item.CustomerName)
				</td>
				<td>
					@Html.DisplayFor(modelItem => item.PhoneNo)
				</td>
				<td>
					@Html.DisplayFor(modelItem => item.PointsNo)
				</td>
				<td>
					@Html.DisplayFor(modelItem => item.Amount)
				</td>
				<td>
					<a onclick="ReplaceOrder(@item.Id,this,1)" class="tablebtn"><img style="width:35px" src="/img/true.png" class="" /></a> |
					<a onclick="ReplaceOrder(@item.Id,this,2)" class="tablebtn"><img style="width:35px" src="/img/x.png" class="" /></a>
				</td>

			</tr>
		}
	</tbody>
</table>
@section Scripts {
	<script>
		let table = new DataTable('#tbl1');

	</script>
}