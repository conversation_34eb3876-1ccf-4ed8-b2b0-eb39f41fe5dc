﻿using HalalaPlusProject.Areas.Identity.Data;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    public class IndexController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        public IndexController(HalalaPlusdbContext context)
        {
            _context = context;
           
            //_emailStore = emailStore;
        }
        public IActionResult Index()
        {
           
            return View();
        }
    }
}
