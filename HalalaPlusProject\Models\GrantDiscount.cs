﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("GrantDiscount")]
public partial class GrantDiscount
{
    [Key]
    public long Id { get; set; }

    public long? UserId { get; set; }

    public int? ProductId { get; set; }

    public double? Amount { get; set; }

    public double? Discount { get; set; }

    public double? Benfit { get; set; }

    [StringLength(500)]
    public string? Service { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("GrantDiscounts")]
    public virtual SystemUser? User { get; set; }
}
