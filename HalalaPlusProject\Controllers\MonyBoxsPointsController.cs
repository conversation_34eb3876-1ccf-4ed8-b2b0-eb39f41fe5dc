﻿using HalalaPlusProject.Models;
using HalalaPlusProject.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    /// <summary>
    /// إدارة قواعد منح النقاط بناءً على المبالغ في الحصالات (صناديق الأموال).
    /// </summary>
    public class MonyBoxsPointsController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IFirebaseNotificationHistoryService fire_notification;

        /// <summary>
        /// إنشاء نسخة جديدة من وحدة التحكم مع حقن الخدمات المطلوبة.
        /// </summary>
        /// <param name="firenotification">خدمة إرسال الإشعارات عبر Firebase.</param>
        /// <param name="context">سياق قاعدة البيانات.</param>
        public MonyBoxsPointsController(IFirebaseNotificationHistoryService firenotification, HalalaPlusdbContext context)
        {
            _context = context;
            fire_notification = firenotification;
        }

        // GET: MonyBoxsPoints
        /// <summary>
        /// عرض قائمة بجميع قواعد منح نقاط الحصالات.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة بقواعد النقاط.</returns>
        public async Task<IActionResult> Index()
        {
            return _context.MonyBoxsPoints != null ?
                        View(await _context.MonyBoxsPoints.ToListAsync()) :
                        Problem("Entity set 'HalalaPlusdbContext.MonyBoxsPoints'  is null.");
        }

        // GET: MonyBoxsPoints/Details/5
        /// <summary>
        /// عرض التفاصيل الخاصة بقاعدة نقاط معينة.
        /// </summary>
        /// <param name="id">معرف قاعدة النقاط المراد عرض تفاصيلها.</param>
        /// <returns>عرض يحتوي على تفاصيل قاعدة النقاط، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null || _context.MonyBoxsPoints == null)
            {
                return NotFound();
            }

            var monyBoxsPoint = await _context.MonyBoxsPoints
                .FirstOrDefaultAsync(m => m.Id == id);
            if (monyBoxsPoint == null)
            {
                return NotFound();
            }

            return View(monyBoxsPoint);
        }

        // GET: MonyBoxsPoints/Create
        /// <summary>
        /// عرض نموذج إنشاء قاعدة نقاط جديدة.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج الإنشاء.</returns>
        public IActionResult Create()
        {
            return View();
        }

        // POST: MonyBoxsPoints/Create
        /// <summary>
        /// معالجة عملية إنشاء قاعدة نقاط جديدة وحفظها في قاعدة البيانات، ثم إرسال إشعار.
        /// </summary>
        /// <param name="monyBoxsPoint">كائن قاعدة النقاط الذي يحتوي على البيانات الجديدة.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة القواعد؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,FromAmount,ToAmount,GrntedPoints,CreateAt,Deleted")] MonyBoxsPoint monyBoxsPoint)
        {
            if (ModelState.IsValid)
            {
                _context.Add(monyBoxsPoint);
                if (await _context.SaveChangesAsync() == 1)
                {
                    try
                    {
                        await this.fire_notification.SendNotification("New Points:" + monyBoxsPoint.Id, monyBoxsPoint.GrntedPoints.ToString(), null, "News");
                    }
                    catch (Exception)
                    {
                        //throw;
                    }

                    return RedirectToAction(nameof(Index));
                }
            }
            return View(monyBoxsPoint);
        }

        // GET: MonyBoxsPoints/Edit/5
        /// <summary>
        /// عرض نموذج تعديل بيانات قاعدة نقاط حالية.
        /// </summary>
        /// <param name="id">معرف قاعدة النقاط المراد تعديلها.</param>
        /// <returns>عرض يحتوي على بيانات القاعدة في نموذج التعديل، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null || _context.MonyBoxsPoints == null)
            {
                return NotFound();
            }

            var monyBoxsPoint = await _context.MonyBoxsPoints.FindAsync(id);
            if (monyBoxsPoint == null)
            {
                return NotFound();
            }
            return View(monyBoxsPoint);
        }

        // POST: MonyBoxsPoints/Edit/5
        /// <summary>
        /// معالجة التعديلات المقدمة لقاعدة نقاط وحفظها في قاعدة البيانات.
        /// </summary>
        /// <param name="id">معرف قاعدة النقاط التي يتم تعديلها.</param>
        /// <param name="monyBoxsPoint">كائن قاعدة النقاط الذي يحتوي على البيانات المحدثة.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة القواعد؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,FromAmount,ToAmount,GrntedPoints,CreateAt,Deleted")] MonyBoxsPoint monyBoxsPoint)
        {
            if (id != monyBoxsPoint.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(monyBoxsPoint);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!MonyBoxsPointExists(monyBoxsPoint.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(monyBoxsPoint);
        }

        // GET: MonyBoxsPoints/Delete/5
        /// <summary>
        /// عرض صفحة تأكيد حذف قاعدة نقاط.
        /// </summary>
        /// <param name="id">معرف قاعدة النقاط المراد حذفها.</param>
        /// <returns>عرض يحتوي على تفاصيل القاعدة لتأكيد الحذف، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null || _context.MonyBoxsPoints == null)
            {
                return NotFound();
            }

            var monyBoxsPoint = await _context.MonyBoxsPoints
                .FirstOrDefaultAsync(m => m.Id == id);
            if (monyBoxsPoint == null)
            {
                return NotFound();
            }

            return View(monyBoxsPoint);
        }

        // POST: MonyBoxsPoints/Delete/5
        /// <summary>
        /// تنفيذ عملية حذف منطقي لقاعدة النقاط (بتعيين `Deleted` إلى `true`) بعد التأكيد.
        /// </summary>
        /// <param name="id">معرف قاعدة النقاط المراد حذفها.</param>
        /// <returns>بعد إتمام العملية بنجاح، يتم عرض صفحة قائمة القواعد.</returns>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            if (_context.MonyBoxsPoints == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.MonyBoxsPoints'  is null.");
            }
            var monyBoxsPoint = await _context.MonyBoxsPoints.FindAsync(id);
            if (monyBoxsPoint != null)
            {
                monyBoxsPoint.Deleted = true;
                _context.MonyBoxsPoints.Update(monyBoxsPoint);
                //_context.MonyBoxsPoints.Remove(monyBoxsPoint);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// التحقق من وجود قاعدة نقاط في قاعدة البيانات بالمعرف المحدد.
        /// </summary>
        /// <param name="id">معرف قاعدة النقاط للتحقق منه.</param>
        /// <returns>إرجاع 'true' إذا كانت القاعدة موجودة، وإلا 'false'.</returns>
        private bool MonyBoxsPointExists(int id)
        {
            return (_context.MonyBoxsPoints?.Any(e => e.Id == id)).GetValueOrDefault();
        }
    }
}