﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Models;

namespace HalalaPlusProject.CustomClasses
{
    public class SpecialOffersClass
    {
        public List<SpecialOffers> retriveDeletedorUnactive(HalalaPlusdbContext _context,bool isActive=true ,bool isDeleated=true)
        {
            try
            {
                return (from ob in _context.OffersAndCopuns
                        where                         
                       ob.RecType=="offer"
                       &&((ob.IsActive == false&&( ob.Deleted==false||ob.Deleted==true))||((ob.Deleted == true)&&(ob.IsActive == false || ob.IsActive == true)))

                        select new CModels.SpecialOffers
                        {
                            OfferName = ob.Name,
                            StartDate = ob.StartDate,
                            Id = ob.Id,
                            ProviderName = ob.StoreName,
                            OverView = ob.OverView,
                            EnOverView = ob.EnoverView,
                            EnProviderName = ob.EnStoreName,
                            ActivityName = (ob.Activity != null) ? _context.Activities.FirstOrDefault(p => p.Id == ob.Activity).Name : "",
                            ActivityNo = (ob.Activity != null) ? ob.Activity : 0,
                            Discount = (ob.Discount != null) ? ob.Discount : 0,
                            EndDate = ob.EndDate,
                            EnOfferName = ob.EnName,
                            state = (ob.Deleted == true)?"محذوف": (ob.IsActive==true)?"نشط":"موقف",
                            Details = ob.Details,
                            imgLink ="/images/"+ ob.Img,
                        }).OrderByDescending(p => p.Id).ToList();
            }
            catch (Exception)
            {
                return new List<SpecialOffers>();
            }
        }
        public List<SpecialOffers> retrive(HalalaPlusdbContext _context, bool isActive = true, bool isDeleated = true)
        {
            try
            {
                return (from ob in _context.OffersAndCopuns
                        where
                       ob.RecType == "offer"
                       &&ob.IsActive == isActive    
                       &&ob.Deleted==isDeleated
                        select new CModels.SpecialOffers
                        {
                            OfferName = ob.Name,
                            StartDate = ob.StartDate,
                            Id = ob.Id,
                            ActivityName = (ob.Activity != null) ? _context.Activities.FirstOrDefault(p => p.Id == ob.Activity).Name : "",
                            ActivityNo = (ob.Activity != null) ? ob.Activity : 0,
                            Discount = (ob.Discount != null) ? ob.Discount : 0,
                            EndDate = ob.EndDate,
                            ProviderName = ob.StoreName,
                            OverView = ob.OverView,
                            EnOverView = ob.EnoverView,
                            EnProviderName = ob.EnStoreName,
                            EnOfferName = ob.EnName,
                            EnDetails = ob.EnDetails,
                            state = (ob.Deleted == true) ? "محذوف" : (ob.IsActive == true) ? "نشط" : "موقف",
                            Details = ob.Details,
                            imgLink = "/images/" + ob.Img,
                        }).OrderByDescending(p => p.Id).ToList();
            }
            catch (Exception)
            {
                return new List<SpecialOffers>();
            }
        }
        public SpecialOffers retriveOffer(HalalaPlusdbContext _context,long id)
        {
            try
            {
                return _context.OffersAndCopuns.Where(o => o.Id == id).Select(ob => new
                          CModels.SpecialOffers
                {
                    OfferName = ob.Name,
                    StartDate = ob.StartDate,
                    Id = ob.Id,
                    EnOfferName = ob.EnName,
                    EnDetails = ob.EnDetails,
                    EndDate = ob.EndDate,
                    Provider = ob.MasterId ?? 0,
                    ProviderName = ob.StoreName,
                    OverView = ob.OverView,
                    EnOverView=ob.EnoverView,
                    EnProviderName = ob.EnStoreName,
                    state = (ob.IsActive == true) ? "نشط" : "موقف",
                    Details = ob.Details,
                    ActivityName = (ob.Activity != null) ? _context.Activities.FirstOrDefault(p => p.Id == ob.Activity).Name : "",
                    ActivityNo = (ob.Activity != null) ? ob.Activity : 0,
                    Discount = (ob.Discount != null) ? ob.Discount : 0,
                    imgLink = "/images/" + ob.Img,
                }).FirstOrDefault();
            }
            catch (Exception)
            {
                return new SpecialOffers();
            }
        }


        public async Task<bool> disable(HalalaPlusdbContext _context, long id, bool activate)
        {
            try
            {
                var temp = _context.OffersAndCopuns.Find(id);
                if(temp==null) return false;
                temp.IsActive = activate;
                _context.Update(temp);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
        public async Task<bool> delete(HalalaPlusdbContext _context, long id)
        {
            try
            {
                var temp = _context.OffersAndCopuns.Find(id);
                if(temp==null) return false;
                temp.Deleted = true;
                _context.Update(temp);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
        public async Task<bool> insert(HalalaPlusdbContext _context, CModels.SpecialOffers model,long id)
        {
            try
            {
                var temp =new  Models.OffersAndCopun();
                temp.Name = model.OfferName;
                temp.EndDate = model.EndDate;
                temp.StartDate = DateTime.UtcNow;// model.StartDate;
                temp.Details = model.Details;
                temp.IsActive = true;
                temp.OverView = model.OverView;
                temp.EnoverView = model.EnOverView;

                temp.StoreName = model.ProviderName;
                temp.EnStoreName = model.EnProviderName;
                temp.MasterId = model.Provider;
                temp.EnDetails = model.EnDetails;
                temp.EnName = model.EnOfferName;
                temp.RecType = "offer";
                temp.Activity = model.ActivityNo;
                temp.Discount = model.Discount;
                temp.Img = model.imgLink;
                _context.Add(temp);
               await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
            
        }
        public async Task<bool> Edit(HalalaPlusdbContext _context, CModels.SpecialOffers model, IWebHostEnvironment _hosting)
        {
            try
            {
                var temp = _context.OffersAndCopuns.Find(model.Id);
                if (model.Img != null)
                {
                    if (temp.Img != null)
                        HandleImages.RemoveImageRoot(temp.Img, "Images", _hosting);
                    model.imgLink = HandleImages.SaveImage(model.Img, "Images", _hosting);
                    temp.Img = model.imgLink;
                }

                temp.Name = model.OfferName;
                temp.EndDate = model.EndDate;
                temp.StartDate = model.StartDate;
                temp.Activity = model.ActivityNo;
                temp.Discount = model.Discount;
                temp.StoreName = model.ProviderName;
                temp.EnStoreName = model.EnProviderName;
                temp.Details = model.Details;
                temp.OverView=model.OverView;
                temp.EnoverView=model.EnOverView;

                temp.MasterId = model.Provider;
                temp.EnDetails = model.EnDetails;
                temp.EnName = model.EnOfferName;

                _context.Update(temp);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }

        }
    }
}
