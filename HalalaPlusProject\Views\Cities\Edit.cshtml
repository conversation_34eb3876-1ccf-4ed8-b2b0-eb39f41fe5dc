﻿@model HalalaPlusProject.Models.CitiesTable
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["edit"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["edit"]</h2>

<h4>@localizer["thecity"]</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <div class="form-group">
                <label asp-for="City" class="control-label">@localizer["thecity"]</label>
                <input asp-for="City" class="form-control" />
                <span asp-validation-for="City" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="EnCity" class="control-label">@localizer["thecity"]</label>
                <input asp-for="EnCity" class="form-control" />
                <span asp-validation-for="EnCity" class="text-danger"></span>
            </div>
           
             <div class="form-group">
                <label asp-for="CId" class="control-label">@localizer["thecountry"]</label>
                <select asp-for="CId" class="form-select" asp-items="ViewBag.CId"></select>
                <span asp-validation-for="CId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <button type="submit" value="Save" class="btn btn-primary">@localizer["save"]</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
