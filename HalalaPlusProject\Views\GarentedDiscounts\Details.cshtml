﻿@model IEnumerable<HalalaPlusProject.CModels.CustomerGrantedDiscountsViewModel>

@{
    ViewData["Title"] = "Customer Mony Box Transactions";
}

<h1>العملاء وسجلات النقاط</h1>

@foreach (var customer in Model)
{

@*     <h2>CustomerId: (@customer.CustomerId)</h2>
    <p>CustomerName: (@customer.CustomerName)</p>
    <p>Email: (@customer.CustomerEmail)</p> *@

    <ul style="display: flex; flex-wrap: wrap; gap: 20px; justify-content: center; padding: 20px;">
        <li style="padding: 10px; text-align: center;">
            <span class="title-en" style="display:inline">  اسم الحصالة </span>
            <span class="value" style="display:inline">{@customer.MonyBoxName}</span>
            <span class="title-ar" style="display:inline"> MonyBox Name </span>
        </li>
        <li style="padding: 10px; text-align: center;">
            <span class="title-en" style="display:inline">  رقم العميل </span>
            <span class="value" style="display:inline">{@customer.CustomerId}</span>
            <span class="title-ar" style="display:inline"> Customer Id </span>
        </li>
        <li style="padding: 10px; text-align: center;">
            <span class="title-en" style="display:inline">  اسم العميل </span>
            <span class="value" style="display:inline">{@customer.CustomerName}</span>
            <span class="title-ar" style="display:inline"> Customer Name </span>
        </li>
    </ul>

    @foreach (var monyBox in customer.GrantedDiscountUsers)
    {
        <div style="border: 1px solid #ddd; padding: 10px; margin-bottom: 10px;color:cornflowerblue">

            <ul  style="display: flex; flex-wrap: wrap; gap: 20px; justify-content: center; padding: 20px;color:cadetblue;">
                @* <li style="padding: 10px; text-align: center;"><strong>Customer Id:</strong> @monyBox.UserId</li> *@
                @* <li style="padding: 10px; text-align: center;"><strong>Mony Box Name:</strong> @monyBox.Name</li> *@
                <li style="padding: 10px; text-align: center;">
                    <span class="title-en" style="display:inline">  السجل  </span>
                    <span class="value" style="display:inline">{@monyBox.Id}</span>
                    <span class="title-ar" style="display:inline">  The record </span>
                </li>
                @* <li style="padding: 10px; text-align: center;"><strong>End Date:</strong> @monyBox.EndDate?.ToString("yyyy-MM-dd")</li> *@
                <li style="padding: 10px; text-align: center;">
                    <span class="title-en" style="display:inline"> التاريخ  </span>
                    <span class="value" style="display:inline">{@monyBox.GrantDate?.ToString("yyyy-MM-dd")}</span>
                    <span class="title-ar" style="display:inline"> The Date </span>
                </li>
                @* <li style="padding: 10px; text-align: center;"><strong>Target:</strong> @monyBox.Target</li> *@
                <li style="padding: 10px; text-align: center;">
                    <span class="title-en" style="display:inline">  النقاط </span>
                    <span class="value" style="display:inline">{@monyBox.Points}</span>
                    <span class="title-ar" style="display:inline"> Points </span>
                </li>
                @* <li style="padding: 10px; text-align: center;"><strong>Amount:</strong> @monyBox.Amount</li> *@
                <li style="padding: 10px; text-align: center;">
                    <span class="title-en" style="display:inline"> المبلغ </span>
                    <span class="value" style="display:inline">{@monyBox.Amount?.ToString("N2")}</span>
                    <span class="title-ar" style="display:inline"> Amounts </span>
                </li>

                @* <li style="padding: 10px; text-align: center;"><strong>Start Date:</strong> @monyBox.StartDate?.ToString("yyyy-MM-dd")</li> *@
                <li style="padding: 10px; text-align: center;">
                    <span class="title-en" style="display:inline"> الحالة </span>
                    <span class="value" style="display:inline">{@monyBox.OrderState}</span>
                    <span class="title-ar" style="display:inline"> State </span>
                </li>
            </ul>

@*             <ul class="statistics-list" style="color:cadetblue">
                @foreach (var transaction in monyBox.MonyBoxTransactions)
                {<div style="border:dotted">
                    <li>
                        <span class="title-en">رقم العملية </span>
                        <span class="value">@transaction.Id</span>
                        <span class="title-ar"> Transaction Id </span>
                    </li>
                    <li>
                        <span class="title-en"> دائن </span>
                        <span class="value">@transaction.Credit</span>
                        <span class="title-ar"> Credit </span>
                    </li>
                    <li>
                        <span class="title-en"> مدين </span>
                        <span class="value">@transaction.Debit</span>
                        <span class="title-ar"> Debit </span>
                    </li>
                    <li>
                        <span class="title-en">تاريخ العملية </span>
                        <span class="value">@transaction.OperationDate.Value.ToString("yyyy-MM-dd")</span>
                        <span class="title-ar"> Operation Date </span>
                    </li>
                    <li>
                        <span class="title-en"> تم التحقق </span>
                        <span class="value">@(transaction.IsVerified ? "Yes" : "No")</span>
                        <span class="title-ar"> Is Verified </span>
                    </li>
            </div>
                }

            </ul> *@
        </div>
    }
}

<div>
    <a asp-action="Index">الرجوع الى القائمة</a>
</div>


<style>
    .statistics-list {
        list-style-type: none;
        padding: 0;
    }

        .statistics-list li {       
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }

    .title-en {
        text-align: left;
        flex: 1;
        direction: ltr;
    }

    .value {
        text-align: center;
        flex: 1;
    }

    .title-ar {
        text-align: right;
        flex: 1;
        direction: rtl;
    }

    body {
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
        color: #333;
    }

    /*          .container {
                 max-width: 800px;
                 margin: 0 auto;
                 padding: 20px;
             } */

    h1 {
        text-align: center;
        margin-bottom: 20px;
    }

    /*         .card {
                background-color: #fff;
                border-radius: 10px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                margin-bottom: 20px;
                padding: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            } */

    .card span {
        display: block;
    }

    .title-ar {
        text-align: right;
        direction: rtl;
        font-weight: bold;
        flex: 1;
    }

    .value {
        text-align: center;
        flex: 1;
        font-size: 1.5em;
        font-weight: bold;
    }

    .title-en {
        text-align: left;
        direction: ltr;
        font-weight: bold;
        flex: 1;
    }

</style>