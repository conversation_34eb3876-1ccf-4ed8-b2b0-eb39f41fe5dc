﻿@model HalalaPlusProject.Models.CountriesTable

@{
    ViewData["Title"] = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>التفاصيل</h1>

<div>
    <h4>الدول</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
           الدولة
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Country)
        </dd>
        <dt class = "col-sm-2">
           الجنسية
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Nationality)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id">تعديل</a> |
    <a asp-action="Index">عودة للقائمة السابقة</a>
</div>
