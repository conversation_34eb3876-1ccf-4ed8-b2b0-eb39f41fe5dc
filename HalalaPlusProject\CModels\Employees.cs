﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل بيانات الموظف المرتبطة بالمستخدم.
    /// </summary>
    using System;
    using System.ComponentModel.DataAnnotations;

    public class UserEmployeeModel
    {
        /// <summary>
        /// معرف الموظف.
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// اسم الموظف.
        /// </summary>
        [Required(ErrorMessage = "الاسم مطلوب")]
        [StringLength(50, ErrorMessage = "الاسم يجب ألا يتجاوز 50 حرف")]
        public string? Name { get; set; }

        /// <summary>
        /// رقم الجنسية.
        /// </summary>
        [Required(ErrorMessage = "الجنسية مطلوبة")]
        public int? Nationality { get; set; }

        /// <summary>
        /// رقم الدولة.
        /// </summary>
        public int? CountryNo { get; set; }

        /// <summary>
        /// رقم الهوية.
        /// </summary>
        [Required(ErrorMessage = "رقم الهوية مطلوب")] 
        public string? IdentityNo { get; set; }

        /// <summary>
        /// تاريخ الميلاد.
        /// </summary>
        [Required(ErrorMessage = "تاريخ الميلاد مطلوب")]
        [DataType(DataType.Date, ErrorMessage = "تاريخ الميلاد غير صالح")]
        public DateOnly? BirthDate { get; set; }

        /// <summary>
        /// رقم الهاتف.
        /// </summary>
        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        [Phone(ErrorMessage = "رقم الهاتف غير صالح")]
        public string? PhoneNo { get; set; }


        /// <summary>
        /// البريد الإلكتروني.
        /// </summary>
        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "يجب إدخال بريد إلكتروني صالح")]
        [StringLength(50, ErrorMessage = "البريد الإلكتروني يجب ألا يتجاوز 50 حرف")]
        public string? Email { get; set; }

        /// <summary>
        /// اسم المستخدم.
        /// </summary>
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(50, ErrorMessage = "اسم المستخدم يجب ألا يتجاوز 50 حرف")]
        public string? UserName { get; set; }

        /// <summary>
        /// الراتب.
        /// </summary>
        [Required(ErrorMessage = "الراتب مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "الراتب يجب أن يكون قيمة موجبة")]
        public double? Salary { get; set; }
    }


    /// <summary>
    /// يمثل بيانات الموظف المضافة مع معلومات تسجيل الدخول.
    /// </summary>
    public class EmployeesModel : UserEmployeeModel
    {
        /// <summary>
        /// كلمة المرور الخاصة بالموظف.
        /// </summary>
   
        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
     
      
        ////[Display(Name = "كلمة المرور")]
        public string? Password { get; set; }
    }

    /// <summary>
    /// يمثل بيانات الموظف في النظام التجاري.
    /// </summary>
    public class BusineesEmployeesModel
    {
        /// <summary>
        /// معرف الموظف.
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// اسم الموظف.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// رقم جوال الموظف.
        /// </summary>
        public string PhoneNo { get; set; }
    }
}
