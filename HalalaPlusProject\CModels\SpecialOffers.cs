﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    public class SpecialOffers
    {
        //[Required]
        //[Display(Name = "رقم العرض")]
        public long? Id { get; set; }
        [Required]
        //[Display(Name = "اسم العرض")]
        public string? OfferName { get; set; }
        public string? EnOfferName { get; set; }
        [Required]
        //[Display(Name = "بداية العرض")]
        public DateTime? StartDate { get; set; }
        [Required]
        //[Display(Name = "نهاية العرض ")]
        public DateTime? EndDate { get; set; }
        //[Required]
        //[Display(Name = "صورة العرض")]
        public IFormFile? Img { get; set; }
        //[Required]
        //[Display(Name = "تفاصيل العرض")]
        public string? Details { get; set; }
       
        //[Display(Name = "تفاصيل العرض")]
        public long? Provider { get; set; }
        
        public string? SysProvider { get; set; }
        public string? ProviderName { get; set; }
        public string? EnProviderName { get; set; }
        public string? EnDetails { get; set; }
        //[Display(Name = " النشاط")]
        public int? ActivityNo { get; set; }
        //[Display(Name = "الخصم")]
        public double? Discount { get; set; }
        //[Display(Name = " النشاط")]
        public string? ActivityName { get; set; }
        public string? state { get; set; }
        public string? imgLink { get; set; }
        public string? OverView { get; set; }
        public string? EnOverView { get; set; }

    }
    public class RemoveModel
    {
        public long? id { get; set; }
    }
    public class CoupunModel
    {
        //[Required]
        //[Display(Name = "رقم الكود")]
        public long? Id { get; set; }
        [Required]
        //[Display(Name = "اسم المتجر")]
        public string? Name { get; set; } 
        [Required]
        //[Display(Name = "اسم المتجر")]
        public string? EnName { get; set; }
        [Required]
        //[Display(Name = "رابط المتجر")]
        public string? StoreLink { get; set; }
        [Required]
        //[Display(Name = "كود الخصم ")]
        public string? CoupunCode { get; set; }
        //[Required]
        //[Display(Name = "تفاصيل العرض")]
        public long? Provider { get; set; }
        public string? SysProvider { get; set; }
        public string? ProviderName { get; set; }
        public string? EnProviderName { get; set; }
        //[Display(Name = " النشاط")]
        public int? ActivityNo { get; set; }
        //[Display(Name = "الخصم")]
        [Required]
        public double? Discount { get; set; }
        //[Display(Name = " النشاط")]
        public string? ActivityName { get; set; }
        //[Required]
        //[Display(Name = "صورة المتجر")]
        public IFormFile? Img { get; set; }
        [Required]
        //[Display(Name = "بداية الكود")]
        public DateTime? StartDate { get; set; }
        [Required]
        //[Display(Name = "نهاية الكود ")]
        public DateTime? EndDate { get; set; }
        public string? Details { get; set; }
        public string? EnDetails { get; set; }

        //[Display(Name = "نبذة عن المتجر ")]
        public string? OverView { get; set; }
        public string? EnOverView { get; set; }
        public string? state { get; set; }
        public string? imgLink { get; set; }


    }
    public class SpecialOffersIndex
    {
       
        public List<SpecialOffers>? SpecialOffers { get; set; }
        public List<SpecialOffers>? StoppedOffers { get; set; }


    } 
    public class CoupunModelIndex
    {
       
        public List<CoupunModel>? ActiveCopuns { get; set; }
        public List<CoupunModel>? stoppedCopuns { get; set; }


    }

}
