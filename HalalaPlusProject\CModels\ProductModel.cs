﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل نموذج إدخال منتج جديد أو تعديل منتج موجود، مع دعم للغات متعددة وصورة.
    /// </summary>
    public class ProductModel : Models.Product
    {
        /// <summary>
        /// اسم المنتج باللغة العربية.
        /// </summary>
        [Required]
        public string? Name { get; set; }

        /// <summary>
        /// وصف المنتج باللغة العربية.
        /// </summary>
        //[Required]
        public string? Description { get; set; }

        /// <summary>
        /// اسم المنتج باللغة الإنجليزية.
        /// </summary>
        public string? Engname { get; set; }

        /// <summary>
        /// وصف المنتج باللغة الإنجليزية.
        /// </summary>
        public string? Engdescription { get; set; }

        /// <summary>
        /// معرف الفئة التي ينتمي إليها المنتج.
        /// </summary>
        [Required]
        public int? Catagory { get; set; }

        /// <summary>
        /// سعر المنتج.
        /// </summary>
        public string? Price { get; set; }
        /// <summary>
        /// سعر المنتج.
        /// </summary>
        
        public string? SellPrice { get; set; }
        /// <summary>
        /// تفاصيل عن المنتج.
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// ملف الصورة المرفوعة للمنتج.
        /// </summary>
        public IFormFile? file { get; set; }
    }
}
