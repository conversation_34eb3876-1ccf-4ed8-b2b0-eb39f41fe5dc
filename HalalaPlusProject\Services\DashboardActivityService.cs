﻿using HalalaPlusProject.Models;
using Microsoft.EntityFrameworkCore;
using System;

namespace HalalaPlusProject.Services
{
    public class DashboardActivityService : IDashboardActivityService
    {
        private readonly HalalaPlusdbContext _context;
        private readonly TimeZoneInfo _timeZone;
        public DashboardActivityService(HalalaPlusdbContext context, TimeZoneInfo timeZone)
        {
            _context = context;
            _timeZone = timeZone;
        }

        public double GetActivityTotalDiscountsByMonth(int month)
        {
            // Get the current year
            int currentYear = DateTime.Today.Year;

            // Create the start and end dates for the specified month
            DateTime startDate = new DateTime(currentYear, month, 1);
            DateTime endDate = startDate.AddMonths(1).AddDays(-1);

            // Calculate the total discounts for the specified month
            var totalDiscounts = _context.DiscountsTables
                                         .Where(op => op.Orderdate >= startDate && op.Orderdate <= endDate)
                                         .Sum(op => (double?)op.Discount) ?? 0.0;

            return totalDiscounts;
        }

        public double GetActivityTotalDiscounts(int select)
        {
            
            // Calculate the date based on the select parameter
            DateTime targetDate = DateTime.Today.AddDays(-select);

            // Calculate the total discounts for the target date
            var totalDiscounts = _context.DiscountsTables.Where(op => op.Orderdate.Date == targetDate.Date).Sum(op => op.Discount);
            // Return the result
            return (totalDiscounts != null && totalDiscounts.Value > 0) ? totalDiscounts.Value : 00;
        }

        //public int GetActivityCount(int select)
        //{
        //    // Calculate the date based on the select parameter
        //    DateTime targetDate = DateTime.Today.AddDays(-select);

        //    // Return the count of anonymous user activities for the target date
        //    return _context.AnonymousUserActivities
        //                   .Where(op => op.LoginTime.Date == targetDate.Date)
        //                   .Count();
        //}
        public int GetActivityCount(int select)
        {
            // Calculate the UTC date based on the select parameter
            var targetDateUtc = DateTime.UtcNow.AddDays(-select);

            // Convert UTC date to the Arabian Standard Time time zone
            //var timeZone = TimeZoneInfo.FindSystemTimeZoneById("Arabian Standard Time");
            var targetDate = TimeZoneInfo.ConvertTimeFromUtc(targetDateUtc, _timeZone);

            // Return the count of anonymous user activities for the target date
            var count = _context.AnonymousUserActivities
                                .Where(op => op.LoginTime.Date == targetDate.Date)
                                .Count();
            return count;
        }


        public int GetActivityCountForDay(int select)
        {
            // Calculate the date based on the select parameter
            DateTime targetDate = DateTime.Today.AddDays(1 - select);
            // Return the count of anonymous user activities for the target day
            return _context.AnonymousUserActivities
                           .Where(op => op.LoginTime.Date == targetDate.Date)
                           .Count();
        }

        //public int GetActivityCountForMonth(int month)
        //{
        //    int currentYear = DateTime.Today.Year;
        //    DateTime startDate = new DateTime(currentYear, month, 1);
        //    DateTime endDate = startDate.AddMonths(1).AddDays(-1);
        //    // Return the count of anonymous user activities for the target month
        //    return _context.AnonymousUserActivities
        //                   .Where(op => op.LoginTime >= startDate && op.LoginTime <= endDate)
        //                   .Count();
        //}
        public int GetActivityCountForMonth(int month)
        {
            // Get the current year
            int currentYear = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, _timeZone).Year;

            // Calculate the start and end dates for the specified month in the target time zone
            DateTime startDate = new DateTime(currentYear, month, 1);
            DateTime endDate = startDate.AddMonths(1).AddDays(-1);

            // Convert start and end dates to UTC
            startDate = TimeZoneInfo.ConvertTimeToUtc(startDate, _timeZone);
            endDate = TimeZoneInfo.ConvertTimeToUtc(endDate, _timeZone);

            // Return the count of anonymous user activities for the target month
            return _context.AnonymousUserActivities
                           .Where(op => op.LoginTime >= startDate && op.LoginTime <= endDate)
                           .Count();
        }

        //public List<string> GetDateOfLast7Days()
        //{
        //    // Create a list to hold the dates of the last 7 days
        //    List<string> last7Days = new List<string>();

        //    // Iterate through the past 7 days
        //    for (int i = 7; i > 0; i--)
        //    {
        //        // Add the formatted date (MM-dd) to the list for each day
        //        last7Days.Add(DateTime.Today.AddDays(-i).ToString("MM-dd"));
        //    }

        //    // Return the list of dates
        //    return last7Days;
        //}
        public List<string> GetDateOfLast7Days()
        {
            // Create a list to hold the dates of the last 7 days
            List<string> last7Days = new List<string>();

            // Get the current date in the specified time zone
            var currentDate = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, _timeZone);

            // Iterate through the past 7 days
            for (int i = 7; i > 0; i--)
            {
                // Calculate the date in the specified time zone
                var dateInTargetZone = currentDate.AddDays(-i);

                // Add the formatted date (MM-dd) to the list for each day
                last7Days.Add(dateInTargetZone.ToString("MM-dd"));
            }

            // Return the list of dates
            return last7Days;
        }

        //public List<string> GetDateOfLast7Months()
        //{
        //    // Create a list to hold the dates of the last 7 months
        //    List<string> last7Months = new List<string>();

        //    // Iterate through the past 7 months
        //    for (int i = 7; i > 0; i--)
        //    {
        //        // Add the formatted date (yy-MM) to the list for each month
        //        last7Months.Add(DateTime.Today.AddMonths(-i).ToString("yy-MM"));
        //    }

        //    // Return the list of dates
        //    return last7Months;
        //}
        public List<string> GetDateOfLast7Months()
        {
            // Create a list to hold the dates of the last 7 months
            List<string> last7Months = new List<string>();

            // Get the current date in the specified time zone
            var currentDate = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, _timeZone);

            // Iterate through the past 7 months
            for (int i = 6; i >= 0; i--)
            {
                // Add the formatted date (yy-MM) to the list for each month
                var dateInTargetZone = currentDate.AddMonths(-i);
                last7Months.Add(dateInTargetZone.ToString("yy-MM"));
            }

            // Return the list of dates
            return last7Months;
        }

        //public DateTime GetTheRightDataTime()
        //{
        //    // Find the time zone information for Arabian Standard Time
        //    var timeZone = TimeZoneInfo.FindSystemTimeZoneById("Arabian Standard Time");

        //    // Convert the current UTC time to the specified time zone
        //    var targetDate = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, timeZone);

        //    // Return the converted date and time
        //    return targetDate;
        //}

        public DateTime GetTheRightDateTime(int count=0)
        {
            // Convert the current UTC time to the specified time zone
            var targetDate = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, _timeZone);

            // Decrement the time by 1 hour
            targetDate = targetDate.AddHours(-count);

            // Return the adjusted date and time
            return targetDate;
        }



    }

}
