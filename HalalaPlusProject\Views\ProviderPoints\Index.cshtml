﻿@model HalalaPlusProject.CModels.ProviderPointsIndex
    @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
<!-- Tab links -->
<div class="tab">
    <button class="tablinks" onclick="openCity(event, 'PointsTab')">@localizer["points"]  </button>
    <button class="tablinks" onclick="openCity(event, 'ReplacePointsOrders')">@localizer["pointsreplaceorders"]</button>
    <button class="tablinks" onclick="openCity(event, 'ReturnReminder')">@localizer["returnremain"]</button>

</div>
<div id="PointsTab" class="tabcontent"  style="display:block">

 <div class="row">

    
    
    <div class="row">
         <div class="col-md-6 col-sm-6 col-xs-12">
                  <form asp-action="AddEditSales" class="submitfm">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" name="Id"   asp-for="sales.Id" />
            <input type="hidden" name="userId" id="userId" value="@Model.Id" />
             <div class="row">
                   <div class="col-md-12">
                            <h5>@localizer["sales"]</h5>
                    <div class="form-group">
                                <label asp-for="sales.SalesNo" class="control-label">@localizer["salesno"]</label>
                        <input asp-for="sales.SalesNo" required name="SalesNo" class="form-control" />
                        <span asp-validation-for="sales.SalesNo"  class="text-danger"></span>
                    </div>
                      <div class="form-group">
                                <label asp-for="sales.DeservePoints" class="control-label">@localizer["deservpoints"]</label>
                        <input asp-for="sales.DeservePoints" required name="DeservePoints" class="form-control" />
                        <span asp-validation-for="sales.DeservePoints"  class="text-danger"></span>
                    </div>

                    </div>
                         <div class="form-group mt-2">
                            <button type="submit" value="Save" class="btn btn-primary">@localizer["save"]</button>
                     </div>

                 </div>
                  
                 
            </form>
         </div>
         <div class="col-md-6 col-sm-6 col-xs-12">
                  <form asp-action="AddEditPoints" class="submitfm">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" name="Id"   asp-for="Points.Id" />
            <input type="hidden" name="userId" id="userId" value="@Model.Id" />
             <div class="row">
                   
                  <div class="col-md-12">
                            <h5>@localizer["points"]</h5>
                    <div class="form-group">
                                <label asp-for="Points.PointNo" class="control-label">@localizer["salesno"]</label>
                        <input asp-for="Points.PointNo" required name="PointNo"  class="form-control" />
                        <span asp-validation-for="Points.PointNo"  class="text-danger"></span>
                    </div>
                      <div class="form-group">
                                <label asp-for="Points.Prize" class="control-label">@localizer["prize"]</label>
                        <input asp-for="Points.Prize" name="Prize" required class="form-control" />
                        <span asp-validation-for="Points.Prize"  class="text-danger"></span>
                    </div>
                    <div class="form-group">
                                <label asp-for="Points.PointsConditions" class="control-label">@localizer["conditions"]</label>
                        <textarea asp-for="Points.PointsConditions" required name="PointsConditions" class="form-control" ></textarea>
                        <span asp-validation-for="Points.PointsConditions"  class="text-danger"></span>
                    </div>
                    <div class="form-group mt-2">
                                <button type="submit" value="Save" class="btn btn-primary">@localizer["save"]</button>
            </div>

                 </div>
                 </div>
                 
            </form>
         </div>
        </div> <br />   <div class="row">
     <div class="col-md-12 col-sm-12 col-xs-12">
               <div class="table-responsive p-0">
               
                <table id="tbl1" class="table table-striped text-center">
                  <thead>
                    <tr>


                                <th scope="col">@localizer["pointsno"]</th>
                                <th scope="col">@localizer["deservpoints"] </th>
                                <th scope="col"> @localizer["conditions"]</th>
                                <th scope="col"> @localizer["options"]</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.allPoints) {
        <tr>
            
           
            <td>
                @Html.DisplayFor(modelItem => item.PointNo)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Prize)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.PointsConditions)
            </td>
          
             <td>
                                        <a onclick="EditPoints(@item.Id,this)" class="btn btn-outline-info tablebtn">@localizer["edit"]</a> |
                                        <a onclick="DeletePoints(@item.Id)" href="#" class="btn btn-outline-danger tablebtn">@localizer["delete"]</a>
              
            </td>
        </tr>
}                  
                  </tbody>              
                </table>
              </div>
        </div>
            </div>
            </div>
            
</div>
<div id="ReplacePointsOrders" class="tabcontent"  >
 <div class="row">
     </div>
            </div>
<div id="ReturnReminder" class="tabcontent"  >
 <div class="row">
     </div>
            </div>
            @section Scripts{
    <script>
  let table = new DataTable('#tbl1');

    </script>
}