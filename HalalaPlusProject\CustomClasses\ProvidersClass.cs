﻿using System.Globalization;
using HalalaPlusProject.Controllers;
using HalalaPlusProject.Utils;
using Microsoft.AspNetCore.Identity;


namespace HalalaPlusProject.CustomClasses
{
    public class ProvidersClass
    {
        Models.HalalaPlusdbContext _context;
        private readonly IWebHostEnvironment _hosting;
        private readonly ILogger<ProvidersClass> _logger;
        public ProvidersClass(Models.HalalaPlusdbContext context, IWebHostEnvironment hosting,ILogger<ProvidersClass> _logger=default) { 
            this._context = context;
            this._hosting = hosting;
            this._logger = _logger;
        }
        public ProvidersClass( ILogger<ProvidersClass> _logger=default) { 
            this._logger = _logger;
        }

        /// <summary>
        /// Checks if a user already has a service provider profile linked to their account and in the same time has the exact name.
        /// </summary>
        /// <param name="userId">The ID of the user to check.</param>
        /// <returns>True if a provider profile exists for the user, otherwise false.</returns>
        public bool IsUserAlreadyProvider(string userId,string profileName)
        {
            // Check here
            return _context.SystemUsers.Any(p => p.AspId == userId&&p.Name==profileName);
        }

        public async Task< (bool ,string,string)> getLocation(string shortUrl)
        {

            using var httpClient = new HttpClient(new HttpClientHandler
            {
                AllowAutoRedirect = false // we want to capture the redirect
            });
            var atIndex = shortUrl.IndexOf("@");
            if (atIndex == -1)
            {
                var response = await httpClient.GetAsync(shortUrl);
                if (response.Headers.Location != null)
                {
                    shortUrl = response.Headers.Location.ToString();
                }
                else
                {
                    return (false, "", "");
                }
            }
            double lat = 0, lng = 0;
            if (atIndex > -1)
            {
                var coordsPart = shortUrl.Substring(atIndex + 1);
                var parts = coordsPart.Split(',');
                try
                {
                    lat = double.Parse(parts[0], CultureInfo.GetCultureInfo("en-US"));
                    double.TryParse(parts[0], CultureInfo.GetCultureInfo("en-US"), out lat);
                    double.TryParse(parts[1], CultureInfo.GetCultureInfo("en-US"), out lng);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error parsing coordinates from URL: {Message}", ex.Message);
                    return (false, "", "");



                }
                if (parts.Length >= 2 && lat > 0 && lng > 0)
                {
                    Console.WriteLine($"Latitude: {lat}, Longitude: {lng}");
                    return (true, lat.ToString(),lng.ToString());
                }
                return (false, "", "");
            }
            else
            {
                Console.WriteLine("No redirect found, cannot extract coordinates.");
                return (false, "", "");
            }

            return (false, "", "");
        }

        public  async Task<bool>  Insert(CModels.ServiceProvidersCreate model, Areas.Identity.Data.HalalaPlusProjectUser user,string MasterId)
        {

            try
            {

               
                _context.Database.BeginTransaction();
                var ob = new Models.SystemUser();
                ob.Name = model.Name;
                ob.ActorDescription = model.ActorDescription;
                ob.EnName = model.EnName;
                ob.ServiceProviderRepresent = model.ServiceProviderRepresent;
                ob.PhoneNo = model.PhoneNumber;
                ob.EnterPrisePhoneNo = model.EnterprisePhoneNo;
                ob.Activity = model.Activity;
                ob.BusinessNo = model.BusnissNo;
                ob.Email = model.Email;
                ob.BranchesNo = model.BranchsNo??0;
                ob.StoreLink=model.StoreLink;
                //ob.Lat=model.Lat;
                //ob.Lng = model.lng;
                ob.OverView = model.overview;
                ob.EnOverView = model.enoverview;
                ob.Benefitfrompoints = model.bnifitfrompoints;
                ob.EnBenefitfrompoints=model.enbnifitfrompoints;
                ob.City = model.City;
                ob.Logo = user.Image;
                ob.AccountType = "Provider";
                ob.Status = "A";
                ob.AspId = user.Id;
                ob.MasterId = MasterId;
                ob.ContractNo = model.ContractNo;
                ob.Deleted = false;
                ob.CashBack = model.CashBack;
                if (model.ContractDate != null) ob.ContractDate = model.ContractDate;
                ob.DateofJoin = DateTime.Now;
                // Mapping Lat & Lng مباشرة من الفورم
                ob.Lat = model.Lat;
                ob.Lng = model.Lng;

            //    var t=await getLocation(model.LocationLink);
            //     if (t.Item1)
            //     {
            //         ob.Lat = t.Item2;
            //         ob.Lng = t.Item3;
            //     }
            //     else
            //     {
            //         //_logger.LogWarning($"Failed to get location from link: {model.LocationLink}", model.LocationLink);
            //         ob.Lat = null;
            //         ob.Lng = null;
            //     }
                ob.ContractEndDate = model.ContractEndDate;
                _context.Add(ob);
                _context.SaveChanges();
                if (model.Files != null)
                    new FilesClass().insertOrdersFiles(model.Files, ob.Id, "Files", _hosting, _context, 1); 
                if (model.Images != null)
                    new FilesClass().insertOrdersFiles(model.Files, ob.Id, "images", _hosting, _context, 5);
                _context.Database.CommitTransaction();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inserting service provider: {Message}", ex.Message);    
                _context.Database.RollbackTransaction();
                return false;
            }

        }
        public  async Task< bool > Insert2(CModels.ServiceProvidersCreate model,int discount,string? terms, Areas.Identity.Data.HalalaPlusProjectUser user,string MasterId)
        {

            try
            {
                _context.Database.BeginTransaction();
                var ob = new Models.SystemUser();
                ob.Name = model.Name;
                    ob.EnName = model.EnName;
                ob.ServiceProviderRepresent = model.ServiceProviderRepresent;
                ob.PhoneNo = model.PhoneNumber;
                ob.EnterPrisePhoneNo = model.EnterprisePhoneNo;
                ob.Activity = model.Activity;
                ob.BusinessNo = model.BusnissNo;
                ob.Email = model.Email;
                ob.StoreLink=model.StoreLink;
                //ob.Lat=model.Lat;
                //ob.Lng = model.lng;
                ob.OverView = model.overview;
                ob.EnOverView = model.enoverview;
                ob.Benefitfrompoints = model.bnifitfrompoints;
                ob.EnBenefitfrompoints=model.enbnifitfrompoints;
                ob.City = model.City;
                ob.Logo = user.Image;
                ob.AccountType = "Provider";
                ob.Status = "A";
                ob.AspId = user.Id;
                ob.MasterId = MasterId;
                ob.ContractNo = model.ContractNo;
                ob.Deleted = false;
                ob.CashBack = model.CashBack;
                ob.Lat = model.Lat;
                ob.Lng = model.Lng;
                // var t = await getLocation(model.LocationLink);
                // if (t.Item1)
                // {
                //     ob.Lat = t.Item2;
                //     ob.Lng = t.Item3;
                // }
                // else
                // {
                //     _logger.LogWarning("Failed to get location from link: {LocationLink}", model.LocationLink);
                //     ob.Lat = null;
                //     ob.Lng = null;
                // }
                if (model.ContractDate != null) ob.ContractDate = model.ContractDate;
                ob.DateofJoin = DateTime.Now;
             ob.ContractEndDate = model.ContractEndDate;
                _context.Add(ob);
                _context.SaveChanges();

                var mdiscount = new Models.DiscountsTable()
                {
                    Discount = discount,
                    UserId = ob.Id,
                    Conditions = terms,
                    
                };
                _context.Add(mdiscount);    
                _context.SaveChanges(); 
                //if (model.Files != null)
                //    new FilesClass().insertOrdersFiles(model.Files, ob.Id, "Files", _hosting, _context, 1); 
                //if (model.Images != null)
                //    new FilesClass().insertOrdersFiles(model.Files, ob.Id, "images", _hosting, _context, 5);
                _context.Database.CommitTransaction();
                return true;
            }
            catch (Exception ex)
            {
                TelegramHandler1.Instance.SendSingleSMSwithoutasync("exception in adding :"+ex.Message+"/n"+ex.StackTrace);

                _context.Database.RollbackTransaction();
                return false;
            }

        }

    }
}
