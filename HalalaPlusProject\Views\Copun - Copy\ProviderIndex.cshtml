﻿@model HalalaPlusProject.CModels.CoupunModelIndex

@{
    ViewData["Title"] = "العروض";
}
<div class="row">
    <h3>اكواد الخصم </h3>
     <a asp-action="Create" style="float:left; margin-right: 10px; " >اضافة كود خصم</a>
                </div>
<div class="tab">
  <button class="tablinks Active" onclick="openCity(event, 'OffersTab')">اكواد الخصم الحالية</button>
  <button class="tablinks" onclick="openCity(event, 'stoppedOffersTab')">الموقفة والمنتهية  </button>
  

</div>
<div id="OffersTab" class="tabcontent" style="display:block" >
<div class="row">
        <div class="col-12">
          <div class="card mb-4">
         
            <div class="card-body px-0 pt-0 pb-2">
              <div class="table-responsive p-0">
               
                <table id="tbl1"  class="table table-striped text-center">
                  <thead>
                    <tr>
                     
                      <th scope="col">اسم المتجر</th>
                      <th scope="col">تفاصيل المتجر</th>
                      <th scope="col">بداية الكود</th>
                      <th scope="col">نهاية الكود</th>
                      <th scope="col">الحالة</th>
                      <th scope="col">المزيد</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.ActiveCopuns) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.OverView)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.StartDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EndDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.state)
            </td>
           
            
            
            <td>
                
                <a onclick="setField('/Copun/Disable?id='+@item.Id)" class="btn btn-primary"  asp-route-id="@item.Id">ايقاف</a> | 
                <a onclick="Delete('/Copun/Delete?id='+@item.Id)" class="btn btn-primary"  asp-route-id="@item.Id">حذف</a> | 
                <a asp-action="Details" class="btn btn-primary"  asp-route-id="@item.Id">المزيد..</a>
            </td>
        </tr>
}

             
             

                  


                    
                  </tbody>
               
                </table>

              </div>
              
            </div>
          </div>
        </div>
      </div>
      </div>
    <div id="stoppedOffersTab" class="tabcontent" >
<div class="row">
        <div class="col-12">
          <div class="card mb-4">
            
            <div class="card-body px-0 pt-0 pb-2">
              <div class="table-responsive p-0">
               
                <table id="tbl2"  class="table table-striped text-center">
                  <thead>
                    <tr>
                     
                       <th scope="col">اسم المتجر</th>
                      <th scope="col">تفاصيل المتجر</th>
                      <th scope="col">بداية الكود</th>
                      <th scope="col">نهاية الكود</th>
                      <th scope="col">الحالة</th>
                      <th scope="col">المزيد</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.stoppedCopuns) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.OverView)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.StartDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EndDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.state)
            </td>
           
            
            
            <td>
                
                <a asp-action="Details" class="btn btn-primary"  asp-route-id="@item.Id">المزيد..</a> | 
            </td>
        </tr>
}

             
             

                  


                    
                  </tbody>
               
                </table>

              </div>
              
            </div>
          </div>
        </div>
      </div>
      </div>
    
                @section Scripts{
    <script>
  let table = new DataTable('#tbl1');
  let table2 = new DataTable('#tbl2');

    </script>
}