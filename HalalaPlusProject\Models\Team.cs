﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("Team")]
public partial class Team
{
    [Key]
    public int Id { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    [StringLength(500)]
    public string? EnName { get; set; }

    [StringLength(500)]
    public string? Image { get; set; }

    [StringLength(500)]
    public string? Major { get; set; }

    [StringLength(500)]
    public string? EnMajor { get; set; }

    [Column("overview")]
    [StringLength(300)]
    public string? Overview { get; set; }

    [Column("enoverview")]
    [StringLength(300)]
    public string? Enoverview { get; set; }
}
