﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("Countries_Table")]
public partial class CountriesTable
{
    [Key]
    public int Id { get; set; }

    [StringLength(250)]
    public string? Country { get; set; }

    [StringLength(250)]
    public string? Nationality { get; set; }

    [StringLength(50)]
    public string? EnCountry { get; set; }

    [StringLength(250)]
    public string? EnNationality { get; set; }

    [InverseProperty("CIdNavigation")]
    public virtual ICollection<CitiesTable> CitiesTables { get; set; } = new List<CitiesTable>();
}
