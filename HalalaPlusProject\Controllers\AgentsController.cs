﻿using HalalaPlusProject.Areas.Identity.Data;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Data;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System.Text;
using System.Text.Encodings.Web;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// يتحكم في إدارة العملاء داخل النظام: عرض، إضافة، تعديل، حذف، وإرسال رسائل التفعيل عبر البريد الإلكتروني.
    /// </summary>
    [Authorize]
    public class AgentsController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly UserManager<HalalaPlusProjectUser> _userManager;
        private readonly IEmailSender _emailSender;
        private readonly IStringLocalizer<AgentsController> _localization;

        public AgentsController(
            HalalaPlusdbContext context,
            IStringLocalizer<AgentsController> _localization,
            UserManager<HalalaPlusProjectUser> userManager,
            IEmailSender _emailSender)
        {
            _context = context;
            _userManager = userManager;
            this._emailSender = _emailSender;
            this._localization = _localization;
        }

        /// <summary>
        /// يعرض قائمة العملاء المصنفين كـ "Customer".
        /// </summary>
        public async Task<IActionResult> Index()
        {
            return View(new UsersClass().retrive("Customer", _context));
        }

        /// <summary>
        /// يعرض تفاصيل عميل بناءً على معرّف النظام.
        /// </summary>
        /// <param name="id">معرّف العميل.</param>
        public async Task<IActionResult> Details(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }

            var systemUser = new UsersClass().retriveUser(id ?? 0, _context);
            return View(systemUser);
        }

        /// <summary>
        /// يعرض نموذج إنشاء عميل جديد.
        /// </summary>
        public IActionResult Create()
        {
            ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality");
            return View();
        }

        /// <summary>
        /// ينشئ عميلًا جديدًا بعد التحقق من عدم تكرار البريد أو اسم المستخدم، ويرسل بريد تأكيد.
        /// </summary>
        /// <param name="model">بيانات العميل الجديد.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CModels.Agents model)
        {
            if (ModelState.IsValid)
            {
                var userExists = await _userManager.FindByEmailAsync(model.Email);
                if (userExists != null)
                    return Ok(new { state = 0, message = _localization["emailused"].Value });

                userExists = await _userManager.FindByNameAsync(model.UserName);
                if (userExists != null)
                    return Ok(new { state = 0, message = _localization["usedusername"].Value });

                HalalaPlusProjectUser user = new()
                {
                    Email = model.Email,
                    SecurityStamp = Guid.NewGuid().ToString(),
                    PhoneNumber = model.PhoneNo,
                    UserName = model.UserName
                };

                var passwordValidator = new PasswordValidator<HalalaPlusProjectUser>();
                var result = await passwordValidator.ValidateAsync(_userManager, user, model.Password);

                if (result.Succeeded)
                {
                    result = await _userManager.CreateAsync(user, model.Password);
                    if (result.Succeeded)
                    {
                        await _userManager.AddToRoleAsync(user, "Customer");

                        var ob = new Models.SystemUser
                        {
                            Name = model.Name,
                            PhoneNo = model.PhoneNo,
                            Email = model.Email,
                            BirthDate = DateOnly.Parse(model.BirthDate.ToString()) ,
                            IdentityNo = model.IdentityNo,
                            Nationality = model.Nationality,
                            AccountNo = model.AccountNo,
                            AspId = user.Id,
                            Deleted = false,
                            AccountType = "Customer",
                            MasterId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value,
                            DateofJoin = DateTime.Now
                        };

                        _context.SystemUsers.Add(ob);
                        await _context.SaveChangesAsync();

                        var code = await _userManager.GenerateEmailConfirmationTokenAsync(user);
                        var userId = await _userManager.GetUserIdAsync(user);
                        code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));

                        var callbackUrl = Url.Page(
                            "/Account/ConfirmEmail",
                            pageHandler: null,
                            values: new { area = "Identity", userId, code, returnUrl = "~/" },
                            protocol: Request.Scheme);

                        await _emailSender.SendEmailAsync(
                            model.Email,
                            "Confirm your email",
                            $"Please confirm your account by <a href='{HtmlEncoder.Default.Encode(callbackUrl)}'>clicking here</a>.");

                        //if (_userManager.Options.SignIn.RequireConfirmedAccount)
                        //{
                        //    return RedirectToPage("RegisterConfirmation", new { email = user.Email, returnUrl = "~/" });
                        //}
                        //else
                        //{
                        //    await _signInManager.SignInAsync(user, isPersistent: false);
                        //    return Ok(new { state = 5, message = "تم التعديل بنجاح" });
                        //}

                        code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(code));
                        await _userManager.ConfirmEmailAsync(user, code);

                        return Ok(new { state = 7, message = _localization["savedsuccessfuly"].Value, Url = "Index" });
                    }
                }
            }

            return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });
        }

        /// <summary>
        /// يعرض نموذج تعديل بيانات عميل حالي.
        /// </summary>
        /// <param name="id">معرّف العميل.</param>
        public async Task<IActionResult> Edit(long? id)
        {
            if (id == null || _context.SystemUsers == null)
                return NotFound();

            var systemUser = new UsersClass().retriveUser(id ?? 0, _context);
            if (systemUser == null)
                return NotFound();

            ViewData["National"] = new SelectList(
                _context.CountriesTables,
                "Id",
                "Nationality",
                systemUser.NationalityNo);

            return View(systemUser);
        }

        /// <summary>
        /// يحدّث بيانات عميل بعد التحقق من عدم تكرار البريد أو اسم المستخدم.
        /// </summary>
        /// <param name="model">نموذج البيانات المعدلة للعميل.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(CModels.AgentUser model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var ob = _context.SystemUsers.Find(model.Id);
                    if (ob != null)
                    {
                        bool isDuplicate = _context.AspNetUsers
                            .Any(p => (p.Email == model.Email || p.UserName == model.UserName)
                                      && p.Id != ob.AspId);

                        if (isDuplicate)
                            return Ok(new { state = 0, message = _localization["emailused"].Value });

                        ob.Name = model.Name;
                        ob.PhoneNo = model.PhoneNo;
                        ob.Email = model.Email;
                        ob.AccountNo = model.AccountNo;
                        ob.BirthDate = DateOnly.Parse(model.BirthDate.ToString());
                        ob.IdentityNo = model.IdentityNo;
                        ob.Nationality = model.Nationality;

                        _context.Update(ob);

                        var userExists = await _userManager.FindByEmailAsync(model.Email);
                        userExists.Email = model.Email;
                        userExists.UserName = model.UserName;
                        await _userManager.UpdateAsync(userExists);

                        await _context.SaveChangesAsync();
                    }
                }
                catch (DbUpdateConcurrencyException)
                {
                    return Ok(new { state = 0, message = _localization["errorwillsaving1"].Value });
                }

                return Ok(new { state = 7, message = _localization["savedsuccessfuly"].Value, Url = "Index" });
            }

            ViewData["National"] = new SelectList(
                _context.CountriesTables,
                "Id",
                "Nationality",
                model.Nationality);

            return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });
        }

        /// <summary>
        /// يعرض صفحة تأكيد حذف عميل.
        /// </summary>
        /// <param name="id">معرّف العميل.</param>
        public async Task<IActionResult> Delete(long? id)
        {
            if (id == null || _context.SystemUsers == null)
                return NotFound();

            var systemUser = new UsersClass().retriveUser(id ?? 0, _context);
            if (systemUser == null)
                return NotFound();

            return View(systemUser);
        }

        /// <summary>
        /// ينفذ الحذف المنطقي للعميل (Deleted = true).
        /// </summary>
        /// <param name="id">معرّف العميل.</param>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(long id)
        {
            if (_context.SystemUsers == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.SystemUsers'  is null.");
            }

            var systemUser = await _context.SystemUsers.FindAsync(id);
            if (systemUser != null)
            {
                systemUser.Deleted = true;
                _context.Update(systemUser);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// يتحقق من وجود عميل في النظام حسب المعرّف.
        /// </summary>
        /// <param name="id">معرّف العميل.</param>
        /// <returns>صحيح إذا وُجد، وإلا خاطئ.</returns>
        private bool SystemUserExists(long id)
        {
            return (_context.SystemUsers?.Any(e => e.Id == id)).GetValueOrDefault();
        }
    }
}