﻿@model HalalaPlusProject.Models.Achievement

@{
    ViewData["Title"] = "حذف انجاز";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<h4>هل انت متاكد من حذف هذا الانجاز</h4>

<div class="row">
    <div class="col-md-12">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="Title" class="control-label">العنوان</label>
                        <input asp-for="Title" class="form-control" />
                        <span asp-validation-for="Title" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="EnTitle" class="control-label">العنوان انجليزي</label>
                        <input asp-for="EnTitle" class="form-control" />
                        <span asp-validation-for="EnTitle" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="Details" class="control-label">التفاصيل</label>
                        <input asp-for="Details" class="form-control" />
                        <span asp-validation-for="Details" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="EnDetails" class="control-label">التفاصيل انجليزي</label>
                        <input asp-for="EnDetails" class="form-control" />
                        <span asp-validation-for="EnDetails" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <div class="row justify-content-center" style="margin-bottom:1px;">
                            <div class="circle ">
                                <img src="/img/@Model.Image" id="Achievementicon">
                            </div>
                        </div>
                    </div>

                  
                </div>
            </div>
            
    </div>
</div>

<div>
  
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-danger" >حـذف</button> |
        <a asp-action="Index">عودة للقائمة السابقة</a>
    </form>
</div>
