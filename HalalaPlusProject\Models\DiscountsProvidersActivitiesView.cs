﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class DiscountsProvidersActivitiesView
{
    public int ActivityId { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    [StringLength(250)]
    public string? EnName { get; set; }

    [Column("image")]
    [StringLength(500)]
    public string? Image { get; set; }

    [StringLength(250)]
    public string? DisCountName { get; set; }

    public string? Conditions { get; set; }

    public DateOnly? StartDate { get; set; }

    public DateOnly? EndDate { get; set; }

    public double? CashBack { get; set; }

    public long? UserId { get; set; }

    public bool? Deleted { get; set; }

    [Column("isActive")]
    public bool? IsActive { get; set; }

    [Column("orderstate")]
    [StringLength(50)]
    public string? Orderstate { get; set; }

    public bool? IsOrder { get; set; }

    [Column("orderdate", TypeName = "datetime")]
    public DateTime Orderdate { get; set; }

    [Column("masterId")]
    public long? MasterId { get; set; }

    public bool GrantPoints { get; set; }

    public bool GrantDiscount { get; set; }

    [StringLength(50)]
    public string? GrantType { get; set; }

    [Column("discImg")]
    [StringLength(250)]
    public string? DiscImg { get; set; }

    [StringLength(250)]
    public string? DiscountCode { get; set; }

    [StringLength(250)]
    public string? EnDiscountName { get; set; }

    public string? EnConditions { get; set; }

    public double? Discount { get; set; }

    public long Id { get; set; }

    public long ProviderId { get; set; }

    public int? City { get; set; }

    [StringLength(500)]
    public string? ProviderName { get; set; }

    [StringLength(1500)]
    public string? ProviderEnName { get; set; }

    [StringLength(450)]
    public string? Logo { get; set; }

    [StringLength(1)]
    [Unicode(false)]
    public string? Status { get; set; }

    public bool? ProIsOrder { get; set; }

    [StringLength(20)]
    public string? AccountType { get; set; }

    [Column("contractDate")]
    public DateOnly? ContractDate { get; set; }

    public DateOnly? ContractEndDate { get; set; }

    public bool? ProDeleted { get; set; }

    [StringLength(50)]
    public string? ProOrderState { get; set; }
}
