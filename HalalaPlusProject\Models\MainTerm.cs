﻿using System;
using System.Collections.Generic;

namespace HalalaPlusProject.Models;

public partial class MainTerm
{
    public int Id { get; set; }

    public string? Name { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? CreatedDate { get; set; }

    public string? UpdatedBy { get; set; }

    public DateTime? UpdatedDate { get; set; }

    public bool? IsDeleted { get; set; }

    public string? DeletedBy { get; set; }

    public DateTime? DeletedDate { get; set; }

    public virtual ICollection<ManualCommitment> ManualCommitments { get; set; } = new List<ManualCommitment>();

    public virtual ICollection<SubTerm> SubTerms { get; set; } = new List<SubTerm>();
}
