﻿@model HalalaPlusProject.Models.CountriesTable
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["details"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["details"]</h1>

<div>
   
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @localizer["thecountry"]
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Country)
        </dd>
        <dt class="col-sm-2">
            @localizer["countrynationality"]
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Nationality)
        </dd>
        <dt class="col-sm-2">
            @localizer["thecountry"]
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.EnCountry)
        </dd>
        <dt class="col-sm-2">
            @localizer["countrynationality"]
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.EnNationality)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id">@localizer["edite"]</a> |
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>
