﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class VwOverallSystemPerformance
{
    public long? Id { get; set; }

    public int? ActiveEmployees { get; set; }

    public int? ActiveCustomers { get; set; }

    public int? ActiveProviders { get; set; }

    public int? ActiveMarketers { get; set; }

    public int? OrdersThisMonth { get; set; }

    public double TotalRevenue { get; set; }

    [StringLength(250)]
    public string CustomerCountry { get; set; } = null!;

    [StringLength(250)]
    public string ProviderCountry { get; set; } = null!;

    [StringLength(250)]
    public string MarketerCountry { get; set; } = null!;

    [StringLength(250)]
    public string EmployeeCountry { get; set; } = null!;

    [StringLength(1500)]
    public string TopPackageName { get; set; } = null!;

    public int TopPackageUsageCount { get; set; }
}
