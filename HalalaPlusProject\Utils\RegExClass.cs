﻿using System.Text.RegularExpressions;

namespace HalalaPlusProject.Utils
{
    public class RegExClass
    {
        public string isEmailorPhone(string input) {
           
            var r = new Regex(@"^\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b");
            if (r.<PERSON>(input))
                return  "email";
            r = new Regex(@"^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$");
            if (r.<PERSON>(input))
                return "phone";
            return "invalid";
        }
        public bool isDigits(string input) {
           
            
            var r = new Regex(@"^\d{10}$");
            if (r.<PERSON>(input))
                return true;
            return false;
         
        } 
        public bool isChars(string input) {
           
            var r = new Regex(@"^[A-Za-z ]+$");
            if (r.<PERSON>(input))
                return true;
            return false;
         
        }
        public bool isCharUniCode(string input)
        {
            var r = new Regex(@"^[\u0600-\u06FF ]{1,}$");
            if (r.<PERSON>(input))
                return true;
            r = new Regex(@"^[A-Za-z ]+$");
            if (r.IsMatch(input))
                return true;
            return false;

        }
    }
}
