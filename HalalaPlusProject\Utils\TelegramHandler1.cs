﻿namespace HalalaPlusProject.Utils
{
    public class TelegramHandler1
    {
        private static readonly Lazy<TelegramHandler1> _instance = new Lazy<TelegramHandler1>(() => new TelegramHandler1());

        // Public static property to access the instance
        public static TelegramHandler1 Instance => _instance.Value;

        // Private constructor to prevent external instantiation
        private TelegramHandler1()
        {
            // Initialization logic (if needed)
        }
        public void SendSingleSMSwithoutasync(string message)
        {
            Task savetask = Task.Run(() => SendSMS("control: "+message));

        }

        public async Task<object> SendSMS(string message)
        {
            try
            {
                var client = new HttpClient();
                var request = new HttpRequestMessage(HttpMethod.Post, "http://mimassoft-001-site25.jtempurl.com/api/CardReportsApi/send");
                var content = new StringContent("{\r\n    \"token\":\"bot7680248120:AAEmf6s7Ojl7mhpEgaNgPFj4kdYvISxp23M\",\r\n    \"chataid\":\"DailyPules\",\r\n    \"message\":\""+message+"\"\r\n}", null, "application/json");
                request.Content = content;
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();
                Console.WriteLine(await response.Content.ReadAsStringAsync());

                //var client = new HttpClient();
                //var request = new HttpRequestMessage(HttpMethod.Post, "https://api.telegram.org/bot7583893098:AAHJfUd7v4muQAsSoW-Dy-Tshlnyw9OatM0/sendMessage?chat_id=@halalaexception&text=" + message);
                //var response = await client.SendAsync(request);
                //response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }
        public void SendSingleSMSwithoutasync1(string message)
        {
            Task savetask = Task.Run(() => SendSMS1(message));

        }

        public async Task<object> SendSMS1(string message)
        {
            try
            {
                var client = new HttpClient();
                var request = new HttpRequestMessage(HttpMethod.Post, "https://api.telegram.org/bot7583893098:AAHJfUd7v4muQAsSoW-Dy-Tshlnyw9OatM0/sendMessage?chat_id=@halalaexception&text=" + message);
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }
    }
}

