﻿using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    public class DateGreaterThanAttribute : ValidationAttribute, IClientModelValidator
    {
        public string OtherProperty { get; }

        public DateGreaterThanAttribute(string otherProperty)
        {
            OtherProperty = otherProperty;
            ErrorMessage = "تاريخ النهاية يجب أن يكون بعد تاريخ البداية";
        }

        protected override ValidationResult? IsValid(object? value, ValidationContext context)
        {
            var current = ToNullableDateTime(value);

            var otherPropInfo = context.ObjectType.GetProperty(OtherProperty)
                ?? throw new ArgumentException($"لم يتم العثور على خاصية باسم {OtherProperty}");

            var other = ToNullableDateTime(otherPropInfo.GetValue(context.ObjectInstance));

            
            if (current is null || other is null)
                return ValidationResult.Success;

            return current <= other
                ? new ValidationResult(ErrorMessage)
                : ValidationResult.Success;
        }

        private static DateTime? ToNullableDateTime(object? v)
        {
            if (v is null) return null;
            if (v is DateTime dt) return dt;
            return DateTime.TryParse(v.ToString(), out var parsed) ? parsed : null;
        }

   
        public void AddValidation(ClientModelValidationContext context)
        {
            Merge(context.Attributes, "data-val", "true");
            Merge(context.Attributes, "data-val-dategreaterthan", ErrorMessage!);
            // اشارة للحقل الآخر
            Merge(context.Attributes, "data-val-dategreaterthan-other", $"*.{OtherProperty}");
        }

        private static void Merge(IDictionary<string, string> attrs, string key, string value)
        {
            if (!attrs.ContainsKey(key)) attrs.Add(key, value);
        }
    }
    public class ServiceProviders
    {
        public long Id { get; set; }
        [Required]
        [Display(Name = "اسم مقدم الخدمة")]
        public string? Name { get; set; }
        [Required]
        [RegularExpression(@"^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{8,}$", ErrorMessage = "يجب ان تحتوي كلمة المرور على ارقام وحروف كبيرة وصغيرة ورموز")]

        //[DataType(DataType.Password, ErrorMessage = "يجب ان تحتوي كلمة المرور على ارقام وحروف كبيرة وصغيرة ورموز")]
        [Display(Name = "كلمة المرور")]
        public string? Password { get; set; }
        [Required]
        [Display(Name = "المدينة")]
        public int? City { get; set; }
        [Required]
        [Display(Name = "النشاط")]
        public List<int>? Activity { get; set; }
        [Required]
        [Display(Name = "اسم الممثل")]
        public string? ServiceProviderRepresent { get; set; }
        [Display(Name = "رقم الجوال")]
        public string? PhoneNumber { get; set; }
        [Display(Name = "الايميل")]
        [RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }
        //public string AspId { get; set; } = null!;
        [Display(Name = "رقم السجل التجاري")]
        public string BusnissNo { get; set; } = null!;
        [Display(Name = "رقم المنشأة")]
        public string EnterprisePhoneNo { get; set; } = null!;
        [Display(Name = "الموقع")]
        public string Locatin { get; set; } = null!;
        [Display(Name = "الشعار")]
        public IFormFile? Logo { get; set; } = null!;
        [Display(Name = "المرفقات")]
        public List<IFormFile>? Files { get; set; } = null!;
        [Display(Name = "الكاش باك")]
        public double? CashBack { get; set; }
        [Required]
        [Display(Name = "تاريخ التعاقد")]
        public DateTime? ContractDate { get; set; }
        [Required]
        [Display(Name = "بداية الخصومات")]
        public DateTime? StartDate { get; set; }
        [Required]
        [Display(Name = "نهاية الخصومات")]
        public DateTime? EndDate { get; set; }
        [Required]
        [Display(Name = "عدد النقاط")]
        public int? PointNo { get; set; }
        [Required]
        [Display(Name = "عدد المبيعات")]
        public int? SalesNo { get; set; }
        [Required]
        [Display(Name = "النقاط المستحقة")]
        public int? DeservePoints { get; set; }
        [Required]
        [Display(Name = "نسبة الخصم")]
        public double? Discount { get; set; }
        [Required]
        [Display(Name = "اسم الخدمة (الخصم)")]
        public string? DiscountName { get; set; }
        [Required]
        [Display(Name = "المكافأء المستحقة")]
        public int? Prize { get; set; }
        [Required]
        [Display(Name = "الشروط")]
        public string Conditions { get; set; } = null!;
        [Required]
        [Display(Name = "الشروط")]
        public string PointsConditions { get; set; } = null!;


    }

    public class Sales
    {
        public long? Id { get; set; }
        public long userId { get; set; }

        [Required]
        //[Display(Name = "عدد المبيعات")]
        public double? SalesNo { get; set; }
        [Required]
        //[Display(Name = "النقاط المستحقة")]
        public double? DeservePoints { get; set; }
    }
    public class Points
    {
        public long? Id { get; set; }
        public long userId { get; set; }

        [Required]
        //[Display(Name = "عدد النقاط")]
        public double? PointNo { get; set; }
        [Required]
        //[Display(Name = "المكافأء المستحقة")]
        public double? Prize { get; set; }

        [Required]
        //[Display(Name = "الشروط")]
        public string? PointsConditions { get; set; } = null!;
    }
    public class PointsModel
    {

        public long? Id { get; set; }
        public long userId { get; set; }
        [Required]
        //[Display(Name = "عدد النقاط")]
        public double? PointNo { get; set; }
        [Required]
        //[Display(Name = "عدد المبيعات")]
        public double? SalesNo { get; set; }
        [Required]
        //[Display(Name = "النقاط المستحقة")]
        public double? DeservePoints { get; set; }

        [Required]
        //[Display(Name = "المكافأء المستحقة")]
        public double? Prize { get; set; }

        [Required]
        //[Display(Name = "الشروط")]
        public string PointsConditions { get; set; } = null!;

    }


    public class socialMediaAccounts
    {
        public int? Id { get; set; }
        public long userId { get; set; }
        [Required]
        //[Display(Name = "اسم المنصة")]
        public string? SiteName { get; set; }
        [Required]
        //[Display(Name = "الرابط")]
        public string? Link { get; set; }

    }

    public class ServiceProvidersCreate
    {
        public long Id { get; set; }
        [Required]
        //[Display(Name = "اسم مقدم الخدمة")]
        public string? Name { get; set; }
        //[Required]
        public string? EnName { get; set; }
        //[Required]
        public string? overview { get; set; }
        //[Required]
        public string enoverview { get; set; }
        //[Required]
        //[Display(Name = "اسم المستخدم")]
        //public string? UserName { get; set; }
        //[Required]
        //[Display(Name = "المدينة")]
        //[Required]
        public string? StoreLink { get; set; }
        public int? City { get; set; }
        //[Required]
        //[Display(Name = "النشاط")]
        public int? Activity { get; set; }
        ////[Required]
        //[Display(Name = "اسم الممثل")]
        public string? ServiceProviderRepresent { get; set; }
        //[Display(Name = "رقم الجوال")]
        public string? PhoneNumber { get; set; }
        public string? ActorDescription { get; set; }
        public int? BranchsNo { get; set; }
        //[Required]
        //[Display(Name = "الايميل")]
        [RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }
        [Required]
        //[Display(Name = "كلمة المرور")]
        [RegularExpression(@"^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{8,}$", ErrorMessage = "يجب ان تحتوي كلمة المرور على ارقام وحروف كبيرة وصغيرة ورموز")]
        public string? Password { get; set; }
        //public string AspId { get; set; } = null!;
        //[Display(Name = "رقم السجل التجاري")]
        //[Required(ErrorMessage = "جب إدخال رقم المنشأة")]
        public string? BusnissNo { get; set; } = null!;
        //[Display(Name = "رقم المنشأة")]
        public string? EnterprisePhoneNo { get; set; } = null!;

        //[Required]
        //public string? LocationLink { get; set; }   
        //[Display(Name = "الموقع")]
        //[Required]
        //public string? Locatin { get; set; } = null!;
        //[Required]

        //[Display(Name = "لموقع x")]
        public string? Lat { get; set; } = null!;
        //[Display(Name = "الموقع y")]
        //[Required]
        public string? Lng { get; set; } = null!;
        //[Display(Name = "الشعار")]
        [Required]
        public IFormFile? Logo { get; set; } = null!;
        //[Display(Name = "المرفقات")]

        public List<IFormFile>? Files { get; set; } = null!;
        public List<IFormFile>? Images { get; set; } = null!;
        //[Display(Name = "الكاش باك")]
        [Range(0, float.MaxValue, ErrorMessage = "Please enter valid float Number")]
        public double? CashBack { get; set; }
        //[Required]
        //[Display(Name = "بداية التعاقد")]
        public DateOnly? ContractDate { get; set; }
        //[Required]
        //[Display(Name = "نهاية التعاقد")]
        public DateOnly? ContractEndDate { get; set; }
        //[Display(Name = "رقم العقد")]
        public string? ContractNo { get; set; }
        //public List<DiscountsCreate> discounts { get; set; } = null!;
        //public List<PointsCreate> Points { get; set; } = null!;
        //public List<socialMediaAccounts> Accounts { get; set; } = null!;
        //public string disc { get; set; } = null!;
        //public string Poin { get; set; } = null!;
        public string? Accounts { get; set; } = null!;
        //[Required]
        public string? bnifitfrompoints { get; set; } = null!;
        //[Required]
        public string? enbnifitfrompoints { get; set; } = null!;


    }
    public class ServiceProvidersgetEdit
    {
        public long Id { get; set; }
        [Required]
        //[Display(Name = "اسم مقدم الخدمة")]
        public string? Name { get; set; }
        public string? EnName { get; set; }
        //[Display(Name = "اسم المستخدم")]

        //[Required]
        public string? StoreLink { get; set; }
        public string? UserName { get; set; }
        [Required]
        //[Display(Name = "المدينة")]
        public int? City { get; set; }
        [Required]
        public string? overview { get; set; }
        [Required]
        public string? enoverview { get; set; }
        [Required]
        public string? bnifitfrompoints { get; set; } = null!;
        [Required]
        public string? enbnifitfrompoints { get; set; } = null!;

        [Required]
        //[Display(Name = "النشاط")]
        public int? Activity { get; set; }
        //[Required]
        //[Display(Name = "اسم الممثل")]
        public string? ServiceProviderRepresent { get; set; }
        //[Display(Name = "رقم الجوال")]
        public string? PhoneNumber { get; set; }
        //[Display(Name = "الايميل")]
        //[RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }
        //public string AspId { get; set; } = null!;
        //[Display(Name = "رقم السجل التجاري")]
        public string? BusnissNo { get; set; }
        //[Display(Name = "رقم المنشأة")]
        public string? EnterprisePhoneNo { get; set; } 
        //[Display(Name = "الموقع")]
        //public string? Locatin { get; set; } = null!;
        //[Display(Name = "لموقع x")]
        public string? Lat { get; set; } = null!;
        //public string? LocationLink { get; set; } = null!;
        //[Display(Name = "الموقع y")]
        public string? Lng { get; set; } = null!;
        //[Display(Name = "الشعار")]
        public IFormFile? Logo { get; set; } = null!;
        public string? LogoLink { get; set; } = null!;
        //[Display(Name = "المرفقات")]
        public List<IFormFile>? Files { get; set; } = null!;
        public List<Files>? FilesList { get; set; } = null!;
        public List<Files>? images { get; set; } = null!;
        public DateOnly? ContractDate { get; set; }
        //[Required]
        //[Display(Name = "نهاية التعاقد")]
        public DateOnly? ContractEndDate { get; set; }
        //[Display(Name = "رقم العقد")]
        public string? ContractNo { get; set; }

        public List<SocialAccounts>? Accounts { get; set; } = null!;
        public Points Points { get; set; } = new Points();
        public List<Points> allPoints { get; set; }
        public Sales Sales { get; set; } = null!;
        public DiscountsModel Discounts { get; set; } = null!;
        public List<DiscountsModel> DiscountsList { get; set; } = null!;
        public socialMediaAccounts AccountsData { get; set; } = null!;

    }


    public class ServiceProvidersEdit
    {
        //[Required]
        public string? StoreLink { get; set; }
        public long Id { get; set; }
        [Required]
        //[Display(Name = "اسم مقدم الخدمة")]
        public string? Name { get; set; }
        //[Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        //[EmailAddress(ErrorMessage = "يجب إدخال بريد إلكتروني صالح")]
        //[StringLength(50, ErrorMessage = "البريد الإلكتروني يجب ألا يتجاوز 50 حرف")]
        public string? Email { get; set; }
        //[Display(Name = "اسم المستخدم")]
        [Required]
        public string? overview { get; set; }
        [Required]
        public string? enoverview { get; set; }
        [Required]
        public string? bnifitfrompoints { get; set; } = null!;
        [Required]
        public string? enbnifitfrompoints { get; set; } = null!;
        public string? UserName { get; set; }
        [Required]
        public string? EnName { get; set; }
        [Required]
        //[Display(Name = "المدينة")]
        public int? City { get; set; }
        [Required]
        //[Display(Name = "النشاط")]
        public int? Activity { get; set; }
        //[Required]
        //[Display(Name = "اسم الممثل")]
        public string? ServiceProviderRepresent { get; set; }
        //[Display(Name = "رقم الجوال")]
        public string? PhoneNumber { get; set; }
        //[Display(Name = "الايميل")]
        //[RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        //public string? Email { get; set; }
        //public string AspId { get; set; } = null!;
        //[Display(Name = "رقم السجل التجاري")]
        public string? BusnissNo { get; set; } 
        //[Display(Name = "رقم المنشأة")]
        public string? EnterprisePhoneNo { get; set; } 
        //[Display(Name = "الموقع")]
        //public string? Locatin { get; set; } = null!;
        //[Display(Name = "لموقع x")]
        public string? Lat { get; set; } = null!;
        //public string? LocationLink { get; set; } = null!;
        //[Display(Name = "الموقع y")]
        public string? Lng { get; set; } = null!;
        //[Display(Name = "الشعار")]
        public IFormFile? Logo { get; set; } = null!;
        public string? LogoLink { get; set; } = null!;
        //[Display(Name = "المرفقات")]
        public List<IFormFile>? Files { get; set; } = null!;
        public List<Files>? FilesList { get; set; } = null!;
        public DateOnly? ContractDate { get; set; }
        //[Display(Name = "{رقم العقد")]
        public string? ContractNo { get; set; }
        //[Required]
        //[Display(Name = "نهاية التعاقد")]
        public DateOnly? ContractEndDate { get; set; }

    }


    public class SocialAccounts
    {
        public long Id { get; set; }
        public string? Name { get; set; }
        public string Link { get; set; }

    }
    public class socialMediaAccountsCreateEdit
    {
        [Required]
        public int Id { get; set; }
        public long userId { get; set; }
        [Required]
        //[Display(Name = "اسم المنصة")]
        public string? SiteName { get; set; }
        [Required]
        //[Display(Name = "الرابط")]
        public string? Link { get; set; }

    }
    public class Files
    {
        public long Id { get; set; }
        public string? Name { get; set; }
        public string Link { get; set; }

    }


    public class ServiceProvidersDetails
    {
        public long Id { get; set; }
        [Display(Name = "اسم مقدم الخدمة")]

        public string? Name { get; set; }
        public string? EnName { get; set; }
        [Display(Name = "اسم المستخدم")]
        public string? UserName { get; set; }
        [Display(Name = "المدينة")]
        public string? City { get; set; }
        [Required]
        public string? overview { get; set; }
        [Required]
        public string? enoverview { get; set; }
        [Required]
        public string? bnifitfrompoints { get; set; } = null!;
        [Required]
        public string? enbnifitfrompoints { get; set; } = null!;
        public string? StoreLink { get; set; }
        [Required]
        [Display(Name = "النشاط")]
        public string? ActivityName { get; set; }
        [Required]
        [Display(Name = "اسم الممثل")]
        public string? ServiceProviderRepresent { get; set; }
        [Display(Name = "رقم الجوال")]
        public string? PhoneNumber { get; set; }
        [Display(Name = "الايميل")]
        //[RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }
        //public string AspId { get; set; } = null!;
        [Display(Name = "رقم السجل التجاري")]
        public string BusnissNo { get; set; } = null!;
        [Display(Name = "لموقع x")]
        public string? Lat { get; set; } = null!;
        [Display(Name = "الموقع y")]
        public string? Lng { get; set; } = null!;
        [Display(Name = "رقم المنشأة")]
        public string EnterprisePhoneNo { get; set; } = null!;
        [Display(Name = "الموقع")]
        public string Locatin { get; set; } = null!;
        [Display(Name = "الشعار")]
        public string? Logo { get; set; } = null!;
        [Display(Name = "المرفقات")]
        public List<Files>? Files { get; set; } = null!;
        public List<Files>? images { get; set; } = null!;
        [Display(Name = "الكاش باك")]
        public double? CashBack { get; set; }
        [Required]
        [Display(Name = "تاريخ التعاقد")]
        public DateTime? ContractDate { get; set; }
        [Display(Name = "{رقم العقد")]
        public string? ContractNo { get; set; }
        [Display(Name = "نهاية التعاقد")]
        public DateTime? ContractEndDate { get; set; }
        public List<SocialAccounts>? Accounts { get; set; } = null!;
        public List<Points> Points { get; set; } = null!;
        public Sales sales { get; set; } = null!;
        public List<DiscountsModel> DiscountsList { get; set; } = null!;
    }

    public class DiscountsList
    {
        public long Id { get; set; }
        [Required]
        [Display(Name = "بداية الخصومات")]
        public string? StartDate { get; set; }
        [Required]
        [Display(Name = "نهاية الخصومات")]
        public string? EndDate { get; set; }
        [Required]
        [Display(Name = "عدد النقاط")]
        public double? PointNo { get; set; }
        [Required]
        [Display(Name = "عدد المبيعات")]
        public double? SalesNo { get; set; }
        [Required]
        [Display(Name = "النقاط المستحقة")]
        public double? DeservePoints { get; set; }
        [Required]
        [Display(Name = "نسبة الخصم")]
        public double? Discount { get; set; }
        [Required]
        [Display(Name = "اسم الخدمة (الخصم)")]
        public string? DiscountName { get; set; }
        [Required]
        [Display(Name = "اسم الخدمة بالانجليزي (الخصم)")]
        public string? EnDiscountName { get; set; }
        [Required]
        [Display(Name = "المكافأء المستحقة")]
        public double? Prize { get; set; }
        [Required]
        [Display(Name = "الشروط")]
        public string Conditions { get; set; } = null!;
        [Required]
        [Display(Name = "الشروط")]
        public string PointsConditions { get; set; } = null!;
    }

     

public class DiscountsProviderList
    {
        public long Id { get; set; }

        [Required(ErrorMessage = "بداية الخصومات مطلوبة")]
  
        public DateTime? StartDate { get; set; }

        [Required(ErrorMessage = "نهاية الخصومات مطلوبة")]
     
        public DateTime? EndDate { get; set; }

        [Required(ErrorMessage = "نسبة الخصم مطلوبة")]
        [Range(1, 100, ErrorMessage = "الخصم يجب أن يكون بين 1% و 100%")]
        [Display(Name = "نسبة الخصم")]
        public double? Discount { get; set; }

        [Required(ErrorMessage = "اسم الخدمة بالعربية مطلوب")] 
        [Display(Name = "اسم الخدمة (عربي)")]
        public string? DiscountName { get; set; }

        [Required(ErrorMessage = "اسم الخدمة بالإنجليزية مطلوب")] 
        [Display(Name = "اسم الخدمة (English)")]
        public string? EnDiscountName { get; set; }

        [Required(ErrorMessage = "نوع الخصم مطلوب")]
        [Display(Name = "النوع للخصم")]
        public string GrantType { get; set; } = null!;

        [Display(Name = "صورة الخصم")]
        public IFormFile? DiscImage { get; set; } = null!;

       
        public string Conditions { get; set; } = null!;

        public List<DiscountsModel> ListDis { get; set; } = null!;
    }

    //public class PointsProvider
    //{
    //    public long Id { get; set; }

    //    [Required]
    //    [Display(Name = "عدد النقاط")]
    //    public double? PointNo { get; set; }
    //    [Required]
    //    [Display(Name = "عدد المبيعات")]
    //    public double? SalesNo { get; set; }
    //    [Required]
    //    [Display(Name = "النقاط المستحقة")]
    //    public double? DeservePoints { get; set; }

    //    [Required]
    //    [Display(Name = "المكافأء المستحقة")]
    //    public double? Prize { get; set; }

    //    [Required]
    //    [Display(Name = "الشروط")]
    //    public string PointsConditions { get; set; } = null!;
    //}
    public class ProviderPointsIndex
    {
        public Points Points { get; set; } = null!;
        public List<Points> allPoints { get; set; } = null!;
        public Sales sales { get; set; } = null!;
        public long? Id { get; set; } = null!;

    }
}
