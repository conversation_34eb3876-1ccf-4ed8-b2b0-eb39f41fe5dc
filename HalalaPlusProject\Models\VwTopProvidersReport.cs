﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class VwTopProvidersReport
{
    public long ProviderId { get; set; }

    [StringLength(500)]
    public string? ProviderName { get; set; }

    [StringLength(250)]
    public string? CityName { get; set; }

    [StringLength(500)]
    public string? ActivityName { get; set; }

    public int? DiscountsCount { get; set; }

    [Column(TypeName = "decimal(38, 2)")]
    public decimal DiscountsTotalValue { get; set; }

    public int? OffersCount { get; set; }

    [Column(TypeName = "decimal(38, 2)")]
    public decimal OffersTotalValue { get; set; }

    public int? CouponCodesCount { get; set; }

    [Column(TypeName = "decimal(38, 2)")]
    public decimal CouponCodesTotalValue { get; set; }

    public int? UsedCouponCodesCount { get; set; }

    [Column(TypeName = "decimal(38, 2)")]
    public decimal TotalPointsGranted { get; set; }

    [Column(TypeName = "decimal(38, 2)")]
    public decimal TotalGrantedDiscountsValue { get; set; }
}
