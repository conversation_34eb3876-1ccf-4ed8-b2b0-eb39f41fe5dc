﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class GrantedDiscount
{
    [Key]
    public long Id { get; set; }

    public long? UserId { get; set; }

    public long? ProviderId { get; set; }

    public long? ProductId { get; set; }

    public double? Amount { get; set; }

    public double? Rate { get; set; }

    public double? Discount { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? GrantDate { get; set; }

    [StringLength(20)]
    public string OrderState { get; set; } = null!;

    public int? Points { get; set; }

    [Column("replaced")]
    public bool Replaced { get; set; }

    public long? MonyBoxId { get; set; }

    public int? PointsId { get; set; }

    public long? ReplaceOrderId { get; set; }

    public double? CachBack { get; set; }

    public double? CachBackPercentage { get; set; }

    public bool GrantedByHalalalPlus { get; set; }

    [StringLength(500)]
    public string? Details { get; set; }

    [StringLength(50)]
    public string? Purpose { get; set; }

    public long? DiscountId { get; set; }

    [ForeignKey("DiscountId")]
    [InverseProperty("GrantedDiscounts")]
    public virtual DiscountsTable? DiscountNavigation { get; set; }

    [ForeignKey("MonyBoxId")]
    [InverseProperty("GrantedDiscounts")]
    public virtual UsersMonyBox? MonyBox { get; set; }

    [ForeignKey("PointsId")]
    [InverseProperty("GrantedDiscounts")]
    public virtual MonyBoxsPoint? PointsNavigation { get; set; }

    [ForeignKey("ProviderId")]
    [InverseProperty("GrantedDiscountProviders")]
    public virtual SystemUser? Provider { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("GrantedDiscountUsers")]
    public virtual SystemUser? User { get; set; }
}
