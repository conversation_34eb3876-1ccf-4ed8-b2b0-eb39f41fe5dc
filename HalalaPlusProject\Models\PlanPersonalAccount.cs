﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("PlanPersonalAccount")]
public partial class PlanPersonalAccount
{
    [Key]
    public int AccountId { get; set; }

    public long? UserId { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? Salary { get; set; }

    [StringLength(500)]
    public string? Notes { get; set; }

    [StringLength(100)]
    public string? CreatedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [StringLength(100)]
    public string? UpdatedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    public bool? Deleted { get; set; }

    [StringLength(100)]
    public string? DeletedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? DeletedAt { get; set; }

    [StringLength(10)]
    public string? CalendarType { get; set; }

    public byte? SalaryDeliverDate { get; set; }

    [InverseProperty("Account")]
    public virtual ICollection<AdditionalIncome> AdditionalIncomes { get; set; } = new List<AdditionalIncome>();

    [ForeignKey("UserId")]
    [InverseProperty("PlanPersonalAccounts")]
    public virtual SystemUser? User { get; set; }
}
