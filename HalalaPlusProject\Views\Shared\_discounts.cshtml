﻿
@model IEnumerable<HalalaPlusProject.CModels.DiscountsModel>

<div class="table-responsive p-0">
               
                <table class="table table-striped text-center">
                  <thead>
                    <tr>
                     
                      <th scope="col">اسم الخدمة(الخصم)</th>
                      <th scope="col">الخصم</th>
                      <th scope="col">بداية الخصم</th>
                      <th scope="col">نهاية الخصم</th>
                      <th scope="col">الشروط</th>
                      <th scope="col">خيارات</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model) {
        <tr id="<EMAIL>">
            <td>
                @Html.DisplayFor(modelItem => item.DiscountName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Discount)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.SStartDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.SEndDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Conditions)
            </td>
            <td>
                 <a onclick="EditDiscount(@item.Id)" class="btn btn-outline-info tablebtn" >تعديل</a> |    
                 <a onclick="setField('/ServiceProvider/DisableDiscount?id='+@item.Id)" class="btn btn-primary tablebtn" >ايقاف</a> | 
                            <a href="#" class="btn btn-outline-danger tablebtn"  onclick="deleteDiscount('@item.Id')">حذف</a>
                
            </td>
        </tr>
}
             
                  </tbody>
               
                </table>

              </div>