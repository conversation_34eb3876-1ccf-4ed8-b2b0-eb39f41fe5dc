﻿using HalalaPlusProject.CustomClasses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة بيانات الموظفين، بما في ذلك عرض تفاصيلهم الشخصية وإدارة المهام الموكلة إليهم.
    /// </summary>
    [Authorize]
    public class EmployeeDataController : Controller
    {
        Models.HalalaPlusdbContext _context;
        private readonly IWebHostEnvironment _hosting;
        private readonly IStringLocalizer<EmployeeDataController> _localization;
        public EmployeeDataController(Models.HalalaPlusdbContext context, IStringLocalizer<EmployeeDataController> _localization, IWebHostEnvironment hosting)
        {
            this._context = context;
            this._localization = _localization;
            this._hosting = hosting;
        }

        /// <summary>
        /// عرض التفاصيل الشخصية للموظف الذي قام بتسجيل الدخول حاليًا.
        /// </summary>
        /// <returns>عرض يحتوي على تفاصيل الموظف.</returns>
        public IActionResult Index()
        {
            var systemUser = new UsersClass().retriveUserDetails(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value, _context);
            if (User != null)
                return View(systemUser);
            return View(new CModels.UserDetails());
        }

        /// <summary>
        /// عرض قائمة المهام الموكلة إلى الموظف الحالي.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة المهام.</returns>
        public async Task<IActionResult> Tasks()
        {
            var halalaPlusdbContext = new TaskClass().retrive(_context, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value);
            return View(halalaPlusdbContext);
        }

        /// <summary>
        /// عرض نموذج إنشاء مهمة جديدة.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج الإنشاء.</returns>
        public IActionResult Create()
        {
            ViewData["Emloyee"] = new SelectList(_context.SystemUsers.Where(p => p.AccountType == "Employee"), "AspId", "Name");
            return View();
        }

        /// <summary>
        /// معالجة عملية إنشاء مهمة جديدة وإسنادها لموظف.
        /// </summary>
        /// <param name="model">بيانات المهمة الجديدة.</param>
        /// <returns>إذا نجحت العملية، يتم عرض صفحة الفهرس؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CModels.TasksCreate model)
        {
            if (ModelState.IsValid)
                if (new TaskClass().insert(model, _context, _hosting, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value, true).Result)
                    return RedirectToAction(nameof(Index));
            ViewData["Emloyee"] = new SelectList(_context.SystemUsers.Where(p => p.AccountType == "Employee"), "Id", "Name");
            return View(model);
        }

        /// <summary>
        /// عرض التفاصيل الكاملة لمهمة معينة.
        /// </summary>
        /// <param name="id">معرف المهمة المراد عرض تفاصيلها.</param>
        /// <returns>عرض يحتوي على تفاصيل المهمة، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Details(long? id)
        {
            if (id == null || _context.TaskesTables == null)
            {
                return NotFound();
            }

            var Taskdata = new TaskClass().retriveDetails(id ?? 0, _context);
            if (Taskdata == null)
            {
                return NotFound();
            }

            return View(Taskdata);
        }

        /// <summary>
        /// إغلاق مهمة معينة وتحديث حالتها.
        /// </summary>
        /// <param name="id">معرف المهمة المراد إغلاقها.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        public async Task<IActionResult> CloseTask(int? id)
        {
            if (id == null || _context.TaskesTables == null) return Ok(new { state = 0, message = _localization["nodata"].Value });
            var Taskdata = new TaskClass().close(id ?? 0, _context).Result;
            if (Taskdata != true) return Ok(new { state = 0, message = _localization["tasknotstoped"].Value });
            return Ok(new { state = 7, message = _localization["taskstoped"].Value, Url = "Tasks" });
        }

        /// <summary>
        /// عرض نموذج تعديل بيانات مهمة حالية.
        /// </summary>
        /// <param name="id">معرف المهمة المراد تعديلها.</param>
        /// <returns>عرض يحتوي على بيانات المهمة في نموذج التعديل، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Edit(long? id)
        {
            if (id == null || _context.TaskesTables == null) return NotFound();
            var employeeTask = new TaskClass().retriveDetails(id ?? 0, _context);
            if (employeeTask == null) return NotFound();
            ViewData["Emloyee"] = new SelectList(_context.SystemUsers.Where(p => p.AccountType == "Employee"), "Id", "Name", employeeTask.Employee);
            return View(employeeTask);
        }

        /// <summary>
        /// معالجة التعديلات المقدمة لمهمة وحفظها.
        /// </summary>
        /// <param name="model">البيانات المحدثة للمهمة.</param>
        /// <returns>إذا نجحت العملية، يتم عرض صفحة الفهرس؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(CModels.TasksCreate model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    if (new TaskClass().update(model, _context, _hosting).Result) return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    return View(model);
                }
            }
            ViewData["Emloyee"] = new SelectList(_context.SystemUsers.Where(p => p.AccountType == "Employee"), "Id", "Name", model.Employee);
            return View(model);
        }

    }
}