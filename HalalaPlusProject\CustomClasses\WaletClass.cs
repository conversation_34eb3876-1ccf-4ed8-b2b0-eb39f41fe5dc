﻿using HalalaPlusProject.Models;

namespace HalalaPlusProject.CustomClasses
{
    public class WaletClass
    {
        public async Task<Object> intransction(CModels.GrantDiscountModel model, HalalaPlusdbContext _context, long userId,string note,string type="in")
        {

            try
            {

                if (model == null || model.Id == 0)
                {
                    var obj=_context.Walets.Where(p=>p.UserId == userId).FirstOrDefault();
                    if (obj == null) {

                        var temp = new Walet();
                        temp.UserId = model.Id;
                        temp.Amount = model.amount;
                        _context.Add(temp);
                       await _context.SaveChangesAsync();
                        var transction = new WaletTransction();
                        transction.UserId = model.Id;
                        transction.WaletId = temp.Id;
                        transction.InAmount = model.amount;
                        transction.Optype = type;
                        transction.DistUser = userId;
                        transction.Note = note;
                        _context.Add(transction);
                        await _context.SaveChangesAsync();
                        return new { state = 1, message = "تمت العملية بنجاح" };
                    }
                    else
                    {
                        var transction = new WaletTransction();
                        transction.UserId = model.Id;
                        transction.WaletId = obj.Id;
                        transction.InAmount = model.amount;
                        transction.Optype = type;
                        transction.DistUser = userId;
                        transction.Note = note;
                        _context.Add(transction);
                        await _context.SaveChangesAsync();
                        obj.Amount += model.amount;
                        _context.Update(obj);
                        await _context.SaveChangesAsync();
                        return new { state = 1, message = "تمت العملية بنجاح" };
                    }

                   
                    
                }
                return new { state = 0, message = "ليس هناك بيانات" };
            }
            catch (Exception)
            {

                return new { state = 0, message = "خطاء " };
            }

        }

        public async Task<Object> outtransction(CModels.GrantDiscountModel model, HalalaPlusdbContext _context, long userId, string note, string type = "out")
        {

            try
            {

                if (model == null || model.Id == 0)
                {
                    var obj = _context.Walets.Where(p => p.UserId == userId).FirstOrDefault();
                    if (obj == null)
                    {

                        var temp = new Walet();
                        temp.UserId = model.Id;
                        temp.Amount = -model.amount;
                        _context.Add(temp);
                        await _context.SaveChangesAsync();
                        var transction = new WaletTransction();
                        transction.UserId = model.Id;
                        transction.WaletId = temp.Id;
                        transction.InAmount = model.amount;
                        transction.Optype = type;
                        transction.DistUser = userId;
                        transction.Note = note;
                        _context.Add(transction);
                        await _context.SaveChangesAsync();
                        return new { state = 1, message = "تمت العملية بنجاح" };
                    }
                    else
                    {
                        var transction = new WaletTransction();
                        transction.UserId = model.Id;
                        transction.WaletId = obj.Id;
                        transction.InAmount = model.amount;
                        transction.Optype = type;
                        transction.DistUser = userId;
                        transction.Note = note;
                        _context.Add(transction);
                        await _context.SaveChangesAsync();
                        obj.Amount -= model.amount;
                        _context.Update(obj);
                        await _context.SaveChangesAsync();
                        return new { state = 1, message = "تمت العملية بنجاح" };
                    }



                }
                return new { state = 0, message = "ليس هناك بيانات" };
            }
            catch (Exception)
            {

                return new { state = 0, message = "خطاء " };
            }

        }
    }
}
