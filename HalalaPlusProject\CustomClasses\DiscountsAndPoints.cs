﻿using HalalaPlusProject.Models;

namespace HalalaPlusProject.CustomClasses
{
    public class DiscountsAndPoints
    {
        public async Task<long> NewDiscountAsync(DiscountsTable model, HalalaPlusdbContext _context) {

            _context.Add(model);
            _ = await _context.SaveChangesAsync();
            return model.Id;
        }
        public async Task<long> NewPointAsync(PointsTable model, HalalaPlusdbContext _context)
        {

            _context.Add(model);
            _ = await _context.SaveChangesAsync();
            return model.Id;
        }

    }
}
