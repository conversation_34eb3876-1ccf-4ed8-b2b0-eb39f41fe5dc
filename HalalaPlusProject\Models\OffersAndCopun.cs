﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class OffersAndCopun
{
    [Key]
    public long Id { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? StartDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? EndDate { get; set; }

    [StringLength(1500)]
    public string? Details { get; set; }

    [StringLength(500)]
    public string? StoreLink { get; set; }

    [Column("isActive")]
    public bool? IsActive { get; set; }

    [StringLength(500)]
    public string? Img { get; set; }

    public long? MasterId { get; set; }

    public bool Deleted { get; set; }

    [Column("recType")]
    [StringLength(20)]
    public string? RecType { get; set; }

    [Column("overView")]
    [StringLength(500)]
    public string? OverView { get; set; }

    [StringLength(20)]
    public string? CopunCode { get; set; }

    public double? Discount { get; set; }

    public int? Activity { get; set; }

    [StringLength(1500)]
    public string? EnName { get; set; }

    public string? EnDetails { get; set; }

    [StringLength(1500)]
    public string? EnoverView { get; set; }

    [StringLength(250)]
    public string? StoreName { get; set; }

    [StringLength(250)]
    public string? EnStoreName { get; set; }

    public bool IsOwnedByProvider { get; set; }

    [InverseProperty("DiscountOffer")]
    public virtual ICollection<CustomerDiscount> CustomerDiscounts { get; set; } = new List<CustomerDiscount>();
}
