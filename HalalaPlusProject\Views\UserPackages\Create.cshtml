﻿@model HalalaPlusProject.CModels.PackageViewModel
@{
    ViewData["Title"] = "إنشاء";
}
<h1>إنشاء</h1>
<h4>الباقة</h4>
<hr />
<div class="row">
    <div class="col-md-12">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group"><label asp-for="Name" class="control-label">الاسم</label><input asp-for="Name" class="form-control" /></div>
                    <div class="form-group"><label asp-for="Characters" class="control-label">مميزات الباقة</label><textarea asp-for="Characters" class="form-control"></textarea></div>
                    <div class="form-group"><label asp-for="Price" class="control-label">السعر</label><input asp-for="Price" class="form-control" /></div>
                    <div class="form-group"><label asp-for="Period" class="control-label">الفترة</label><input asp-for="Period" class="form-control" /></div>
                </div>
                <div class="col-md-4">
                    <div class="form-group"><label asp-for="EnName" class="control-label">الاسم بالإنجليزية</label><input asp-for="EnName" class="form-control" /></div>
                    <div class="form-group"><label asp-for="EnCharacters" class="control-label">مميزات بالإنجليزية</label><textarea asp-for="EnCharacters" class="form-control"></textarea></div>
                    <div class="form-group"><label asp-for="EnPeriod" class="control-label">الفترة بالإنجليزية</label><input asp-for="EnPeriod" class="form-control" /></div>
                    <div class="form-group"><label asp-for="PackageDays" class="control-label">أيام الباقة</label><input asp-for="PackageDays" class="form-control" type="number" /></div>
                </div>
                <div class="col-md-4">
                    <!-- STRONGLY-TYPED FEATURE FIELDS -->
                    <div class="form-group">
                        <label asp-for="MaxOffersAllowed" class="control-label">الحد الأقصى للعروض</label>
                        <input asp-for="MaxOffersAllowed" class="form-control" type="number" value="0" />
                    </div>
                    <div class="form-group">
                        <label asp-for="MaxMonyBoxesAllowed" class="control-label">الحد الأقصى للحصالات</label>
                        <input asp-for="MaxMonyBoxesAllowed" class="form-control" type="number" value="0" />
                    </div>
                    <hr />
                    <!-- DYNAMIC FEATURES PARTIAL VIEW -->
                    @await Html.PartialAsync("_PackageFeatures", Model.Features)
                </div>
            </div>
            <div class="form-group mt-3"><input type="submit" value="إنشاء" class="btn btn-primary" /></div>
        </form>
    </div>
</div>
<div class="mt-2"><a asp-action="Index">الرجوع إلى القائمة</a></div>
@section Scripts {
    <!-- The JavaScript for adding/removing rows remains the same -->
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}