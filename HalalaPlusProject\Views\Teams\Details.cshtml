﻿@model HalalaPlusProject.Models.Team

@{
    ViewData["Title"] = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Details</h1>

<div>
    <h4>Team</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Name)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.EnName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Image)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Image)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Major)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Major)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.EnMajor)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnMajor)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
