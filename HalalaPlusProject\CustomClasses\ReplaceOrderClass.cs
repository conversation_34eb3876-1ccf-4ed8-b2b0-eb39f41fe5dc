﻿using System.Linq;
using HalalaPlusProject.CModels;
using HalalaPlusProject.Models;

namespace HalalaPlusProject.CustomClasses
{
    public class ReplaceOrderClass
    {
        public List<ReplaceOrderModel> retrive(long userId, HalalaPlusdbContext _context)
        {
            try
            {
                var x=_context.CustomerRewardSettings.FirstOrDefault();
               // if (userId != 0)
                    return _context.PointReplaceOrders.Where(p => p.ProviderId == userId&& p.State=="waiting").Select(l => new ReplaceOrderModel { Id = l.Id,PhoneNo=l.User.PhoneNo, PointsNo = l.PointCount, Amount = (l.PointCount/(int)x.PointValueSar), CustomerName = l.User.Name ,Status=l.State,date=l.OrderDate}).ToList().OrderByDescending(p => p.Id).ToList();
           
            }
            catch (Exception)
            {
                return new List<ReplaceOrderModel>();
            }
            //return new List<ReplaceOrderModel>();
        }
        public List<ReplaceOrderModel> retrive1( HalalaPlusdbContext _context)
        {
            try
            {
                var x = _context.CustomerRewardSettings.FirstOrDefault();
                var result = _context.PointReplaceOrders
    .Where(p => p.ProviderId == null && p.State == "waiting")
    .OrderByDescending(l => l.Id)
    .Select(l => new ReplaceOrderModel
    {
        Id = l.Id,
        PhoneNo = l.User.PhoneNo,
        PointsNo = l.PointCount,
        Amount = (l.PointCount / (int)x.PointValueSar),
        CustomerName = l.User.Name,
        Status = l.State,
        date = l.OrderDate
    })
    .ToList();
                return result;
                //  return _context.PointReplaceOrders.Where(p => p.ProviderId == null && p.State == "waiting").Select(l => new ReplaceOrderModel { Id = l.Id, PhoneNo = l.User.PhoneNo, PointsNo = l.PointCount, Amount = (l.PointCount / (int)x.PointValueSar), CustomerName = l.User.Name, Status = l.State, date = l.OrderDate }).ToList().OrderByDescending(p => p.Id);

            }
            catch (Exception)
            {
                //return _context.PointReplaceOrders.Where(p => p.ProviderId == null && p.State == "waiting").Select(l => new ReplaceOrderModel { Id = l.Id, PhoneNo = l.User.PhoneNo, PointsNo = l.PointCount, Amount = 0, CustomerName = l.User.Name, Status = l.State, date = l.OrderDate }).ToList().OrderByDescending(o=>o.Id);
            }
            return new List<ReplaceOrderModel>();
        }

    }
}
