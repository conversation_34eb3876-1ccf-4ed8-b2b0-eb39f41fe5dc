﻿@model HalalaPlusProject.Models.MonyBoxsPoint

@{
    ViewData["Title"] = "التفاصيل";
}

<h1>التفاصيل</h1>

<div>
    <h4>النقاط</h4>
    <hr />
    <dl class="row">
        <div class="col-md-3">
            <dt>من القيمة</dt>
            <dd>@Html.DisplayFor(model => model.FromAmount)</dd>
        </div>
        <div class="col-md-3">
            <dt>إلى القيمة</dt>
            <dd>@Html.DisplayFor(model => model.ToAmount)</dd>
        </div>
        <div class="col-md-3">
            <dt>النقاط الممنوحة</dt>
            <dd>@Html.DisplayFor(model => model.GrntedPoints)</dd>
        </div>
        <div class="col-md-3">
            <dt>تاريخ الإنشاء</dt>
            <dd>@Html.DisplayFor(model => model.CreateAt)</dd>
        </div>
        <div class="col-md-3">
            <dt>محذوف</dt>
            <dd>@Html.DisplayFor(model => model.Deleted)</dd>
        </div>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id">تعديل</a> |
    <a asp-action="Index">العودة إلى القائمة</a>
</div>
