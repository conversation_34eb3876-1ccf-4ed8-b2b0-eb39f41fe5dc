﻿@model IEnumerable<HalalaPlusProject.Models.BanksAccount>
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["banklist"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<div class="row">
    <div class="col-md-12">
        <div class="row">
        <div class="col-md-4">
                <a asp-action="Create">@localizer["bulkaccountsettings"]</a>
         </div>
         <div class="col-md-4">
                <a asp-action="Create">@localizer["bankaccountsettings"]</a>
         </div>
         <div class="col-md-4">
                <a asp-action="Create" class="btn btn-primary">@localizer["addbank"]</a>
         </div>
         </div>
    </div>
    <h3>@localizer["serviceproviders"]</h3>
    
                </div>

<table id="tbl1" class="table">
    <thead>
        <tr>
            <th>
                @localizer["bankname"]
            </th> <th>
                EnBankName
            </th>
            <th>
                @localizer["accountno"]
            </th>
            <th>
                @localizer["connectioncode"]
            </th>
            <th>@localizer["options"]</th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.BankName)
            </td>  <td>
                    @Html.DisplayFor(modelItem => item.EnBankName)
            </td>
            <td>
               @Html.DisplayFor(modelItem => item.AccountNumber)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.ConnectionCode)
            </td>
            <td>
                    <a asp-action="Edit" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id">@localizer["edit"]</a> |
                    <a asp-action="Details" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id">@localizer["details"]</a> |
                    <a asp-action="Delete" class="btn btn-outline-danger tablebtn" asp-route-id="@item.Id">@localizer["delete"]</a>
            </td>
        </tr>
}
    </tbody>
</table>
@section Scripts{
    <script>
  let table = new DataTable('#tbl1');
  

    </script>
}