﻿ @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["addserviceprovider"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div>
    <a href="~/ServiceProvider/index">
        <img src="../assets/img/svgs/solid/arrow-right.svg" style="width: 40px;" alt=""></a>
    <h3> @localizer["serviceproviders"]</h3>

</div>
<div class="row">
    <div class="col-md-12">
        <form asp-action="impotexcel" enctype="multipart/form-data" >
            <div class="row">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label  class="control-label">file 
                            <span style="font-size:20pt; color: red;">&nbsp*</span></label>
                        <input type="file" name="file" required class="form-control" />
                    </div>
          </div>
            </div>
            <div class="mt-2">
                <button type="submit" value="Create" class="btn btn-primary">@localizer["save"]</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>
@* <script src="https://polyfill.io/v3/polyfill.min.js?features=default"></script> *@
<script> (g => { var h, a, k, p = "The Google Maps JavaScript API", c = "google", l = "importLibrary", q = "__ib__", m = document, b = window; b = b[c] || (b[c] = {}); var d = b.maps || (b.maps = {}), r = new Set, e = new URLSearchParams, u = () => h || (h = new Promise(async (f, n) => { await (a = m.createElement("script")); e.set("libraries", [...r] + ""); for (k in g) e.set(k.replace(/[A-Z]/g, t => "_" + t[0].toLowerCase()), g[k]); e.set("callback", c + ".maps." + q); a.src = `https://maps.${c}apis.com/maps/api/js?` + e; d[q] = f; a.onerror = () => h = n(Error(p + " could not load.")); a.nonce = m.querySelector("script[nonce]")?.nonce || ""; m.head.append(a) })); d[l] ? console.warn(p + " only loads once. Ignoring:", g) : d[l] = (f, ...n) => r.add(f) && u().then(() => d[l](f, ...n)) })
        ({ key: "AIzaSyBRDq842x31n-rAmjPQ7hZotsZaGvbjI_U", v: "weekly" });</script>

@* <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBRDq842x31n-rAmjPQ7hZotsZaGvbjI_U&language=ar&callback=initMap&libraries=&v=weekly" async></script>
 *@
@* <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyB7hT8IIEb4bAxpdczoZqy_Ld_IBofuZwo&language=ar&callback=initMap&libraries=&v=weekly"async></script> *@
@* 
    <script>  function initMap() {
        const infowindow = new google.maps.InfoWindow();
        infowindow.setContent('اختار الموقع الذي تريد توصيل الطلب الية');
        const myLatlng = {
            lat: 24.623265,
            lng: 46.5399469
        };
        const map = new google.maps.Map(document.getElementById("map"), {
            zoom: 11,
            center: myLatlng,
        });
        marker = new google.maps.Marker({
            position: myLatlng,
            map: map,
            title: 'الموقع'
        });
        infowindow.open(map, marker);
        map.addListener("click", (mapsMouseEvent) => {
            // console.log(mapsMouseEvent.latLng.lat());
            var result = {
                lat: mapsMouseEvent.latLng.lat(),
                lng: mapsMouseEvent.latLng.lng()
            };
            $('.title').empty();
            $('input[name="lat"]').val(mapsMouseEvent.latLng.lat());
            $('input[name="lng"]').val(mapsMouseEvent.latLng.lng());
            $('input[name="address"]').val('العنوان');
            marker.setPosition(result);

        });

    }
    </script> *@
@* <script src="https://polyfill.io/v3/polyfill.min.js?features=default"></script> *@
@*       
    <script> async function initMap() {

  // Request needed libraries.
  const { Map } = await google.maps.importLibrary("maps");
  const myLatlng = { lat:  20.99265387585728, lng:40.54420880475375};

  const map = new google.maps.Map(document.getElementById("map"), {
    zoom: 6,
    center: myLatlng,
  });

        // Create the initial InfoWindow.
  let infoWindow = new google.maps.InfoWindow({
    // content: "Click the map to get Lat/Lng!",
    position: myLatlng,
    name:'KSA'
  });
  var marker = new google.maps.Marker({  
                   position: myLatlng,  
                   map: map,  
                   title: 'Halala Plus'  
               });

        infoWindow.open(map, marker);
  // Configure the click listener.
  map.addListener("click", (mapsMouseEvent) => {
      document.getElementById('Lat').value =mapsMouseEvent.latLng.lat();
            document.getElementById('lng').value = mapsMouseEvent.latLng.lng();
    // console.log("clicked:",myLatlng);
    // Close the current InfoWindow.
            // var result = {
            //     lat: mapsMouseEvent.latLng.lat(),
            //     lng: mapsMouseEvent.latLng.lng()
            // };

            infoWindow.close();

    infoWindow.open(map);
  });
}

initMap();
  </script> *@
