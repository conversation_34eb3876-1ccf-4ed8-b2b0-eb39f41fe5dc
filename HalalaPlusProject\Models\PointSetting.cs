﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class PointSetting
{
    [Key]
    public long Id { get; set; }

    public double? Sales { get; set; }

    public double? Points { get; set; }

    [Column("userId")]
    public long? UserId { get; set; }
}
