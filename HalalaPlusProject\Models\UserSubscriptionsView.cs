﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class UserSubscriptionsView
{
    public long Id { get; set; }

    public long? UserId { get; set; }

    public int? PackId { get; set; }

    [Column("state")]
    [StringLength(500)]
    public string? State { get; set; }

    [Column("isSubscribed")]
    public bool? IsSubscribed { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? StartDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? EndDate { get; set; }

    [Column("price")]
    public double? Price { get; set; }

    [StringLength(1500)]
    public string? Name { get; set; }

    [Column("characters")]
    public string? Characters { get; set; }

    [StringLength(250)]
    public string? Period { get; set; }

    public int? PackageDays { get; set; }

    [StringLength(1500)]
    public string? EnName { get; set; }

    public string? EnCharacters { get; set; }

    [StringLength(1500)]
    public string? EnPeriod { get; set; }

    [Column("isActive")]
    public bool? IsActive { get; set; }

    [StringLength(50)]
    public string? PackageState { get; set; }
}
