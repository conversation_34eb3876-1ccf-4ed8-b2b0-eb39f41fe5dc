﻿@model HalalaPlusProject.Models.GetProviderStatistics 
@using System.Globalization
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer

@{
    ViewBag.Title = @localizer["Providerstatistics"];
}

<h2 class="mb-4">@localizer["Providerstatistics"]</h2>

 
<div class="d-flex justify-content-center mb-4">
    <div style="width: 100%; height: 450px;">
        <canvas id="statisticsChart"></canvas>
    </div>
</div>

 
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    const ctx = document.getElementById('statisticsChart').getContext('2d');

    const chart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: [
                ' @localizer["Numberofdiscounts"]',
                ' @localizer["Numberofoffers"]',
                '@localizer["Numberofdiscountcodes"] ',
                '@localizer["Numberofcodesused"] '
            ],
            datasets: [{
                label: '@localizer["Statisticsnumbers"] ',
                data: [
                    @Model.DiscountsCount,
                    @Model.OffersCount,
                    @Model.CodeCount,
                    @Model.UsedDiscountCodes
                ],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(255, 159, 64, 0.7)',
                    'rgba(153, 102, 255, 0.7)',
                    'rgba(255, 99, 132, 0.7)'
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 159, 64, 1)',
                    'rgba(153, 102, 255, 1)',
                    'rgba(255, 99, 132, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: true
                },
                tooltip: {
                    enabled: true
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
</script> 
<div class="row mt-5">
    <div class="col-md-4 mb-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <h5 class="card-title">@localizer["Totalvalueofdiscounts"] </h5>
                <p class="card-text">@Model.TotalDiscountAmount.ToString("N2")</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <h5 class="card-title">@localizer["Totalvalueofoffers"]</h5>
                <p class="card-text">@Model.TotalOffersCount.ToString("N2")</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <h5 class="card-title">@localizer["Totalvalueofdiscountcodes"]</h5>
                <p class="card-text">@Model.TotalCodeCount.ToString("N2")</p>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <h5 class="card-title">@localizer["Totalpointsawarded"]</h5>
                <p class="card-text">@Model.TotalGrantedPoints.ToString("N2")</p>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <h5 class="card-title">@localizer["Totaldiscountsactuallygranted"]</h5>
                <p class="card-text">@Model.TotalGrantedDiscount.ToString("N2")</p>
            </div>
        </div>
    </div>
</div>
