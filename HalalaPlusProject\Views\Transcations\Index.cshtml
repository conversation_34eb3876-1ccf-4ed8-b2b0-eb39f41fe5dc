﻿@model HalalaPlusProject.Entities.MonyBoxsTransctionsViewModel
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer

@{
    ViewData["Title"] = "العمليات المالية";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    .modal-backdrop.show{
        z-index:auto
    }
</style>
<div class="tab">
    <button class="tablinks active" onclick="openCity(event, 'retrivemony')"> طلبات الاستعادة</button>
    <button class="tablinks" onclick="openCity(event, 'monyboxs')">عمليات الحصالات</button>
    <button class="tablinks" onclick="openCity(event, 'Orders')">طلبات الدفع</button>
</div>

<div id="retrivemony" style="display: block;" class="tabcontent">
    <div class="row">


        <div class="col-md-12">
            <div class="col-md-12">
                <div class="table-responsive p-0">

                    <table id="tbl12" class="table table-striped text-center">
                        <thead>
                            <tr>


                                <th scope="col">الرقم</th>
                                <th scope="col"> اسم الشخص</th>
                                <th scope="col"> المبلع</th>
                                <th scope="col"> الحصالة</th>
                                <th scope="col"> البنك</th>
                                <th scope="col"> الايبان</th>
                               
                                <th scope="col">تاريخ الطلب </th>
                                <th scope="col">حالة الطلب </th>
                                <th scope="col">تم الدفع </th>
                                <th scope="col">تاريخ التحقق </th>
                                <th scope="col"> رقم العملية </th>

                                <th scope="col"> @localizer["options"]</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.retrivemonyorders)
                            {
                                <tr id="<EMAIL>">
                                    <td>
                                        @Html.DisplayFor(modelItem => item.Id)
                                    </td>

                                    <td>
                                        @Html.DisplayFor(modelItem => item.Name)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.Amount)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.MonyBox)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.BankName)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.Iban)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.CreateAt)
                                    </td>
                                   
                                    <td>
                                        @if (item.Status == "wait")
                                        {
                                            <p>تم الرفع</p>
                                        }
                                        else if (item.Status == "processing")
                                        {
                                            <p>قيد المعالجة</p>
                                            
                                        } 
                                        else if (item.Status == "canceled")
                                        {
                                            <p> ملغي</p>
                                            
                                        }else if (item.Status == "rejected")
                                        {
                                            <p>مرفوض</p>

                                        }
                                        else
                                        {
                                              <p>تم التحويل</p>
                                            
                                        }

                                    </td>
                                    <td>
                                        @if(item.Payed==true){
                                            <p>نعم</p>
                                        }else
                                        {
                                               <p>لا</p>
                                            
                                        }
                                        
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.PayDate)
                                    </td>
                                     <td>
                                        @Html.DisplayFor(modelItem => item.PaymentId)
                                    </td>

                                    <td>
                                        @if (item.Status == "wait")
                                        {<button type="button" class="btn btn-primary mb-0" onclick="openAcceptModal(@item.Id);">
    قبول
</button>

                                          @*   <button type="button" class="btn btn-primary mb-0" onclick="accept(@item.Id);">
                                                قبول
                                            </button> *@
                                            <button type="button" class="btn btn-primary mb-0" onclick="reject(@item.Id);">
                                                رفض
                                            </button>
                                        } @if (item.Status == "processing")
                                        {
                                            <button type="button" class="btn btn-primary w-50" onclick="reject(@item.Id);">
                                                رفض
                                            </button>
                                        }
                                    <a asp-controller="Transcations" asp-action="Edit" asp-route-id="@item.Id" class="btn btn-outline-info tablebtn">@localizer["showdetails"] </a>
                                   

                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>
</div>

<div id="Orders" class="tabcontent" >
    <div class="row">


        <div class="col-md-12">
            <div class="col-md-12">
                <div class="table-responsive p-0">

                    <table id="tbl13" class="table table-striped text-center">
                        <thead>
                            <tr>


                                <th scope="col">الرقم</th>
                                <th scope="col"> اسم الشخص</th>
                                <th scope="col"> المبلع</th>
                                @* <th scope="col">سحب </th> *@
                              @*   <th scope="col">الحصالة </th> *@
                                <th scope="col">التاريخ </th>
                                <th scope="col">تم التحقق </th>
                                <th scope="col">تاريخ التحقق </th>

                                <th scope="col"> @localizer["options"]</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.PaymentsOrdersTrans)
                            {
                                <tr id="<EMAIL>">
                                    <td>
                                        @Html.DisplayFor(modelItem => item.Id)
                                    </td>

                                    <td>
                                        @Html.DisplayFor(modelItem => item.Name)
                                    </td>
                                   
                                    <td>
                                        @Html.DisplayFor(modelItem => item.Amount)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.CreateAt)
                                    </td>
                                    <td>
                                        @if(item.IsVerified==true){
                                            <p>True</p>
                                        }else
                                        {
                                               <p>False</p>
                                            
                                        }
                                        
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.VerfayDate)
                                    </td>
                                    <td>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>
</div>

<div id="monyboxs" class="tabcontent" style="display:block">
    <div class="row">


        <div class="col-md-12">
            <div class="col-md-12">
                <div class="table-responsive p-0">

                    <table id="tbl14" class="table table-striped text-center">
                        <thead>
                            <tr>


                                <th scope="col">الرقم</th>
                                <th scope="col"> اسم الشخص</th>
                                <th scope="col"> دفع</th>
                                <th scope="col">سحب </th>
                                <th scope="col">الحصالة </th>
                                <th scope="col">التاريخ </th>
                                <th scope="col">تم التحقق </th>
                                <th scope="col">تاريخ التحقق </th>

                                <th scope="col"> @localizer["options"]</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.MonyBoxsTrans)
                            {
                                <tr id="<EMAIL>">
                                    <td>
                                        @Html.DisplayFor(modelItem => item.Id)
                                    </td>

                                    <td>
                                        @Html.DisplayFor(modelItem => item.Name)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.Credit)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.Debit)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.MonyBox)
                                    </td> 
                                    <td>
                                        @Html.DisplayFor(modelItem => item.OperationDate)
                                    </td>
                                   
                                     <td>
                                        @if (item.IsVerified == true)
                                        {
                                            <p>True</p>
                                        }
                                        else
                                        {
                                            <p>False</p>

                                        }
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.VerifayDate)
                                    </td>

                                    <td>
                                        @* <a asp-controller="Transcations" asp-action="Edit" asp-route-id="@item.Id" class="btn btn-outline-info tablebtn">@localizer["showdetails"] </a> *@
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="acceptModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">تفاصيل طلب السحب</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body" id="acceptModalBody">
            <!-- هيتم تحميل الصفحة Edit هنا بالـ Ajax -->
        </div>
    </div>
  </div>
</div>

@section Scripts{
    <script>
        function openAcceptModal(id) {
    $.get("/Transcations/popup/" + id, function (html) {
        $("#acceptModalBody").html(html);
        $("#acceptModal").modal("show");
    });
}

        function accept(id) {
            Swal.fire({
                title: "هل انت متاكد من قبول الطلب؟",
                type: "warning",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                cancelButtonText: "لا",
                confirmButtonText: "نعم",

            }).then((result) => {
                if (result.value) {

                    $.ajax({
                     url: "/Transcations/Accept/" + id,

                        
                        type: "POST",
    dataType: "JSON",
                        success: function (data) {
                            if (data.state == 1) {
                                Swal.fire({
                                    title: 'نجـــاح',
                                    html: data.message,
                                    icon: "success",
                                    type: "success",

                                    confirmButtonText: "Ok",
                                }).then((result) => {
                                    if (result.value) {

                                        window.location.reload();
                                    }
                                });
                            }

                            else {
                                Swal.fire({
                                    type: 'error',
                                    title: 'خطاء...',
                                    text: data.message,
                                })
                            }

                        },
                        error: function (xhr, desc, err) {
                            Swal.fire({
                                type: 'error',
                                title: 'خطاء...',
                                text: "هنالك خطاء تاكد من صحة البيانات",
                            })
                        }
                    });
                }
            });
        }
        function reject(id) {
            Swal.fire({
                title: "هل انت متاكد من رفض الطلب؟",
                type: "warning",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                cancelButtonText: "لا",
                confirmButtonText: "نعم",

            }).then((result) => {
                if (result.value) {

                    $.ajax({
                        url: "/Transcations/reject/"+id,
                       type: "POST",
    dataType: "JSON",
                        success: function (data) {
                            if (data.state == 1) {
                                Swal.fire({
                                    title: 'نجـــاح',
                                    html: data.message,
                                    icon: "success",
                                    type: "success",

                                    confirmButtonText: "Ok",
                                }).then((result) => {
                                    if (result.value) {

                                        window.location.reload();
                                    }
                                });
                            }

                            else {
                                Swal.fire({
                                    type: 'error',
                                    title: 'خطاء...',
                                    text: data.message,
                                })
                            }

                        },
                        error: function (xhr, desc, err) {
                            Swal.fire({
                                type: 'error',
                                title: 'خطاء...',
                                text: "هنالك خطاء تاكد من صحة البيانات",
                            })
                        }
                    });
                }
            });
        }


        $(".removecretria").on("submit", function (event) {
            event.preventDefault();
            var data = new FormData(this);
            var url = $(this).attr("action");
            console.log("item");
            Swal.fire({
                title: "هل انت متاكد من قبول الطلب؟",
                type: "warning",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                cancelButtonText: "لا",
                confirmButtonText: "نعم",

            }).then((result) => {
                if (result.value) {

                    $.ajax({
                        url: url,
                        type: "POST",
                        dataType: "JSON",
                        data: new FormData(this),
                        processData: false,
                        contentType: false,
                        success: function (data) {
                            if (data.state == 1) {
                                Swal.fire({
                                    title: 'نجـــاح',
                                    html: data.message,
                                    icon: "success",
                                    type: "success",

                                    confirmButtonText: "Ok",
                                }).then((result) => {
                                    if (result.value) {

                                        window.location.reload();
                                    }
                                });
                            }

                            else {
                                Swal.fire({
                                    type: 'error',
                                    title: 'خطاء...',
                                    text: data.message,
                                })
                            }

                        },
                        error: function (xhr, desc, err) {
                            Swal.fire({
                                type: 'error',
                                title: 'خطاء...',
                                text: "هنالك خطاء تاكد من صحة البيانات",
                            })
                        }
                    });
                }
            });
        });
        let table = new DataTable('#tbl13');
        let table2 = new DataTable('#tbl12');
        let table1 = new DataTable('#tbl14');
    </script>
}
