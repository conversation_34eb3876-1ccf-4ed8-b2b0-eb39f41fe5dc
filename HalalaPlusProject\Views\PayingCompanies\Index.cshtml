﻿@model IEnumerable<HalalaPlusProject.CModels.PayingCompaniesModel>
    @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = @localizer["payingcom"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["payingcom"] </h2>

<p>
    <a asp-action="Create">@localizer["addpayingcompany"]</a>
</p>
<table id="tbl1" class="table">
    <thead>
        <tr>
            <th>
                @localizer["payingcompany"]
            </th> <th>
                اسم الشركة انجليزي
            </th>
            <th>
                @localizer["connectioncode"]
            </th>
            <th>
                @localizer["opdetails"]
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EnName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.ConnectionCode)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.OperationDetils)
            </td>
            <td>
                    <a asp-action="Edit" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id">@localizer["edit"]</a> |
                    <a asp-action="Details" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id">@localizer["details"]</a> |
                    <a asp-action="Delete" class="btn btn-outline-danger tablebtn" asp-route-id="@item.Id">@localizer["delete"]</a>
            </td>
        </tr>
}
    </tbody>
</table>
@section Scripts{
    <script>
  let table = new DataTable('#tbl1');

    </script>
}
