﻿using Google.Cloud.Firestore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using System.Linq;
using HalalaPlusProject.Utils;

namespace HalalaPlusProject.Services
{
    public interface IFirebaseNotificationHistoryService
    {
        Task AddNotificationToHistory(string title, string body, string deviceToken = null, string topic = null);
        Task<List<NotificationHistoryItem>> GetNotificationHistory();
        Task<string> SendNotification(string title, string body, string deviceToken = null, string topic = null);
        Task<List<string>> SendMulticastNotification(string title, string body, List<string> deviceTokens);
    }

    public class FirebaseNotificationHistoryService : IFirebaseNotificationHistoryService
    {
        private readonly FirestoreDb _db;
        private readonly ILogger<FirebaseNotificationHistoryService> _logger;
        private readonly IConfiguration _configuration;
        private const string CollectionName = "notifications";
        private readonly FirebaseMessaging _messaging;

        public FirebaseNotificationHistoryService(ILogger<FirebaseNotificationHistoryService> logger, IConfiguration configuration)
        {
            
            _logger = logger;
            _configuration = configuration; 
            _logger.LogError($"Firebase configuration !");
            string projectId = configuration["FirebaseProjectId"];
            
            if (string.IsNullOrEmpty(projectId))
            {
                _logger.LogError("FirebaseProjectId is not set in configuration!");
                TelegramHandler1.Instance.SendSingleSMSwithoutasync("FirebaseProjectId is not set in configuration!");
                throw new InvalidOperationException("FirebaseProjectId is not set in configuration.  Firebase functionality will not work.");
            }

            try
            {
                _logger.LogError($"try FirestoreDb.Create !");
                _db = FirestoreDb.Create(projectId);

                // Initialize Firebase Admin SDK and FirebaseMessaging
                if (FirebaseApp.DefaultInstance == null)
                {
                    string firebaseCredentialsPath = configuration["FirebaseCredentialsPath"]; // Get credentials path
                    TelegramHandler1.Instance.SendSingleSMSwithoutasync("FirebaseCredentialsPath : " + firebaseCredentialsPath);
                    if (string.IsNullOrEmpty(firebaseCredentialsPath))
                    {
                        _logger.LogError("FirebaseCredentialsPath is not set in configuration!");
                        TelegramHandler1.Instance.SendSingleSMSwithoutasync("FirebaseCredentialsPath is not set in configuration!");
                        throw new InvalidOperationException("FirebaseCredentialsPath is not set in configuration. Firebase messaging will not work.");
                    }

                    FirebaseApp.Create(new AppOptions()
                    {
                        Credential = GoogleCredential.FromFile(firebaseCredentialsPath),
                    });
                }
                _messaging = FirebaseMessaging.DefaultInstance;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating FirestoreDb or initializing Firebase Admin SDK. Check your FirebaseProjectId and credentials.");
                TelegramHandler1.Instance.SendSingleSMSwithoutasync("error : " + ex.Message);
                throw;
            }
        }




        public async Task AddNotificationToHistory(string title, string body, string deviceToken = null, string topic = null)
        {
            try
            {
                CollectionReference collection = _db.Collection(CollectionName);
                Dictionary<string, object> data = new Dictionary<string, object>()
                {
                    { "title", title },
                    { "body", body },
                    { "deviceToken", deviceToken ?? string.Empty },
                    { "topic", topic ?? string.Empty },
                    { "timestamp", Timestamp.FromDateTime(DateTime.UtcNow) },
                    { "sentBy", "Admin" }
                };
                await collection.AddAsync(data);
                _logger.LogInformation("Notification added to history: {Title}", title);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding notification to history: {Title}", title);
            }
        }

        public async Task<List<NotificationHistoryItem>> GetNotificationHistory()
        {
            List<NotificationHistoryItem> notificationList = new List<NotificationHistoryItem>();
            try
            {
                CollectionReference collection = _db.Collection(CollectionName);
                QuerySnapshot snapshot = await collection.OrderByDescending("timestamp").Limit(20).GetSnapshotAsync();

                foreach (DocumentSnapshot document in snapshot.Documents)
                {
                    if (document.Exists)
                    {
                        NotificationHistoryItem notification = document.ConvertTo<NotificationHistoryItem>();
                        notificationList.Add(notification);
                    }
                }
                return notificationList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notification history", ex);
                return new List<NotificationHistoryItem>();
            }
        }

        public async Task<string> SendNotification(string title, string body, string deviceToken = null, string topic = null)
        {
            try
            {
                var message = new Message()
                {
                    Notification = new Notification
                    {
                        Title = title,
                        Body = body,
                    },
                };

                if (!string.IsNullOrEmpty(topic))
                {
                    message.Topic = topic;
                }
                else if (!string.IsNullOrEmpty(deviceToken))
                {
                    message.Token = deviceToken;
                }
                else
                {
                    throw new ArgumentException("Either deviceToken or topic must be specified.");
                }

                // Send the message
                string response = await _messaging.SendAsync(message);
                _logger.LogInformation("Successfully sent message: {Response}", response);

                // Optionally, add the notification to history after successful sending
                await AddNotificationToHistory(title, body, deviceToken, topic);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message");
                return $"Error sending message: {ex.Message}";
            }
        }

        public async Task<List<string>> SendMulticastNotification(string title, string body, List<string> deviceTokens)
        {
            try
            {
                var message = new MulticastMessage()
                {
                    Notification = new Notification
                    {
                        Title = title,
                        Body = body,
                    },
                    Tokens = deviceTokens,
                };

                // Send the message
                var responses = await _messaging.SendEachForMulticastAsync(message);

                // Log Responses
                List<string> errorList = new List<string>();

                for (int i = 0; i < responses.Responses.Count; i++)
                {
                    if (responses.Responses[i].Exception != null)
                    {
                        errorList.Add($"Error to send message to {deviceTokens[i]}: " + responses.Responses[i].Exception.Message);
                        _logger.LogError(responses.Responses[i].Exception, "Error sending message to {Token}", deviceTokens[i]);
                    }
                }

                _logger.LogInformation("Successfully sent multicast message: {SuccessCount} success, {FailureCount} failure", responses.SuccessCount, responses.FailureCount);

                // Optionally, add the notification to history after successful sending - we add to history with device token = null (since it was many devices)
                await AddNotificationToHistory(title, body, deviceToken: null, topic: null);

                return errorList; // Or handle in a better way
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending multicast message");
                return null; // Or handle in a better way
            }
        }
    }

    [FirestoreData]
    public class NotificationHistoryItem
    {
        [FirestoreProperty("title")]
        public string Title { get; set; }

        [FirestoreProperty("body")]
        public string Body { get; set; }

        [FirestoreProperty("deviceToken")]
        public string DeviceToken { get; set; }

        [FirestoreProperty("topic")]
        public string Topic { get; set; }

        [FirestoreProperty("timestamp")]
        public Timestamp Timestamp { get; set; }

        [FirestoreProperty("sentBy")]
        public string SentBy { get; set; }

        public DateTime SentDateTime => Timestamp.ToDateTime();
    }
}