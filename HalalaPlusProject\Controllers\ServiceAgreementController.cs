﻿using HalalaPlusProject.Models;
using iTextSharp.text;
using iTextSharp.text.pdf;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

public class ServiceAgreementController : Controller
{
    private readonly HalalaPlusdbContext _context;
    private readonly IWebHostEnvironment _env;

    public ServiceAgreementController(HalalaPlusdbContext context, IWebHostEnvironment env)
    {
        _context = context;
        _env = env;
    }

    public async Task<IActionResult> IndexProvider()
    {
        var user = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value;
        var users =  _context.SystemUsers.Where(j=>j.MasterId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value)
          
            .FirstOrDefault();

        return View(users);
    }
    public async Task<IActionResult> Index()
    {
        var users = await _context.SystemUsers
            .OrderByDescending(u => u.Id)
            .ToListAsync();

        return View(users);
    }
    [HttpGet]
    public async Task<IActionResult> GeneratePdf(long id)
    {
        var user = await _context.SystemUsers.FirstOrDefaultAsync(u => u.Id == id);
        if (user == null)
            return NotFound();

        try
        {
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);

            using (var memoryStream = new MemoryStream())
            {
                var doc = new Document(PageSize.A4, 30, 30, 30, 30);
                var writer = PdfWriter.GetInstance(doc, memoryStream);
                doc.Open();

                // إنشاء الخطوط
                var (arabicFont, englishFont, titleFont, headerFont, smallFont) = CreateFonts();

                // إضافة المحتوى
                AddFirstPage(doc, user, arabicFont, englishFont, titleFont, headerFont, smallFont);
                doc.NewPage();
                AddSecondPage(doc, arabicFont, englishFont, headerFont, smallFont);
                doc.NewPage();
                AddThirdPage(doc, arabicFont, englishFont, headerFont, smallFont);

                doc.Close();

                byte[] bytes = memoryStream.ToArray();
                return File(bytes, "application/pdf", $"ServiceAgreement_{user.Name}.pdf");
            }
        }
        catch (Exception ex)
        {
            return Content($"خطأ في إنشاء PDF: {ex.Message}");
        }
    }

    private (Font arabicFont, Font englishFont, Font titleFont, Font headerFont, Font smallFont) CreateFonts()
    {
        BaseFont arabicBaseFont;
        string arabicFontPath = Path.Combine(_env.WebRootPath, "fonts", "Amiri-Regular.ttf");

        if (System.IO.File.Exists(arabicFontPath))
        {
            arabicBaseFont = BaseFont.CreateFont(arabicFontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        }
        else
        {
            arabicBaseFont = BaseFont.CreateFont(BaseFont.TIMES_ROMAN, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        }

        BaseFont englishBaseFont = BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.EMBEDDED);

        var arabicFont = new Font(arabicBaseFont, 11, Font.NORMAL);
        var englishFont = new Font(englishBaseFont, 11, Font.NORMAL);
        var titleFont = new Font(arabicBaseFont, 16, Font.BOLD);
        var headerFont = new Font(arabicBaseFont, 13, Font.BOLD);
        var smallFont = new Font(arabicBaseFont, 10, Font.NORMAL);

        return (arabicFont, englishFont, titleFont, headerFont, smallFont);
    }

    private void AddFirstPage(Document doc, SystemUser user, Font arabicFont, Font englishFont, Font titleFont, Font headerFont, Font smallFont)
    {
        AddHeader(doc, arabicFont, englishFont);

        // العنوان الرئيسي
        AddCenteredText(doc, "اتفاقية تعاون وتقديم خدمات لهللة بلس", titleFont, new BaseColor(0, 51, 102), 10f);
        AddCenteredText(doc, "Cooperation and Service Agreement for Halala Plus", englishFont, new BaseColor(0, 51, 102), 15f);

        // النص الإنجليزي
        var englishParagraph = new Paragraph("This agreement is made to establish cooperation and service provision in favor of Halala Plus, based on the following details:", englishFont)
        {
            SpacingAfter = 10f
        };
        doc.Add(englishParagraph);

        // جدول المعلومات
        AddInfoTable(doc, user, arabicFont, englishFont, headerFont);

        doc.Add(new Paragraph(" "));

        // القسم الإنجليزي
        var englishSection = new Paragraph("The parties agree to the following:", englishFont)
        {
            SpacingAfter = 10f
        };
        doc.Add(englishSection);

        // المادة 1 والمواد 2 في الصفحة الأولى
        AddDualArticle(doc,
            "Article 1: Purpose",
            "This agreement establishes cooperation between Halala Plus and the service provider to:\n" +
            "1. Provide exclusive discounts to Halala Plus users on selected products/services.\n" +
            "2. Increase brand visibility of the provider among savings-focused users.\n" +
            "3. Support sales growth by attracting new users through Halala Plus.",
            "المادة 1: هدف الاتفاقية",
            "يهدف هذا العقد إلى إرساء تعاون استراتيجي بين هللة بلس ومقدم الخدمة، ويشمل ما يلي:\n" +
            "1. تقديم خصومات حصرية لعملاء هللة بلس على منتجات أو خدمات محددة.\n" +
            "2. دعم انتشار علامة مقدم الخدمة لدى جمهور هللة بلس المهتم بالتوفير.\n" +
            "3. تعزيز مبيعات مقدم الخدمة عبر استقطاب عملاء جدد من مستخدمي هللة بلس.",
            headerFont, arabicFont);

        AddDualArticle(doc,
            "Article 2: Term",
            "This Agreement starts on the date stated above and remains effective for the period specified in the schedule. " +
            "It renews automatically for an equal term unless either party gives written notice of termination or non-renewal at least thirty (30) days.",
            "المادة 2: المدة",
            "تسري هذه الاتفاقية من تاريخ البدء المذكور في أعلى العقد، وتستمر للمدة المحددة في الجدول، وتتجدد تلقائيا لمدة مماثلة وبالشروط ذاتها، " +
            "ما لم يُخطر أحد الطرفين الآخر برغبته في الإلغاء أو عدم التجديد، وذلك قبل انتهائها بمدة لا تقل عن ثلاثين (30) يومًا.",
            headerFont, arabicFont);
    }

    private void AddSecondPage(Document doc, Font arabicFont, Font englishFont, Font headerFont, Font smallFont)
    {
        AddHeader(doc, arabicFont, englishFont);

        // المادة 3 فقط في الصفحة الثانية
        AddDualArticle(doc,
            "Article 3: Discounts",
            "The Service Provider shall provide the discounts listed in the table below on the agreed products or services.\n\n" +
            "The discount must be granted to Halala Plus customers upon verifying their status, as per instructions issued by Halala Plus. Failure to do so constitutes a material breach of this Agreement.",
            "المادة 3: الخصومات",
            "يلتزم مقدم الخدمة بتقديم الخصومات المحددة في الجدول أدناه، وذلك على المنتجات أو الخدمات المتفق عليها بين الطرفين.\n\n" +
            "ويتعهد مقدم الخدمة بمنح الخصم لعملاء هللة بلس بعد التحقق من صفتهم كعملاء، وفق التعليمات التي يصدرها هللة بلس ويبلغ بها مقدم الخدمة عند الحاجة. " +
            "وأي امتناع عن تقديم الخصم مخالفة جوهرية لأحكام هذه الاتفاقية.",
            headerFont, arabicFont);

        // جدول الخصومات
        AddDiscountsTable(doc, arabicFont, englishFont, smallFont);
        doc.Add(new Paragraph(" "));

        AddDualArticle(doc,
            "Article 3: Discount Display",
            "1. The service provider must ensure that each discount offer is clearly detailed, including:\n" +
            "   - Provider name\n" +
            "   - Covered products or services\n" +
            "   - Discount value\n" +
            "   - Any specific conditions or exclusions\n\n" +
            "2. Offers will be promoted via Halala Plus's official digital channels.",
            "المادة 3: آلية عرض الخصومات",
            "1. يلتزم مقدم الخدمة بضمان وضوح وتفصيل عرض الخصومات، بما يشمل ما يلي:\n" +
            "   - اسم مقدم الخدمة.\n" +
            "   - نوع المنتجات أو الخدمات التي تشملها الخصومات.\n" +
            "   - قيمة الخصم.\n" +
            "   - أي شروط أو استثناءات خاصة بالعرض.\n\n" +
            "2. يتم عرض الخصومات لعملاء هللة بلس من خلال القنوات الرقمية والتسويقية الخاصة بالمنصة.",
            headerFont, arabicFont);
    }

    private void AddThirdPage(Document doc, Font arabicFont, Font englishFont, Font headerFont, Font smallFont)
    {
        AddHeader(doc, arabicFont, englishFont);

        // المادة 4 والمواد 5 في الصفحة الثالثة
        AddDualArticle(doc,
            "Article 4: Special Memberships for the Service Provider's Personnel",
            "Halala Plus may, at its discretion, offer special memberships or benefits to the Service Provider's personnel as an optional feature to support cooperation. Activation is coordinated as needed.\n\n" +
            "Halala Plus may modify or discontinue this feature at any time without obligation.",
            "المادة 4: العضويات الخاصة بمنسوبي مقدم الخدمة",
            "يجوز لهللة بلس، حسب تقديره، تمكين منسوبي مقدم الخدمة من عضويات خاصة أو مزايا إضافية ضمن خدماته، كميزة اختيارية تهدف إلى تعزيز التعاون بين الطرفين، ويتم الترتيب بشأنها بين الطرفين عند الحاجة.\n\n" +
            "ويحتفظ هللة بلس بحقه في تعديل أو إيقاف هذه الميزة متى ما رأى ذلك مناسباً دون أن يترتب على ذلك أي التزام دائم.",
            headerFont, arabicFont);

        AddDualArticle(doc,
            "Article 5: General Provisions",
            "Halala Plus may amend discount terms with 7 days' prior notice. The provider may accept the changes or terminate the agreement.\n\n" +
            "The First Party reserves the right to obtain a court injunction to prevent or stop any violation of this agreement.\n\n" +
            "This agreement is governed by the laws of the Kingdom of Saudi Arabia and shall be interpreted and applied accordingly.\n\n" +
            "Accordingly, by signing this agreement, the Service Provider confirms full acceptance of all its terms and unconditionally commits to their implementation.",
            "المادة 5: أحكام عامة",
            "تحتفظ هللة بلس بحق تعديل شروط الخدمات أو الخصومات أو العروض، مع إشعار مقدم الخدمة بذلك قبل 45 يوماً. ويجوز لمقدم الخدمة بعد الإشعار قبول التعديل أو إنهاء الاتفاقية.\n\n" +
            "تخضع هذه الاتفاقية لأنظمة المملكة العربية السعودية، وتفسر وتُطبق وفقاً لها.\n\n" +
            "حررت هذه الاتفاقية من نسختين باللغتين العربية والإنجليزية، ويستلم كل طرف نسخة للعمل بموجبها وفي حال التعارض، تعتمد النسخة العربية.\n\n" +
            "وبناء عليه، يقر مقدم الخدمة بتوقيعه على هذه الاتفاقية بقبوله الكامل لكافة ما ورد فيها، والالتزام بتنفيذ جميع بنودها دون تحفظ.",
            headerFont, arabicFont);

        // التوقيعات
        AddSignatures(doc, arabicFont, englishFont);
    }

    private void AddHeader(Document doc, Font arabicFont, Font englishFont)
    {
        var table = new PdfPTable(2)
        {
            WidthPercentage = 100
        };

        var rightCell = new PdfPCell(new Phrase("No. :الرقم", arabicFont))
        {
            Border = Rectangle.NO_BORDER,
            HorizontalAlignment = Element.ALIGN_RIGHT,
            RunDirection = PdfWriter.RUN_DIRECTION_RTL
        };

        var leftCell = new PdfPCell(new Phrase("Date: :التاريخ", englishFont))
        {
            Border = Rectangle.NO_BORDER,
            HorizontalAlignment = Element.ALIGN_LEFT,
            RunDirection = PdfWriter.RUN_DIRECTION_LTR
        };

        table.AddCell(rightCell);
        table.AddCell(leftCell);
        doc.Add(table);
        doc.Add(new Paragraph(" "));
    }

    private void AddCenteredText(Document doc, string text, Font font, BaseColor color = null, float spacingAfter = 10f)
    {
        var table = new PdfPTable(1)
        {
            WidthPercentage = 100
        };

        if (color == null)
            color = BaseColor.Black;

        var coloredFont = new Font(font.BaseFont, font.Size, font.Style, color);

        var cell = new PdfPCell(new Phrase(text, coloredFont))
        {
            Border = Rectangle.NO_BORDER,
            HorizontalAlignment = Element.ALIGN_CENTER,
            RunDirection = font.BaseFont.GetWidth("ا") > 0 ? PdfWriter.RUN_DIRECTION_RTL : PdfWriter.RUN_DIRECTION_LTR,
            PaddingBottom = 5f
        };

        table.AddCell(cell);
        doc.Add(table);

        if (spacingAfter > 0)
            doc.Add(new Paragraph(" ".PadRight((int)spacingAfter)));
    }

    private void AddInfoTable(Document doc, SystemUser user, Font arabicFont, Font englishFont, Font headerFont)
    {
        var infoTable = new PdfPTable(2)
        {
            WidthPercentage = 100
        };

        string[][] infoData = new string[][]
        {
            new string[] { "Service Provider", "مقدم الخدمة", user.Name },
            new string[] { "Type of Activity", "نوع النشاط", user.Activity?.ToString() ?? "-" },
            new string[] { "CR No", "السجل التجاري", user.IdentityNo ?? "-" },
            new string[] { "Head Office", "المقر الرئيسي", user.City?.ToString() ?? "-" },
            new string[] { "Number of Branches", "عدد الفروع", user.BranchesNo.ToString() ?? "-" },
            new string[] { "Representative Name", "اسم الممثل", user.ServiceProviderRepresent ?? "-" },
            new string[] { "Representative Role", "الصفة", "ممثل الخدمة" },
            new string[] { "Contact", "التواصل", user.PhoneNo ?? "-" },
            new string[] { "Service Duration", "مدة تقديم الخدمة", "سنة واحدة" }
        };

        foreach (var row in infoData)
        {
            string englishCellText = $"{row[0]}: {row[2]}";
            infoTable.AddCell(new PdfPCell(new Phrase(englishCellText, englishFont))
            {
                BackgroundColor = new BaseColor(240, 240, 240),
                RunDirection = PdfWriter.RUN_DIRECTION_LTR,
                Padding = 8,
                BorderWidth = 0.5f
            });

            string arabicCellText = $"{row[1]}: {row[2]}";
            infoTable.AddCell(new PdfPCell(new Phrase(arabicCellText, arabicFont))
            {
                BackgroundColor = new BaseColor(240, 240, 240),
                RunDirection = PdfWriter.RUN_DIRECTION_RTL,
                Padding = 8,
                BorderWidth = 0.5f
            });
        }

        doc.Add(infoTable);
    }

    private void AddDualArticle(Document doc, string arabicTitle, string arabicContent, string englishTitle, string englishContent, Font headerFont, Font contentFont)
    {
        var table = new PdfPTable(2)
        {
            WidthPercentage = 100
        };

        // العمود الأيمن (العربي)
        var arabicCell = new PdfPCell()
        {
            Border = Rectangle.NO_BORDER,
            RunDirection = PdfWriter.RUN_DIRECTION_LTR,
            Padding = 7
        };

        // العنوان العربي بالبولـد
        var arabicTitleParagraph = new Paragraph(arabicTitle, headerFont);
        arabicTitleParagraph.SetAlignment("left"); arabicTitleParagraph.Alignment = Element.ALIGN_JUSTIFIED;
        arabicCell.AddElement(arabicTitleParagraph);

        // المحتوى العربي بالـ Justify
        var arabicContentParagraph = new Paragraph(arabicContent, contentFont);
        arabicContentParagraph.SetAlignment("left"); arabicContentParagraph.Alignment = Element.ALIGN_JUSTIFIED;
        arabicCell.AddElement(arabicContentParagraph);

        // العمود الأيسر (الإنجليزي)
        var englishCell = new PdfPCell()
        {
            Border = Rectangle.NO_BORDER,
            RunDirection = PdfWriter.RUN_DIRECTION_RTL,
            Padding = 7
        };

        // العنوان الإنجليزي بالبولـد
        var englishTitleParagraph = new Paragraph(englishTitle, headerFont);
        englishTitleParagraph.SetAlignment("left");
        englishTitleParagraph.Alignment = Element.ALIGN_JUSTIFIED;
        englishCell.AddElement(englishTitleParagraph);

        // المحتوى الإنجليزي بالـ Justify
        var englishContentParagraph = new Paragraph(englishContent, contentFont);
        englishTitleParagraph.SetAlignment("right");
        englishTitleParagraph.Alignment = Element.ALIGN_JUSTIFIED;
        englishCell.AddElement(englishContentParagraph);

        table.AddCell(arabicCell);
        table.AddCell(englishCell);
        doc.Add(table);
        doc.Add(new Paragraph(" "));
    }

    private void AddDiscountsTable(Document doc, Font arabicFont, Font englishFont, Font smallFont)
    {
        var discountTable = new PdfPTable(4)
        {
            WidthPercentage = 100,
            RunDirection = PdfWriter.RUN_DIRECTION_RTL
        };

        string[] headers = {
            "#",
            "الخدمة / المنتج Service / Product",
            "نسبة / قيمة العرض Offer Value / Discount Rate",
            "Terms | الشروط",
        };

        foreach (string header in headers)
        {
            discountTable.AddCell(new PdfPCell(new Phrase(header, smallFont))
            {
                BackgroundColor = new BaseColor(240, 240, 240),
                Padding = 5
            });
        }

        for (int i = 1; i <= 5; i++)
        {
            discountTable.AddCell(new PdfPCell(new Phrase(i.ToString(), arabicFont)) { Padding = 5 });
            discountTable.AddCell(new PdfPCell(new Phrase("", arabicFont)) { Padding = 5 });
            discountTable.AddCell(new PdfPCell(new Phrase("", arabicFont)) { Padding = 5 });
            discountTable.AddCell(new PdfPCell(new Phrase("", arabicFont)) { Padding = 5 });
        }

        doc.Add(discountTable);
    }

    private void AddSignatures(Document doc, Font arabicFont, Font englishFont)
    {
        var signTable = new PdfPTable(2)
        {
            WidthPercentage = 100
        };

        var halalaCell = new PdfPCell(new Phrase("هللة بلس\n(الاسم / التوقيع)", arabicFont))
        {
            Border = Rectangle.NO_BORDER,
            HorizontalAlignment = Element.ALIGN_CENTER,
            PaddingTop = 40f,
            RunDirection = PdfWriter.RUN_DIRECTION_RTL
        };

        var providerCell = new PdfPCell(new Phrase("مقدم الخدمة\n(الاسم / التوقيع)", arabicFont))
        {
            Border = Rectangle.NO_BORDER,
            HorizontalAlignment = Element.ALIGN_CENTER,
            PaddingTop = 40f,
            RunDirection = PdfWriter.RUN_DIRECTION_RTL
        };

        signTable.AddCell(halalaCell);
        signTable.AddCell(providerCell);
        doc.Add(signTable);
    }
}