﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class ProviderStatisticsView
{
    public long ProviderId { get; set; }

    [StringLength(500)]
    public string? ProviderName { get; set; }

    public int? DiscountsCount { get; set; }

    public int? OffersCount { get; set; }

    public int? DiscountCodesCount { get; set; }

    public int? GrantedUsersCount { get; set; }

    public double? TotalDiscountAmount { get; set; }

    public int? TotalGrantedPoints { get; set; }
}
