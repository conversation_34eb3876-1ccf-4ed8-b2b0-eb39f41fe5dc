﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل بيانات العميل الأساسية داخل النظام.
    /// </summary>
    public class CustomerModel
    {
        /// <summary>
        /// اسم العميل.
        /// </summary>
        //[Display(Name = "اسم العميل")]
        public string? Name { get; set; }

        /// <summary>
        /// رقم العميل.
        /// </summary>
        //[Display(Name = "رقم العميل")]
        public long Id { get; set; }

        /// <summary>
        /// رقم الجوال الخاص بالعميل.
        /// </summary>
        //[Display(Name = "رقم الجوال")]
        public string? PhoneNo { get; set; }

        /// <summary>
        /// رقم العضوية للعميل.
        /// </summary>
        //[Display(Name = "رقم العضوية")]
        public string? MemberNo { get; set; }

        /// <summary>
        /// تاريخ ميلاد العميل.
        /// </summary>
        //[Display(Name = "تاريخ الميلاد")]
        public DateTime? Birthdate { get; set; }

        /// <summary>
        /// البريد الإلكتروني للعميل.
        /// </summary>
        //[Display(Name = " الايميل")]
        public string? Email { get; set; }

        /// <summary>
        /// رقم الهوية الوطنية.
        /// </summary>
        //[Display(Name = "الهوية الوطنية")]
        public string? IdentityNo { get; set; }

        /// <summary>
        /// جنس العميل.
        /// </summary>
        //[Display(Name = "الجنس")]
        public string? Gender { get; set; }

        /// <summary>
        /// جنسية العميل.
        /// </summary>
        //[Display(Name = "الجنسية")]
        public string? Nationality { get; set; }

        /// <summary>
        /// الحالة الحالية للعميل.
        /// </summary>
        //[Display(Name = "الحالة")]
        public string? state { get; set; }
    }
}
