﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class CustomerGrantedDiscountsView
{
    public long CustomerId { get; set; }

    [StringLength(500)]
    public string? CustomerName { get; set; }

    [StringLength(50)]
    public string? CustomerPhone { get; set; }

    public long MonyBoxId { get; set; }

    public string MonyBoxName { get; set; } = null!;

    public int? CustomerGrantedDiscountsCount { get; set; }

    public double CustomerTotalAmount { get; set; }
}
