﻿namespace HalalaPlusProject.Entities
{
    public class BussinessEmpModel
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? Image { get; set; }
        public string? Description { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
      
       
        public bool IsActive { get; set; } = true;
        //public DateTime CreatedAt { get; set; } = DateTime.Now;
    }
}
