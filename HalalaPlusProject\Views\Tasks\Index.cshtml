﻿@model IEnumerable<HalalaPlusProject.CModels.Tasks>
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["tasks"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["tasks"]</h1>

<p>
    <a asp-action="Create"> @localizer["tasks"]</a>
</p>
<table id="tbl1" class="table">
    <thead>
             <tr>
            <th scope="col">@localizer["taskname"]</th>
            <th scope="col">الاسم انجليزي</th>
            <th scope="col">@localizer["theemployee"]</th>
            <th scope="col">@localizer["offerstartdate"]</th>
            <th scope="col">@localizer["offerenddate"]</th>
            <th scope="col">@localizer["state"]</th>
            <th scope="col">@localizer["more"]</th>
                     
                    </tr>
            
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.TaskName)
            </td>  <td>
                @Html.DisplayFor(modelItem => item.EnTaskName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Employee)
            </td>
         
            <td>
                @Html.DisplayFor(modelItem => item.StartDate)
            </td>

            <td>
                @Html.DisplayFor(modelItem => item.EndDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.status)
            </td>
          
           
           
          
            <td>
                <a asp-action="Details"   asp-route-id="@item.Id">@localizer["more"]..</a>
            </td>
        </tr>
}
    </tbody>
</table>

@section Scripts{
    <script>
  let table = new DataTable('#tbl1');

    </script>
}