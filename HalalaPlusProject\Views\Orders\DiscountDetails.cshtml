﻿
@model HalalaPlusProject.CModels.DiscountOrderModel
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = @localizer["orderdetails"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["orderdetails"]</h2>

<div>
    
    <div class="col-md-12">
             <div class="row">

     
             <div class="col-md-5">
                  <div class="form-group">
                    <label class="form-label">@localizer["orderno"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Id)</label>               
                    </div>
                     <div class="form-group">
                    <label class="form-label">@localizer["name"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.DiscountName)</label>               
                    </div>
                  <div class="form-group">
                    <label class="form-label">@localizer["orderdetails"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Provider)</label>               
                    </div>
                       <div class="form-group">
                    <label class="form-label">    @localizer["discount"]   </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Discount)</label>               
                    </div>
             
            </div>
               <div class="col-md-5">        
                     <div class="form-group">
                    <label class="form-label"> @localizer["date"]  </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Date)</label>               
                     </div>
                       <div class="form-group">
                    <label class="form-label"> @localizer["startdate"]  </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.StartDate)</label>               
                     </div>
                     <div class="form-group">
                    <label class="form-label"> @localizer["enddate"] </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.EndDate)</label>               
                     </div>
                     <div class="form-group">
                    <label class="form-label"> @localizer["conditions"] </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Conditions)</label>               
                     </div>
                   
            </div>
    </div>
    </div>

</div>
<div class="mt-2">
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>

