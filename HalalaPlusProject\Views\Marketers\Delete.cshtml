﻿@model HalalaPlusProject.CModels.UserDetailsModel
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["delete"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["delete"]</h2>

<h3>@localizer["deletemarktermsg"]</h3>
<div>
    
    <hr />
     <div class="col-md-12">
         <div class="row">
               
     <div class="col-md-5">
          <div class="form-group">
                <label  class="form-label">@localizer["name"]</label>
                <label  class="form-control">@Model.Name</label>               
            </div>
               <div class="form-group">
                <label  class="form-label">@localizer["nationality"]</label>
                <label  class="form-control">@Model.Nationality</label>               
            </div>
               <div class="form-group">
                    <label class="form-label">@localizer["identityno"]</label>
                <label  class="form-control">@Model.IdentityNo</label>               
            </div>
              <div class="form-group">
                    <label class="form-label">@localizer["birthdate"]</label>
                <label  class="form-control">@Model.BirthDate</label>               
            </div>
             
   

        </div>
         <div class="col-md-5">
              <div class="form-group">
                    <label class="form-label">@localizer["phoneno"]</label>
                <label  class="form-control">@Model.PhoneNo</label>               
            </div>
            <div class="form-group">
                    <label class="form-label"> @localizer["mail"] </label>
                <label  class="form-control">@Model.Email</label>               
            </div>
            <div class="form-group">
                    <label class="form-label"> @localizer["couponcode"] </label>
                <label  class="form-control">@Model.DiscountCode</label>               
            </div>
             <div class="form-group">
                    <label class="form-label">   @localizer["dealingwayamount"]</label>
                <label  class="form-control">@Model.Amount</label>               
            </div>

             <div class="form-group">
                    <label class="form-label">    @localizer["dealingwayrate"]</label>
                <label  class="form-control">@Model.Precentage</label>               
            </div>





</div>
          </div>
</div>
    <form asp-action="Delete" class="mt-2">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-danger mt-2"> @localizer["delete"]</button>|
        <a asp-action="Index"> @localizer["backtolist"]</a>
    </form>
</div>
