﻿@model HalalaPlusProject.CModels.TasksCreate


<div class="row">
    <div class="offset-md-2 col-md-8 col-sm-12">
        <form asp-action="Create" enctype="multipart/form-data">
            <div class="row">
                  <div asp-validation-summary="ModelOnly" class="text-danger"></div>
             </div>           
         <div class="row">
           <div class="col-md-5">
          <div class="form-group">
                <label asp-for="TaskName" class="control-label">اسم المهمة</label>
                <input asp-for="TaskName" class="form-control" />
                <span asp-validation-for="TaskName"  class="text-danger"></span>
            </div>            
            <div class="form-group"> 
                <label asp-for="TaskDescribe" class="control-label">شرحها</label>
                 <input asp-for="TaskDescribe" class="form-control" />
                  <span asp-validation-for="TaskDescribe"  class="text-danger"></span>
            </div>
              <div class="form-group">
                <label asp-for="StartDate" class="control-label">تاريخ البداية</label>
                <input asp-for="StartDate"  class="form-control" />
                <span asp-validation-for="StartDate" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="EndDate" class="control-label">تاريخ النهاية</label>
                <input asp-for="EndDate" type="date" class="form-control" />
                <span asp-validation-for="EndDate" class="text-danger"></span>
            </div>
                     
          </div>

           <div class="col-md-5">
         <div class="form-group">
                <label asp-for="Files" class="control-label"> المرفقات</label>
                <input asp-for="Files" class="form-control" />
                <span asp-validation-for="Files" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Notes" class="control-label">الملاحظات</label>
                <input asp-for="Notes" class="form-control" />
                <span asp-validation-for="Notes" class="text-danger"></span>
            </div>
              <div class="form-group">
                <label asp-for="Employee" class="control-label">الموظف</label>
               
                 <select asp-for="Employee" required class ="form-select" asp-items="ViewBag.Emloyee"></select>
                <span asp-validation-for="Employee" class="text-danger"></span>
            </div>
             
          </div>
             </div>

           
           
           
            
           
          
       
       
            <div class="form-group">
                <button type="submit" value="Create" class="btn btn-primary" >إنشاء</button>
            </div>
        </form>
    </div>
</div>


