﻿@model HalalaPlusProject.Models.SystemUser
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["deleteserviceprovider"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["delete"]</h1>

<h3>@localizer["deleteserprovidermsg"]</h3>
<div>
    <h4>ServiceProvider</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @localizer["name"]
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
      
      
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-danger" >@localizer["delete"]</button> 
        <br />
        <a asp-action="Index">@localizer["backtolist"]</a>
    </form>
</div>
