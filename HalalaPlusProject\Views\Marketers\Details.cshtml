﻿@model HalalaPlusProject.CModels.UserDetailsModel
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] =localizer["details"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h3>@localizer["details"]</h3>

    <h4>@localizer["markter"]</h4>
    <hr />
         <div class="col-md-12">
         <div class="row">

     
     <div class="col-md-5">
          <div class="form-group">
                <label class="form-label">@localizer["name"]</label>
                <label  class="form-control">@Model.Name</label>               
            </div>
               <div class="form-group">
                <label class="form-label">@localizer["nationality"]</label>
                <label  class="form-control">@Model.Nationality</label>               
            </div>
               <div class="form-group">
                <label class="form-label">@localizer["identityno"]</label>
                <label  class="form-control">@Model.IdentityNo</label>               
            </div>
              <div class="form-group">
                <label class="form-label">@localizer["birthdate"]</label>
                <label  class="form-control">@Model.BirthDate</label>               
            </div>

   @* <dl class="row">
        <dt class = "col-sm-2">
           الاسم
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt class = "col-sm-2">
            الجنسية
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Nationality)
        </dd>
        <dt class = "col-sm-2">
          رقم الهوية
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.IdentityNo)
        </dd>
        
     
        <dt class = "col-sm-2">
         تاريخ الميلاد
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.BirthDate)
        </dd>
        
         </dl>*@
        </div>
         <div class="col-md-5">
 <div class="form-group">
                <label class="form-label">@localizer["phoneno"]</label>
                <label  class="form-control">@Model.PhoneNo</label>               
            </div>
            <div class="form-group">
                <label class="form-label">@localizer["mail"]</label>
                <label  class="form-control">@Model.Email</label>               
            </div>
            <div class="form-group">
                <label class="form-label">@localizer["couponcode"]</label>
                <label  class="form-control">@Model.DiscountCode</label>               
            </div>
             <div class="form-group">
                <label class="form-label">   @localizer["dealingwayamount"]</label>
                <label  class="form-control">@Model.Amount</label>               
            </div>

             <div class="form-group">
                <label class="form-label">   @localizer["dealingwayrate"]</label>
                <label  class="form-control">@Model.Precentage</label>               
            </div>

   @* <dl class="row">
        <dt class = "col-sm-2">
          رقم الجوال
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.PhoneNo)
        </dd>
        <dt class = "col-sm-2">
          البريد الالكتروني
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Email)
        </dd>
        <dt class = "col-sm-2">
           كود الخصم
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.DiscountCode)
        </dd>
        <dt class = "col-sm-2">
           طريقة التعامل
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Amount)
        </dd>
        
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Precentage)
        </dd>
    </dl>*@


</div>
          </div>  </div>
<div class="mt-2">
    <a asp-action="Edit" class="btn btn-primary px-5" asp-route-id="@Model?.Id">@localizer["edit"]</a> |
    <a asp-action="Delete" class="btn btn-primary px-5" asp-route-id="@Model?.Id">@localizer["delete"]</a> |
    <a class="btn btn-primary px-5" asp-action="Index">@localizer["backtolist"]</a>
</div>
