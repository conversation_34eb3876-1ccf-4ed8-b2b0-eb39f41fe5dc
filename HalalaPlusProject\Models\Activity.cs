﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class Activity
{
    [Key]
    public int Id { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    [StringLength(500)]
    public string? Describtion { get; set; }

    [StringLength(250)]
    public string? EnName { get; set; }

    public string? EnDescribtion { get; set; }

    [Column("image")]
    [StringLength(500)]
    public string? Image { get; set; }

    [InverseProperty("ActivityNavigation")]
    public virtual ICollection<ServiceProvider> ServiceProviders { get; set; } = new List<ServiceProvider>();
}
