﻿using Google.Api;
using Google.Cloud.Firestore;
using HalalaPlusProject.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HalalaPlusProject.Services
{
    public interface IRegisterUserService
    {
        Task<dynamic> AddNewUser(string senderEmail);
        Task<List<dynamic>> GetFireUsers(string? userId, string? userEmail);
        Task<bool> CheckIfEmailExists(string email);
    }

    public class RegisterUserService : IRegisterUserService
    {
        private readonly FirestoreDb _db;
        private readonly ILogger<ChatService> _logger;
        private const string UsersCollectionName = "Users";
        private readonly HalalaPlusdbContext _context;
        public RegisterUserService(HalalaPlusdbContext context, FirestoreDb db, ILogger<ChatService> logger)
        {
            _db = db;
            _logger = logger;
            _context = context;
        }

      
        public async Task<dynamic> AddNewUser(string senderEmail)
        {
            try
            {
                CollectionReference allusersCollection = _db.Collection(UsersCollectionName);

                // Let Firestore generate a new document with a unique ID
                DocumentReference newDoc = allusersCollection.Document();
                string generatedId = newDoc.Id;

                var data = new Dictionary<string, object>
                  {
                    { "email", senderEmail },
                    { "uid", generatedId }
                  };

                await newDoc.SetAsync(data); // Save data with the auto-generated ID

                _logger.LogInformation("User added with generated ID {senderEmail}: {roomId}", senderEmail, generatedId);

                return data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding user {senderEmail}", senderEmail);
                return null;
                //throw;
            }
        }

        public async Task<bool> CheckIfEmailExists(string email)
        {
            try
            {
                CollectionReference usersCollection = _db.Collection(UsersCollectionName);
                Query query = usersCollection.WhereEqualTo("email", email);
                QuerySnapshot snapshot = await query.GetSnapshotAsync();

                return snapshot.Count > 0; // true if the email is found
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking for existing email: {email}", email);
                throw;
            }
        }


        public async Task<List<dynamic>> GetFireUsers(string? userId,string? userEmail)
        {
            List<dynamic> chatRoomList = new List<dynamic>();
            try
            {
                CollectionReference usercollection = _db.Collection("Users");
                QuerySnapshot usersnapshot = await usercollection.GetSnapshotAsync();
                Console.WriteLine($"Number of users found: {usersnapshot.Count}");
                var userData = await _context.AspNetUsers.FindAsync((userId));

                foreach (DocumentSnapshot document in usersnapshot.Documents)
                {
                    if (document.Exists)
                    {
                        Console.WriteLine($"User ID: {document.Id}");

                        var personA = document.GetValue<string>("email") ?? string.Empty;

                        var chatRoom = new 
                        {
                            email = document.Id, 
                            uid = personA, 
                        };

                        chatRoomList.Add(chatRoom);
                    }
                }
                return chatRoomList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chat rooms");
                return new List<dynamic>();
            }
        }


    }
}