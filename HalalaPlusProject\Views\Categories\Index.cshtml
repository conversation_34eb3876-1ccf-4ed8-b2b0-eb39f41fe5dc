﻿@model IEnumerable<HalalaPlusProject.Models.Category>

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>الاصناف</h1>

<p>
    <a asp-action="Create">انشاء صنف جديد</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
               اسم الصنف 
            </th>
            <th>
               الاسم الانجليزي
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EngName)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.Id">تعديل</a> |
                <a asp-action="Delete" asp-route-id="@item.Id">حذف</a>
            </td>
        </tr>
}
    </tbody>
</table>
