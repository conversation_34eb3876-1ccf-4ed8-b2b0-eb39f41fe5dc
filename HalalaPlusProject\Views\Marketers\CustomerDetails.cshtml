﻿@model HalalaPlusProject.CModels.CustomerModel
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["details"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["details"]</h2>

<div>
    
    <div class="col-md-12">
             <div class="row">

     
             <div class="col-md-5">
                  <div class="form-group">
                    <label class="form-label">@localizer["memberno"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.MemberNo)</label>               
                    </div>
                  <div class="form-group">
                    <label class="form-label">@localizer["customername"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Name)</label>               
                    </div>
                      <div class="form-group">
                    <label class="form-label"> @localizer["phoneno"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.PhoneNo)</label>               
                     </div>
                      <div class="form-group">
                    <label class="form-label"> @localizer["email"] </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Email)</label>               
                     </div>
                       <div class="form-group">
                    <label class="form-label"> @localizer["identityno"] </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.IdentityNo)</label>               
                     </div>
                      
            </div>
               <div class="col-md-5">         
                       <div class="form-group">
                    <label class="form-label"> @localizer["state"] </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.state)</label>               
                     </div>
                      <div class="form-group">
                    <label class="form-label">   @localizer["nationality"] </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Nationality)</label>               
                    </div>

                     <div class="form-group">
                    <label class="form-label"> @localizer["customername"] </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Gender)</label>               
                     </div>
                     <div class="form-group">
                    <label class="form-label">@localizer["birthdate"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Birthdate)</label>               
                     </div>
                    
            </div>
    </div>
    </div>

</div>
<div class="mt-2">
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>
