﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    public class InvestorsController : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public InvestorsController(HalalaPlusdbContext context)
        {
            _context = context;
        }

        // GET: Investors
        public async Task<IActionResult> Index()
        {
            return View(await _context.InvestorsTables.ToListAsync());
                          
        }

        //// GET: Investors/Details/5
        //public async Task<IActionResult> Details(long? id)
        //{
        //    if (id == null || _context.InvestorsTables == null)
        //    {
        //        return NotFound();
        //    }

        //    var investorsTable = await _context.InvestorsTables
        //        .FirstOrDefaultAsync(m => m.Id == id);
        //    if (investorsTable == null)
        //    {
        //        return NotFound();
        //    }

        //    return View(investorsTable);
        //}

        //// GET: Investors/Create
        //public IActionResult Create()
        //{
        //    return View();
        //}

        //// POST: Investors/Create
        //// To protect from overposting attacks, enable the specific properties you want to bind to.
        //// For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        //[HttpPost]
        //[ValidateAntiForgeryToken]
        //public async Task<IActionResult> Create([Bind("Id,InvestorName,PhoneNumber,Email,StocksNumber,StocksValue,TransctionId,CreateAt")] InvestorsTable investorsTable)
        //{
        //    if (ModelState.IsValid)
        //    {
        //        _context.Add(investorsTable);
        //        await _context.SaveChangesAsync();
        //        return RedirectToAction(nameof(Index));
        //    }
        //    return View(investorsTable);
        //}

        //// GET: Investors/Edit/5
        //public async Task<IActionResult> Edit(long? id)
        //{
        //    if (id == null || _context.InvestorsTables == null)
        //    {
        //        return NotFound();
        //    }

        //    var investorsTable = await _context.InvestorsTables.FindAsync(id);
        //    if (investorsTable == null)
        //    {
        //        return NotFound();
        //    }
        //    return View(investorsTable);
        //}

        //// POST: Investors/Edit/5
        //// To protect from overposting attacks, enable the specific properties you want to bind to.
        //// For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        //[HttpPost]
        //[ValidateAntiForgeryToken]
        //public async Task<IActionResult> Edit(long id, [Bind("Id,InvestorName,PhoneNumber,Email,StocksNumber,StocksValue,TransctionId,CreateAt")] InvestorsTable investorsTable)
        //{
        //    if (id != investorsTable.Id)
        //    {
        //        return NotFound();
        //    }

        //    if (ModelState.IsValid)
        //    {
        //        try
        //        {
        //            _context.Update(investorsTable);
        //            await _context.SaveChangesAsync();
        //        }
        //        catch (DbUpdateConcurrencyException)
        //        {
        //            if (!InvestorsTableExists(investorsTable.Id))
        //            {
        //                return NotFound();
        //            }
        //            else
        //            {
        //                throw;
        //            }
        //        }
        //        return RedirectToAction(nameof(Index));
        //    }
        //    return View(investorsTable);
        //}

        //// GET: Investors/Delete/5
        //public async Task<IActionResult> Delete(long? id)
        //{
        //    if (id == null || _context.InvestorsTables == null)
        //    {
        //        return NotFound();
        //    }

        //    var investorsTable = await _context.InvestorsTables
        //        .FirstOrDefaultAsync(m => m.Id == id);
        //    if (investorsTable == null)
        //    {
        //        return NotFound();
        //    }

        //    return View(investorsTable);
        //}

        //// POST: Investors/Delete/5
        //[HttpPost, ActionName("Delete")]
        //[ValidateAntiForgeryToken]
        //public async Task<IActionResult> DeleteConfirmed(long id)
        //{
        //    if (_context.InvestorsTables == null)
        //    {
        //        return Problem("Entity set 'HalalaPlusdbContext.InvestorsTables'  is null.");
        //    }
        //    var investorsTable = await _context.InvestorsTables.FindAsync(id);
        //    if (investorsTable != null)
        //    {
        //        _context.InvestorsTables.Remove(investorsTable);
        //    }
            
        //    await _context.SaveChangesAsync();
        //    return RedirectToAction(nameof(Index));
        //}

        //private bool InvestorsTableExists(long id)
        //{
        //  return (_context.InvestorsTables?.Any(e => e.Id == id)).GetValueOrDefault();
        //}
    }
}
