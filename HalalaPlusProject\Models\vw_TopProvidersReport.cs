﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models
{
    [Keyless]

    public partial class vw_TopProvidersReport
    {
        public long ProviderId { get; set; }

        public string ProviderName { get; set; } = string.Empty;
        public string CityName { get; set; } = string.Empty;
        public string ActivityName { get; set; } = string.Empty;

        // count columns remain int
        public int DiscountsCount { get; set; }

        // monetary columns use decimal if the view sums DECIMAL/MONEY,
        // or double if it sums FLOAT.  Usually DECIMAL → decimal:
        public decimal DiscountsTotalValue { get; set; }

        public int OffersCount { get; set; }
        public decimal OffersTotalValue { get; set; }

        public int CouponCodesCount { get; set; }
        public decimal CouponCodesTotalValue { get; set; }

        public int UsedCouponCodesCount { get; set; }
        public decimal TotalPointsGranted { get; set; }
        public decimal TotalGrantedDiscountsValue { get; set; }
    }

    }

