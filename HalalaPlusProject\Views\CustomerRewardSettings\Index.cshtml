@model HalalaPlusProject.CModels.CustomerRewardSettingViewModel
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["Customer Reward Settings"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["Customer Reward Settings"]</h1>

<div class="row">
    <div class="col-md-12">
        <form asp-action="Edit" class="submitfm">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />

            <div class="row">
                <!-- Left Column -->
                <div class="col-md-6">
                    <!-- Login Reward -->
                    <div class="form-group">
                        <label asp-for="LoginRewardPoints" class="control-label">نقاط مكافأة تسجيل الدخول</label>
                        <input asp-for="LoginRewardPoints" class="form-control" />
                    </div>
                    <div class="form-group form-check form-switch mb-3">
                        <label class="form-check-label" for="IsLoginRewardActiveSwitch">تفعيل مكافأة تسجيل الدخول</label>
                        <input class="form-check-input" type="checkbox" asp-for="IsLoginRewardActive" id="IsLoginRewardActiveSwitch" />
                    </div>
                    <hr />

                    <!-- Charge Reward -->
                    <div class="form-group">
                        <label asp-for="ChargeRewardPoints" class="control-label">نقاط مكافأة الشحن</label>
                        <input asp-for="ChargeRewardPoints" class="form-control" />
                    </div>
                    <div class="form-group form-check form-switch mb-3">
                        <label class="form-check-label" for="IsChargeRewardActiveSwitch">تفعيل مكافأة الشحن</label>
                        <input class="form-check-input" type="checkbox" asp-for="IsChargeRewardActive" id="IsChargeRewardActiveSwitch" />
                    </div>
                    <hr />

                    <!-- Piggy Bank Creation Bonus -->
                    <div class="form-group">
                        <label asp-for="PiggyCreationBonus" class="control-label">مكافأة إنشاء الحصالة</label>
                        <input asp-for="PiggyCreationBonus" class="form-control" />
                    </div>
                    <div class="form-group form-check form-switch mb-3">
                        <label class="form-check-label" for="IsPiggyCreationActiveSwitch">تفعيل مكافأة إنشاء الحصالة</label>
                        <input class="form-check-input" type="checkbox" asp-for="IsPiggyCreationActive" id="IsPiggyCreationActiveSwitch" />
                    </div>
                    <hr />

                    <!-- Savings Goal Reward -->
                    <div class="form-group">
                        <label asp-for="SavingsGoalReward" class="control-label">مكافأة تحقيق هدف الإدخار</label>
                        <input asp-for="SavingsGoalReward" class="form-control" />
                    </div>
                    <div class="form-group form-check form-switch mb-3">
                        <label class="form-check-label" for="IsSavingsGoalActiveSwitch">تفعيل مكافأة هدف الإدخار</label>
                        <input class="form-check-input" type="checkbox" asp-for="IsSavingsGoalActive" id="IsSavingsGoalActiveSwitch" />
                    </div>
                    <hr />

                    <!-- Point Value -->
                    <div class="form-group">
                        <label asp-for="PointValueSar" class="control-label">عدد نقاط هللة كوين مقابل  كل ريال</label>
                        <input asp-for="PointValueSar" class="form-control" />
                    </div>
                    <div class="form-group form-check form-switch mb-3">
                        <label class="form-check-label" for="IsPointValueActiveSwitch">تفعيل قيمة النقطة</label>
                        <input class="form-check-input" type="checkbox" asp-for="IsPointValueActive" id="IsPointValueActiveSwitch" />
                    </div>
                </div>

                <!-- Right Column -->
                <div class="col-md-6">
                    <!-- Discount Usage Reward -->
                    <div class="form-group">
                        <label asp-for="DiscountUsageReward" class="control-label">مكافأة استخدام الخصومات</label>
                        <input asp-for="DiscountUsageReward" class="form-control" />
                    </div>
                    <div class="form-group form-check form-switch mb-3">
                        <label class="form-check-label" for="IsDiscountUsageActiveSwitch">تفعيل مكافأة استخدام الخصومات</label>
                        <input class="form-check-input" type="checkbox" asp-for="IsDiscountUsageActive" id="IsDiscountUsageActiveSwitch" />
                    </div>
                    <hr />

                    <!-- Digital Purchase Reward -->
                    <div class="form-group">
                        <label asp-for="DigitalPurchaseReward" class="control-label">مكافأة المشتريات الرقمية</label>
                        <input asp-for="DigitalPurchaseReward" class="form-control" />
                    </div>
                    <div class="form-group form-check form-switch mb-3">
                        <label class="form-check-label" for="IsDigitalPurchaseActiveSwitch">تفعيل مكافأة المشتريات الرقمية</label>
                        <input class="form-check-input" type="checkbox" asp-for="IsDigitalPurchaseActive" id="IsDigitalPurchaseActiveSwitch" />
                    </div>
                    <hr />

                    <!-- Plan Creation Bonus -->
                    <div class="form-group">
                        <label asp-for="PlanCreationBonus" class="control-label">مكافأة إنشاء الخطة</label>
                        <input asp-for="PlanCreationBonus" class="form-control" />
                    </div>
                    <div class="form-group form-check form-switch mb-3">
                        <label class="form-check-label" for="IsPlanCreationActiveSwitch">تفعيل مكافأة إنشاء الخطة</label>
                        <input class="form-check-input" type="checkbox" asp-for="IsPlanCreationActive" id="IsPlanCreationActiveSwitch" />
                    </div>
                    <hr />
                    
                    <!-- Engagement Reward -->
                    <div class="form-group">
                        <label asp-for="EngagementReward" class="control-label">مكافأة التفاعل</label>
                        <input asp-for="EngagementReward" class="form-control" />
                    </div>
                    <div class="form-group form-check form-switch mb-3">
                        <label class="form-check-label" for="IsEngagementRewardActiveSwitch">تفعيل مكافأة التفاعل</label>
                        <input class="form-check-input" type="checkbox" asp-for="IsEngagementRewardActive" id="IsEngagementRewardActiveSwitch" />
                    </div>
                    <hr />

                    <!-- Max Transfer Amount -->
                    <div class="form-group">
                        <label asp-for="MaxTransferAmount" class="control-label">الحد الأقصى للتحويل</label>
                        <input asp-for="MaxTransferAmount" class="form-control" />
                    </div>
                    <div class="form-group form-check form-switch mb-3">
                        <label class="form-check-label" for="IsTransferLimitActiveSwitch">تفعيل حد التحويل</label>
                        <input class="form-check-input" type="checkbox" asp-for="IsTransferLimitActive" id="IsTransferLimitActiveSwitch" />
                    </div>
                </div>
            </div>

            <div class="form-group mt-4">
                <input type="submit" value="حـفـظ" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

@* @section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
} *@