﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class ProductDetail
{
    [Key]
    public long Id { get; set; }

    [StringLength(500)]
    public string? ProductName { get; set; }

    [StringLength(50)]
    public string? Lang { get; set; }

    public int? ProductId { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateAt { get; set; }

    [ForeignKey("ProductId")]
    [InverseProperty("ProductDetails")]
    public virtual Product? Product { get; set; }
}
