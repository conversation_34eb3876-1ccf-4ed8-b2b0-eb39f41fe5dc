﻿
@model IEnumerable<HalalaPlusProject.CModels.DiscountsModel>
    @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
<div class="table-responsive p-0">
               
                <table class="table table-striped text-center">
                  <thead>
                    <tr>

                <th scope="col"> @localizer["servicename"]</th>
                <th scope="col"> الاسم انجليزي</th>
                <th scope="col"> @localizer["discount"]</th>
                <th scope="col"> @localizer["startdate"]</th>
                <th scope="col"> @localizer["enddate"]</th>
                <th scope="col"> @localizer["discounttype"]</th>
                <th scope="col"> @localizer["conditions"]</th>
                <th scope="col"> الشروط انجليزي</th>
                <th scope="col"> @localizer["options"]</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model) {
        <tr id="<EMAIL>">
            <td>
                @Html.DisplayFor(modelItem => item.DiscountName)
            </td> 
            <td>
                @Html.DisplayFor(modelItem => item.EnDiscountName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Discount)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.SStartDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.SEndDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.GrantType)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Conditions)
            </td> 
            <td>
                @Html.DisplayFor(modelItem => item.EnConditions)
            </td>
            <td>
                        <a onclick="EditDiscount(@item.Id)" class="btn btn-outline-info tablebtn"> @localizer["edit"]</a> |
                        <a href="#" class="btn btn-outline-danger tablebtn" onclick="deleteDiscount('@item.Id')"> @localizer["delete"]</a>
                
            </td>
        </tr>
}
             
                  </tbody>
               
                </table>

              </div>