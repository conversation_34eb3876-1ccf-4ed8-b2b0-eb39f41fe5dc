﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class ContactU
{
    [Key]
    public long Id { get; set; }

    [Column("phoneNo")]
    [StringLength(50)]
    public string? PhoneNo { get; set; }

    [StringLength(20)]
    public string? Email { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? OrderDate { get; set; }

    [Column("isRead")]
    public bool IsRead { get; set; }

    [StringLength(250)]
    public string? Subject { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    [Column("message")]
    public string? Message { get; set; }

    [Column("isReplay")]
    public bool IsReplay { get; set; }

    public string? Replay { get; set; }
}
