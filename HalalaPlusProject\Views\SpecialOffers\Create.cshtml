﻿@model HalalaPlusProject.CModels.SpecialOffers
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["addoffer"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div>
    <a href="~/SpecialOffers/Index"><img src="../assets/img/svgs/solid/arrow-right.svg" style="width: 40px;" alt=""></a>
    <h3>@localizer["offers"] </h3>

</div>
<div class="row">
    <div class="col-md-12">
        <form asp-action="Create" enctype="multipart/form-data" class="submitfm" >
            <div class="row">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label asp-for="OfferName" class="control-label">@localizer["offername"]</label>
                        <input asp-for="OfferName" class="form-control" />
                        <span asp-validation-for="OfferName"  class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="EnOfferName" class="control-label">@localizer["offername"]</label>
                        <input asp-for="EnOfferName" class="form-control" />
                        <span asp-validation-for="EnOfferName" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="StartDate" class="control-label">@localizer["offerstartdate"]</label>
                        <input asp-for="StartDate"  class="form-control" />
                        <span asp-validation-for="StartDate" class="text-danger"></span>
                    </div>


                     <div class="form-group">
                        <label asp-for="EndDate" class="control-label">@localizer["offerenddate"]</label>
                        <input asp-for="EndDate"  class="form-control" />
                        <span asp-validation-for="EndDate" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Details" class="control-label">@localizer["details"]</label>
                        <textarea asp-for="Details" class="form-control"></textarea>
                        <span asp-validation-for="Details" class="text-danger"></span>
                    </div>   <div class="form-group">
                        <label asp-for="EnDetails" class="control-label">التفاصيل انجليزي</label>
                        <textarea asp-for="EnDetails" class="form-control"></textarea>
                        <span asp-validation-for="EnDetails" class="text-danger"></span>
                    </div>
            
          </div>

          <div class="col-md-3">
                    <div class="form-group">
                        <label asp-for="Discount" class="control-label">@localizer["discount"]</label>
                        <input asp-for="Discount"  class="form-control" />
                        <span asp-validation-for="Discount" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Provider" class="control-label">@localizer["provider"]</label>
                        <select asp-for="Provider" class="form-select customselect">
                            <option value="">@localizer["chose"]</option>
                        @foreach (var item in ViewBag.Provider)
                        {
                            <option value="@item.Value">@item.Text </option>
                        }
                    </select>
                    </div>
                    <div class="form-group">
                        <label asp-for="ProviderName" class="control-label">@localizer["providername"]</label>
                        <input asp-for="ProviderName" class="form-control" />
                        <span asp-validation-for="ProviderName" class="text-danger"></span>
                    </div> 
                    <div class="form-group">
                        <label asp-for="EnProviderName" class="control-label">@localizer["providerenname"]</label>
                        <input asp-for="EnProviderName" class="form-control" />
                        <span asp-validation-for="EnProviderName" class="text-danger"></span>
                    </div>


           
                     <div class="form-group">
                        <label asp-for="ActivityNo" class="control-label">@localizer["activity"]</label>
                <select asp-for="ActivityNo"  class ="form-select" asp-items="ViewBag.Activity"></select>           
                  <span asp-validation-for="ActivityNo" class="text-danger"></span>
            </div>
        
            </div>
            <div class="col-md-3">
                   
                    <div class="form-group">
                        <label asp-for="OverView" class="control-label">@localizer["storeoverview"] </label>
                        <textarea asp-for="OverView" class="form-control"></textarea>
                        <span asp-validation-for="OverView" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="EnOverView" class="control-label">@localizer["storeoverview"] </label>
                        <textarea asp-for="EnOverView" class="form-control"></textarea>
                        <span asp-validation-for="EnOverView" class="text-danger"></span>
                    </div>
                   
           
                    <div class="form-group">
                        <label asp-for="Img" class="control-label">@localizer["img"]</label>
                        <input asp-for="Img" required class="form-control" />
                        <span asp-validation-for="Img" class="text-danger"></span>
                    </div>
        
            </div>
            </div>
           
            <div class="mt-2">
                <button type="submit" value="Create" class="btn btn-primary">@localizer["save"]</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index"> @localizer["backtolist"] </a>
</div>
<script>


</script>
@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
