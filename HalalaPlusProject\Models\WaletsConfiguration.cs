﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class WaletsConfiguration
{
    [Key]
    public int Id { get; set; }

    public double? MaxLimitForWallets { get; set; }

    public double? MinLimitForWalet { get; set; }

    public bool GrantMonyBoxsChargePoints { get; set; }

    public double? MaxlimtForAllMonyBoxs { get; set; }

    public double? CashBackPrecent { get; set; }

    public double? MaxCachBackAmount { get; set; }

    public bool GrantCachBack { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CachBackStartDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CachBackEndDate { get; set; }

    public int? WelcomPointsNo { get; set; }

    public int? AmountForWelcomePint { get; set; }

    public bool? IsWecomePointsActive { get; set; }

    public double? WelcomeCashBackPrecent { get; set; }

    public double? WelcomeMaxCachBackAmount { get; set; }

    public bool GrantWelcomeCachBack { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? WelcomeCachBackStartDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? WelcomeCachBackEndDate { get; set; }

    public int? WelcomeGrantPointsNo { get; set; }

    public int? WelcomePointsAmountNo { get; set; }

    public int? WelcomePointsAmountValue { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? WelcomePointsStartDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? WelcomePointsEndDate { get; set; }

    public bool GrantWelcomePoints { get; set; }

    public bool SetMaxlimtForAllMonyBoxs { get; set; }

    public double? ChargeCashBackPrecent { get; set; }

    public double? ChargeMaxCachBackAmount { get; set; }

    public bool GranChargeCashback { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? ChargeCachBackStartDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? ChargeCachBackEndDate { get; set; }

    public double? MaxAmountLimitForWallets { get; set; }

    public double? MinAmountLimitForWalet { get; set; }

    public double? MaxChargeAmountLimitForWallets { get; set; }

    public double? MinChargeAmountLimitForWalet { get; set; }

    public int? PointsNoForOnReal { get; set; }

    public bool GrantCreateMonyBoxPoints { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateMonyBoxPointsStartDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateMonyBoxPointsEndDate { get; set; }

    public int? CreateMonyBoxPointsNo { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? ChargeMonyBoxsPointsStartDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? ChargeMonyBoxsPointsEndDate { get; set; }

    public int? ChargeMonyBoxsPointsNo { get; set; }

    public double? CreateMonyBoxCashBackPrecent { get; set; }

    public double? CreateMonyBoxMaxCachBackAmount { get; set; }

    public bool GranCreateMonyBoxCashback { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateMonyBoxCachBackStartDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateMonyBoxCachBackEndDate { get; set; }
}
