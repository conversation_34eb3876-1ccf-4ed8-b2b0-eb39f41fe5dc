﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class UserBanksAccount
{
    [Key]
    public long Id { get; set; }

    [Column("AccountNO")]
    public long? AccountNo { get; set; }

    [StringLength(50)]
    public string? Name { get; set; }

    public long? UserId { get; set; }

    [Column("state")]
    [StringLength(50)]
    public string? State { get; set; }

    public bool Deleted { get; set; }

    public long? MainAccount { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateOn { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? UpdatedOn { get; set; }

    public double? Balance { get; set; }

    [Column("temp1")]
    [StringLength(500)]
    public string? Temp1 { get; set; }

    [Column("temp2")]
    [StringLength(500)]
    public string? Temp2 { get; set; }

    public bool? Active { get; set; }

    [Column("isAuth")]
    public bool? IsAuth { get; set; }

    public int? BankId { get; set; }

    [StringLength(50)]
    public string? EnName { get; set; }

    [StringLength(50)]
    public string? AccountId { get; set; }

    [StringLength(200)]
    public string? AccountHolderName { get; set; }

    [StringLength(50)]
    public string? Status { get; set; }

    [StringLength(50)]
    public string? Currency { get; set; }

    [StringLength(50)]
    public string? Nickname { get; set; }

    [StringLength(100)]
    public string? AccountType { get; set; }

    [StringLength(100)]
    public string? AccountSubType { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? StatusUpdateDateTime { get; set; }

    public string? Description { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? OpeningDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? MaturityDate { get; set; }

    public string? AccountIdentifiers { get; set; }

    public string? Servicer { get; set; }

    public double? TotalOpeningBalance { get; set; }

    public double? TotalAvailableBalance { get; set; }

    public double? TotalClosingAvailable { get; set; }

    public long? ConcentId { get; set; }

    [ForeignKey("BankId")]
    [InverseProperty("UserBanksAccounts")]
    public virtual BanksAccount? Bank { get; set; }

    [InverseProperty("IdNavigation")]
    public virtual BankAccountTransction? BankAccountTransction { get; set; }

    [ForeignKey("ConcentId")]
    [InverseProperty("UserBanksAccounts")]
    public virtual ConsentHistory? Concent { get; set; }

    [InverseProperty("MainAccountNavigation")]
    public virtual ICollection<UserBanksAccount> InverseMainAccountNavigation { get; set; } = new List<UserBanksAccount>();

    [ForeignKey("MainAccount")]
    [InverseProperty("InverseMainAccountNavigation")]
    public virtual UserBanksAccount? MainAccountNavigation { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("UserBanksAccounts")]
    public virtual SystemUser? User { get; set; }
}
