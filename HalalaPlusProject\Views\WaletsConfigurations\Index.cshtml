﻿@model HalalaPlusProject.CModels.WaletsConfigurationViewModel
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["Wallet Configuration"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["Wallet Configuration"]</h1>
<div class="tab">
    <button class="tablinks" onclick="openCity(event, 'waletsconfiguration')" id="defaultOpen">اعدادات النقاط الترحيبية</button>
</div>

<div id="waletsconfiguration" class="tabcontent">
    <div class="row">
        <div class="col-md-12">
            <form asp-action="Edit" class="submitfm">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="MinLimitForWalet" class="control-label">الحد الأدنى للمحفظة</label>
                            <input asp-for="MinLimitForWalet" class="form-control" />
                            <span asp-validation-for="MinLimitForWalet" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="MaxLimitForWallets" class="control-label">الحد الأقصى للمحافظ</label>
                            <input asp-for="MaxLimitForWallets" class="form-control" />
                            <span asp-validation-for="MaxLimitForWallets" class="text-danger"></span>
                        </div>
                        <div class="form-group">
@*                             <label asp-for="GrantMonyBoxsChargePoints" class="control-label">@localizer["Grant Money Box Charge Points"]</label>
                            <input asp-for="GrantMonyBoxsChargePoints" type="checkbox" class="form-control" value="@Model.GrantMonyBoxsChargePoints ?? false" />
                            <span asp-validation-for="GrantMonyBoxsChargePoints" class="text-danger"></span> *@
                            <div class="form-group form-check form-switch">
                                <label class="form-check-label control-label" for="GrantMonyBoxsChargePointsSwitch">منح نقاط الشحن للحصالات</label>
                                <input class="form-check-input" type="checkbox" asp-for="GrantMonyBoxsChargePoints" id="GrantMonyBoxsChargePointsSwitch" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label asp-for="MaxlimtForAllMonyBoxs" class="control-label">الحد الأقصى لجميع الحصالات</label>
                            <input asp-for="MaxlimtForAllMonyBoxs" class="form-control" />
                            <span asp-validation-for="MaxlimtForAllMonyBoxs" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="CashBackPrecent" class="control-label">نسبة الكاش باك</label>
                            <input asp-for="CashBackPrecent" class="form-control" />
                            <span asp-validation-for="CashBackPrecent" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="MaxCachBackAmount" class="control-label">الحد الأقصى لمبلغ الكاش باك</label>
                            <input asp-for="MaxCachBackAmount" class="form-control" />
                            <span asp-validation-for="MaxCachBackAmount" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
@*                             <label asp-for="GrantCachBack" class="control-label">@localizer["Grant Cashback"]</label>
                            <input asp-for="GrantCachBack" type="checkbox" class="form-control" value="@Model.GrantCachBack" />
                            <span asp-validation-for="GrantCachBack" class="text-danger"></span> *@
                            <div class="form-group form-check form-switch">
                                <label class="form-check-label" for="GrantCachBackSwitch">منح الكاش باك</label>
                                <input class="form-check-input" type="checkbox" asp-for="GrantCachBack" id="GrantCachBackSwitch" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label asp-for="CachBackStartDate" class="control-label">تاريخ بدء الكاش باك</label>
                            <input asp-for="CachBackStartDate" type="date" class="form-control" />
                            <span asp-validation-for="CachBackStartDate" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="CachBackEndDate" class="control-label">تاريخ انتهاء الكاش باك</label>
                            <input asp-for="CachBackEndDate" type="date" class="form-control" />
                            <span asp-validation-for="CachBackEndDate" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="WelcomPointsNo" class="control-label">النقاط الترحيبية</label>
                            <input asp-for="WelcomPointsNo" class="form-control" />
                            <span asp-validation-for="WelcomPointsNo" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="AmountForWelcomePint" class="control-label">المبلغ الخاص بنقاط الترحيب</label>
                            <input asp-for="AmountForWelcomePint" class="form-control" />
                            <span asp-validation-for="AmountForWelcomePint" class="text-danger"></span>
                        </div>
@*                         <div class="form-group">
                            <label asp-for="IsWecomePointsActive" class="control-label">Activate Welcome Points</label>
                            <input asp-for="IsWecomePointsActive" type="checkbox" class="form-control" value="@Model.IsWecomePointsActive" />
                            <span asp-validation-for="IsWecomePointsActive" class="text-danger"></span>
                        </div> *@
                        <div class="form-group">
                                <div class="form-group form-check form-switch">
                                <label class="form-check-label" for="DeletedSwitch">تفعيل نقاط الترحيب</label>
                                    <input class="form-check-input" type="checkbox" asp-for="IsWecomePointsActive" id="DeletedSwitch" />
                                </div>              
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <input type="submit" value="حـفـظ" class="btn btn-primary" />
                </div>
            </form>
        </div>
    </div>
</div>
 @* @section Scripts {
     @{
         await Html.RenderPartialAsync("_ValidationScriptsPartial");
     }
 } *@
<script>
    document.addEventListener("DOMContentLoaded", function () {
        document.getElementById("defaultOpen").click();
    });
</script>
