﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// متحكم لإدارة محتوى وبيانات الموقع الإلكتروني العام، بما في ذلك الإعدادات الرئيسية، المقالات، الأسئلة الشائعة، وغيرها.
    /// </summary>
    [Authorize]
    public class WebSiteController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IWebHostEnvironment _hosting;
        public WebSiteController(HalalaPlusdbContext context, IWebHostEnvironment hosting)
        {
            _context = context;
            this._hosting = hosting;
        }

        /// <summary>
        /// يعرض لوحة التحكم الرئيسية لإدارة الموقع، حيث يتم جلب جميع البيانات اللازمة مثل إعدادات الموقع، المقالات، الأسئلة الشائعة، رسائل التواصل، والأيقونات.
        /// </summary>
        public async Task<IActionResult> Index()
        {
            var temp = _context.SiteSettings.FirstOrDefault();
            var t = new WebsiteModel();
            if (temp != null)
            {
                t.vision = temp.Vision;
                t.Defnition = temp.Defnition;
                t.Experts = temp.Experts;
                t.AppAppleLink = temp.AppLinkApple;
                t.AppGoogleLink = temp.AppLinkGoogle;
                t.PhoneNo = temp.Phone;
                t.Place = temp.Place;
                t.enDefnition = temp.EnDefnition;
                t.overview = temp.OverView;
                t.enoverview = temp.EnOverView;
                t.enmessage = temp.OurEnMessage;
                t.message = temp.OurMessage;
                t.envision = temp.Envision;
                t.Email = temp.Email;
                t.FaceookLink = temp.FacebookLink;
                t.YouTubeLink = temp.YoutubeLink;
                t.XLink = temp.TwitterLink;
                t.InstagramLink = temp.InstagramLink;
                t.snapchatLink = temp.SnapchatLink;
                t.whatsappLink = temp.WhatsappLink;
                t.CitiesNo = temp.Cities;
                t.tiktok = temp.TikTokLink;
                t.ProvidersNo = temp.Providers;
                t.CustomersNo = temp.Customers;
            }
            //t.ProvidersNo = _context.SystemUsers.Where(p => p.AccountType == "Provider").Count()+"";
            //t.CustomersNo = _context.SystemUsers.Where(p => p.AccountType == "Customer").Count()+"";
            //t.CitiesNo = _context.CitiesTables.Count() + "";
            t.FAQSList = _context.FaqsTables.Select(p => new WebsiteFAQSModel { Details = p.Details, Id = p.Id, Question = p.Question, EnDetails = p.EnDetails, EnQuestion = p.EnQuestion }).ToList();
            t.ArticlesList = _context.Articles.Where(o => o.IsActive != "false" && o.Deleted != true).Select(p => new ArticlesModel { Content = p.Body, Id = p.Id, entitle = p.EnTitle, EnContent = p.EnBody, link = p.Link, title = p.Title }).ToList();
            t.ContactU = _context.ContactUs.Where(p => p.IsRead != true).ToList();
            t.Icons = _context.MonyBoxsIcons.Where(p => p.Deleted != true).Select(p => new MonyBoxsIcon { Name = p.Name, Id = p.Id, Link = "/Icons/" + p.Link }).ToList();
            t.waletsconfig = _context.WaletsConfigurations.Select(p => new WaletsConfig { MaxLimit = p.MaxLimitForWallets ?? 0, MinLimit = p.MinLimitForWalet ?? 0 }).FirstOrDefault();
            return View(t);
        }

        /// <summary>
        /// يحفظ أو يحدّث البيانات العامة للموقع (مثل الرؤية، الرسالة، روابط التواصل الاجتماعي، إلخ).
        /// </summary>
        /// <param name="model">نموذج يحتوي على جميع بيانات إعدادات الموقع.</param>
        public async Task<IActionResult> SiteData(WebsiteModel model)
        {
            //var data=)
            if (ModelState.IsValid)
            {
                try
                {
                    if (_context.SiteSettings.FirstOrDefault() == null) // Corrected check
                    {
                        var t = new SiteSetting();
                        t.Defnition = model.Defnition;
                        t.Experts = model.Experts;
                        t.Place = model.Place;
                        t.Email = model.Email;
                        t.AppLinkGoogle = model.AppGoogleLink;
                        t.AppLinkApple = model.AppAppleLink;
                        t.YoutubeLink = model.YouTubeLink;
                        t.TwitterLink = model.XLink;
                        t.InstagramLink = model.InstagramLink;
                        t.FacebookLink = model.FaceookLink;
                        t.Vision = model.vision;
                        t.Phone = model.PhoneNo;
                        t.WhatsappLink = model.whatsappLink;
                        t.SnapchatLink = model.snapchatLink;
                        t.Cities = model.CitiesNo;
                        t.TikTokLink = model.tiktok;
                        t.Providers = model.ProvidersNo;
                        t.Customers = model.CustomersNo;

                        t.EnDefnition = model.enDefnition;
                        t.OverView = model.overview;
                        t.EnOverView = model.enoverview;
                        t.OurEnMessage = model.enmessage;
                        t.OurMessage = model.message;
                        t.Envision = model.envision;

                        _context.Add(t);
                        _context.SaveChanges();
                        return Ok(new { state = 1, message = "تم الحفظ بنجاح" });
                    }
                    else
                    {
                        var t = _context.SiteSettings.FirstOrDefault();
                        t.Defnition = model.Defnition;
                        t.Experts = model.Experts;
                        t.Place = model.Place;
                        t.Email = model.Email;
                        t.AppLinkGoogle = model.AppGoogleLink;
                        t.AppLinkApple = model.AppAppleLink;
                        t.Vision = model.vision;
                        t.WhatsappLink = model.whatsappLink;
                        t.SnapchatLink = model.snapchatLink;
                        t.YoutubeLink = model.YouTubeLink;
                        t.TwitterLink = model.XLink;
                        t.InstagramLink = model.InstagramLink;
                        t.FacebookLink = model.FaceookLink;
                        t.Phone = model.PhoneNo;
                        t.Cities = model.CitiesNo;
                        t.TikTokLink = model.tiktok;

                        t.EnDefnition = model.enDefnition;
                        t.OverView = model.overview;
                        t.EnOverView = model.enoverview;
                        t.OurEnMessage = model.enmessage;
                        t.OurMessage = model.message;
                        t.Envision = model.envision;

                        t.Providers = model.ProvidersNo;
                        t.Customers = model.CustomersNo;
                        _context.Update(t);
                        _context.SaveChanges();
                        return Ok(new { state = 1, message = "تم التعديل بنجاح" });
                    }
                }
                catch (Exception)
                {
                    return Ok(new { state = 0, message = "لم يتم حفظ البيانات" });
                }
            }
            return Ok(new { state = 0, message = "لم يتم حفظ البيانات" });
        }

        /// <summary>
        /// يقوم بتعطيل مقالة ومنع ظهورها في الموقع.
        /// </summary>
        /// <param name="id">معرّف المقالة المطلوب تعطيلها.</param>
        [HttpPost]
        public async Task<IActionResult> DisableArticle(int? id)
        {
            try
            {
                if (id == null) return Ok(new { state = 0, message = "يجب تحديد مقالة" });
                var temp = await _context.Articles.FindAsync(id);
                if (temp == null) return Ok(new { state = 0, message = "حدث خطاء اثناء العملية" });

                temp.IsActive = "false";
                _context.Update(temp);
                await _context.SaveChangesAsync();
                return Ok(new { state = 1, message = "تمت العملية بنجاح" });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = "حدث خطاء اثناء العملية" });
            }
        }

        /// <summary>
        /// يقوم بحذف مقالة (حذف ناعم).
        /// </summary>
        /// <param name="id">معرّف المقالة المطلوب حذفها.</param>
        public async Task<IActionResult> DeleteArticle(int? id)
        {
            try
            {
                if (id == null) return Ok(new { state = 0, message = "يجب تحديد مقالة" });
                var temp = await _context.Articles.FindAsync(id);
                if (temp == null) return Ok(new { state = 0, message = "حدث خطاء اثناء العملية" });

                temp.Deleted = true;
                _context.Update(temp);
                await _context.SaveChangesAsync();
                return Ok(new { state = 1, message = "تمت العملية بنجاح" });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = "حدث خطاء اثناء العملية" });
            }
        }

        /// <summary>
        /// يجلب بيانات مقالة واحدة لعرضها (عادة لغرض التعديل).
        /// </summary>
        /// <param name="id">معرّف المقالة.</param>
        public async Task<IActionResult> getArticle(int? id)
        {
            try
            {
                if (id == null) return Ok(new { state = 0, message = "يجب تحديد مقالة" });
                var temp = await _context.Articles.FindAsync(id);
                if (temp == null) return Ok(new { state = 0, message = "لم يتم جلب بيانات المقاله" });

                return Ok(new { state = 1, message = "تمت العملية بنجاح", data = temp });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = "حدث خطاء اثناء جلب بيانات المقاله" });
            }
        }

        /// <summary>
        /// يضيف مقالة جديدة أو يعدّل مقالة موجودة.
        /// </summary>
        /// <param name="model">بيانات المقالة.</param>
        public async Task<IActionResult> AddEditArticle(ArticlesModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    if (model.Id == null)
                    {
                        if (model.Image == null) return Ok(new { state = 0, message = "يجب اختيار صورة" });
                        var t = new Article
                        {
                            Title = model.title,
                            Link = HandleImages.SaveImage(model.Image, "img", _hosting),
                            Body = model.Content,
                            EnTitle = model.entitle,
                            EnBody = model.EnContent,
                            IsActive = "true"
                        };
                        _context.Add(t);
                        await _context.SaveChangesAsync();
                        return Ok(new { state = 5, message = "تم الحفظ بنجاح" });
                    }
                    else
                    {
                        var t = await _context.Articles.FindAsync(model.Id);
                        t.Title = model.title;
                        if (model.Image != null) t.Link = HandleImages.SaveImage(model.Image, "img", _hosting);
                        t.EnTitle = model.entitle;
                        t.EnBody = model.EnContent;
                        t.Body = model.Content;
                        _context.Update(t);
                        await _context.SaveChangesAsync();
                        return Ok(new { state = 5, message = "تم التعديل بنجاح" });
                    }
                }
                catch (Exception)
                {
                    return Ok(new { state = 0, message = "لم يتم حفظ البيانات" });
                }
            }
            return Ok(new { state = 0, message = "لم يتم حفظ البيانات" });
        }

        /// <summary>
        /// يضيف أو يعدل إعدادات حدود المحافظ المالية.
        /// </summary>
        /// <param name="MaxLimit">الحد الأعلى للمحفظة.</param>
        /// <param name="MinLimit">الحد الأدنى للمحفظة.</param>
        public async Task<IActionResult> AddEditwaletsconfig(double MaxLimit, double MinLimit)
        {
            try
            {
                var ob = await _context.WaletsConfigurations.FirstOrDefaultAsync();
                if (ob == null)
                {
                    var t = new WaletsConfiguration
                    {
                        MinLimitForWalet = MinLimit,
                        MaxLimitForWallets = MaxLimit
                    };
                    _context.Add(t);
                    await _context.SaveChangesAsync();
                    return Ok(new { state = 1, message = "تم الحفظ بنجاح" });
                }
                else
                {
                    ob.MinLimitForWalet = MinLimit;
                    ob.MaxLimitForWallets = MaxLimit;
                    _context.Update(ob);
                    await _context.SaveChangesAsync();
                    return Ok(new { state = 1, message = "تم التعديل بنجاح" });
                }
            }
            catch (Exception)
            {
                return Ok(new { state = 0, message = "لم يتم حفظ البيانات" });
            }
        }

        /// <summary>
        /// يضيف سؤالاً جديداً أو يعدل سؤالاً موجوداً في قائمة الأسئلة الشائعة.
        /// </summary>
        /// <param name="model">بيانات السؤال والجواب.</param>
        public async Task<IActionResult> AddEditQuestion(WebsiteFAQSModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    if (model.Id == null)
                    {
                        var t = new FaqsTable
                        {
                            Question = model.Question,
                            Details = model.Details,
                            EnQuestion = model.EnQuestion,
                            EnDetails = model.EnDetails
                        };
                        _context.Add(t);
                        await _context.SaveChangesAsync();
                        return Ok(new { state = 1, message = "تم الحفظ بنجاح" });
                    }
                    else
                    {
                        var t = await _context.FaqsTables.FindAsync(model.Id);
                        t.Question = model.Question;
                        t.Details = model.Details;
                        t.EnQuestion = model.EnQuestion;
                        t.EnDetails = model.EnDetails;
                        _context.Update(t);
                        await _context.SaveChangesAsync();
                        return Ok(new { state = 1, message = "تم التعديل بنجاح" });
                    }
                }
                catch (Exception)
                {
                    return Ok(new { state = 0, message = "لم يتم حفظ البيانات" });
                }
            }
            return Ok(new { state = 0, message = "يجب تعبئة جميع الحقول" });
        }

        /// <summary>
        /// يضيف أيقونة جديدة لصناديق الأموال أو يعدل أيقونة موجودة.
        /// </summary>
        /// <param name="model">بيانات الأيقونة.</param>
        public async Task<IActionResult> AddEditIcon(MonyBoxIconsModel model)
        {
            if (!ModelState.IsValid) return Ok(new { state = 0, message = "لم يتم حفظ البيانات" });
            if (model.Id == null)
            {
                if (model.Icon == null) return Ok(new { state = 0, message = "يجب اختيار الايقونة" });
                var t = new MonyBoxsIcon();
                try
                {
                    t.Name = model.Name;
                    t.Link = HandleImages.SaveImage(model.Icon, "Icons", _hosting);
                    t.IsActive = true;
                    t.Deleted = false;
                    _context.Add(t);
                    await _context.SaveChangesAsync();
                    return Ok(new { state = 1, message = "تم الحفظ بنجاح" });
                }
                catch (Exception)
                {
                    if (t.Link != null) HandleImages.RemoveImageRoot(t.Link, "Icons", _hosting);
                    return Ok(new { state = 0, message = "لم يتم حفظ البيانات" });
                }
            }
            else
            {
                var t = await _context.MonyBoxsIcons.FindAsync(model.Id);
                try
                {
                    var oldicon = t.Link;
                    t.Name = model.Name;
                    if (model.Icon != null)
                        t.Link = HandleImages.SaveImage(model.Icon, "Icons", _hosting);
                    _context.Update(t);
                    await _context.SaveChangesAsync();
                    if (oldicon != null && t.Link != null) HandleImages.RemoveImageRoot(oldicon, "Icons", _hosting);
                    return Ok(new { state = 1, message = "تم التعديل بنجاح" });
                }
                catch (Exception)
                {
                    if (t.Link != null) HandleImages.RemoveImageRoot(t.Link, "Icons", _hosting);
                    return Ok(new { state = 0, message = "لم يتم حفظ البيانات" });
                }
            }
        }

        /// <summary>
        /// يقوم بحذف أيقونة (حذف ناعم).
        /// </summary>
        /// <param name="id">معرّف الأيقونة المطلوب حذفها.</param>
        [HttpPost]
        public async Task<IActionResult> DeleteIcon(int? id)
        {
            try
            {
                if (id == null) return Ok(new { state = 0, message = "يجب تحديد ايقونة" });
                var temp = await _context.MonyBoxsIcons.FindAsync(id);
                if (temp == null) return Ok(new { state = 0, message = "حدث خطاء اثناء العملية" });

                temp.Deleted = true;
                _context.Update(temp);
                await _context.SaveChangesAsync();
                return Ok(new { state = 1, message = "تمت العملية بنجاح" });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = "حدث خطاء اثناء العملية" });
            }
        }

        /// <summary>
        /// يقوم بحذف سؤال من قائمة الأسئلة الشائعة (حذف نهائي).
        /// </summary>
        /// <param name="id">معرّف السؤال المطلوب حذفه.</param>
        public async Task<IActionResult> DeleteQuestion(int? id)
        {
            if (id != null)
            {
                try
                {
                    var t = await _context.FaqsTables.FindAsync(id);
                    _context.Remove(t);
                    await _context.SaveChangesAsync();
                    return Ok(new { state = 1, message = "تم الحذف بنجاح" });
                }
                catch (Exception)
                {
                    return Ok(new { state = 0, message = "لم يتم الحذف " });
                }
            }
            return Ok(new { state = 0, message = "لم يتم الحذف" });
        }

        /// <summary>
        /// يعرض صفحة العروض.
        /// </summary>
        public async Task<IActionResult> Offers()
        {
            return View();
        }

        /// <summary>
        /// يعرض صفحة مقدمي الخدمة.
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Provider()
        {
            return View();
        }
    }
}