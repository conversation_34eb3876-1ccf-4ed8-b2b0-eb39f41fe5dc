﻿@model IEnumerable<HalalaPlusProject.Models.Achievement>

@{
    ViewData["Title"] = "الانجازات";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>الانجازات</h2>

<p>
    <a asp-action="Create"> انجاز جديد </a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
              العنوان
            </th>
            <th>
               العنوان انجليزي
            </th>
            
            <th>
               الصورة
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Title)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EnTitle)
            </td>

                <td>
                    <img width="100px" src="~/img/@item.Image" />
                </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.Id">تعديل </a> |
               
                <a asp-action="Delete" asp-route-id="@item.Id">حـذف</a>
            </td>
        </tr>
}
    </tbody>
</table>
