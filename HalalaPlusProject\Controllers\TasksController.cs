﻿using HalalaPlusProject.Data;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// متحكم لإدارة المهام الموكلة للموظفين، ويشمل عمليات الإنشاء، العرض، التعديل، والحذف.
    /// </summary>
    [Authorize]
    public class TasksController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IWebHostEnvironment _hosting;
        public TasksController(HalalaPlusdbContext context, IWebHostEnvironment hosting)
        {
            _context = context;
            _hosting = hosting;
        }

        // GET: Tasks
        /// <summary>
        /// يعرض قائمة بجميع المهام.
        /// </summary>
        public async Task<IActionResult> Index()
        {
            var halalaPlusdbContext = new TaskClass().retrive(_context);
            return View(halalaPlusdbContext);
        }

        // GET: Tasks/Details/5
        /// <summary>
        /// يعرض تفاصيل مهمة محددة بناءً على المعرّف.
        /// </summary>
        /// <param name="id">معرّف المهمة.</param>
        public async Task<IActionResult> Details(long? id)
        {
            if (id == null || _context.TaskesTables == null)
            {
                return NotFound();
            }

            var Taskdata = new TaskClass().retriveDetails(id ?? 0, _context);
            if (Taskdata == null)
            {
                return NotFound();
            }

            return View(Taskdata);
        }

        // GET: Tasks/Create
        /// <summary>
        /// يعرض صفحة إنشاء مهمة جديدة.
        /// </summary>
        public IActionResult Create()
        {
            ViewData["Emloyee"] = new SelectList(_context.SystemUsers.Where(p => p.AccountType == "Employee"), "AspId", "Name");
            return View();
        }

        // POST: Tasks/Create
        /// <summary>
        /// ينشئ مهمة جديدة ويحفظها في قاعدة البيانات.
        /// </summary>
        /// <param name="model">بيانات المهمة الجديدة.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CModels.TasksCreate model)
        {
            if (ModelState.IsValid)
            {
                if (new TaskClass().insert(model, _context, _hosting, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value, true).Result)
                    return Ok(new { state = 7, message = "تمت الاضافة بنجاح", url = "Index" });
            }
            //ViewData["Emloyee"] = new SelectList(_context.SystemUsers.Where(p => p.AccountType == "Employee"), "Id", "Name");
            return Ok(new { state = 0, message = "خطاء يجب التحقق من جميع الحقول" });
        }

        // GET: Tasks/Edit/5
        /// <summary>
        /// يعرض صفحة تعديل مهمة موجودة.
        /// </summary>
        /// <param name="id">معرّف المهمة المراد تعديلها.</param>
        public async Task<IActionResult> Edit(long? id)
        {
            if (id == null || _context.TaskesTables == null)
            {
                return RedirectToAction(nameof(Index));
            }
            var employeeTask = new TaskClass().retriveDetails(id ?? 0, _context);
            if (employeeTask == null)
            {
                return RedirectToAction(nameof(Index));
            }
            ViewData["Emloyee"] = new SelectList(_context.SystemUsers.Where(p => p.AccountType == "Employee"), "Id", "Name", employeeTask.Employee);
            return View(employeeTask);
        }

        // POST: Tasks/Edit/5
        /// <summary>
        /// يحفظ التعديلات على مهمة موجودة في قاعدة البيانات.
        /// </summary>
        /// <param name="model">بيانات المهمة المحدثة.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(CModels.TasksCreate model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    if (new TaskClass().update(model, _context, _hosting).Result) return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    return View(model);
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["Emloyee"] = new SelectList(_context.SystemUsers.Where(p => p.AccountType == "Employee"), "Id", "Name", model.Employee);
            return View(model);
        }

        // GET: Tasks/Delete/5
        /// <summary>
        /// يعرض صفحة تأكيد حذف المهمة.
        /// </summary>
        /// <param name="id">معرّف المهمة المراد حذفها.</param>
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null || _context.TaskesTables == null)
            {
                return NotFound();
            }
            var Taskdata = new TaskClass().retriveDetails(id ?? 0, _context);
            if (Taskdata == null)
            {
                return NotFound();
            }

            return View(Taskdata);
        }

        // POST: Tasks/Delete/5
        /// <summary>
        /// يحذف المهمة (حذف ناعم) من قاعدة البيانات بعد التأكيد.
        /// </summary>
        /// <param name="id">معرّف المهمة المطلوب حذفها.</param>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            if (_context.TaskesTables == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.SystemUsers'  is null.");
            }
            var task = await _context.TaskesTables.FindAsync(id);
            if (task != null)
            {
                task.Deleted = true;
                _context.Update(task);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// يتحقق من وجود مهمة بالمعرّف المحدد.
        /// </summary>
        /// <param name="id">معرّف المهمة.</param>
        /// <returns>True إذا كانت المهمة موجودة، وإلا False.</returns>
        private bool SystemUserExists(long id)
        {
            return (_context.TaskesTables?.Any(e => e.Id == id)).GetValueOrDefault();
        }

    }
}