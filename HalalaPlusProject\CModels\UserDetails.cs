﻿using HalalaPlusProject.Models;
using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    public class UserDetails
    {
        public GetProviderStatistics? GetProviderStatistics { get; set; }
        public long? Id { get; set; }
        //[Display(Name = "اسم مقدم الخدمة")]

        public string? Name { get; set; }
        //[Display(Name = "اسم المستخدم")]
        public string? UserName { get; set; }
        //[Display(Name = "المدينة")]
        public string? City { get; set; }
        [Required]
        //[Display(Name = "النشاط")]
        public string? ActivityName { get; set; }
        [Required]
        //[Display(Name = "اسم الممثل")]
        public string? ServiceProviderRepresent { get; set; }
        //[Display(Name = "رقم الجوال")]
        public string? PhoneNo { get; set; }
        //[Display(Name = "الايميل")]
        //[RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }
        //public string AspId { get; set; } = null!;
        //[Display(Name = "رقم السجل التجاري")]
        public string BusnissNo { get; set; } = null!;
        //[Display(Name = "رقم المنشأة")]
        public string EnterprisePhoneNo { get; set; } = null!;
        //[Display(Name = "الموقع")]
        public string Locatin { get; set; } = null!;
        //[Display(Name = "الشعار")]
        public string? Logo { get; set; } = null!;
     
        //[Display(Name = "الكاش باك")]
        public double? CashBack { get; set; }
        //[Display(Name = "بداية الخصومات")]
        public string? startDate { get; set; }
        //[Display(Name = "نهاية الخصومات")]
        public string? endDate { get; set; }
   
        //[Display(Name = "الجنسية")]
        public string? Nationality { get; set; }
        //[Display(Name = "الجنسية")]
        public int? NationalityNo { get; set; }
        //[Display(Name = "رقم الهوية")]
        public string? IdentityNo { get; set; }
        //[Display(Name = "تاريخ الميلاد")]
        public DateTime? BirthDate { get; set; }
     
        //public string? Password { get; set; }
        //[Display(Name = "رقم الحساب")]
        public string? AccountNo { get; set; }
        //[Display(Name = "كود الخصم")]
        public string? DiscountCode { get; set; }
        //[Display(Name = "المبلغ")]
        public double? Amount { get; set; }
        //[Display(Name = "الراتب")]
        public double? Salary { get; set; }
        //[Display(Name = "النسبة")]
        public double? Precentage { get; set; }

    }

    public class GrantDiscountModel {
        [Required]
        public long? Id { get; set; }
        //[Display(Name = "رقم العميل")]
        [Required]
        public string? phoneNo  { get; set; }
        //[Display(Name = "الخدمة او المنتج")]
        [Required]
        public long? product { get; set; }
        public string? productName { get; set; }
            public string? CustomerName { get; set; }
        public string? City { get; set; }
        
        //[Display(Name = "مبلغ المشتريات")]
        public double? amount { get; set; }
        [Required]
        //[Display(Name = "اجمالي الخصم")]
        public double? discount { get; set; }
        //[Display(Name = "مبلغ التوفير")]
        public DateTime? OperationDate { get; set; }
        public double? rate { get; set; }
    }
}
