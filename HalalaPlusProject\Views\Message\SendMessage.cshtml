﻿@model HalalaPlusProject.CModels.MessageRequest
@{
    ViewData["Title"] = "إشعارات Firebase";
    List<HalalaPlusProject.Services.NotificationHistoryItem> notificationHistory = ViewBag.NotificationHistory as List<HalalaPlusProject.Services.NotificationHistoryItem>;
}

<div style="max-width: 1000px; margin: 40px auto; font-family: 'Segoe UI', sans-serif; color: #333;">
    <h2 style="text-align: center; color: #6672f8;">إشعارات Firebase</h2>

    @if (ViewBag.Message != null)
    {
        <div style="background-color: #e6ffe6; color: #2d702d; padding: 12px 20px; border-radius: 5px; margin-bottom: 20px; text-align: center;">
            @ViewBag.Message
        </div>
    }

    <div style="display: flex; gap: 25px; flex-wrap: wrap;">
        <!-- Forms Section -->
        <div style="flex: 1 1 600px;">
            <div style="background: #f9f9f9; padding: 20px; border-radius: 10px; box-shadow: 0 0 8px rgba(0,0,0,0.05); margin-bottom: 30px;">
                <h4 style="margin-bottom: 15px; color: #555;">إرسال إشعار إلى جهاز محدد</h4>
                <form method="post" asp-action="SendMessage" id="sendMessageForm">
                    <div style="margin-bottom: 15px;">
                        <label asp-for="Title" class="form-label">العنوان</label>
                        <input type="text" asp-for="Title" name="Title" class="form-control" placeholder="أدخل عنوان الإشعار" />
                        <span asp-validation-for="Title" class="text-danger"></span>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label asp-for="Body" class="form-label">النص</label>
                        <textarea asp-for="Body" name="Body" class="form-control" rows="4" placeholder="أدخل نص الإشعار"></textarea>
                        <span asp-validation-for="Body" class="text-danger"></span>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label for="DeviceToken" class="form-label">المستلم</label>
                        <select name="DeviceToken" id="DeviceToken" class="form-control" required>
                            <option value="">-- اختر المستخدم --</option>
                            @foreach (var user in ViewBag.AllUsers)
                            {
                                <option value="@user.NotificationToken">@user.FullName</option>
                            }
                        </select>
                        <span asp-validation-for="DeviceToken" class="text-danger"></span>
                    </div>

                    @*                     <div style="margin-bottom: 15px;">
                    <label asp-for="DeviceToken" class="form-label">رمز الجهاز</label>
                    <input type="text" asp-for="DeviceToken" name="DeviceToken" class="form-control" placeholder="أدخل رمز الجهاز" />
                    <span asp-validation-for="DeviceToken" class="text-danger"></span>
                    </div> *@
                    <button type="submit" class="btn" style="background-color: #6672f8; color: white; padding: 10px 20px; border: none; border-radius: 5px;">إرسال</button>
                </form>
            </div>

            <div style="background: #f9f9f9; padding: 20px; border-radius: 10px; box-shadow: 0 0 8px rgba(0,0,0,0.05);">
                <h4 style="margin-bottom: 15px; color: #555;">إرسال إشعار إلى موضوع (جميع الأجهزة)</h4>
                <form id="sendTopicForm">
                    <div style="margin-bottom: 15px;">
                        <label for="Title">العنوان</label>
                        <input type="text" id="TopicTitle" name="Title" class="form-control" placeholder="أدخل عنوان الموضوع" />
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label for="Body">النص</label>
                        <textarea id="TopicBody" name="Body" class="form-control" rows="4" placeholder="أدخل نص الموضوع"></textarea>
                    </div>
                    <button type="submit" class="btn" style="background-color: #6672f8; color: white; padding: 10px 20px; border: none; border-radius: 5px;">إرسال</button>
                </form>
            </div>
        </div>

        <!-- Notification History -->
        <div style="flex: 1 1 320px;">
            <div style="background: #fff9f9; padding: 20px; border-radius: 10px; border: 1px solid #f0c8d8;overflow-y: scroll;max-height: 800px;" ">
                <h5 style="margin-bottom: 15px; color: #6672f8;">سجل الإشعارات</h5>
                <ul style="list-style: none; padding: 0; margin: 0;">
                    @if (notificationHistory != null && notificationHistory.Any())
                    {
                        foreach (var n in notificationHistory)
                        {
                            <li style="margin-bottom: 15px; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                                <strong>العنوان:</strong> @n.Title <br />
                                <strong>النص:</strong> @n.Body <br />
                                <strong>الوقت:</strong> @n.SentDateTime.ToString("dd/MM/yyyy HH:mm") <br />
                                <strong>المرسل:</strong> @n.SentBy <br />
                                <strong> نوع الاشعار:</strong> @(n.DeviceToken.StartsWith("/topics/") ? "الى عنوان عام: " + n.DeviceToken : "الى رمز جهاز") <br />
                                @*  <strong>الرمز:</strong> @n.DeviceToken *@
                            </li>
                        }
                    }
                    else
                    {
                        <li>لا يوجد سجل إشعارات</li>
                    }
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
    select {
        position: relative;
        z-index: 1;
    }

</style>
@* <script>
    document.addEventListener("DOMContentLoaded", function () {
        var dropdown = document.getElementById("DeviceToken");
        var input = document.querySelector("input[name='DeviceToken']");

        if (dropdown && input) {
            dropdown.addEventListener("change", function () {
                input.value = this.value;
            });
        }
    });
</script>
 *@

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const form = document.getElementById("sendTopicForm");

        form.addEventListener("submit", function (e) {
            e.preventDefault();

            const title = document.getElementById("TopicTitle").value;
            const body = document.getElementById("TopicBody").value;

            fetch("/Message/SendNotificationToTopic", {
                method: "POST",
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                body: `Title=${encodeURIComponent(title)}&Body=${encodeURIComponent(body)}`
            })
                .then(res => res.ok ? res.text() : Promise.reject(res.statusText))
                .then(response => {
                    alert("تم إرسال الإشعار بنجاح! 🎉");
                    // Optionally reload notification history here
                })
                .catch(err => {
                    alert("حدث خطأ أثناء الإرسال: " + err);
                });
        });
    });
</script>
