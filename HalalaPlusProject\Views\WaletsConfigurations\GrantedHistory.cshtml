﻿@using Azure.Core
@model IEnumerable<HalalaPlusProject.Models.GrantingHistory>

<h2>عمليات المنح</h2>



<table id="tbl1" class="table table-striped text-center">
    <thead>
        <tr>

            <th class="text-center">الغرض</th>
            <th class="text-center">عدد النقاط</th>
            <th class="text-center"> مرات المنح</th>
            
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>

                <td class="text-center">@item.Purpose</td>
                <td class="text-center">@item.PointsCount</td>
                <td class="text-center">@item.GrantTimes</td>
               
           </tr>
        }
    </tbody>
</table>
@section Scripts {
    <script>
        let table = new DataTable('#tbl1');
              table.order([0, 'desc']).draw();

    </script>
}