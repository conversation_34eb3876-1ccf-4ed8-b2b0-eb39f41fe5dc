﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class VwMonthlyGrowthRevenue
{
    [StringLength(4000)]
    public string? YearMonth { get; set; }

    public int? OrdersCount { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TotalRevenue { get; set; }

    [Column(TypeName = "decimal(10, 2)")]
    public decimal? AvgOrderValue { get; set; }

    public int? NewSubscriptions { get; set; }

    public int? NewCustomers { get; set; }

    public int? NewProviders { get; set; }
}
