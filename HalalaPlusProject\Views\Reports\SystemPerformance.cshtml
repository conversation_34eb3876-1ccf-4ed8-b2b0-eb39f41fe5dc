﻿@using System.Globalization
@model HalalaPlusProject.Models.vw_OverallSystemPerformance

@{
    ViewData["Title"] = "System Performance Report";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2 class="mb-4">تقرير الأداء العام للنظام</h2>

<table class="table table-bordered">
    <tbody>
        <tr><th>عدد الموظفين الفعالين</th><td>@Model.ActiveEmployees</td></tr>
        <tr><th>عدد العملاء الفعالين</th><td>@Model.ActiveCustomers</td></tr>
        <tr><th>عدد مقدمي الخدمات الفعالين</th><td>@Model.ActiveProviders</td></tr>
        <tr><th>عدد المسوقين الفعالين</th><td>@Model.ActiveMarketers</td></tr>
        <tr><th>عدد الطلبات المنفذة هذا الشهر</th><td>@Model.OrdersThisMonth</td></tr>
        <tr><th>إجمالي الإيرادات</th><td>@Model.TotalRevenue.ToString("C2", new CultureInfo("ar-SA"))</td></tr>
        <tr><th>أكثر دولة للعملاء</th><td>@Model.CustomerCountry</td></tr>
        <tr><th>أكثر دولة لمقدمي الخدمات</th><td>@Model.ProviderCountry</td></tr>
        <tr><th>أكثر دولة للمسوقين</th><td>@Model.MarketerCountry</td></tr>
        <tr><th>أكثر دولة للموظفين</th><td>@Model.EmployeeCountry</td></tr>
        <tr><th>أكثر باقة استخدامًا</th><td>@Model.TopPackageName</td></tr>
        <tr><th>عدد مرات استخدام الباقة</th><td>@Model.TopPackageUsageCount</td></tr>
    </tbody>
</table>

