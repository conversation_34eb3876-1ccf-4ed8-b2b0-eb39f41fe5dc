﻿@model HalalaPlusProject.CModels.WebsiteModel
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer

@{
    ViewData["Title"] = "التحكم بالموقع الخارجي";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="tab">
    <button class="tablinks" onclick="openCity(event, 'DataTab')"> @localizer["websitesettings"]</button>
    <button class="tablinks" onclick="openCity(event, 'Faqstab')">@localizer["faqs"]</button>
    <button class="tablinks" onclick="openCity(event, 'articlestab')">@localizer["articles"]</button>
    <button class="tablinks" onclick="openCity(event, 'contactusTab')">@localizer["messages"]</button>
    <button class="tablinks" onclick="openCity(event, 'MonyBoxsIconsTab')">@localizer["monyboxicons"]</button>
    <button class="tablinks" onclick="openCity(event, 'waletsconfig')">اعدادات المحافظ</button>


</div>


<div id="contactusTab" class="tabcontent">
    <div class="row">


        <div class="col-md-12">
            <div class="col-md-12">
                <div class="table-responsive p-0">

                    <table id="tbl12" class="table table-striped text-center">
                        <thead>
                            <tr>


                                <th scope="col"> @localizer["no"]</th>
                                <th scope="col"> @localizer["sender"]</th>
                                <th scope="col"> @localizer["email"]</th>
                                <th scope="col">@localizer["subject"] </th>
                                <th scope="col">@localizer["date"] </th>

                                <th scope="col"> @localizer["options"]</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.ContactU)
                            {
                                <tr id="<EMAIL>">
                                    <td>
                                        @Html.DisplayFor(modelItem => item.Id)
                                    </td>

                                    <td>
                                        @Html.DisplayFor(modelItem => item.Name)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.Email)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.Subject)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.OrderDate)
                                    </td>
                                    <td>
                                        <a asp-controller="Messages" asp-action="Edit" asp-route-id="@item.Id" class="btn btn-outline-info tablebtn">@localizer["showdetails"] </a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>
</div>

<div id="waletsconfig" class="tabcontent">
    <div class="row">

        <div class="col-md-12">
            <form asp-action="AddEditwaletsconfig" class="submitfm">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <div class="row">

                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="waletsconfig.MinLimit" class="control-label">اقل مبلغ</label>
                            <input asp-for="waletsconfig.MinLimit" name="MinLimit" class="form-control" />
                            <span asp-validation-for="waletsconfig.MinLimit" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="waletsconfig.MaxLimit" class="control-label">اعلى مبلغ</label>
                            <input asp-for="waletsconfig.MaxLimit" rows="10" name="MaxLimit" class="form-control" />
                            <span asp-validation-for="waletsconfig.MaxLimit" class="text-danger"></span>
                        </div>

                    </div>

                </div>





                <div class="form-group  mt-4">
                    <button type="submit" value="Create" class="btn btn-primary">@localizer["save"]</button>
                </div>
            </form>

        </div>

    </div>
</div>


<div id="articlestab" class="tabcontent">
    <div class="row">

        <div class="col-md-12">
            <form asp-action="AddEditArticle" class="submitfm">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <input type="hidden" asp-for="Article.Id" name="Id" />
                <div class="row">

                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Article.title" class="control-label">@localizer["articletitle"]</label>
                            <textarea asp-for="Article.title" name="title" class="form-control"></textarea>
                            <span asp-validation-for="Article.title" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="Article.Content" class="control-label">@localizer["content"]</label>
                            <textarea asp-for="Article.Content" rows="10" name="Content" class="form-control"></textarea>
                            <span asp-validation-for="Article.Content" class="text-danger"></span>
                        </div>

                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Article.entitle" class="control-label">@localizer["articleentitle"]</label>
                            <textarea asp-for="Article.entitle" name="entitle" class="form-control"></textarea>
                            <span asp-validation-for="Article.entitle" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="Article.EnContent" class="control-label">@localizer["encontent"]</label>
                            <textarea asp-for="Article.EnContent" rows="10" name="EnContent" class="form-control"></textarea>
                            <span asp-validation-for="Article.EnContent" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label class="control-label">image</label>
                            <input name="Image" type="file" accept="image/*" id="image" class="form-control" />
                            <span class="text-danger"></span>
                        </div>
                    </div>
                </div>





                <div class="form-group  mt-4">
                    <button type="submit" value="Create" class="btn btn-primary">@localizer["save"]</button>
                </div>
            </form>

        </div>
        <div class="col-md-12">
            <div class="col-md-12">
                <div class="table-responsive p-0">

                    <table id="tbl10" class="table table-striped text-center">
                        <thead>
                            <tr>


                                <th scope="col">@localizer["no"]</th>
                                <th scope="col"> @localizer["articletitle"]</th>
                                <th scope="col"> @localizer["articletitle"] En</th>


                                <th scope="col">@localizer["options"]</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.ArticlesList)
                            {
                                <tr id="<EMAIL>">
                                    <td>
                                        @Html.DisplayFor(modelItem => item.Id)
                                    </td>

                                    <td>
                                        @Html.DisplayFor(modelItem => item.title)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.entitle)
                                    </td>

                                    <td>
                                        <a onclick="EditArticle(@item.Id)" class="btn btn-outline-info tablebtn">@localizer["edit"]</a> |
                                        <a onclick="setField('/WebSite/DisableArticle?id='+@item.Id)" class="btn btn-outline-info tablebtn">@localizer["stop"]</a> |
                                        <a href="#" class="btn btn-outline-danger tablebtn" onclick="setField('/WebSite/DeleteArticle?id='+@item.Id)">@localizer["delete"]</a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>
</div>

<div id="DataTab" class="tabcontent" style="display:block">
    <div class="row">
        <form asp-action="SiteData" class="submitfm">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="row">

                <div class="col-md-4">

                    <div class="form-group">
                        <label asp-for="vision" class="control-label">@localizer["vision"]</label>
                        <textarea asp-for="vision" class="form-control"></textarea>
                        <span asp-validation-for="vision" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="envision" class="control-label">@localizer["vision"] En</label>
                        <textarea asp-for="envision" class="form-control"></textarea>
                        <span asp-validation-for="envision" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="overview" class="control-label">@localizer["overview"]</label>
                        <textarea asp-for="overview" class="form-control"></textarea>
                        <span asp-validation-for="overview" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="enoverview" class="control-label">@localizer["overview"]</label>
                        <textarea asp-for="enoverview" class="form-control"></textarea>
                        <span asp-validation-for="enoverview" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Defnition" class="control-label">التعريف</label>
                        <textarea asp-for="Defnition" class="form-control"></textarea>
                        <span asp-validation-for="Defnition" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="enDefnition" class="control-label">التعريف انجليزي</label>
                        <textarea asp-for="enDefnition" class="form-control"></textarea>
                        <span asp-validation-for="enDefnition" class="text-danger"></span>
                    </div>












                </div>
                <div class="col-md-4">

                    <div class="form-group">
                        <label asp-for="message" class="control-label">رسالتنا</label>
                        <textarea asp-for="message" class="form-control"></textarea>
                        <span asp-validation-for="message" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="enmessage" class="control-label">رسالتنا انجليزي</label>
                        <textarea asp-for="enmessage" class="form-control"></textarea>
                        <span asp-validation-for="enmessage" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="PhoneNo" class="control-label">@localizer["phoneno"]</label>
                        <input asp-for="PhoneNo" class="form-control" />
                        <span asp-validation-for="PhoneNo" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Email" class="control-label">@localizer["email"]</label>
                        <input asp-for="Email" class="form-control" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Place" class="control-label">@localizer["place"]</label>
                        <input asp-for="Place" class="form-control" />
                        <span asp-validation-for="Place" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Experts" class="control-label">@localizer["experts"]</label>
                        <input asp-for="Experts" class="form-control" />
                        <span asp-validation-for="Experts" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="CustomersNo" class="control-label">العملاء</label>
                        <input asp-for="CustomersNo" class="form-control" />
                        <span asp-validation-for="CustomersNo" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ProvidersNo" class="control-label">مقدمي الخدمات</label>
                        <input asp-for="ProvidersNo" class="form-control" />
                        <span asp-validation-for="ProvidersNo" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="CitiesNo" class="control-label">المدن</label>
                        <input asp-for="CitiesNo" class="form-control" />
                        <span asp-validation-for="CitiesNo" class="text-danger"></span>
                    </div>





                </div>
                <div class="col-md-4">

                    <div class="form-group">
                        <label asp-for="AppGoogleLink" class="control-label">google link</label>
                        <input asp-for="AppGoogleLink" class="form-control" />
                        <span asp-validation-for="AppGoogleLink" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="AppAppleLink" class="control-label">@localizer["appapplelink"]</label>
                        <input asp-for="AppAppleLink" class="form-control" />
                        <span asp-validation-for="AppAppleLink" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="YouTubeLink" class="control-label">LinkedIn</label>
                        <input asp-for="YouTubeLink" class="form-control" />
                        <span asp-validation-for="YouTubeLink" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="FaceookLink" class="control-label">@localizer["facbookurl"]</label>
                        <input asp-for="FaceookLink" class="form-control" />
                        <span asp-validation-for="FaceookLink" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="InstagramLink" class="control-label">@localizer["instagramlink"]</label>
                        <input asp-for="InstagramLink" class="form-control" />
                        <span asp-validation-for="InstagramLink" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="tiktok" class="control-label"> تيك توك</label>
                        <input asp-for="tiktok" class="form-control" />
                        <span asp-validation-for="tiktok" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="XLink" class="control-label">@localizer["xlink"]</label>
                        <input asp-for="XLink" class="form-control" />
                        <span asp-validation-for="XLink" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="snapchatLink" class="control-label">سناب شات</label>
                        <input asp-for="snapchatLink" class="form-control" />
                        <span asp-validation-for="snapchatLink" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="whatsappLink" class="control-label"> واتساب</label>
                        <input asp-for="whatsappLink" class="form-control" />
                        <span asp-validation-for="whatsappLink" class="text-danger"></span>
                    </div>


                </div>
                <div class="col-md-4">
                    <div class="form-group mt-4">
                        <button type="submit" value="Create" class="btn btn-primary">@localizer["create"]</button>
                    </div>


                </div>
            </div>

        </form>
    </div>


</div>

<div id="Faqstab" class="tabcontent">
    <div class="row">

        <div class="col-md-4">
            <form asp-action="AddEditQuestion" class="submitfm">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <div class="form-group">
                    <input type="hidden" name="Id" asp-for="FAQS.Id" />
                    <label asp-for="FAQS.Question" class="control-label">@localizer["question"]</label>
                    <textarea asp-for="FAQS.Question" name="Question" class="form-control"></textarea>
                    <span asp-validation-for="FAQS.Question" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <label asp-for="FAQS.EnQuestion" class="control-label">@localizer["enquestion"]</label>
                    <textarea asp-for="FAQS.EnQuestion" name="EnQuestion" class="form-control"></textarea>
                    <span asp-validation-for="FAQS.EnQuestion" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <label asp-for="FAQS.Details" class="control-label">@localizer["answer"]</label>
                    <textarea asp-for="FAQS.Details" name="Details" class="form-control"></textarea>
                    <span asp-validation-for="FAQS.Details" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <label asp-for="FAQS.EnDetails" class="control-label">@localizer["enanswer"]</label>
                    <textarea asp-for="FAQS.EnDetails" name="EnDetails" class="form-control"></textarea>
                    <span asp-validation-for="FAQS.EnDetails" class="text-danger"></span>
                </div>

                <div class="form-group  mt-4">
                    <button type="submit" value="Create" class="btn btn-primary">@localizer["create"]</button>
                </div>
            </form>

        </div>
        <div class="col-md-8">
            <div class="col-md-12">
                <div class="table-responsive p-0">

                    <table id="tbl1" class="table table-striped text-center">
                        <thead>
                            <tr>
                                <th scope="col">@localizer["questionno"] </th>
                                <th scope="col">@localizer["question"] </th>
                                <th scope="col">@localizer["answer"] </th>
                                <th scope="col">@localizer["enquestion"] </th>
                                <th scope="col">@localizer["enanswer"] </th>
                                <th scope="col"> @localizer["options"]</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.FAQSList)
                            {
                                <tr id="<EMAIL>">
                                    <td>
                                        @Html.DisplayFor(modelItem => item.Id)
                                    </td>

                                    <td>
                                        @Html.DisplayFor(modelItem => item.Question)
                                    </td>

                                    <td>
                                        @Html.DisplayFor(modelItem => item.Details)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.EnQuestion)
                                    </td>
                                    <td>
                                        @Html.DisplayFor(modelItem => item.EnDetails)
                                    </td>
                                    <td>
                                        <a onclick="EditQuestion(@item.Id)" class="btn btn-outline-info tablebtn">@localizer["edit"]</a> |
                                        <a href="#" class="btn btn-outline-danger tablebtn" onclick="DeleteQuestion('@item.Id')">@localizer["delete"]</a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>
</div>

<div id="MonyBoxsIconsTab" class="tabcontent">
    <div class="row">
        <div class="col-md-12">
            <form asp-action="AddEditIcon" class="submitfm">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <input type="hidden" name="Id" asp-for="Iconsob.Id" />
                            <label asp-for="Iconsob.Name" class="control-label">@localizer["iconname"] </label>
                            <input asp-for="Iconsob.Name" required name="Name" class="form-control" />
                            <span asp-validation-for="Iconsob.Name" class="text-danger"></span>
                        </div>

                        <div class="form-group ">
                            <label asp-for="Iconsob.Icon" class="control-label">@localizer["icon"]</label>
                            <input asp-for="Iconsob.Icon" accept=".jpg,.jpeg,.png,.gif" name="Icon" onchange="ShowImagePreview(this, document.getElementById('iconimg'))" class="form-control" />
                            <span asp-validation-for="Iconsob.Icon" class="text-danger"></span>
                        </div>
                        <div class="form-group mt-2">
                            <button type="submit" value="Create" class="btn btn-outline-primary w-100 ">@localizer["save"]</button>
                        </div>
                    </div>
                    <div class="col-md-3 align-content-center">
                        <div class="form-group">
                            <div class="row justify-content-center" style="margin-bottom:1px;">
                                <div class="circle ">
                                    <img src="" id="iconimg">
                                </div>
                            </div>

                        </div>

                    </div>

                </div>
            </form>

        </div>

        <hr class="horizontal dark my-3">
        <div class="row">
            @Html.Partial("_Icons", Model.Icons)
        </div>
    </div>
</div>
@section Scripts {

    <script>


        let table = new DataTable('#tbl1');
        let table1 = new DataTable('#tbl12');
        let table10 = new DataTable('#tbl10');
        let table221 = new DataTable('#tbl221');
        // $(document)
        //     .ready(
        //         function () {
        //             var t = $('#tbl12')
        //                 .DataTable(
        //                     {
        //                         "lengthMenu": [
        //                             [100, 200,
        //                                 500, -1],
        //                             [100, 200,
        //                                 500,
        //                                 "All"]],
        //                         "pagingType": "full",
        //                         "pageLength": 100,
        //                         "processing": true,
        //                         "serverSide": true,
        //                         scrollY: 500,
        //                         scrollX: 900,
        //                         "columnDefs": [{
        //                             "searchable": false,
        //                             "orderable": false,
        //                             "targets": [0]
        //                         }],
        //                         "order": [[2, 'asc']],
        //                         "ajax": "/websmpp/displayContact?groupdata=1&groupId=1"
        //                             // + document.groupDataEntryForm.groupId.value,
        //                     });
        //         });
    </script>
}