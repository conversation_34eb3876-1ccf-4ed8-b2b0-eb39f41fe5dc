﻿@page
@model LoginModel
@using System.Globalization
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer localizer
@{
    var isRTL = CultureInfo.CurrentCulture.Name.StartsWith("ar");

    ViewData["Title"] =isRTL ? "تسجيل الدخول - هللة بلس" : "Log In - Halala Plus";
    Layout = null;
}
<!DOCTYPE html>
<html lang="en" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
     <!-- Bootstrap CSS -->
    <link href="~/css/bootstrap.css" rel="stylesheet">
    <link href="~/css/style.css" rel="stylesheet" >
    <style>
        
 
      
      .txtdir{
            text-align: @(isRTL ?  "right;" : "left;");
        }
        
        


    .btn-primary:hover {
        color: #040202;
          background-color: #a79be8;
    border-color: #bcb3f2;
    }

        .btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active, .show > .btn-primary.dropdown-toggle {
            color: #fff;
            background-color: #694aff;
            border-color: #ffffff;
        }
    </style>
  </head>
<body class="g-sidenav-show  bg-gray-100">
        <div class="container-fluid text-center">
        <form class="mx-auto frm " id="account" method="post" style="  @(isRTL ? " text-align: right;" : " text-align: left;") ">
                  <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <h4 class="text-center">@localizer["login"]</h4>
                <div class="mb-3 mt-5">
                <label asp-for="Input.Email" class="form-label">@localizer["username"]</label>
                <input asp-for="Input.Email" class="form-control txtdir" autocomplete="username" aria-required="true" />
                   
                    <span asp-validation-for="Input.Email" class="text-danger"></span>
                </div>
                <div class="mb-3">
                     <label asp-for="Input.Password" class="form-label">@localizer["password"]</label>
                <input asp-for="Input.Password" class="form-control txtdir" autocomplete="current-password" aria-required="true" />
                   
                    <span asp-validation-for="Input.Password" class="text-danger"></span>
                  @* <a id="forgot-password" asp-page="./ForgotPassword">Forgot your password?</a> *@
                </div>
             @* <div class="mb-3">
                  <label asp-for="Input.RememberMe" class="form-label">
                            <input class="form-check-input" asp-for="Input.RememberMe" />
                            @Html.DisplayNameFor(m => m.Input.RememberMe)
                        </label>
                </div>*@
            <button type="submit" class="btn btn-primary mt-5">@localizer["login"]</button>
               @*  <p style="direction:ltr;text-align:center; display: inline;">
                   dont have account yet: <a asp-page="./Register" asp-route-returnUrl="@Model.ReturnUrl">Register For Free</a>
                </p> *@
                
              </form>
              <br />
        <partial name="_SelectLanguage" />
        </div>
@*
  <main class="main-content  mt-0">
    <section>
      <div class="page-header min-vh-75">
        <div class="container">
          <div class="row">
            <div class="col-xl-4 col-lg-5 col-md-6 d-flex flex-column mx-auto">
              <div class="card card-plain mt-8">
                <div class="card-header pb-0 text-left bg-transparent  text-center">
                  <div class="sidenav-header text-center mb-5">
                    <img src="../assets/img/logo.png" style="    max-height: 8rem;" class="navbar-brand-img h-900" alt="main_logo">
                </div>
                <br>
                  <h3 class="font-weight-bolder text-info text-gradient">أهلاَ وسهلاً</h3>
                  <p class="mb-0">مرحباَ بك مجدداَ.قم بتسجيل الدخول الى حسابك</p>
                </div>

<div class="row">
    <div class="">
       
            <form id="account" method="post">
              
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <div class="form-floating">
                    <input asp-for="Input.Email" class="form-control" autocomplete="username" aria-required="true" />
                    <label asp-for="Input.Email" class="form-label"></label>
                    <span asp-validation-for="Input.Email" class="text-danger"></span>
                </div>
                <div class="form-floating">
                    <input asp-for="Input.Password" class="form-control" autocomplete="current-password" aria-required="true" />
                    <label asp-for="Input.Password" class="form-label"></label>
                    <span asp-validation-for="Input.Password" class="text-danger"></span>
                </div>
                <div>
                    <div class="checkbox">
                        <label asp-for="Input.RememberMe" class="form-label">
                            <input class="form-check-input" asp-for="Input.RememberMe" />
                            @Html.DisplayNameFor(m => m.Input.RememberMe)
                        </label>
                    </div>
                </div>
                <div>
                    <button id="login-submit" type="submit" class="w-100 btn btn-lg btn-primary">Log in</button>
                </div>
                <div>
                    <p>
                        <a id="forgot-password" asp-page="./ForgotPassword">Forgot your password?</a>
                    </p>
                    <p>
                        <a asp-page="./Register" asp-route-returnUrl="@Model.ReturnUrl">Register as a new user</a>
                    </p>
                    <p>
                        <a id="resend-confirmation" asp-page="./ResendEmailConfirmation">Resend email confirmation</a>
                    </p>
                </div>
            </form>
       
    </div>
   <div class="col-md-6 col-md-offset-2">
        <section>
            <h3>Use another service to log in.</h3>
            <hr />
            @{
                if ((Model.ExternalLogins?.Count ?? 0) == 0)
                {
                    <div>
                        <p>
                            There are no external authentication services configured. See this <a href="https://go.microsoft.com/fwlink/?LinkID=532715">article
                            about setting up this ASP.NET application to support logging in via external services</a>.
                        </p>
                    </div>
                }
                else
                {
                    <form id="external-account" asp-page="./ExternalLogin" asp-route-returnUrl="@Model.ReturnUrl" method="post" class="form-horizontal">
                        <div>
                            <p>
                                @foreach (var provider in Model.ExternalLogins!)
                                {
                                    <button type="submit" class="btn btn-primary" name="provider" value="@provider.Name" title="Log in using your @provider.DisplayName account">@provider.DisplayName</button>
                                }
                            </p>
                        </div>
                    </form>
                }
            }
        </section>
    </div>
</div>
   
              </div>
            </div>
            
          </div>
        </div>
      </div>
    </section>
  </main>
*@
@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
</body>
</html>