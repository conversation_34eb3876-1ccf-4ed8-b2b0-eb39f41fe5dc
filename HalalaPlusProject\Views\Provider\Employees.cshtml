﻿@model IEnumerable<HalalaPlusProject.CModels.MarketerIndexModel>

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>الموظفين</h2>

<p>
    <a  asp-action="Create"> إضافة موظف</a>
</p>
<table class="table">
    <thead>
             <tr>                   
                      <th scope="col">الاسم</th>
                      <th scope="col">الجنسية</th>                        
                      <th scope="col">رقم الجوال</th>
                       <th scope="col">البريد الالكتروني</th>
                        <th scope="col">اسم المستخدم</th>
                      <th scope="col">المزيد</th>
                    </tr>
            
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Nationality)
            </td>
         
            <td>
                @Html.DisplayFor(modelItem => item.PhoneNo)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Email)
            </td>
           <td>
                @Html.DisplayFor(modelItem => item.UserName)
            </td>
           
           
          
            <td>
                <a asp-action="Edit"   asp-route-id="@item.Id">المزيد..</a>
            </td>
        </tr>
}
    </tbody>
</table>
