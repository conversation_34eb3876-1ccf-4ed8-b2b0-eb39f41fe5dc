﻿@model HalalaPlusProject.Models.MonyBoxsPoint

@{
    ViewData["Title"] = "تحرير";
}

<h1>تحرير</h1>

<h4>النقاط</h4>
<hr />
<div class="row">
    <div class="col-md-12">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="FromAmount" class="control-label">من القيمة</label>
                        <input asp-for="FromAmount" class="form-control" />
                        <span asp-validation-for="FromAmount" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ToAmount" class="control-label">إلى القيمة</label>
                        <input asp-for="ToAmount" class="form-control" />
                        <span asp-validation-for="ToAmount" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="GrntedPoints" class="control-label">النقاط الممنوحة</label>
                        <input asp-for="GrntedPoints" class="form-control" />
                        <span asp-validation-for="GrntedPoints" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="CreateAt" class="control-label">تاريخ الإنشاء</label>
                        <input asp-for="CreateAt" class="form-control" />
                        <span asp-validation-for="CreateAt" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-check form-switch">
                        <label class="form-check-label" for="DeletedSwitch">محذوف</label>
                        <input class="form-check-input" type="checkbox" asp-for="Deleted" id="DeletedSwitch" />
                    </div>
                </div>
            </div>
            <div class="form-group">
                <input type="submit" value="حفظ" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">العودة إلى القائمة</a>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}




@* @model HalalaPlusProject.Models.MonyBoxsPoint

@{
    ViewData["Title"] = "تحرير";
}

<h1>تحرير</h1>

<h4>النقاط</h4>
<hr />
<div class="row">
    <div class="col-md-12">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="FromAmount" class="control-label">من الكمية</label>
                        <input asp-for="FromAmount" class="form-control" />
                        <span asp-validation-for="FromAmount" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ToAmount" class="control-label">إلى الكمية</label>
                        <input asp-for="ToAmount" class="form-control" />
                        <span asp-validation-for="ToAmount" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="GrntedPoints" class="control-label">النقاط الممنوحة</label>
                        <input asp-for="GrntedPoints" class="form-control" />
                        <span asp-validation-for="GrntedPoints" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="CreateAt" class="control-label">تاريخ الإنشاء</label>
                        <input asp-for="CreateAt" class="form-control" />
                        <span asp-validation-for="CreateAt" class="text-danger"></span>
                    </div>
                    <div class="form-group form-check">
                        <label class="form-check-label">
                            <input class="form-check-input" asp-for="Deleted" /> محذوف
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <input type="submit" value="حفظ" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">العودة إلى القائمة</a>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
 *@

