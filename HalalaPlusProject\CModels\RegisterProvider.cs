﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    public class RegisterProvider
    {
        //[Display(Name = "اسم مقدم الخدمة")]
        public string? Name { get; set; }
        public long? Id { get; set; }
        //[Display(Name = "المدينة")]
        public string? City { get; set; }
        [Required]
        //[Display(Name = "النشاط")]
        public string? Activity { get; set; }
        [Required]
        //[Display(Name = "اسم الممثل")]
        public string? ServiceProviderRepresent { get; set; }
        //[Display(Name = "رقم الجوال")]
        public string? PhoneNumber { get; set; }
        //[Display(Name = "الايميل")]
        [RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }
    }
    public class RegisterProviderOrder : RegisterProvider {
        //[Display(Name = " تاريخ الطلب")]
        public string? Date { get; set; }
        public string? state { get; set; }
    }
    public class DisplayOffers
    {
        public long Id { get; set; }
        public string? Name { get; set; }
        public string? provider { get; set; }
        
        public string? discription { get; set; }
       
        public string? grantPoints { get; set; }
       
        public string? city { get; set; }
       
        public string? logo { get; set; }
        public string? youtube { get; set; }
        public string? twitter { get; set; }
        public string? facebook { get; set; }
        public string? insta { get; set; }
    }
    public class SiteDetails
    {
        public long Id { get; set; }
        public string? Vision { get; set; }
        public string? phoneNo { get; set; }

        public string? discription { get; set; }

        public string? Email { get; set; }

        public string? Location { get; set; }

        public string? appGoogleLink { get; set; }
        public string? appAppleLink { get; set; }
       
    }
}
