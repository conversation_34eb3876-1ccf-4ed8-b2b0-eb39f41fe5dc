﻿@model HalalaPlusProject.Models.RetriveMonyMonyView

@{
    ViewData["Title"] = "طلب سحب مبلغ الحصالة";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div>
    <a href="~/SpecialOffers/Index"><img src="../assets/img/svgs/solid/arrow-right.svg" style="width: 40px;" alt=""></a>
    <h3>طلب سحب مبلغ الحصالة </h3>

</div>
<div class="row">
    <div class="col-md-12">
        <form asp-action="Edit" enctype="multipart/form-data" class="submitfm">
            <div class="row">
                <input type="hidden" asp-for="Id" />
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="control-label">اسم الشخص: @Model.AcountOwnerName</label>
                       
                        
                    </div>
                    <div class="form-group">
                        <label  class="control-label">اسم الحصالة: @Model.MonyBox</label>
                        
                    </div>
                    <div class="form-group">
                        <label  class="control-label">المبلغ المراد سحبة: @Model.Amount</label>
                        
                    </div>
                    <div class="form-group">
                        <label  class="control-label">البنك : @Model.BankName</label>
                        
                    </div>
                      <div class="form-group">
                        <label  class="control-label">الايبان : @Model.Iban</label>
                        
                    </div>
                    
                    <div class="form-group">
                        <label  class="control-label">تاريخ الطلب : @Model.CreateAt</label>
                        
                    </div>

                     <div class="form-group">
                        <label  class="control-label">تاريخ الطلب : @Model.CreateAt</label>
                        
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label">صورة الايصال  : </label>
                        <img src="/images/@Model.Recepit" />
                        
                    </div>


                  


                </div>

                <div class="col-md-3">
                    <div class="form-group">
                        <label asp-for="PaymentId" class="control-label"></label>
                        <input asp-for="PaymentId" class="form-control" />
                        <span asp-validation-for="PaymentId" class="text-danger"></span>
                    </div>



                    <div class="form-group">
                        <label class="control-label">صورة الايصال</label>
                        <input name="recept"  type="file" required class="form-control" />
                        <span  class="text-danger"></span>
                    </div>

                </div>
            </div>

            <div class="mt-2">
                <button type="submit" value="Create" class="btn btn-primary">حفظ</button>
           
            @if (Model.Status == "wait")
            {
                <button type="button" class="btn btn-primary " onclick="accept(@Model.Id);">
                    قبول
                </button>
                <button type="button" class="btn btn-primary " onclick="reject(@Model.Id);">
                    رفض
                </button>
            } </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">الرجوع الى القائمة</a>
</div>
<script>


</script>
@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
    <script>
               document.querySelector("form.submitfm").addEventListener("submit", function (e) {
            var fileInput = document.querySelector("input[name='recept']");
            var existingImage = "@Model.Recepit"; // الصورة القديمة لو موجودة

            if (!fileInput.value && !existingImage) {
                e.preventDefault();
                alert("يجب رفع صورة الايصال قبل الحفظ");
            }
        });

    </script>

}
