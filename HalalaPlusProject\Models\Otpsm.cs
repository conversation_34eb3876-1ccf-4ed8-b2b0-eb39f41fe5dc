﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("OTPSMS")]
public partial class Otpsm
{
    [Key]
    public long Id { get; set; }

    public long? OtpId { get; set; }

    [Column("sendStatus")]
    public string? SendStatus { get; set; }

    [StringLength(50)]
    public string? MsgId { get; set; }

    [Column("status")]
    public string? Status { get; set; }

    [Column("date", TypeName = "datetime")]
    public DateTime? Date { get; set; }

    [Column("responce")]
    public string? Responce { get; set; }

    [Column("message")]
    public string? Message { get; set; }

    [Column("code")]
    [StringLength(50)]
    public string? Code { get; set; }
}
