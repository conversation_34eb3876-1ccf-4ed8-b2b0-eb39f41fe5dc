﻿@model HalalaPlusProject.CModels.CoupunModel
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = @localizer["coupondetails"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div>
    <a href="~/SpecialOffers/Index"><img src="../assets/img/svgs/solid/arrow-right.svg" style="width: 40px;" alt=""></a>
    <h3>@localizer["coupondetails"]  </h3>

</div>
<div class="row">
    <div class="col-md-12">
        
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">

                    @localizer["coupon"]
                    <br />
                     @Html.DisplayFor(model => model.Name)
       
                    </div>
                <div class="form-group">

                    @localizer["coupon"] English
                    <br />
                    @Html.DisplayFor(model => model.EnName)

                </div>
                    <div class="form-group">
                    @localizer["couponstart"]
                 <br />
                    @Html.DisplayFor(model => model.StartDate)
                    </div>


                     <div class="form-group">
                    @localizer["couponends"]
                    <br />
                        @Html.DisplayFor(model => model.EndDate)
                    </div>
                <div class="form-group">
                    @localizer["details"]
                    <br />
                    @Html.DisplayFor(model => model.Details)
                </div>
                <div class="form-group">
                    @localizer["endetails"]
                    <br />
                    @Html.DisplayFor(model => model.EnDetails)
                </div>
            
          </div>

          <div class="col-md-3">
              <div class="form-group">
                    @localizer["provider"]
                    <br />
                    @Html.DisplayFor(model => model.SysProvider)
                </div>
                <div class="form-group">
                    @localizer["providername"]
                    <br />
                    @Html.DisplayFor(model => model.ProviderName)
                </div>
                <div class="form-group">
                    @localizer["providerenname"]
                    <br />
                    @Html.DisplayFor(model => model.EnProviderName)
                </div>

                <div class="form-group">
                    @localizer["couponcode"]
                    <br />
                    @Html.DisplayFor(model => model.CoupunCode)
                </div>
                    <div class="form-group">
                    @localizer["storeoverview"]
                     <br />
                        @Html.DisplayFor(model => model.OverView)
                    </div>
                    <div class="form-group">
                    @localizer["storeoverview"]
                     <br />
                        @Html.DisplayFor(model => model.EnOverView)
                    </div>
               
        <div class="form-group">
                    @localizer["storelink"]
                     <br />
                        @Html.DisplayFor(model => model.StoreLink)
                    </div>
       
          
           
        
            </div> 
            <div class="col-md-3">
                   
                    <div class="form-group">
                        <img style="max-width:150px;" src="@Model.imgLink"/>
                    </div>
         <div class="form-group">
                    @localizer["discount"]
                     <br />
                        @Html.DisplayFor(model => model.Discount)
                    </div>
                    <div class="form-group">
                    @localizer["activity"]
                     <br />
                        @Html.DisplayFor(model => model.ActivityName)
                    </div>
            </div>
            </div>
           
            
    </div>
</div>

<div>
    <a class="btn btn-primary tablebtn" asp-action="Index">@localizer["backtolist"]</a> |
    <a class="btn btn-primary tablebtn" asp-action="Edit" asp-route-id="@Model.Id">@localizer["edit"]</a>
   
</div>
<script>


</script>
@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
