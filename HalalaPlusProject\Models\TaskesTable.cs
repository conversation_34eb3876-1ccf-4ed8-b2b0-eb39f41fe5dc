﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("TaskesTable")]
public partial class TaskesTable
{
    [Key]
    public int Id { get; set; }

    public string? TaskName { get; set; }

    public string? TaskDescribe { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? ConractDate { get; set; }

    public DateOnly? StartDate { get; set; }

    public DateOnly? EndDate { get; set; }

    public string? Notes { get; set; }

    [Column("Asp_Id")]
    [StringLength(450)]
    public string? AspId { get; set; }

    [Column("masterId")]
    [StringLength(450)]
    public string? MasterId { get; set; }

    public bool? Accepted { get; set; }

    [Column("complated")]
    public bool? Complated { get; set; }

    public string? EnTaskName { get; set; }

    public string? EnTaskDescribe { get; set; }

    public string? EnNote { get; set; }

    public bool? Deleted { get; set; }

    [ForeignKey("AspId")]
    [InverseProperty("TaskesTables")]
    public virtual AspNetUser? Asp { get; set; }

    [InverseProperty("Task")]
    public virtual ICollection<FilesTable> FilesTables { get; set; } = new List<FilesTable>();
}
