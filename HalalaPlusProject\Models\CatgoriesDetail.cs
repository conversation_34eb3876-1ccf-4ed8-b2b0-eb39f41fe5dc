﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class CatgoriesDetail
{
    [Key]
    public long Id { get; set; }

    [StringLength(250)]
    public string? CategoryName { get; set; }

    [Column("description")]
    public string? Description { get; set; }

    [Column("redeemSteps")]
    public string? RedeemSteps { get; set; }

    [Column("TANDC")]
    public string? Tandc { get; set; }

    [StringLength(50)]
    public string? Lang { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateAt { get; set; }

    public int? CategoryId { get; set; }

    [ForeignKey("CategoryId")]
    [InverseProperty("CatgoriesDetails")]
    public virtual Category? Category { get; set; }
}
