﻿@model HalalaPlusProject.CModels.ProductModel

@{
    ViewData["Title"] = "Create";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<h4>انشاء منتج جديد</h4>
<hr />
<div class="row">
   
        <form asp-action="Create" class="submitfm" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
        <div class="row">
        <div class="col-md-4">
            <div class="form-group">
                <label asp-for="Name" class="control-label">اسم المنتج</label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Description" class="control-label">الوصف</label>
                <input asp-for="Description" class="form-control" />
                <span asp-validation-for="Description" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="file" class="control-label">صورة المنتج</label>
                    <input asp-for="file" class="form-control" />
                    <span asp-validation-for="file" class="text-danger"></span>
            </div>
            </div>
            <div class="col-md-4">
            <div class="form-group">
                <label asp-for="Engname" class="control-label">اسم المنتج بالانجليزي</label>
                <input asp-for="Engname" class="form-control" />
                <span asp-validation-for="Engname" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Engdescription" class="control-label">الوصف بالانجليزي</label>
                <input asp-for="Engdescription" class="form-control" />
                <span asp-validation-for="Engdescription" class="text-danger"></span>
            </div>
            
        
            <div class="form-group">
                <label asp-for="Catagory" class="control-label">الصنف</label>
                <select asp-for="Catagory" class ="form-control" asp-items="ViewBag.Catagory"></select>
            </div>
            <div class="form-group">
                <label asp-for="Price" class="control-label">السعر</label>
                <input asp-for="Price" class="form-control" />
                <span asp-validation-for="Price" class="text-danger"></span>
            </div>
            <div class="form-group">
                    <label asp-for="SellPrice" class="control-label">سعر البيع</label>
                <input asp-for="SellPrice" class="form-control" />
                    <span asp-validation-for="SellPrice" class="text-danger"></span>
            </div>
            <div class="form-group">
                <button  type="submit" value="Create" class="btn btn-primary" >حفظ</button>
            </div>
            </div>
            </div>
        </form>
   
</div>

<div>
    <a asp-action="Index">عودة للقائمة السابقة</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
