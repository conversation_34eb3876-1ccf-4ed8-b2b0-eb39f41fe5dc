﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("Checkout")]
public partial class Checkout
{
    [Key]
    [StringLength(100)]
    public string SessionId { get; set; } = null!;

    [StringLength(250)]
    public string Type { get; set; } = null!;

    [StringLength(250)]
    public string PaymentType { get; set; } = null!;

    [StringLength(20)]
    public string Amount { get; set; } = null!;

    [StringLength(10)]
    public string CurrencyCode { get; set; } = null!;

    [StringLength(250)]
    public string State { get; set; } = null!;

    [StringLength(250)]
    public string? CustomerId { get; set; }

    [StringLength(250)]
    public string? Token { get; set; }

    public string? Agreement { get; set; }

    public string? ExtraParams { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime UpdatedAt { get; set; }

    [InverseProperty("CheckoutSession")]
    public virtual ICollection<Webhook> Webhooks { get; set; } = new List<Webhook>();
}
