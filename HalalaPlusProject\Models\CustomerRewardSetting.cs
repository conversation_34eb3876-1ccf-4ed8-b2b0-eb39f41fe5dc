﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class CustomerRewardSetting
{
    [Key]
    public int Id { get; set; }

    public int? LoginRewardPoints { get; set; }

    public bool IsLoginRewardActive { get; set; }

    public int? ChargeRewardPoints { get; set; }

    public bool IsChargeRewardActive { get; set; }

    public int? PiggyCreationBonus { get; set; }

    public bool IsPiggyCreationActive { get; set; }

    public int? SavingsGoalReward { get; set; }

    public bool IsSavingsGoalActive { get; set; }

    public int? DiscountUsageReward { get; set; }

    public bool IsDiscountUsageActive { get; set; }

    public int? DigitalPurchaseReward { get; set; }

    public bool IsDigitalPurchaseActive { get; set; }

    public int? PlanCreationBonus { get; set; }

    public bool IsPlanCreationActive { get; set; }

    public int? EngagementReward { get; set; }

    public bool IsEngagementRewardActive { get; set; }

    [Column("PointValueSAR", TypeName = "decimal(10, 2)")]
    public decimal? PointValueSar { get; set; }

    public bool IsPointValueActive { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MaxTransferAmount { get; set; }

    public bool IsTransferLimitActive { get; set; }
}
