﻿@model HalalaPlusProject.Models.BanksAccount

@{
    ViewData["Title"] = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Details</h1>

<div>
    <h4>BanksAccount</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.BankName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.BankName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.ConnectionCode)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.ConnectionCode)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.AccountNumber)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.AccountNumber)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
