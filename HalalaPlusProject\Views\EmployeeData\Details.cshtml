﻿@model HalalaPlusProject.CModels.TasksDetails

@{
    ViewData["Title"] = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

    <h4>تفاصيل المهمة</h4>
    <hr />
         <div class="col-md-12">
         <div class="row">

     
     <div class="col-md-5">

          <div class="form-group">
                <label  class="form-label">الاسم</label>
                <label  class="form-control">@Model.TaskName</label>               
            </div>
               <div class="form-group">
                <label  class="form-label">الجنسية</label>
                <label  class="form-control">@Model.TaskDescribe</label>               
            </div>
               <div class="form-group">
                <label  class="form-label">رقم الهوية</label>
                <label  class="form-control">@Model.StartDate</label>               
            </div>
              <div class="form-group">
                <label  class="form-label">تاريخ الميلاد</label>
                <label  class="form-control">@Model.EndDate</label>               
            </div>
             


        </div>
         <div class="col-md-5">

             <div class="form-group">
                <label  class="form-label">ملاحظات</label>
                <label  class="form-control">@Model.Notes</label>               
            </div>
            <div class="form-group">
                <label  class="form-label"> اسم الموظف</label>
                <label  class="form-control">@Model.Employee</label>               
            </div>
            <div class="form-group">
                <label  class="form-label"> اسم المرفقات</label>
                <label  class="form-control">@Model.Files</label>               
            </div>

          </div> 
          </div>
          </div>
<div>
     <form asp-action="CloseTask" class="submitfm">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-primary" >إغلاق المهمة</button>|
        <a asp-action="Tasks"> عودة للقائمة</a>
    </form>
</div>
