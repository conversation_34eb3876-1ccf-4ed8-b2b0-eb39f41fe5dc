﻿using HalalaPlusProject.Areas.Identity.Data;
using HalalaPlusProject.CModels;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة الطلبات المختلفة في النظام، مثل طلبات البطاقات، الخصومات، ومقدمي الخدمة، والموافقة عليها أو رفضها.
    /// </summary>
    [Authorize]
    public class OrdersController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly UserManager<HalalaPlusProjectUser> _userManager;
        private readonly IStringLocalizer<OrdersController> _localization;

        /// <summary>
        /// إنشاء نسخة جديدة من وحدة التحكم مع حقن الخدمات المطلوبة.
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات.</param>
        /// <param name="_localization">خدمة الترجمة.</param>
        /// <param name="userManager">خدمة إدارة المستخدمين.</param>
        public OrdersController(HalalaPlusdbContext context, IStringLocalizer<OrdersController> _localization, UserManager<HalalaPlusProjectUser> userManager)
        {
            _context = context;
            _userManager = userManager;
            this._localization = _localization;
        }

        /// <summary>
        /// عرض جميع الطلبات، مصنفة إلى طلبات جديدة، مقبولة، ومرفوضة.
        /// </summary>
        /// <returns>عرض يحتوي على قوائم الطلبات المصنفة.</returns>
        public async Task<IActionResult> Index()
        {
            CModels.OrdersIndexModel ob = new CModels.OrdersIndexModel();
            OrdersClass oc = new OrdersClass();
            ob.newOrders = oc.retrive(_context);
            ob.acceptedOrders = oc.retrive(_context, "Accepted");
            ob.rejectedOrders = oc.retrive(_context, "rejected");
            return View(ob);
        }

        /// <summary>
        /// قبول طلب معين وتغيير حالته إلى "مقبول".
        /// </summary>
        /// <param name="id">معرف الطلب.</param>
        /// <param name="type">نوع الطلب (1: بطاقة، 2: خصم، 3: مقدم خدمة).</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        public async Task<IActionResult> AcceptOrder(long? id, int? type)
        {
            if (id != null && type != null)
            {
                if (type == 1)
                {
                    if (await new OrdersClass().SetCardOrder(_context, id ?? 0, "Accepted")) return Ok(new { state = 1, message = _localization["requestaccepted"].Value });
                }
                else if (type == 2)
                {
                    if (await new OrdersClass().SetDiscountOrder(_context, id ?? 0, "Accepted")) return Ok(new { state = 1, message = _localization["requestaccepted"].Value });
                }
                else if (type == 3)
                {
                    if (await new OrdersClass().SetProviderOrder(_context, id ?? 0, "Accepted")) return Ok(new { state = 1, message = _localization["requestaccepted"].Value });
                }
            }
            return Ok(new { state = 0, message = _localization["requestnotaccepted"].Value });
        }

        /// <summary>
        /// رفض طلب معين وتغيير حالته إلى "مرفوض".
        /// </summary>
        /// <param name="id">معرف الطلب.</param>
        /// <param name="type">نوع الطلب (1: بطاقة، 2: خصم، 3: مقدم خدمة).</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        public async Task<IActionResult> RejectOrder(long? id, int? type)
        {
            if (id != null && type != null)
            {
                if (type == 1)
                {
                    if (await new OrdersClass().SetCardOrder(_context, id ?? 0, "rejected")) return Ok(new { state = 1, message = _localization["requestaccepted"].Value });
                }
                else if (type == 2)
                {
                    if (await new OrdersClass().SetDiscountOrder(_context, id ?? 0, "rejected")) return Ok(new { state = 1, message = _localization["requestaccepted"].Value });
                }
                else if (type == 3)
                {
                    if (await new OrdersClass().SetProviderOrder(_context, id ?? 0, "rejected")) return Ok(new { state = 1, message = _localization["requestaccepted"].Value });
                }
            }
            return Ok(new { state = 0, message = _localization["requestnotaccepted"].Value });
        }

        /// <summary>
        /// عرض التفاصيل الخاصة بطلب بطاقة معين.
        /// </summary>
        /// <param name="id">معرف طلب البطاقة.</param>
        /// <returns>عرض يحتوي على تفاصيل طلب البطاقة.</returns>
        public async Task<IActionResult> CardDetails(long? id)
        {
            try
            {
                if (id != null)
                {
                    var ob = _context.CardsOrders.Find(id);
                    if (ob == null) return RedirectToAction(nameof(Index));
                    CardOrdersModel temp = new CardOrdersModel()
                    {
                        Id = ob.Id,
                        Name = ob.Name,
                        PhoneNo = ob.PhoneNo,
                        OrderDate = ob.Orderdate.ToString(),

                    };
                    return View(temp);

                }


            }
            catch (Exception ex)
            {
            }
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// عرض التفاصيل الخاصة بطلب تسجيل مقدم خدمة.
        /// </summary>
        /// <param name="id">معرف مقدم الخدمة.</param>
        /// <returns>عرض يحتوي على تفاصيل طلب مقدم الخدمة.</returns>
        public async Task<IActionResult> ProviderDetails(long? id)
        {
            try
            {
                if (id != null)
                {
                    var ob = _context.SystemUsers.Find(id);
                    if (ob == null) return RedirectToAction(nameof(Index));
                    RegisterProviderOrder temp = new RegisterProviderOrder()
                    {
                        Id = ob.Id,
                        Name = ob.Name,
                        PhoneNumber = ob.PhoneNo,
                        Date = ob.Orderdate.ToString(),
                        Activity = _context.Activities.Find(ob.Activity)?.Name,
                        City = _context.CitiesTables.Find(ob.City)?.City,
                        Email = ob.Email,
                        ServiceProviderRepresent = ob.ServiceProviderRepresent

                    };
                    return View(temp);

                }


            }
            catch (Exception ex)
            {
            }
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// عرض التفاصيل الخاصة بطلب إنشاء خصم.
        /// </summary>
        /// <param name="id">معرف الخصم.</param>
        /// <returns>عرض يحتوي على تفاصيل طلب الخصم.</returns>
        public async Task<IActionResult> DiscountDetails(long? id)
        {
            try
            {
                if (id != null)
                {
                    var ob = _context.DiscountsTables.Find(id);
                    if (ob == null) return RedirectToAction(nameof(Index));
                    DiscountOrderModel temp = new DiscountOrderModel()
                    {
                        Id = ob.Id,
                        DiscountName = ob.DisCountName,
                        Discount = ob.Discount,
                        Date = ob.Orderdate.ToString(),
                        Conditions = ob.Conditions,
                        EndDate =  ob.EndDate  ,
                        StartDate =  ob.StartDate  ,
                        Provider = ob.User.Name,


                    };
                    return View(temp);

                }


            }
            catch (Exception ex)
            {
            }
            return RedirectToAction(nameof(Index));
        }


    }
}