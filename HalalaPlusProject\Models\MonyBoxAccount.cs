﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class MonyBoxAccount
{
    [Key]
    public long Id { get; set; }

    public long? MonyBoxId { get; set; }

    public long? UserId { get; set; }

    public long? UserAccId { get; set; }

    public int? BankId { get; set; }

    public double? Deduction { get; set; }

    public int? DeductionDay { get; set; }

    public double? DeductionRate { get; set; }

    [Column("state")]
    [StringLength(50)]
    public string? State { get; set; }

    [Column("isActive")]
    public bool? IsActive { get; set; }

    [Column("discountMethod")]
    public byte? DiscountMethod { get; set; }

    [ForeignKey("MonyBoxId")]
    [InverseProperty("MonyBoxAccounts")]
    public virtual UsersMonyBox? MonyBox { get; set; }

    [InverseProperty("Account")]
    public virtual ICollection<MonyBoxTransaction> MonyBoxTransactions { get; set; } = new List<MonyBoxTransaction>();

    [ForeignKey("UserId")]
    [InverseProperty("MonyBoxAccounts")]
    public virtual SystemUser? User { get; set; }
}
