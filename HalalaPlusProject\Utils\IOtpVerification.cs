﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HalalaPlusProject.Utils
{
    public interface IOtpVerification
    {
        OtpVia Generate(string id);
        OtpVia Generate(string id, out DateTime expire);
        OtpVia Generate(string id, out DateTime expire,out string hash);
        OtpVia Generate(string id, OtpVerificationOptions option);
        OtpVia Generate(string id, OtpVerificationOptions option, out DateTime expire);
        bool Scan(string id, string plain);
        bool Scan(string id, string plain,string hash);
        bool Scan(string id, string plain, OtpVerificationOptions option);
        bool <PERSON>an(string id, string plain, int expire);
        bool <PERSON>an(string url);
    }
}
