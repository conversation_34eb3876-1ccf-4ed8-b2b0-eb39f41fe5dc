﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Controllers;
using HalalaPlusProject.Models;
using HalalaPlusProject.Utils;
using Microsoft.Extensions.Localization;
namespace HalalaPlusProject.CustomClasses
{
    public class DiscountsClass
    {
        public async Task< Object> insert(CModels.DiscountsModel model, IStringLocalizer<ServiceProviderController> _localization, HalalaPlusdbContext _context, long userId,string masterId,bool isOrder=true)
        {

            try
            {
                    if (model.Id == null || model.Id == 0)
                    {

                        var ob = new DiscountsTable();
                    ob.StartDate = DateOnly.Parse(model.StartDate.ToString());
                    ob.EndDate = DateOnly.Parse(model.EndDate.ToString());
                    ob.Discount = model.Discount;
                        ob.Conditions = model.Conditions;
                        ob.DisCountName = model.DiscountName;
                        ob.EnDiscountName = model.EnDiscountName;
                        ob.EnConditions = model.EnConditions;
                        ob.IsOrder = isOrder;
                        ob.IsActive = true;
                    ob.DiscountCode= Generator.RandomString(6, StringsOfLetters.NumberAndUpper);
                    bool f = true;
                    do
                    {
                        if (_context.DiscountsTables.Where(p => p.DiscountCode == ob.DiscountCode).Any())
                        {
                            ob.DiscountCode = Generator.RandomString(6, StringsOfLetters.NumberAndUpper);
                        }
                        else
                        {
                            f=false;
                        }
                    }while (f);

                        if (isOrder==true)
                        ob.Orderstate = "new";
                        ob.UserId = userId;
                         ob.MasterId = _context.SystemUsers.FirstOrDefault(p => p.AspId == masterId).Id;
                        _context.Add(ob);
                        await  _context.SaveChangesAsync();
                        return new { state = 5, message = _localization["savedsuccessfuly"].Value };
                    }
                    else
                    {
                        var ob = _context.DiscountsTables.Find(model.Id);
                    ob.StartDate = DateOnly.Parse(model.StartDate.ToString());
                    ob.EndDate = DateOnly.Parse(model.EndDate.ToString());
                    ob.Discount = model.Discount;
                        ob.Conditions = model.Conditions;
                    ob.EnConditions = model.EnConditions;
                    ob.DisCountName = model.DiscountName;
                    ob.EnDiscountName = model.EnDiscountName;
                    _context.Update(ob);
                        await _context.SaveChangesAsync();
                        return new { state = 5, message = _localization["modefiedsuccessfuly"].Value };
                    }
                }
            
            catch (Exception)
            {

                return new { state = 0, message = _localization["anerroroccured"].Value };
            }

        }
        public async Task<bool> disable(HalalaPlusdbContext _context, long id)
        {
            try
            {
                var temp = _context.DiscountsTables.Find(id);
                if (temp == null) return false;
                temp.IsActive = false;
                _context.Update(temp);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
        public Object insert(CModels.DiscountsModel model, HalalaPlusdbContext _context, long userId, bool isOrder = true)
        {
            try
            {
                if (model.Id == null || model.Id == 0)
                {
                    var ob = new DiscountsTable();

                    if (model.StartDate.HasValue)
                    {
                        string startDateStr = model.StartDate.Value.ToString("yyyy/MM/dd");
                        ob.StartDate = DateOnly.Parse(startDateStr);
                    }

                    if (model.EndDate.HasValue)
                    {
                        string endDateStr = model.EndDate.Value.ToString("yyyy/MM/dd");
                        ob.EndDate = DateOnly.Parse(endDateStr);
                    }

                    ob.Discount = model.Discount;
                    ob.EnDiscountName = model.EnDiscountName;
                    ob.Conditions = model.Conditions;
                    ob.DisCountName = model.DiscountName;
                    ob.IsOrder = isOrder;
                    ob.GrantPoints = (model.GrantType == "2" || model.GrantType == "3") ? true : false;
                    ob.GrantDiscount = (model.GrantType == "1" || model.GrantType == "2") ? true : false;
                    ob.GrantType = model.GrantType;
                    ob.IsActive = true;
                    if (isOrder)
                        ob.Orderstate = "new";
                    ob.UserId = userId;

                    _context.Add(ob);
                    _context.SaveChanges();

                    return new { state = 5, message = "تم الحفظ بنجاح" };
                }
                else
                {
                    var ob = _context.DiscountsTables.Find(model.Id);

                    if (model.StartDate.HasValue)
                    {
                        ob.StartDate = model.StartDate.Value ;
                    }
                    else
                    {
                        ob.StartDate = default;   
                    }

                    if (model.EndDate.HasValue)
                    {
                        ob.EndDate =  model.EndDate.Value ;
                    }
                    else
                    {
                        ob.EndDate = default;  // Handle null scenario if necessary
                    }
                    ob.EnDiscountName = model.EnDiscountName;
                    ob.Discount = model.Discount;
                    ob.Conditions = model.Conditions;
                    ob.DisCountName = model.DiscountName;
                    ob.GrantPoints = (model.GrantType == "2" || model.GrantType == "3") ? true : false;
                    ob.GrantDiscount = (model.GrantType == "1" || model.GrantType == "2") ? true : false;
                    ob.GrantType = model.GrantType;

                    _context.Update(ob);
                    _context.SaveChanges();

                    return new { state = 7, message = "تم التعديل بنجاح" };
                }
            }
            catch (Exception e)
            {
                return new { state = 0, message = "خطاء " };
            }
        }

        public async Task<Object> delete(long Id, HalalaPlusdbContext _context, IStringLocalizer<ServiceProviderController> _localization)
        {

            try
            {
                var ob = _context.DiscountsTables.Find(Id);
                if (ob != null)
                {
                    if (_context.GrantedDiscounts.FirstOrDefault(p => p.ProductId == ob.Id) != null)
                    {
                        ob.Deleted = true;
                        _context.Update(ob);
                      await  _context.SaveChangesAsync();
                        return new { state = 1, message = _localization["deletessuccessfuly"].Value };
                    }
                    else
                    {
                        _context.Remove(ob);
                        await _context.SaveChangesAsync();
                        return new { state = 1, message = _localization["deletessuccessfuly"].Value };
                    }
                }

            }
            catch (Exception)
            {

                return new { state = 0, message = _localization["datanotdeleted"].Value };
            }
            return new { state = 0, message = _localization["nodata"].Value };
        }

        public async Task<List<DiscountsModel>> retrive(long userId, HalalaPlusdbContext _context)
        {
            try
            {
                if (userId != 0)
                    return _context.DiscountsTables.Where(p => p.UserId == userId).Select(o => new DiscountsModel {EnConditions=o.EnConditions,EnDiscountName=o.EnDiscountName, Id = o.Id, Conditions = o.Conditions, Discount = o.Discount, DiscountName = o.DisCountName, SStartDate = o.StartDate.Value.ToString("yyyy-MM-dd"), SEndDate = o.EndDate.Value.ToString("yyyy-MM-dd"),GrantType=(o.GrantDiscount==true&&o.GrantPoints==true)?"خصم ونقاط":(o.GrantDiscount == true && o.GrantPoints == false)?"خصم فقط":"نقاط فقط" }).ToList();
            }
            catch (Exception)
            {
                return new List<DiscountsModel>();
            }
            return new List<DiscountsModel>();
        }
    }
}
