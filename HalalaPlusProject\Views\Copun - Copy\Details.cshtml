﻿@model HalalaPlusProject.CModels.CoupunModel

@{
    ViewData["Title"] = "تفاصيل الكود ";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div>
    <a href="~/SpecialOffers/Index"><img src="../assets/img/svgs/solid/arrow-right.svg" style="width: 40px;" alt=""></a>
    <h3>تفاصيل الكود </h3>

</div>
<div class="row">
    <div class="col-md-12">
        
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        
            @Html.DisplayNameFor(model => model.Name)
         <br />
            @Html.DisplayFor(model => model.Name)
       
                    </div>
                    <div class="form-group">
                        @Html.DisplayNameFor(model => model.StartDate)
         <br />
            @Html.DisplayFor(model => model.StartDate)
                    </div>


                     <div class="form-group">
                          @Html.DisplayNameFor(model => model.EndDate)
        <br />
            @Html.DisplayFor(model => model.EndDate)
                    </div>

            
          </div>

          <div class="col-md-3">
                      <div class="form-group">
                          @Html.DisplayNameFor(model => model.CoupunCode)
                     <br />
                        @Html.DisplayFor(model => model.CoupunCode)
                    </div>
                    <div class="form-group">
                          @Html.DisplayNameFor(model => model.OverView)
                     <br />
                        @Html.DisplayFor(model => model.OverView)
                    </div>
        <div class="form-group">
                          @Html.DisplayNameFor(model => model.StoreLink)
                     <br />
                        @Html.DisplayFor(model => model.StoreLink)
                    </div>
       
          
           
        
            </div> 
            <div class="col-md-3">
                   
                    <div class="form-group">
                        <img style="max-width:150px;" src="@Model.imgLink"/>
                    </div>
        
            </div>
            </div>
           
            
    </div>
</div>

<div>
    <a asp-action="Index">الرجوع الى القائمة</a> |
    <a asp-action="Edit" asp-route-id="@Model.Id">تعديل</a>
   
</div>
<script>


</script>
@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
