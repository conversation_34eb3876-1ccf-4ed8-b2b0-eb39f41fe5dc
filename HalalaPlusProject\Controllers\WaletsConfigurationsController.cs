﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Data;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;


namespace HalalaPlusProject.Controllers
{
    [Authorize]
    /// <summary>
    /// متحكم لإدارة الإعدادات العامة لمحافظ المستخدمين، مثل الحدود، نقاط الشحن، والكاش باك.
    /// </summary>
    public class WaletsConfigurationsController : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public WaletsConfigurationsController(HalalaPlusdbContext context)
        {
            _context = context;
        }



        // GET: WaletsConfigurations
        /// <summary>
        /// يعرض صفحة الإعدادات الحالية للمحافظ.
        /// </summary>
        public async Task<IActionResult> Index()
        {
            var waletsConfiguration = await _context.WaletsConfigurations.FirstOrDefaultAsync();
            if (waletsConfiguration == null)
            {
                return NotFound();
            }
            var viewModel = new WaletsConfigurationViewModel
            {
                Id = waletsConfiguration.Id,
                MaxLimitForWallets = waletsConfiguration.MaxLimitForWallets ?? 0,
                MinLimitForWalet = waletsConfiguration.MinLimitForWalet ?? 0,
                GrantMonyBoxsChargePoints = waletsConfiguration.GrantMonyBoxsChargePoints,
                MaxlimtForAllMonyBoxs = waletsConfiguration.MaxlimtForAllMonyBoxs ?? 0,
                CashBackPrecent = waletsConfiguration.CashBackPrecent ?? 0,
                MaxCachBackAmount = waletsConfiguration.MaxCachBackAmount ?? 0,
                GrantCachBack = waletsConfiguration.GrantCachBack,
                CachBackStartDate = waletsConfiguration.CachBackStartDate,
                CachBackEndDate = waletsConfiguration.CachBackEndDate,
                WelcomPointsNo = waletsConfiguration.WelcomPointsNo ?? 0,
                AmountForWelcomePint = waletsConfiguration.AmountForWelcomePint ?? 0,
                IsWecomePointsActive = waletsConfiguration.IsWecomePointsActive ?? false
            };
            return View(viewModel);
        }

       
        
        // GET: WaletsConfigurations/Edit
        /// <summary>
        /// يعرض صفحة تعديل الإعدادات العامة للمحافظ.
        /// </summary>
        public async Task<IActionResult> Edit()
        {
            var waletsConfiguration = await _context.WaletsConfigurations.FirstOrDefaultAsync();
            if (waletsConfiguration == null)
            {
                return NotFound();
            }
            var viewModel = new WaletsConfigurationViewModel
            {
                Id = waletsConfiguration.Id,
                MaxLimitForWallets = waletsConfiguration.MaxLimitForWallets,
                MinLimitForWalet = waletsConfiguration.MinLimitForWalet,
                GrantMonyBoxsChargePoints = waletsConfiguration.GrantMonyBoxsChargePoints,
                MaxlimtForAllMonyBoxs = waletsConfiguration.MaxlimtForAllMonyBoxs,
                CashBackPrecent = waletsConfiguration.CashBackPrecent,
                MaxCachBackAmount = waletsConfiguration.MaxCachBackAmount,
                GrantCachBack = waletsConfiguration.GrantCachBack,
                CachBackStartDate = waletsConfiguration.CachBackStartDate,
                CachBackEndDate = waletsConfiguration.CachBackEndDate,
                WelcomPointsNo = waletsConfiguration.WelcomPointsNo,
                AmountForWelcomePint = waletsConfiguration.AmountForWelcomePint,
                IsWecomePointsActive = waletsConfiguration.IsWecomePointsActive.GetValueOrDefault()
            };
            return View(viewModel);
        }

        // POST: WaletsConfigurations/Edit
        /// <summary>
        /// يستقبل الإعدادات المحدثة ويحفظها في قاعدة البيانات.
        /// </summary>
        /// <param name="viewModel">نموذج العرض الذي يحتوي على الإعدادات الجديدة.</param>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit([Bind("Id,MaxLimitForWallets,MinLimitForWalet,GrantMonyBoxsChargePoints,MaxlimtForAllMonyBoxs,CashBackPrecent,MaxCachBackAmount,GrantCachBack,CachBackStartDate,CachBackEndDate,WelcomPointsNo,AmountForWelcomePint,IsWecomePointsActive")] WaletsConfigurationViewModel viewModel)
        {
            //if (!ModelState.IsValid)
            //{
            //    return View(viewModel);
            //}

            try
            {
                var waletsConfiguration = await _context.WaletsConfigurations.FirstOrDefaultAsync();
                if (waletsConfiguration == null)
                {
                    return NotFound();
                }

                waletsConfiguration.MaxLimitForWallets = viewModel.MaxLimitForWallets;
                waletsConfiguration.MinLimitForWalet = viewModel.MinLimitForWalet;
                waletsConfiguration.GrantMonyBoxsChargePoints = viewModel.GrantMonyBoxsChargePoints;
                waletsConfiguration.MaxlimtForAllMonyBoxs = viewModel.MaxlimtForAllMonyBoxs;
                waletsConfiguration.CashBackPrecent = viewModel.CashBackPrecent;
                waletsConfiguration.MaxCachBackAmount = viewModel.MaxCachBackAmount;
                waletsConfiguration.GrantCachBack = viewModel.GrantCachBack;
                waletsConfiguration.CachBackStartDate = viewModel.CachBackStartDate;
                waletsConfiguration.CachBackEndDate = viewModel.CachBackEndDate;
                waletsConfiguration.WelcomPointsNo = viewModel.WelcomPointsNo;
                waletsConfiguration.AmountForWelcomePint = viewModel.AmountForWelcomePint;
                waletsConfiguration.IsWecomePointsActive = viewModel.IsWecomePointsActive;

                _context.Update(waletsConfiguration);
                await _context.SaveChangesAsync();
                return Ok(new { state = 1, message = "تم الحفظ بنجاح" });
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!_context.WaletsConfigurations.Any(e => e.Id == viewModel.Id))
                {
                    return NotFound();
                }
                else
                {
                    return Ok(new { state = 0, message = "فشل الحفظ" });
                }
            }
            //  return RedirectToAction(nameof(Index));
        }

        // GET: WaletsConfigurations
        /// <summary>
        /// يعرض صفحة الإعدادات الحالية للمحافظ.
        /// </summary>
        public async Task<IActionResult> GrantedHistory()
        {
            var waletsConfiguration = await _context.GrantingHistories.ToListAsync();
            if (waletsConfiguration == null)
            {

                return View();
            }
           
            return View(waletsConfiguration);
        }



    }
}