﻿using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using static System.Net.Mime.MediaTypeNames;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    public class ReportsController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IWebHostEnvironment _host;

        public ReportsController(HalalaPlusdbContext context, IWebHostEnvironment host)
        {
            _context = context;
            _host = host;
        }
        public async Task<IActionResult> SystemPerformance()
        {
            var model = _context.vw_OverallSystemPerformance
                .FromSqlRaw("SELECT * FROM vw_OverallSystemPerformance")
                .AsEnumerable()
                .FirstOrDefault();

            return View(model);

        }

        public async Task<IActionResult> TopCustomersReport()
        {
            var data = _context.vw_TopCustomersReport
               .FromSqlRaw("SELECT * FROM vw_TopCustomersReport").OrderByDescending(c => c.TotalOrders)
               .AsNoTracking()
               .ToList();  
            return View(data);

        }
        public async Task<IActionResult> TopProvidersReport()
        {
            var data = _context.vw_TopProvidersReport
               .FromSqlRaw("SELECT * FROM vw_TopProvidersReport").OrderBy(c => c.DiscountsCount)
               .AsNoTracking()
               .ToList();
            return View(data);

        }
        
        public async Task<IActionResult> SubscriptionsOverview()
        {
            var data = _context.vw_SubscriptionsOverview
               .FromSqlRaw("SELECT * FROM vw_SubscriptionsOverview")/*.OrderBy(c => c.DiscountsCount)*/
               .AsNoTracking()
               .ToList();
            return View(data);

        }
        public async Task<IActionResult> MonthlyGrowth()
        {
            var data = _context.vw_MonthlyGrowthRevenue
               .FromSqlRaw("SELECT * FROM vw_MonthlyGrowthRevenue")/*.OrderBy(c => c.DiscountsCount)*/
               .AsNoTracking()
               .ToList();
            return View(data);

        }
    }
}
