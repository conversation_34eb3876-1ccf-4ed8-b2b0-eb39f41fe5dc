﻿using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using static System.Net.Mime.MediaTypeNames;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    /// <summary>
    /// يتحكم في إدارة الإنجازات داخل النظام عرض ، إنشاء ، تعديل ، حذف.
    /// </summary>
    public class AchievementsController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IWebHostEnvironment _host;

        public AchievementsController(HalalaPlusdbContext context, IWebHostEnvironment host)
        {
            _context = context;
            _host = host;
        }

        // GET: Achievements
        /// <summary>
        /// يعرض قائمة بجميع الإنجازات.
        /// </summary>
        public async Task<IActionResult> Index()
        {
            return View(await _context.Achievements.ToListAsync());
        }

        // GET: Achievements/Details/5
        //public async Task<IActionResult> Details(int? id)
        //{
        //    if (id == null || _context.Achievements == null)
        //    {
        //        return NotFound();
        //    }

        //    var achievement = await _context.Achievements
        //        .FirstOrDefaultAsync(m => m.Id == id);
        //    if (achievement == null)
        //    {
        //        return NotFound();
        //    }

        //    return View(achievement);
        //}

        // GET: Achievements/Create
        /// <summary>
        /// يعرض نموذج إنشاء إنجاز جديد.
        /// </summary>
        public IActionResult Create()
        {
            return View();
        }

        // POST: Achievements/Create
        /// <summary>
        /// يحفظ إنجاز جديد في قاعدة البيانات ويعالج صورة الإنجاز إن وجدت.
        /// </summary>
        /// <param name="achievement">بيانات الإنجاز.</param>
        /// <param name="file">الصورة المرفقة.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Title,EnTitle,Details,EnDetails")] Achievement achievement, IFormFile file)
        {
            if (ModelState.IsValid)
            {
                if (file != null) achievement.Image = HandleImages.SaveImage(file, "img", _host);
                _context.Add(achievement);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(achievement);
        }

        // GET: Achievements/Edit/5
        /// <summary>
        /// يعرض نموذج تعديل إنجاز محدد.
        /// </summary>
        /// <param name="id">معرف الإنجاز.</param>
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null || _context.Achievements == null)
            {
                return NotFound();
            }

            var achievement = await _context.Achievements.FindAsync(id);
            if (achievement == null)
            {
                return NotFound();
            }
            return View(achievement);
        }

        // POST: Achievements/Edit/5
        /// <summary>
        /// يعدل بيانات إنجاز موجود ويحدث الصورة إذا تم رفعها.
        /// </summary>
        /// <param name="id">معرف الإنجاز.</param>
        /// <param name="achievement">البيانات المعدلة.</param>
        /// <param name="file">الصورة الجديدة.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Title,EnTitle,Details,EnDetails")] Achievement achievement, IFormFile? file)
        {
            if (id != achievement.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var t = _context.Achievements.AsNoTracking().FirstOrDefault(p => p.Id == achievement.Id);
                    achievement.Image = t.Image;
                    string old = achievement.Image;
                    if (file != null) achievement.Image = HandleImages.SaveImage(file, "img", _host);

                    _context.Update(achievement);
                    await _context.SaveChangesAsync();
                    if (file != null)
                        if (old != null) HandleImages.RemoveImage(old, "img", _host);
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!AchievementExists(achievement.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(achievement);
        }

        // GET: Achievements/Delete/5
        /// <summary>
        /// يعرض تأكيد حذف الإنجاز.
        /// </summary>
        /// <param name="id">معرف الإنجاز المطلوب حذفه.</param>
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null || _context.Achievements == null)
            {
                return NotFound();
            }

            var achievement = await _context.Achievements
                .FirstOrDefaultAsync(m => m.Id == id);
            if (achievement == null)
            {
                return NotFound();
            }

            return View(achievement);
        }

        // POST: Achievements/Delete/5
        /// <summary>
        /// ينفذ عملية حذف الإنجاز من قاعدة البيانات ويحذف الصورة المرتبطة إذا وجدت.
        /// </summary>
        /// <param name="id">معرف الإنجاز.</param>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            if (_context.Achievements == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.Achievements'  is null.");
            }
            var achievement = await _context.Achievements.FindAsync(id);
            if (achievement != null)
            {
                _context.Achievements.Remove(achievement);
            }

            await _context.SaveChangesAsync();
            if (achievement.Image != null) HandleImages.RemoveImage(achievement.Image, "img", _host);
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// يتحقق من وجود إنجاز حسب المعرف المحدد.
        /// </summary>
        /// <param name="id">معرف الإنجاز.</param>
        /// <returns>صحيح إذا كان الإنجاز موجوداً، وإلا خاطئ.</returns>
        private bool AchievementExists(int id)
        {
            return (_context.Achievements?.Any(e => e.Id == id)).GetValueOrDefault();
        }
    }
}
