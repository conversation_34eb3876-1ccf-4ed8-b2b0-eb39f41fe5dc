﻿@model HalalaPlusProject.Models.CountriesTable
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["delete"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["delete"]</h1>

<h2>@localizer["deletecountrymsg"]</h2>
<div>
    
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @localizer["thecountry"]
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Country)
        </dd>
        <dt class = "col-sm-2">
            @localizer["countrynationality"]
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Nationality)
        </dd>
        <dt class = "col-sm-2">
            @localizer["thecountry"]
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnCountry)
        </dd>
        <dt class = "col-sm-2">
            @localizer["countrynationality"]
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnNationality)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-danger">@localizer["delete"]</button> |
        <a asp-action="Index">@localizer["backtolist"]</a>
    </form>
</div>
