﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class Plan
{
    [Key]
    public int Id { get; set; }

    [StringLength(200)]
    public string? Name { get; set; }

    public string? Description { get; set; }

    public bool? IsCustom { get; set; }

    [StringLength(100)]
    public string? CreatedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreatedDate { get; set; }

    [StringLength(100)]
    public string? UpdatedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? UpdatedDate { get; set; }

    public bool? IsDeleted { get; set; }

    [InverseProperty("Plan")]
    public virtual ICollection<CustomerPlan> CustomerPlans { get; set; } = new List<CustomerPlan>();

    [InverseProperty("Plan")]
    public virtual ICollection<PlanAllocation> PlanAllocations { get; set; } = new List<PlanAllocation>();

    [InverseProperty("Plan")]
    public virtual ICollection<PlanExpense> PlanExpenses { get; set; } = new List<PlanExpense>();
}
