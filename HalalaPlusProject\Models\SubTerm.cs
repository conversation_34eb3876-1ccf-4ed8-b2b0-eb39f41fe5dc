﻿using System;
using System.Collections.Generic;

namespace HalalaPlusProject.Models;

public partial class SubTerm
{
    public long Id { get; set; }

    public int? MainCategoryId { get; set; }

    public string? Name { get; set; }

    public string? Icon { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? CreatedDate { get; set; }

    public string? UpdatedBy { get; set; }

    public DateTime? UpdatedDate { get; set; }

    public bool? IsDeleted { get; set; }

    public string? DeletedBy { get; set; }

    public DateTime? DeletedDate { get; set; }

    public virtual MainTerm? MainCategory { get; set; }

    public virtual ICollection<ManualCommitment> ManualCommitments { get; set; } = new List<ManualCommitment>();
}
