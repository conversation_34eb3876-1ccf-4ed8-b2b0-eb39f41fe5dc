﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل تفاصيل طلب معين داخل النظام.
    /// </summary>
    public class OrdersModel
    {
        // [Display(Name = "الرقم")]
        /// <summary>
        /// معرف الطلب.
        /// </summary>
        public long? Id { get; set; }

        // [Display(Name = "نوع الطلب")]
        /// <summary>
        /// نوع الطلب كنص.
        /// </summary>
        public string? OrderType { get; set; }

        /// <summary>
        /// رقم نوع الطلب.
        /// </summary>
        public int? OrderTypeNo { get; set; }

        // [Display(Name = "رقم الطلب")]
        /// <summary>
        /// رقم الطلب.
        /// </summary>
        public string? OrderNo { get; set; }

        // [Display(Name = "مقدم الطلب")]
        /// <summary>
        /// اسم مقدم الطلب.
        /// </summary>
        public string? UserOrdered { get; set; }

        /// <summary>
        /// رقم جوال مقدم الطلب.
        /// </summary>
        public string? PhoneNo { get; set; }

        /// <summary>
        /// رابط تفاصيل الطلب.
        /// </summary>
        public string? DetailLink { get; set; }

        /// <summary>
        /// تاريخ تقديم الطلب.
        /// </summary>
        public DateTime? date { get; set; }
    }

    /// <summary>
    /// يحتوي على قوائم الطلبات حسب حالتها (جديدة، مقبولة، مرفوضة).
    /// </summary>
    public class OrdersIndexModel
    {
        /// <summary>
        /// قائمة الطلبات الجديدة.
        /// </summary>
        public List<OrdersModel>? newOrders { get; set; }

        /// <summary>
        /// قائمة الطلبات المقبولة.
        /// </summary>
        public List<OrdersModel>? acceptedOrders { get; set; }

        /// <summary>
        /// قائمة الطلبات المرفوضة.
        /// </summary>
        public List<OrdersModel>? rejectedOrders { get; set; }
    }

    /// <summary>
    /// يمثل نموذج عرض مختصر لطلب بطاقة.
    /// </summary>
    public class CardOrdersModel
    {
        // [Display(Name = "رقم الطلب")]
        /// <summary>
        /// معرف الطلب.
        /// </summary>
        public long? Id { get; set; }

        // [Display(Name = "الاسم ")]
        /// <summary>
        /// اسم مقدم الطلب.
        /// </summary>
        public string? Name { get; set; }

        // [Display(Name = "رقم الجوال")]
        /// <summary>
        /// رقم جوال مقدم الطلب.
        /// </summary>
        public string? PhoneNo { get; set; }

        // [Display(Name = "تاريخ الطلب")]
        /// <summary>
        /// تاريخ تقديم الطلب كنص.
        /// </summary>
        public string? OrderDate { get; set; }
    }
}
