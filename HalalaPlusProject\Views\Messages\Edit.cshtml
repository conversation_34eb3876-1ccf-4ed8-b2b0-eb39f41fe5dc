﻿@model HalalaPlusProject.Models.ContactU
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = @localizer["msgdetails"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div>
    <a href="~/WebSite/Index"><img src="../assets/img/svgs/solid/arrow-right.svg" style="width: 40px;" alt=""></a>
</div>

<div class="row">
    <div class="col-md-8 col-sm-12">
          
            <div class="row">
                <div class="col-md-4 col-sm-6">
                    <div class="form-group">
                    <label asp-for="Name" class="control-label">@localizer["sendername"]</label>
                        
                         @Html.DisplayFor(model => model.Name)
                        <span asp-validation-for="Name"  class="text-danger"></span>
                    </div>
                    <div class="form-group">
                    <label asp-for="Email" class="control-label">@localizer["senderemail"]</label>
                        @Html.DisplayFor(model => model.Email)
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                    <label asp-for="PhoneNo" class="control-label">@localizer["senderphoneno"]</label>
                        @Html.DisplayFor(model => model.PhoneNo)
                        <span asp-validation-for="PhoneNo" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                    <label asp-for="OrderDate" class="control-label">@localizer["msgdete"]</label>
                         @Html.DisplayFor(model => model.OrderDate)
                        <span asp-validation-for="OrderDate" class="text-danger"></span>
                    </div>


          </div>

          
          <div class="col-md-8 col-sm-6">
               <div class="form-group">
                    <label asp-for="Subject" class="control-label">@localizer["subject"]</label>
                        @*<input asp-for="Subject"  class="form-control" />*@
                         @Html.DisplayFor(model => model.Subject)
                        <span asp-validation-for="Subject" class="text-danger"></span>
                    </div>
       
           <div class="form-group">
                    <label asp-for="Message" class="control-label">@localizer["message"]</label>
@*                        <input asp-for="Message"  class="form-control" />
*@                         @Html.DisplayFor(model => model.Message)
                        <span asp-validation-for="Message" class="text-danger"></span>
                    </div> 
        
            </div>    
           
            </div>
           
    </div>

      <div class="col-md-4">
        <form asp-action="Edit" enctype="multipart/form-data" class="submitfm" >
            <div class="row">
                  <input type="hidden" asp-for="Id" />
                  <input type="hidden" asp-for="Email" />
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            </div>
            <div class="row">
                <div class="col-md-12">
                     <div class="form-group">
                        <label asp-for="Name" class="control-label">@localizer["sendername"]</label>
                        
                         @Html.DisplayFor(model => model.Name)
                        <span asp-validation-for="Name"  class="text-danger"></span>
                    </div>
                      <div class="form-group">
                        <label asp-for="Email" class="control-label"> @localizer["senderemail"]</label>
                        @Html.DisplayFor(model => model.Email)
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Replay" class="control-label">@localizer["replay"]</label>
                        <textarea asp-for="Replay" required class="form-control" ></textarea>
                         
                        <span asp-validation-for="Replay"  class="text-danger"></span>
                    </div>                 
          </div>
            </div>       
            <div class="mt-2">
                <button type="submit" value="Create" class="btn btn-primary">@localizer["send"]</button>
            </div>
        </form>
    </div>
</div>

@section Scripts {


    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}