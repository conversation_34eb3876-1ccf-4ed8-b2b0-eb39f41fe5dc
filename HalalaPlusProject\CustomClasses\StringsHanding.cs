﻿using HalalaPlusProject.CModels;

namespace HalalaPlusProject.CustomClasses
{
    public static class StringsHanding
    {
        public static List<socialMediaAccounts> getSits(string data) {
            List<socialMediaAccounts> list = new List<socialMediaAccounts>();

            if (data!="") {
                if (data.Contains(","))
                {
                    string[] rows = data.Split(",");
                    foreach (string row in rows)
                    {
                        string[] curent = row.Split('|');
                        socialMediaAccounts ob = new socialMediaAccounts();
                        ob.Id = int.Parse(curent[0]);
                        ob.SiteName = curent[1];
                        ob.Link = curent[2];
                        list.Add(ob);
                    }
                }
                else
                {
                    string[] curent = data.Split('|');
                    socialMediaAccounts ob = new socialMediaAccounts();
                    ob.Id = int.Parse(curent[0]);
                    ob.SiteName = curent[1];
                    ob.Link = curent[2];
                    list.Add(ob);

                }
               
            
            }
            return list;
        }

        public static List<PointsModel> getPounts(string data)
        {
            List<PointsModel> list = new List<PointsModel>();

            if (String.IsNullOrWhiteSpace(data))
            {
                string[] rows = data.Split(",");
                foreach (string row in rows)
                {
                    string[] curent = row.Split('|');
                    PointsModel ob = new PointsModel();
                    ob.Id = long.Parse(curent[0]);
                    //ob.p = curent[1];
                    //ob.Link = curent[2];
                    list.Add(ob);
                }

            }
            return list;
        }



    }
}
