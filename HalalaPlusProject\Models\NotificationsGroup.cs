﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("NotificationsGroup")]
public partial class NotificationsGroup
{
    [Key]
    public int Id { get; set; }

    [StringLength(250)]
    public string? GroupName { get; set; }

    public string? GroupToken { get; set; }

    [Column("isActive")]
    public bool? IsActive { get; set; }
}
