﻿using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace HalalaPlusProject.Utils
{
    public class CustomDateConverter : JsonConverter<DateTime>
    {
        private readonly string[] formats = new[] {
        "yyyy/MM/dd", "yyyy/M/d", "yyyy-MM-dd", "yyyy-M-d"
    };

        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            string? dateString = reader.GetString();
            if (DateTime.TryParseExact(dateString, formats, CultureInfo.InvariantCulture,
                DateTimeStyles.None, out var date))
            {
                return date;
            }

            throw new JsonException($"Invalid date format: {dateString}");
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            // Always output in yyyy-MM-dd format
            writer.WriteStringValue(value.ToString("yyyy-MM-dd"));
        }
    }
}
