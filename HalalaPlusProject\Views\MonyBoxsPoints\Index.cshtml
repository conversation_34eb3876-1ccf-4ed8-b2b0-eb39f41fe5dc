﻿@model IEnumerable<HalalaPlusProject.Models.MonyBoxsPoint>

@{
    ViewData["Title"] = "اعدادات نقاط التوفير";
}

<h1>@ViewData["Title"]</h1>

<p>
    <a asp-action="Create" class="btn btn-primary">إنشاء نقاط جديدة</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                من القيمة
            </th>
            <th>
                إلى القيمة
            </th>
            <th>
                النقاط الممنوحة
            </th>
            <th>
                تاريخ الإنشاء
            </th>
            <th>
                محذوف
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.FromAmount)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.ToAmount)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.GrntedPoints)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.CreateAt)
                </td>
                <td>
                    <input type="checkbox" disabled="disabled" @(item.Deleted ? "checked" : "") />
                </td>
                <td>
                    <a asp-action="Edit" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id">تعديل</a>
                    <a asp-action="Details" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id">استعراض</a>
                    <a asp-action="Delete" class="btn btn-outline-danger tablebtn" asp-route-id="@item.Id">حذف</a>
                </td>
            </tr>
        }
    </tbody>
</table>



@* @model IEnumerable<HalalaPlusProject.Models.MonyBoxsPoint>

@{
    ViewData["Title"] = "قائمة النقاط";
}

<h1>قائمة النقاط</h1>

<p>
    <a asp-action="Create" class="btn btn-primary">إنشاء نقاط جديدة</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                من الكمية
            </th>
            <th>
                إلى الكمية
            </th>
            <th>
                النقاط الممنوحة
            </th>
            <th>
                تاريخ الإنشاء
            </th>
            <th>
                محذوف
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.FromAmount)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.ToAmount)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.GrntedPoints)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.CreateAt)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Deleted)
                </td>
                <td>
                    <a asp-action="Edit" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id">تعديل</a>
                    <a asp-action="Details" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id">استعراض</a>
                    <a asp-action="Delete" class="btn btn-outline-danger tablebtn" asp-route-id="@item.Id">حذف</a>
                </td>
            </tr>
        }
    </tbody>
</table>
 *@