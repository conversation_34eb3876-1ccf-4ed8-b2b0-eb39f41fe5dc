﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class PlanAllocation
{
    [Key]
    public long Id { get; set; }

    public int PlanId { get; set; }

    public int MainCommitmentTypesId { get; set; }

    [Column(TypeName = "decimal(5, 2)")]
    public decimal Percentage { get; set; }

    [ForeignKey("MainCommitmentTypesId")]
    [InverseProperty("PlanAllocations")]
    public virtual PlanAutoMainCommitmentType MainCommitmentTypes { get; set; } = null!;

    [ForeignKey("PlanId")]
    [InverseProperty("PlanAllocations")]
    public virtual Plan Plan { get; set; } = null!;
}
