﻿@model IEnumerable<HalalaPlusProject.Models.CountriesTable>

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>الدول</h1>

<p>
    <a asp-action="Create">إنشاء</a>
</p>


<div class="row">
        <div class="col-12">
          <div class="card mb-4">
           
            <div class="card-body px-0 pt-0 pb-2">
              <div class="table-responsive p-0">
<table class="table">
    <thead>
        <tr>
            <th>
                م
            </th>
             <th>
               الدولة
            </th>
            <th>
                الجنسية
            </th>
            <th>خيارات</th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Id)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Country)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Nationality)
            </td>
            <td>
                <a asp-action="Edit" class="btn btn-outline-info" asp-route-id="@item.Id">تعديل</a> |
                <a asp-action="Details" class="btn btn-outline-info" asp-route-id="@item.Id">التفاصيل</a> |
                <a asp-action="Delete" class="btn btn-outline-danger" asp-route-id="@item.Id">حذف</a>
                <a asp-controller="Cities" asp-action="Index" class="btn btn-outline-danger" asp-route-id="@item.Id">المدن</a>
            </td>
        </tr>
}
    </tbody>
</table>
</div>
              
            </div>
          </div>
        </div>
      </div>
   <div  style="margin-top: 350px;display:10px">
    <h3 style="display:inline;">الأنشطة</h3>
     <a asp-controller="Cities" asp-action="Index" style="float:left;     margin-right: 10px;" class="btn btn-primary px-5">عرض المدن</a>
      <a asp-controller="Cities"  asp-action="Create" style="float:left;     margin-right: 10px;" class="btn btn-primary px-5">اضافة مدينة</a>
                  
                </div>
