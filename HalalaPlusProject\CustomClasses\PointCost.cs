﻿namespace HalalaPlusProject.CustomClasses
{
    public class PointCost
    {
        //public int Points { get; set; }
        //public decimal Cost { get; set; }

        public static (int totalCost, int remainingPoints) CalculateCost(int points, List<(int points, int cost)> costs)
        {
            costs.Sort((a, b) => b.points.CompareTo(a.points)); // Sort costs in descending order by points

            return CalculateCostHelper(points, costs, 0);
        }

        private static (int totalCost, int remainingPoints) CalculateCostHelper(int remainingPoints, List<(int points, int cost)> costs, int totalCost)
        {
            if (remainingPoints == 0)
            {
                return (totalCost, remainingPoints);
            }

            foreach ((int pointThreshold, int cost) in costs)
            {
                if (remainingPoints >= pointThreshold)
                {
                    remainingPoints -= pointThreshold;
                    totalCost += cost;
                    return CalculateCostHelper(remainingPoints, costs, totalCost);
                }
            }

            return  (totalCost, remainingPoints); ; // No matching cost found
        }

    }
}
