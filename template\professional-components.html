<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HalalaPlus - Professional Components</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    
    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css">
    
    <!-- Toastr -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Professional CSS -->
    <link rel="stylesheet" href="css/professional-style.css">
    
    <style>
        /* Component Showcase Styles */
        .component-section {
            margin-bottom: 3rem;
        }
        
        .component-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-color);
            display: inline-block;
        }
        
        .demo-card {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .form-floating-modern {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .form-floating-modern input,
        .form-floating-modern select,
        .form-floating-modern textarea {
            width: 100%;
            padding: 1rem 1rem 0.5rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: var(--bg-primary);
            font-size: var(--font-size-base);
            transition: var(--transition-fast);
        }
        
        .form-floating-modern input:focus,
        .form-floating-modern select:focus,
        .form-floating-modern textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(203, 12, 159, 0.1);
        }
        
        .form-floating-modern label {
            position: absolute;
            top: 1rem;
            right: 1rem;
            color: var(--text-muted);
            font-size: var(--font-size-sm);
            transition: var(--transition-fast);
            pointer-events: none;
            background: var(--bg-primary);
            padding: 0 0.5rem;
        }
        
        .form-floating-modern input:focus ~ label,
        .form-floating-modern input:not(:placeholder-shown) ~ label,
        .form-floating-modern select:focus ~ label,
        .form-floating-modern select:not([value=""]) ~ label,
        .form-floating-modern textarea:focus ~ label,
        .form-floating-modern textarea:not(:placeholder-shown) ~ label {
            top: -0.5rem;
            font-size: var(--font-size-xs);
            color: var(--primary-color);
            font-weight: 600;
        }
        
        [dir="ltr"] .form-floating-modern label {
            right: auto;
            left: 1rem;
        }
        
        .switch-modern {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .switch-modern input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider-modern {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-tertiary);
            transition: var(--transition-fast);
            border-radius: 34px;
        }
        
        .slider-modern:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background: white;
            transition: var(--transition-fast);
            border-radius: 50%;
            box-shadow: var(--shadow-sm);
        }
        
        input:checked + .slider-modern {
            background: var(--primary-gradient);
        }
        
        input:checked + .slider-modern:before {
            transform: translateX(26px);
        }
        
        .card-modern {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: var(--transition-normal);
        }
        
        .card-modern:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }
        
        .card-modern .card-header {
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem 2rem;
        }
        
        .card-modern .card-body {
            padding: 2rem;
        }
        
        .alert-modern {
            border: none;
            border-radius: var(--radius-lg);
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }
        
        .alert-modern::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
        }
        
        [dir="ltr"] .alert-modern::before {
            right: auto;
            left: 0;
        }
        
        .alert-success-modern {
            background: rgba(130, 214, 22, 0.1);
            color: var(--success-color);
        }
        
        .alert-success-modern::before {
            background: var(--success-color);
        }
        
        .alert-info-modern {
            background: rgba(23, 193, 232, 0.1);
            color: var(--info-color);
        }
        
        .alert-info-modern::before {
            background: var(--info-color);
        }
        
        .alert-warning-modern {
            background: rgba(251, 207, 51, 0.1);
            color: var(--warning-color);
        }
        
        .alert-warning-modern::before {
            background: var(--warning-color);
        }
        
        .alert-danger-modern {
            background: rgba(234, 6, 6, 0.1);
            color: var(--danger-color);
        }
        
        .alert-danger-modern::before {
            background: var(--danger-color);
        }
        
        .timeline-modern {
            position: relative;
            padding: 2rem 0;
        }
        
        .timeline-modern::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--border-color);
            right: 2rem;
        }
        
        [dir="ltr"] .timeline-modern::before {
            right: auto;
            left: 2rem;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 2rem;
            padding-right: 4rem;
        }
        
        [dir="ltr"] .timeline-item {
            padding-right: 0;
            padding-left: 4rem;
        }
        
        .timeline-marker {
            position: absolute;
            top: 0.5rem;
            right: 1.25rem;
            width: 1.5rem;
            height: 1.5rem;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
            z-index: 1;
        }
        
        [dir="ltr"] .timeline-marker {
            right: auto;
            left: 1.25rem;
        }
        
        .timeline-content {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }
        
        .timeline-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .timeline-time {
            font-size: var(--font-size-xs);
            color: var(--text-muted);
            margin-bottom: 0.5rem;
        }
        
        .timeline-description {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
        }
    </style>
</head>
<body>
    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobileOverlay"></div>
    
    <!-- Language Toggle -->
    <button class="language-toggle" onclick="toggleLanguage()" id="langToggle">
        EN
    </button>
    
    <!-- Professional Sidebar -->
    <aside class="professional-sidebar" id="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
            <a href="professional.html" class="sidebar-logo">
                <img src="assets/img/placeholder.svg" alt="Logo">
                <span class="sidebar-logo-text">HalalaPlus</span>
            </a>
        </div>
        
        <!-- Sidebar Navigation -->
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="professional.html" class="nav-link">
                    <div class="nav-icon">
                        <i class="material-icons-round">dashboard</i>
                    </div>
                    <span class="nav-text">لوحة التحكم</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="professional-components.html" class="nav-link active">
                    <div class="nav-icon">
                        <i class="material-icons-round">widgets</i>
                    </div>
                    <span class="nav-text">المكونات</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <div class="nav-icon">
                        <i class="material-icons-round">people</i>
                    </div>
                    <span class="nav-text">المستخدمون</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <div class="nav-icon">
                        <i class="material-icons-round">inventory_2</i>
                    </div>
                    <span class="nav-text">المنتجات</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <div class="nav-icon">
                        <i class="material-icons-round">settings</i>
                    </div>
                    <span class="nav-text">الإعدادات</span>
                </a>
            </div>
        </nav>
        
        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
            <a href="#" class="sidebar-user">
                <div class="user-avatar">أم</div>
                <div class="user-info">
                    <div class="user-name">أحمد محمد</div>
                    <div class="user-role">مدير النظام</div>
                </div>
            </a>
        </div>
    </aside>
    
    <!-- Professional Header -->
    <header class="professional-header" id="header">
        <div class="header-content">
            <div class="header-left">
                <button class="mobile-menu-toggle" onclick="toggleMobileSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="breadcrumb-container">
                    <nav class="breadcrumb">
                        <div class="breadcrumb-item">
                            <a href="professional.html">الرئيسية</a>
                        </div>
                        <div class="breadcrumb-item active">المكونات</div>
                    </nav>
                    <h1 class="page-title">عرض المكونات المتقدمة</h1>
                </div>
            </div>
            
            <div class="header-right">
                <div class="header-search">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="البحث في المكونات...">
                </div>
                
                <div class="header-actions">
                    <button class="header-action" title="الإشعارات">
                        <i class="material-icons-round">notifications</i>
                        <span class="notification-badge"></span>
                    </button>
                    
                    <div class="user-dropdown">
                        <button class="user-trigger">
                            <div class="user-avatar-header">أم</div>
                            <span class="user-name-header">أحمد محمد</span>
                            <i class="material-icons-round">keyboard_arrow_down</i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        <div class="content-wrapper">
            
            <!-- Buttons Section -->
            <div class="component-section" data-aos="fade-up">
                <h2 class="component-title">الأزرار المتقدمة</h2>
                <div class="demo-card">
                    <div class="demo-grid">
                        <div>
                            <h6 class="mb-3">الأزرار الأساسية</h6>
                            <div class="d-flex flex-wrap gap-2 mb-3">
                                <button class="btn-primary-professional">زر أساسي</button>
                                <button class="btn-outline-professional">زر محدد</button>
                                <button class="btn-primary-professional" onclick="dashboard.simulateLoading(this)">
                                    <i class="material-icons-round">download</i>
                                    تحميل
                                </button>
                            </div>
                        </div>
                        
                        <div>
                            <h6 class="mb-3">أزرار الإشعارات</h6>
                            <div class="d-flex flex-wrap gap-2 mb-3">
                                <button class="btn-primary-professional" onclick="dashboard.showNotification('success', 'نجح!', 'تم تنفيذ العملية بنجاح')">
                                    إشعار نجاح
                                </button>
                                <button class="btn-outline-professional" onclick="dashboard.showNotification('error', 'خطأ!', 'حدث خطأ في النظام')">
                                    إشعار خطأ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Forms Section -->
            <div class="component-section" data-aos="fade-up">
                <h2 class="component-title">النماذج المتطورة</h2>
                <div class="demo-card">
                    <div class="demo-grid">
                        <div>
                            <h6 class="mb-3">حقول الإدخال العائمة</h6>
                            <form>
                                <div class="form-floating-modern">
                                    <input type="text" id="name" placeholder=" ">
                                    <label for="name">الاسم الكامل</label>
                                </div>
                                
                                <div class="form-floating-modern">
                                    <input type="email" id="email" placeholder=" ">
                                    <label for="email">البريد الإلكتروني</label>
                                </div>
                                
                                <div class="form-floating-modern">
                                    <select id="city">
                                        <option value="">اختر المدينة</option>
                                        <option value="riyadh">الرياض</option>
                                        <option value="jeddah">جدة</option>
                                        <option value="dammam">الدمام</option>
                                    </select>
                                    <label for="city">المدينة</label>
                                </div>
                                
                                <div class="form-floating-modern">
                                    <textarea id="message" rows="3" placeholder=" "></textarea>
                                    <label for="message">الرسالة</label>
                                </div>
                            </form>
                        </div>
                        
                        <div>
                            <h6 class="mb-3">المفاتيح المتطورة</h6>
                            <div class="d-flex flex-column gap-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <span>تفعيل الإشعارات</span>
                                    <label class="switch-modern">
                                        <input type="checkbox" checked>
                                        <span class="slider-modern"></span>
                                    </label>
                                </div>
                                
                                <div class="d-flex align-items-center justify-content-between">
                                    <span>الوضع المظلم</span>
                                    <label class="switch-modern">
                                        <input type="checkbox">
                                        <span class="slider-modern"></span>
                                    </label>
                                </div>
                                
                                <div class="d-flex align-items-center justify-content-between">
                                    <span>التحديثات التلقائية</span>
                                    <label class="switch-modern">
                                        <input type="checkbox" checked>
                                        <span class="slider-modern"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cards Section -->
            <div class="component-section" data-aos="fade-up">
                <h2 class="component-title">البطاقات المتقدمة</h2>
                <div class="demo-grid">
                    <div class="card-modern">
                        <div class="card-header">
                            <h6 class="mb-0 fw-bold">بطاقة بسيطة</h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-3">هذه بطاقة بسيطة مع تصميم عصري وتأثيرات hover جميلة.</p>
                            <button class="btn-primary-professional">إجراء</button>
                        </div>
                    </div>
                    
                    <div class="card-modern">
                        <div class="card-header">
                            <h6 class="mb-0 fw-bold">بطاقة إحصائيات</h6>
                        </div>
                        <div class="card-body text-center">
                            <div class="stat-icon mx-auto mb-3">
                                <i class="material-icons-round">trending_up</i>
                            </div>
                            <div class="stat-number">1,234</div>
                            <div class="stat-label">إجمالي الزيارات</div>
                        </div>
                    </div>
                    
                    <div class="card-modern">
                        <div class="card-header">
                            <h6 class="mb-0 fw-bold">بطاقة تقدم</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>تقدم المشروع</span>
                                <span>75%</span>
                            </div>
                            <div class="progress-professional">
                                <div class="progress-bar-professional" style="width: 75%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Alerts Section -->
            <div class="component-section" data-aos="fade-up">
                <h2 class="component-title">التنبيهات المتطورة</h2>
                <div class="demo-card">
                    <div class="alert-success-modern">
                        <div class="d-flex align-items-center">
                            <i class="material-icons-round me-2">check_circle</i>
                            <div>
                                <strong>تم بنجاح!</strong> تم حفظ البيانات بنجاح.
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert-info-modern">
                        <div class="d-flex align-items-center">
                            <i class="material-icons-round me-2">info</i>
                            <div>
                                <strong>معلومات:</strong> يرجى مراجعة البيانات قبل الحفظ.
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert-warning-modern">
                        <div class="d-flex align-items-center">
                            <i class="material-icons-round me-2">warning</i>
                            <div>
                                <strong>تحذير:</strong> هناك بعض المشاكل في البيانات.
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert-danger-modern">
                        <div class="d-flex align-items-center">
                            <i class="material-icons-round me-2">error</i>
                            <div>
                                <strong>خطأ:</strong> فشل في حفظ البيانات.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Timeline Section -->
            <div class="component-section" data-aos="fade-up">
                <h2 class="component-title">الخط الزمني المتقدم</h2>
                <div class="demo-card">
                    <div class="timeline-modern">
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="material-icons-round">check</i>
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-title">تم إنشاء المشروع</div>
                                <div class="timeline-time">منذ ساعتين</div>
                                <div class="timeline-description">تم إنشاء مشروع جديد بنجاح وإضافة الفريق المطلوب.</div>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="material-icons-round">code</i>
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-title">بدء التطوير</div>
                                <div class="timeline-time">منذ 4 ساعات</div>
                                <div class="timeline-description">بدأ فريق التطوير العمل على المرحلة الأولى من المشروع.</div>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="material-icons-round">bug_report</i>
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-title">إصلاح الأخطاء</div>
                                <div class="timeline-time">منذ يوم</div>
                                <div class="timeline-description">تم إصلاح جميع الأخطاء المكتشفة في مرحلة الاختبار.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </main>
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Professional JavaScript -->
    <script src="js/professional.js"></script>
</body>
</html>
