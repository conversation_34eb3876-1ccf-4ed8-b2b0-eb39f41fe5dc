﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using HalalaPlusProject.CustomClasses;
using static System.Net.Mime.MediaTypeNames;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// تحكم في عمليات الأنشطة مثل العرض، الإضافة، التعديل والحذف.
    /// </summary>
    [Authorize]
    public class ActivitiesController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IWebHostEnvironment _hosting;

        public ActivitiesController(HalalaPlusdbContext context, IWebHostEnvironment hosting)
        {
            _context = context;
            _hosting = hosting;
        }

        // GET: Activities
        /// <summary>
        /// يعرض قائمة بجميع الأنشطة المخزنة في قاعدة البيانات.
        /// </summary>
        public async Task<IActionResult> Index()
        {
            return _context.Activities != null ?
                        View(await _context.Activities.ToListAsync()) :
                        Problem("Entity set 'HalalaPlusContext.Activities'  is null.");
        }

        // GET: Activities/Details/5
        /// <summary>
        /// يعرض تفاصيل نشاط معين بناءً على المعرف.
        /// </summary>
        /// <param name="id">معرف النشاط المطلوب.</param>
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null || _context.Activities == null)
            {
                return NotFound();
            }

            var activity = await _context.Activities
                .FirstOrDefaultAsync(m => m.Id == id);
            if (activity == null)
            {
                return NotFound();
            }

            return View(activity);
        }

        // GET: Activities/Create
        /// <summary>
        /// يعرض نموذج إنشاء نشاط جديد.
        /// </summary>
        public IActionResult Create()
        {
            return View();
        }

        // POST: Activities/Create
        /// <summary>
        /// يقوم بإضافة نشاط جديد إلى قاعدة البيانات ويحفظ الصورة إذا تم رفعها.
        /// </summary>
        /// <param name="activity">النشاط الجديد.</param>
        /// <param name="file">الصورة المرتبطة بالنشاط.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Activity activity, IFormFile file)
        {
            if (ModelState.IsValid)
            {
                if (file != null) activity.Image = HandleImages.SaveImage(file, "img", _hosting);
                _context.Add(activity);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(activity);
        }

        // GET: Activities/Edit/5
        /// <summary>
        /// يعرض نموذج تعديل نشاط معين.
        /// </summary>
        /// <param name="id">معرف النشاط المطلوب تعديله.</param>
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null || _context.Activities == null)
            {
                return NotFound();
            }

            var activity = await _context.Activities.FindAsync(id);
            if (activity == null)
            {
                return NotFound();
            }
            return View(activity);
        }

        // POST: Activities/Edit/5
        /// <summary>
        /// يقوم بتحديث بيانات نشاط موجود ويحفظ الصورة الجديدة إن وجدت.
        /// </summary>
        /// <param name="id">معرف النشاط.</param>
        /// <param name="activity">بيانات النشاط المحدثة.</param>
        /// <param name="file">صورة جديدة للنشاط.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Activity activity, IFormFile file)
        {
            if (id != activity.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    if (file != null) activity.Image = HandleImages.SaveImage(file, "img", _hosting);
                    _context.Update(activity);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ActivityExists(activity.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(activity);
        }

        // GET: Activities/Delete/5
        /// <summary>
        /// يعرض تأكيد حذف نشاط معين.
        /// </summary>
        /// <param name="id">معرف النشاط المطلوب حذفه.</param>
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null || _context.Activities == null)
            {
                return NotFound();
            }

            var activity = await _context.Activities
                .FirstOrDefaultAsync(m => m.Id == id);
            if (activity == null)
            {
                return NotFound();
            }

            return View(activity);
        }

        // POST: Activities/Delete/5
        /// <summary>
        /// ينفذ عملية حذف النشاط بشكل نهائي من قاعدة البيانات.
        /// </summary>
        /// <param name="id">معرف النشاط.</param>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            if (_context.Activities == null)
            {
                return Problem("Entity set 'HalalaPlusContext.Activities'  is null.");
            }
            var activity = await _context.Activities.FindAsync(id);
            if (activity != null)
            {
                _context.Activities.Remove(activity);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// يتحقق من وجود نشاط بناءً على المعرف.
        /// </summary>
        /// <param name="id">معرف النشاط المطلوب التحقق منه.</param>
        /// <returns>صحيح إذا كان النشاط موجوداً، وإلا خاطئ.</returns>
        private bool ActivityExists(int id)
        {
            return (_context.Activities?.Any(e => e.Id == id)).GetValueOrDefault();
        }
    }
}
