﻿@model HalalaPlusProject.Models.Category

@{
    ViewData["Title"] = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<h3>هل انت متاكد منحذف الصنف</h3>
<div>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
          اسم الصنف
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt class = "col-sm-2">
           اسم الصنف انجليزي
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EngName)
        </dd>
    </dl>
    
    <form asp-action="Delete" class="submitfm ">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-danger mt-2">حفظ</button> |
        <a asp-action="Index">عودة</a>
    </form>
</div>
