﻿using HalalaPlusProject.CModels;
using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.Entities
{
    public class CreateOrginazationModel
    {
        public long Id { get; set; }
        [Required]
        //[Display(Name = "اسم مقدم الخدمة")]
        public string? Name { get; set; }
        [Required]
        public string? EnName { get; set; }
        [Required]
        public string? overview { get; set; }
        [Required]
        public string? enoverview { get; set; }
        [Required]
        public string? FirstColor { get; set; }
        [Required]
        public string? SecondColor { get; set; }
        //[Required]
        public IFormFile? OffersIcon { get; set; }
        //[Required]
        //[Display(Name = "اسم المستخدم")]
        //public string? UserName { get; set; }
        //[Required]
        //[Display(Name = "المدينة")]
        //[Required]
        public string? StoreLink { get; set; }
        public int? City { get; set; }
        //[Required]
        //[Display(Name = "النشاط")]
        public int? Activity { get; set; }
        ////[Required]
        //[Display(Name = "اسم الممثل")]
        public string? ServiceProviderRepresent { get; set; }
        //[Display(Name = "رقم الجوال")]
        public string? PhoneNumber { get; set; }
        //[Required]
        //[Display(Name = "الايميل")]
        [RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }
        [Required]
        //[Display(Name = "كلمة المرور")]
        [RegularExpression(@"^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{8,}$", ErrorMessage = "يجب ان تحتوي كلمة المرور على ارقام وحروف كبيرة وصغيرة ورموز")]
        public string? Password { get; set; }
        //public string AspId { get; set; } = null!;
        //[Display(Name = "رقم السجل التجاري")]
        //[Required(ErrorMessage = "جب إدخال رقم المنشأة")]
        public string? BusnissNo { get; set; } = null!;
        //[Display(Name = "رقم المنشأة")]
        public string? EnterprisePhoneNo { get; set; } = null!;
        //[Display(Name = "الموقع")]
        //[Required]
        //public string? Locatin { get; set; } = null!;
        [Required]

        //[Display(Name = "لموقع x")]
        public string? Lat { get; set; } = null!;
        //[Display(Name = "الموقع y")]
        [Required]
        public string? lng { get; set; } = null!;
        //[Display(Name = "الشعار")]
        [Required]
        public IFormFile? Logo { get; set; } = null!;
        //[Display(Name = "المرفقات")]

        public List<IFormFile>? Files { get; set; } = null!;
        public List<IFormFile>? Images { get; set; } = null!;
        //[Display(Name = "الكاش باك")]
        [Range(0, float.MaxValue, ErrorMessage = "Please enter valid float Number")]
        public double? CashBack { get; set; }
        //[Required]
        //[Display(Name = "بداية التعاقد")]
        public DateOnly? ContractDate { get; set; }
        //[Required]
        //[Display(Name = "نهاية التعاقد")]
        public DateOnly? ContractEndDate { get; set; }
        //[Display(Name = "رقم العقد")]
        public string? ContractNo { get; set; }
        //public List<DiscountsCreate> discounts { get; set; } = null!;
        //public List<PointsCreate> Points { get; set; } = null!;
        //public List<socialMediaAccounts> Accounts { get; set; } = null!;
        //public string disc { get; set; } = null!;
        //public string Poin { get; set; } = null!;
        public string? Accounts { get; set; } = null!;
        [Required]
        public string? bnifitfrompoints { get; set; } = null!;
        [Required]
        public string? enbnifitfrompoints { get; set; } = null!;
    }
 public class CreateEventModel
    {
        public long Id { get; set; }
        [Required]
        //[Display(Name = "اسم مقدم الخدمة")]
        public string? Name { get; set; }
        [Required]
        public string? EnName { get; set; }
        //[Required]
        //public string? overview { get; set; }
        //[Required]
        //public string? enoverview { get; set; }
        
        //[Required]
        public IFormFile? OffersIcon { get; set; }
        //[Required]
        //[Display(Name = "اسم المستخدم")]
        //public string? UserName { get; set; }
        //[Required]
        //[Display(Name = "المدينة")]
        //[Required]
        public string? StoreLink { get; set; }
        public int? City { get; set; }
        //[Required]
        //[Display(Name = "النشاط")]
        //public int? Activity { get; set; }
        ////[Required]
        //[Display(Name = "اسم الممثل")]
        //public string? ServiceProviderRepresent { get; set; }
        //[Display(Name = "رقم الجوال")]
        public string? PhoneNumber { get; set; }
        //[Required]
        //[Display(Name = "الايميل")]
        [RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }
        [Required]
        //[Display(Name = "كلمة المرور")]
        [RegularExpression(@"^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{8,}$", ErrorMessage = "يجب ان تحتوي كلمة المرور على ارقام وحروف كبيرة وصغيرة ورموز")]
        public string? Password { get; set; }
        //public string AspId { get; set; } = null!;
        //[Display(Name = "رقم السجل التجاري")]
        //[Required(ErrorMessage = "جب إدخال رقم المنشأة")]
        //public string? BusnissNo { get; set; } = null!;
        ////[Display(Name = "رقم المنشأة")]
        //public string? EnterprisePhoneNo { get; set; } = null!;
        //[Display(Name = "الموقع")]
        //[Required]
        //public string? Locatin { get; set; } = null!;
        [Required]

        //[Display(Name = "لموقع x")]
        public string? Lat { get; set; } = null!;
        //[Display(Name = "الموقع y")]
        [Required]
        public string? lng { get; set; } = null!;
        //[Display(Name = "الشعار")]
        [Required]
        public IFormFile? Logo { get; set; } = null!;
        //[Display(Name = "المرفقات")]

        public List<IFormFile>? Files { get; set; } = null!;
        public List<IFormFile>? Images { get; set; } = null!;
        //[Display(Name = "الكاش باك")]
        //[Range(0, float.MaxValue, ErrorMessage = "Please enter valid float Number")]
        //public double? CashBack { get; set; }
        //[Required]
        //[Display(Name = "بداية التعاقد")]
        public DateOnly? ContractDate { get; set; }
        //[Required]
        //[Display(Name = "نهاية التعاقد")]
        public DateOnly? ContractEndDate { get; set; }
        //[Display(Name = "رقم العقد")]
        public string? ContractNo { get; set; }
        //public List<DiscountsCreate> discounts { get; set; } = null!;
        //public List<PointsCreate> Points { get; set; } = null!;
        //public List<socialMediaAccounts> Accounts { get; set; } = null!;
        //public string disc { get; set; } = null!;
        //public string Poin { get; set; } = null!;
        //public string? Accounts { get; set; } = null!;
        //[Required]
        //public string? bnifitfrompoints { get; set; } = null!;
        //[Required]
        //public string? enbnifitfrompoints { get; set; } = null!;
    
    }

    public class OrginazationEdit
    {
        //[Required]
        public string? StoreLink { get; set; }
        public long Id { get; set; }
        [Required]
        //[Display(Name = "اسم مقدم الخدمة")]
        public string? Name { get; set; }
        //[Display(Name = "اسم المستخدم")]
        [Required]
        public string? overview { get; set; }

        public string? FirstColor { get; set; }
        [Required]
        public string? SecondColor { get; set; }
        //[Required]
        public IFormFile? OffersIcon { get; set; }
        public string? OffersIconLink { get; set; }
        public bool HasCustomBrand { get; set; }
        [Required]
        public string? enoverview { get; set; }
        [Required]
        public string? bnifitfrompoints { get; set; } 
        //[Required]
        public string? enbnifitfrompoints { get; set; }
        public string? UserName { get; set; }
        [Required]
        public string? EnName { get; set; }
        [Required]
        //[Display(Name = "المدينة")]
        public int? City { get; set; }
        [Required]
        //[Display(Name = "النشاط")]
        public int? Activity { get; set; }
        //[Required]
        //[Display(Name = "اسم الممثل")]
        public string? ServiceProviderRepresent { get; set; }
        //[Display(Name = "رقم الجوال")]
        public string? PhoneNumber { get; set; }
        //[Display(Name = "الايميل")]
        //[RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        //public string? Email { get; set; }
        //public string AspId { get; set; } = null!;
        //[Display(Name = "رقم السجل التجاري")]
        public string? BusnissNo { get; set; } 
        //[Display(Name = "رقم المنشأة")]
        public string? EnterprisePhoneNo { get; set; }
        //[Display(Name = "الموقع")]
        public string? Locatin { get; set; } 
        //[Display(Name = "لموقع x")]
        public string? Lat { get; set; } 
        //[Display(Name = "الموقع y")]
        public string? lng { get; set; } 
        //[Display(Name = "الشعار")]
        public IFormFile? Logo { get; set; } = null!;
        public string? LogoLink { get; set; } = null!;
        //[Display(Name = "المرفقات")]
        public List<IFormFile>? Files { get; set; } = null!;
        public List<Files>? FilesList { get; set; } = null!;
        public DateOnly? ContractDate { get; set; }
        //[Display(Name = "{رقم العقد")]
        public string? ContractNo { get; set; }
        //[Required]
        //[Display(Name = "نهاية التعاقد")]
        public DateOnly? ContractEndDate { get; set; }

    }

}
