﻿@model IEnumerable<HalalaPlusProject.Models.Activity>
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = @localizer["activities"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="row">
    <h3> @localizer["activities"]</h3>
    
                  @*<a href="add-consol.html" class="btn btn-primary px-5">إضافة مستشار</a>*@
                </div>
<div class="row">
        <div class="col-12">
          <div class="card mb-4">
            
            <div class="card-body px-0 pt-0 pb-2">
              <div class="table-responsive p-0">
<table id="tbl1" class="table">
    <thead>
        <tr>
            <th>
              #
            </th>
            <th>
                                    @localizer["activityname"]
            </th>
            <th>
                                    @localizer["activityname"]
            </th>
            <th>
                                    @localizer["discribe"]
            </th>
                                <th> @localizer["options"]</th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Id)
            </td>
             <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td><td>
                @Html.DisplayFor(modelItem => item.EnName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Describtion)
            </td>
            <td>
                                        <a asp-action="Edit" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id"> @localizer["edit"]</a> |
                                        <a asp-action="Details" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id"> @localizer["details"]</a> |
                                        <a asp-action="Delete" class="btn btn-outline-danger tablebtn" asp-route-id="@item.Id"> @localizer["delete"]</a>
            </td>
        </tr>
}
    </tbody>
</table>
  </div>
              
            </div>
          </div>
        </div>
      </div>
      <div  style="margin-top: 350px;display:10px">
    <h3 style="display:inline;">@localizer["activities"]</h3>
    <a asp-action="Create" style="float:left;margin-right: 10px;" class="btn btn-primary px-5"> @localizer["addactivity"]</a>
                 
                </div>
@section Scripts{
    <script>
  let table = new DataTable('#tbl1');
 
    </script>
}