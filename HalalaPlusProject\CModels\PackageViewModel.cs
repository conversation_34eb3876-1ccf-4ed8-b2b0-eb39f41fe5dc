﻿using System.Collections.Generic;

namespace HalalaPlusProject.CModels
{
    // A simple class to represent a single feature key-value pair
    public class PackageFeatureViewModel
    {
        public string Key { get; set; }
        public string Value { get; set; }
    }

    // The main view model for the Create and Edit pages
    public class PackageViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string EnName { get; set; }
        public string Characters { get; set; }
        public string EnCharacters { get; set; }
        public double? Price { get; set; }
        public string Period { get; set; }
        public string EnPeriod { get; set; }
        public int? PackageDays { get; set; }
        public bool? IsActive { get; set; }

        public int MaxOffersAllowed { get; set; }
        public int MaxMonyBoxesAllowed { get; set; }


        // A list to hold the dynamic features from the form
        public List<PackageFeatureViewModel> Features { get; set; } = new List<PackageFeatureViewModel>();
    }
}