﻿@model HalalaPlusProject.Models.CountriesTable

@{
    ViewData["Title"] = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>حذف</h1>

<h3>هل انت متاكد من عملية الحذف هذه?</h3>
<div>
    <h4>الدولة</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
           الدولة
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Country)
        </dd>
        <dt class = "col-sm-2">
           الجنسية
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Nationality)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-danger" >حــذف</button> |
        <a asp-action="Index">عودة للقائمة السابقة</a>
    </form>
</div>
