﻿@model IEnumerable<HalalaPlusProject.Models.InvestorsTable>

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>المستثمرين</h1>

<table class="table">
    <thead>
        <tr>
            <th>
               اسم المستثمر
            </th>
            <th>
               رقم الهاتف
            </th>
            <th>
              الايميل
            </th>
            <th>
               عدد الاسهم
            </th>
            <th>
               قيمة الاسهم
            </th>
            <th>
               رقم العملية
            </th>
            <th>
               تاريخ الطلب
            </th>
           
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.InvestorName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.PhoneNumber)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Email)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.StocksNumber)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.StocksValue)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.TransctionId)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.CreateAt)
            </td>
            
        </tr>
}
    </tbody>
</table>
