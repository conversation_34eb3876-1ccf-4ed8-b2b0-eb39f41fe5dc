<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#cb0c9f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7928CA;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="200" height="200" fill="url(#grad1)" rx="15"/>
  <text x="100" y="90" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white" text-anchor="middle">HalalaPlus</text>
  <text x="100" y="120" font-family="Arial, sans-serif" font-size="14" fill="white" text-anchor="middle" opacity="0.8">Logo Placeholder</text>
</svg>
