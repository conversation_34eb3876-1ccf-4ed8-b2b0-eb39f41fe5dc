﻿@model IEnumerable<HalalaPlusProject.CModels.ProductIndeexModel>
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<p>
    <a asp-action="Create"> انشاء منتج جديد</a>
</p>
<table id="tbl1" class="table">
    <thead>
        <tr>

            <th>
                اسم المنتج
            </th>

            <th>
                اسم المنتج انجليزي
            </th>
            <th>

                السعر
            </th> <th>

                سعر البيع
            </th>
            <th>
                الصنف
            </th>
            <th>خيارات</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model) {
            <tr>

                <td>
                    @Html.DisplayFor(modelItem => item.Name)
                </td>


                <td>
                    @Html.DisplayFor(modelItem => item.EnName)
                </td>


                <td>
                    @Html.DisplayFor(modelItem => item.Price)
                </td><td>
                    @Html.DisplayFor(modelItem => item.SellPrice)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Catagory)
                </td>
                <td>
                    <a asp-action="Details" asp-route-id="@item.Id">@localizer["more"]</a> |
                    <a asp-action="Edit" asp-route-id="@item.Id">@localizer["edit"]</a> |
                    @if(item.active) {
                        <a class="tablebtn" onclick="stopProduct(@item.Id)">ايقاف</a>
                        
                    }
                    else
                    {
                        <a class="tablebtn" onclick="ActiveProduct(@item.Id)">تشغيل</a>
                        <a asp-action="Delete" asp-route-id="@item.Id"></a>                        
                    }
                    
            </td>
        </tr>
}
    </tbody>
</table>
@section Scripts {
    <script>
        let table = new DataTable('#tbl1');

    </script>
}