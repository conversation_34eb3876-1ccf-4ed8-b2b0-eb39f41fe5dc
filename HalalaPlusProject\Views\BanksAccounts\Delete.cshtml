﻿@model HalalaPlusProject.Models.BanksAccount
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["delete"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["delete"]</h2>

<h4>@localizer["deletebanktmsg"]</h4>
<div>
   
<div class="col-md-12">
         <div class="row">

     
     <div class="col-md-5">

          <div class="form-group">
                    <label class="form-label">@localizer["bankname"]</label>
                <label  class="form-control">@Model.BankName</label>               
            </div><div class="form-group">
                    <label class="form-label">@localizer["bankname"]</label>
                <label  class="form-control">@Model.EnBankName</label>               
            </div>
               <div class="form-group">
                    <label class="form-label"> @localizer["accountno"]</label>
                <label  class="form-control">@Model.AccountNumber</label>               
            </div>
             
    </div>
       <div class="col-md-5">         
               <div class="form-group">
                    <label class="form-label">@localizer["connectioncode"]</label>
                <label  class="form-control">@Model.ConnectionCode</label>               
             </div>
    </div>
</div>
</div>

    
    <form asp-action="Delete" class="mt-2">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-danger">@localizer["delete"]</button> |
        <a asp-action="Index"> @localizer["backtolist"] </a>
    </form>
</div>
