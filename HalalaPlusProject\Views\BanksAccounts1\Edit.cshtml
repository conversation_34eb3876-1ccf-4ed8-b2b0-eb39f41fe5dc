﻿@model HalalaPlusProject.Models.BanksAccount

@{
    ViewData["Title"] = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Edit</h1>

<h4>BanksAccount</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <div class="form-group">
                <label asp-for="BankName" class="control-label"></label>
                <input asp-for="BankName" class="form-control" />
                <span asp-validation-for="BankName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="ConnectionCode" class="control-label"></label>
                <input asp-for="ConnectionCode" class="form-control" />
                <span asp-validation-for="ConnectionCode" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="AccountNumber" class="control-label"></label>
                <input asp-for="AccountNumber" class="form-control" />
                <span asp-validation-for="AccountNumber" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
