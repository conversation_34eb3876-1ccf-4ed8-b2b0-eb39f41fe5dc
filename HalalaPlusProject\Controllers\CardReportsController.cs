﻿using DocumentFormat.OpenXml.Office2010.Excel;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Reporting.NETCore;
using Microsoft.ReportingServices.Interfaces;
using System;
using System.IO;
using System.IO.Compression;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    public class CardReportsController : Controller
    {
        private readonly IWebHostEnvironment _env;
        private readonly HalalaPlusdbContext _context;
        private readonly ILogger<CardReportsController> _logger;

        public CardReportsController(HalalaPlusdbContext context, ILogger<CardReportsController> _logger, IWebHostEnvironment env)
        {
            _context = context;
            this._logger = _logger;
            _env = env;
            // This line is important for some report features
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
        }

        [HttpGet]
        public IActionResult Index()
        {
            return View();
        }

        [HttpGet]
        public IActionResult DownloadCard(long id)
        {
            try
            {

                if (id == null)
                {
                    return RedirectToAction("Index", "Business");
                }
                var temp = _context.SystemUsers.Find(id);

                // Sample data
                var members = new[]
                {
               //new Member {
               //     Organization = "Acme Corp",
               //     MemberNo     = "12345",
               //     Name         = string.Format("{0} / {1}",temp.EnName,temp.Name),
               // }
                new Member {
                    Organization = _context.SystemUsers.Find(temp.OrgId).Name,
                    MemberNo     = temp.Id.ToString(),
                    Name         = temp.Name,
                }
            };
                _logger.LogInformation("print card ");
                string fileName = "jalal-althahab";
                fileName = temp.Name.Replace('.', '-');
                // Instantiate report, load definition, and enable external images
                var report = new LocalReport();
                var rdlcPath = Path.Combine(_env.ContentRootPath, "Resources", "MembershipCard.rdlc");
                _logger.LogInformation("print card rport path  " + rdlcPath);

                report.LoadReportDefinition(System.IO.File.OpenRead(rdlcPath));
                report.EnableExternalImages = true; // <-- Must be enabled for external images.

                // Bind your data
                report.DataSources.Add(
                    new ReportDataSource("MembershipCardData", members) // Must match the DataSet name in the RDLC
                );


				// Build public URL to the image
				//string halalaImageUrl = $"https://admin.halalaplus.com/halalaicon.png";
                //string companyImageUrl = $"https://admin.halalaplus.com/halalaicon.png";
				//string halalaImageUrl = $"{Request.Scheme}://{Request.Host}/halalaicon.png";
				//string companyImageUrl = $"{Request.Scheme}://{Request.Host}/halalaicon.png";
				//_logger.LogInformation("print card rport halalaImageUrl  " + halalaImageUrl);
    //            _logger.LogInformation("print card rport companyImageUrl  " + companyImageUrl);

    //            // Add report parameter
    //            var parameters = new[]
    //            {
    //           new ReportParameter("IconPath", halalaImageUrl),
    //           new ReportParameter("CompanyIconPath", companyImageUrl)
    //        };
    //            report.SetParameters(parameters);


                // Render to PDF
                byte[] pdf = report.Render( "PDF",
                             null,
                            out string mimeType,
                            out string encoding,
                            out string fileNameExtension,
                            out string[] streams,
                            out Warning[] warnings
                            );
                _logger.LogInformation("print card render pdf finisheds  ");

                //  Return the PDF file
                string fileNameFormat = string.Format("{0}.{1}", fileName, "pdf");
                _logger.LogInformation("print card rformate file name");

                return base.File(pdf, "application/pdf", fileNameFormat);
            }
            catch (Exception ex)
            {

                _logger.LogError("error message :" + ex.Message);
                _logger.LogError("error StackTrace :" + ex.StackTrace);

                return RedirectToAction("Index", "Business");

            }
        }

        public IActionResult GenerateZipOfCards(long id)
        {

            var members = new List<Member>
          {
            //new Member {
            //    Organization = "RockStar",
            //    MemberNo     = "JH-2024-9876",
            //    Name         = "Jalal Althahab /  جلال الذهب",
            //    FileName     = "Jalal_Althahab_Card"
            //},
            //new Member {
            //    Organization = "Innovate Solutions",
            //    MemberNo     = "IN-2024-1122",
            //    Name         = "Bssam Alazab / بسام العزب",
            //    FileName     = "Bssam_Alazab_Card"
            //},
            //new Member {
            //    Organization = "Innovate Solutions",
            //    MemberNo     = "IN-2024-1122",
            //    Name         = "Saeed Al-Shaari / سعيد الشعري",
            //    FileName     = "Saeed_Al-Shaari_Card"
            //},
            //new Member {
            //    Organization = "Future Systems",
            //    MemberNo     = "FS-2024-3344",
            //    Name         = "Osama Ali / اسامة علي",
            //    FileName     = "Osama_Ali_Card"
            //}
          };

            var tempdata=_context.SystemUsers.Where(op=>op.OrgId==id).ToList();
            var org = _context.SystemUsers.Find(id);
            foreach (var member in tempdata)
            {
                members.Add(new Member
                {
                    Organization = org.Name ?? org.Id.ToString(),
                    MemberNo = member.Id.ToString(),
                    Name = member.Name ?? member.Id.ToString(),
					FileName = member.Name??member.Id.ToString()
                });
            }

            string companyImageUrl;
            var rdlcPath = Path.Combine(_env.ContentRootPath, "Resources", "MembershipCard.rdlc");
            //string halalaImageUrl = $"{Request.Scheme}://{Request.Host}/halalaicon.png";
            string halalaImageUrl = $"https://admin.halalaplus.com/halalaicon.png";
            if (org.Providerimages != null)
            {
                companyImageUrl = $"https://admin.halalaplus.com/images/" + (org.Providerimages);
                // companyImageUrl = $"{Request.Scheme}://{Request.Host}/images/" + (organization.Providerimages);
            }
            else
            {
                companyImageUrl = (halalaImageUrl);
            }
            _logger.LogInformation("print card rport halalaImageUrl  " + halalaImageUrl);
            _logger.LogInformation("print card rport companyImageUrl  " + companyImageUrl);


            //var parameters = new[]
            //{
            //   new ReportParameter("IconPath", halalaImageUrl),
            //   new ReportParameter("CompanyIconPath", companyImageUrl)
            //};

            using (var memoryStream = new MemoryStream())
            {
                using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
                {
                    foreach (var member in members)
                    {
                        var report = new LocalReport();
                        report.LoadReportDefinition(System.IO.File.OpenRead(rdlcPath));
                        report.EnableExternalImages = true;

                        report.DataSources.Add(
                            new ReportDataSource("MembershipCardData", new[] { member })
                        );

                        // report.SetParameters(parameters);

                        // Add report parameter
                        var parameters = new[]
                        {
                           new ReportParameter("IconPath", halalaImageUrl),
                           new ReportParameter("CompanyIconPath",companyImageUrl)
                        };
                        report.SetParameters(parameters);

                        byte[] pdf = report.Render("PDF");


                        // Use the FileName property from the member object to name the PDF.
                        var zipEntry = archive.CreateEntry($"{member.FileName.Replace('.','_').Replace(' ', '_')}.pdf");

                        using (var entryStream = zipEntry.Open())
                        {
                            entryStream.Write(pdf, 0, pdf.Length);
                        }
                    }
                }

                string folderName = string.Format("{0}_{1}.zip", org.Name, "MembershipCards");
                _logger.LogInformation("Successfully created zip archive.");
                return File(memoryStream.ToArray(), "application/zip", folderName);

              //  return File(memoryStream.ToArray(), "application/zip", "MembershipCards.zip");
            }
        }


        // POCO matching the RDLC dataset definition
        public class Member
        {
            public string Organization { get; set; }
            public string MemberNo { get; set; }
            public string Name { get; set; }
            public string FileName { get; set; }
        }

    }
}


