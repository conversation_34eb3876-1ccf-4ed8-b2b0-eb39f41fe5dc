﻿@model HalalaPlusProject.CModels.UserDetails

@{
    ViewData["Title"] = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

 
         <div class="col-md-12">
         <div class="row">
              <div class="col-md-12" align="center">
                  <img style="width:100px; margin-bottom:5px;" src="@Model.Logo" />

              </div>
               <div class="col-md-12" align="center">
                     <label style="font-size:1.25rem; " class="form-label">@Model.Name</label>      
              </div>
              </div>
      <div class="row mt-4" >
     <div class="col-md-12">
          <div class="form-group" align="center">
                <label    style="font-size:1rem; width:49%; text-align:left;" class="form-label">@Html.DisplayNameFor(model => model.ActivityName):</label>
                <label style="font-size:1.25rem; width:49%; text-align:right;"  class="form-label">@Model.ActivityName
                </label>               
            </div>
               <div class="form-group" align="center">
                <label   style="font-size:1rem; width:49%; text-align:left;"  class="form-label">@Html.DisplayNameFor(model => model.IdentityNo):</label>
                <label style="font-size:1.25rem; width:49%; text-align:right;"   class="form-label">@Model.IdentityNo</label>               
            </div>
               <div class="form-group" align="center">
                <label    style="font-size:1rem; width:49%; text-align:left;" class="form-label">@Html.DisplayNameFor(model => model.BirthDate):</label>
                <label style="font-size:1.25rem; width:49%; text-align:right;"  class="form-label">@Model.BirthDate</label>               
            </div>
              <div class="form-group" align="center">
                <label    style="font-size:1rem; width:49%; text-align:left;" class="form-label" align="center">@Html.DisplayNameFor(model => model.Salary):</label>
                <label style="font-size:1.25rem; width:49%; text-align:right;"  class="form-label">@Model.Salary</label>               
            </div>


      
 <div class="form-group" align="center">
                <label    style="font-size:1rem; width:49%; text-align:left;" class="form-label" align="center">@Html.DisplayNameFor(model => model.PhoneNo):</label>
                <label  style="font-size:1.25rem; width:49%; text-align:right;" class="form-label">@Model.PhoneNo</label>               
            </div>
            <div class="form-group" align="center">
                <label    style="font-size:1rem; width:49%; text-align:left;" class="form-label" align="center">@Html.DisplayNameFor(model => model.Email):</label>
                <label style="font-size:1.25rem; width:49%; text-align:right;"  class="form-label">@Model.Email</label>               
            </div>                    
             <div class="form-group" align="center">
                <label  style="font-size:1rem; width:49%; text-align:left;" class="form-label">اسم المستخدم:</label>
                <label style="font-size:1.25rem; width:49%; text-align:right;"   class="form-label">@Model.UserName</label>               
            </div>




</div>
          </div>  </div>

