﻿@model HalalaPlusProject.Models.CitiesTable
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] =localizer["create"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["create"]</h2>

<h4>@localizer["city"]</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create" id="cityCreate">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="City" class="control-label">@localizer["thecity"]</label>
                <input asp-for="City" class="form-control" />
                <span asp-validation-for="City" class="text-danger"></span>
            </div><div class="form-group">
                <label asp-for="EnCity" class="control-label">Eng City</label>
                <input asp-for="EnCity" class="form-control" />
                <span asp-validation-for="EnCity" class="text-danger"></span>
            </div>
            @*<div class="form-group">
                <label asp-for="CId" class="control-label">الدولة</label>
                <input asp-for="CId" class="form-control" />
                <span asp-validation-for="CId" class="text-danger"></span>
            </div>*@

              <div class="form-group">
                <label asp-for="CId" class="control-label">@localizer["thecountry"]</label>
                    
                <select asp-for="CId" class ="form-select" >
                    @foreach(var item in ViewBag.CId){
                    <option value="@item.Value">@item.Text </option>
                    }
                </select>
            <span asp-validation-for="CId" class="text-danger"></span>
               
                @*<select asp-for="CId"  required   class="form-select" asp-items="ViewBag.Activity"></select>*@
            </div>
            @*<div class="form-group">
                <label asp-for="CId" class="control-label"></label>
                <select asp-for="CId" class ="form-control" asp-items="ViewBag.CId"></select>
            </div>*@
            <div class="form-group">
                <button type="submit" value="Create" class="btn btn-primary">@localizer["create"]</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">@localizer["backtolist"] </a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
