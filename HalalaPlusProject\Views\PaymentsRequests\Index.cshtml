﻿@using Azure.Core
@model IEnumerable<HalalaPlusProject.CModels.PaymentsRequests>

<h2>عمليات الدفع</h2>

<form method="get">
    <input type="text" name="search" placeholder="بحث بالاسم أو رقم الهاتف" value="@ViewBag.Search" />
    <button type="submit">بحث</button>
     <a href="@Url.Action("ExportToExcel", new { search = @ViewBag.Search })" class="btn btn-success">تصدير إلى Excel</a> 
     
</form>

<table id="tbl1" class="table table-striped text-center">
    <thead>
        <tr>
          
            <th>المستخدم</th>
            <th>رقم الهاتف</th>
            <th>المبلغ</th>
            <th>نوع العملية</th>
            <th>الحالة</th>
            <th>تحقق</th>
            <th>تاريخ الإنشاء</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                
                <td>@item.UserName</td>
                <td>@item.UserPhone</td>
                <td>@item.Amount</td>
                <td>
                    @if (item.EntityId == "pr")
                    {
                        <span>دفع منتج</span>
                    }
                    else if (item.EntityId == "m")
                    {

                        <span>شحن حصالة</span>
                    }
                    else
                    {

                        <span>@item.EntityId</span>
                    }
                </td>
                <td>
                    @if (item.Status == "CAPTURED" || item.Status == "Transaction succeeded")
                    {
                        <span style="color:green;">تمت</span>
                    }
                    else
                    {

                        <span style="color:red;">لم تتم</span>
                    }
                </td>
                <td>@(item.IsVerified == true ? "نعم" : "لا")</td>
                <td>@item.CreateAt?.ToString("yyyy-MM-dd HH:mm")</td>
            </tr>
        }
    </tbody>
</table>
         @section Scripts{
    <script>
  let table = new DataTable('#tbl1');
        table.order([0, 'desc']).draw();

    </script>
         }