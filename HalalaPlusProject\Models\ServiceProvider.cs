﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Index("AspId", Name = "IX_ServiceProviders_ASP_Id")]
[Index("Activity", Name = "IX_ServiceProviders_Activity")]
public partial class ServiceProvider
{
    [Key]
    public int Id { get; set; }

    public string? Name { get; set; }

    public int? City { get; set; }

    public int? Activity { get; set; }

    public string? ServiceProviderRepresent { get; set; }

    public string? PhoneNumber { get; set; }

    public string? Email { get; set; }

    public int? MasterId { get; set; }

    public int? AddUser { get; set; }

    [Column("ASP_Id")]
    public string AspId { get; set; } = null!;

    [Column("BusnissNO")]
    [StringLength(450)]
    public string BusnissNo { get; set; } = null!;

    [StringLength(250)]
    public string EnterricePhoneNo { get; set; } = null!;

    [StringLength(250)]
    public string Locatin { get; set; } = null!;

    [StringLength(450)]
    public string Logo { get; set; } = null!;

    [Column(TypeName = "datetime")]
    public DateTime? DateofAdding { get; set; }

    public string? EnName { get; set; }

    public string? EnServiceProviderRepresent { get; set; }

    [StringLength(250)]
    public string? EnLocatin { get; set; }

    [ForeignKey("Activity")]
    [InverseProperty("ServiceProviders")]
    public virtual Activity? ActivityNavigation { get; set; }

    [ForeignKey("AspId")]
    [InverseProperty("ServiceProviders")]
    public virtual AspNetUser Asp { get; set; } = null!;
}
