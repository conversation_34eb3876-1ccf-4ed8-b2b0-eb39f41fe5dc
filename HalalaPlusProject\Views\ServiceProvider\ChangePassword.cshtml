﻿@model HalalaPlusProject.CModels.ChangePasswordViewModel
@{
    ViewBag.Title = "Change Password";
}
 
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["addserviceprovider"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    .btn{
        margin-bottom:0px !important
    }
</style>
@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success">
        @TempData["SuccessMessage"]
    </div>
}
@if (ViewBag.Message != null)
{
    <div class="alert alert-danger">
        @ViewBag.Message
    </div>
}

<div class="row">
    <div class="col-md-12">
        <form asp-action="ChangePassword" id="ProvidersFrm" novalidate>

            <div class="row">
                <div class="col-md-3"> 
                    <div class="form-group position-relative">
                        <label asp-for="CurrentPassword" class="control-label">
                            @localizer["Currentpassword"]<span style="font-size:20pt; color: red;">*</span>
                        </label>
                        <div class="input-group">
                            <input asp-for="CurrentPassword" type="password" required class="form-control" id="CurrentPassword" />
                            <button type="button" class="btn btn-outline-secondary toggle-password" data-target="#CurrentPassword">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <span asp-validation-for="CurrentPassword" class="text-danger"></span>
                    </div>
                     
                    <div class="form-group position-relative">
                        <label asp-for="NewPassword" class="control-label">
                            @localizer["Newpassword"]<span style="font-size:20pt; color: red;">*</span>
                        </label>
                        <div class="input-group">
                            <input asp-for="NewPassword" type="password" required class="form-control" id="NewPassword" />
                            <button type="button" class="btn btn-outline-secondary toggle-password" data-target="#NewPassword">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <span asp-validation-for="NewPassword" class="text-danger"></span>
                    </div>
                     
                    <div class="form-group position-relative">
                        <label asp-for="ConfirmPassword" class="control-label">
                            @localizer["Confirmnewpassword"]<span style="font-size:20pt; color: red;">*</span>
                        </label>
                        <div class="input-group">
                            <input asp-for="ConfirmPassword" type="password" required class="form-control" id="ConfirmPassword" />
                            <button type="button" class="btn btn-outline-secondary toggle-password" data-target="#ConfirmPassword">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                    </div>
                </div>
            </div>

            <div class="mt-2">
                <button type="submit" value="Create" class="btn btn-primary">@localizer["save"]</button>
            </div>
        </form>
    </div>
</div>
 
<script>
    document.querySelectorAll(".toggle-password").forEach(button => {
        button.addEventListener("click", function () {
            const target = document.querySelector(this.dataset.target);
            const icon = this.querySelector("i");

            if (target.type === "password") {
                target.type = "text";
                icon.classList.remove("bi-eye");
                icon.classList.add("bi-eye-slash");
            } else {
                target.type = "password";
                icon.classList.remove("bi-eye-slash");
                icon.classList.add("bi-eye");
            }
        });
    });
</script>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/localization/messages_ar.js"></script>
}
