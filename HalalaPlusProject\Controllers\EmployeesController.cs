﻿using HalalaPlusProject.Areas.Identity.Data;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System.Text;
using System.Text.Encodings.Web;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة الموظفين، وتوفير عمليات الإنشاء والقراءة والتحديث والحذف (CRUD) لبياناتهم.
    /// </summary>
    [Authorize]
    public class EmployeesController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly UserManager<HalalaPlusProjectUser> _userManager;
        private readonly IEmailSender _emailSender;
        private readonly IStringLocalizer<EmployeesController> _localization;
        public EmployeesController(HalalaPlusdbContext context,
            UserManager<HalalaPlusProjectUser> userManager
            , IEmailSender emailSender,
            IStringLocalizer<EmployeesController> _localization)
        {
            _context = context;
            this._localization = _localization;
            _userManager = userManager;
            _emailSender = emailSender;
        }

        /// <summary>
        /// عرض قائمة بجميع الموظفين.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة الموظفين.</returns>
        public async Task<IActionResult> Index()
        {
            return View(new UsersClass().retrive("Employee", _context, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value));
        }

        /// <summary>
        /// عرض التفاصيل الخاصة بموظف معين.
        /// </summary>
        /// <param name="id">معرف الموظف المراد عرض تفاصيله.</param>
        /// <returns>عرض يحتوي على تفاصيل الموظف، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Details(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }
            var systemUser = new UsersClass().retriveUser(id ?? 0, _context);
            if (systemUser == null)
            {
                return NotFound();
            }
            return View(systemUser);
        }


        /// <summary>
        /// عرض نموذج إنشاء موظف جديد.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج الإنشاء.</returns>
        public IActionResult Create()
        {
            ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality");
            return View();
        }

        /// <summary>
        /// معالجة عملية إنشاء موظف جديد، بما في ذلك إنشاء حساب له في نظام الهوية.
        /// </summary>
        /// <param name="model">البيانات الخاصة بالموظف الجديد.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة الموظفين؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CModels.EmployeesModel model)
        {
            if (ModelState.IsValid)
            {
                
                var userExists = await _userManager.FindByEmailAsync(model.Email);
                if (userExists != null)
                {
                    return Ok(new { state = 0, message = "هذا البريد الإلكتروني مستخدم بالفعل" });
                }

                
                var userNameExists = await _userManager.FindByNameAsync(model.UserName);
                if (userNameExists != null)
                {
                    return Ok(new { state = 0, message = "اسم المستخدم مستخدم بالفعل" });
                }

                HalalaPlusProjectUser user = new()
                {
                    Email = model.Email,
                    SecurityStamp = Guid.NewGuid().ToString(),
                    PhoneNumber = model.PhoneNo,
                    UserName = model.UserName,
                    FullName = model.Name
                };

                var result = await _userManager.CreateAsync(user, model.Password);
                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(user, "Employee");
                    try
                    {
                        EmployeeClass ob = new EmployeeClass(_context);
                        if (!await ob.Insert(model, user, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value))
                        {
                            await _userManager.DeleteAsync(user);
                            return Ok(new { state = 0, message = "حدث خطأ أثناء حفظ بيانات الموظف في قاعدة البيانات" });
                        }

                        user.EmailConfirmed = true;
                        await _userManager.UpdateAsync(user);

                        return Ok(new { state = 7, message = "تم إنشاء الموظف بنجاح", url = "/Employees/Index" });
                    }
                    catch (Exception)
                    {
                        await _userManager.DeleteAsync(user);
                        return Ok(new { state = 0, message = "حدث خطأ غير متوقع أثناء إنشاء الموظف" });
                    }
                }
                else
                {
                    var errorMessages = new List<string>();

                    foreach (var error in result.Errors)
                    {
                        switch (error.Code)
                        {
                            case "PasswordTooShort":
                                errorMessages.Add("كلمة المرور يجب أن تكون على الأقل 6 أحرف.");
                                break;
                            case "PasswordRequiresNonAlphanumeric":
                                errorMessages.Add("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل (مثل ! @ # $).");
                                break;
                            case "PasswordRequiresDigit":
                                errorMessages.Add("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل.");
                                break;
                            case "PasswordRequiresLower":
                                errorMessages.Add("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل.");
                                break;
                            case "PasswordRequiresUpper":
                                errorMessages.Add("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل.");
                                break;
                            case "DuplicateUserName":
                                errorMessages.Add("اسم المستخدم مستخدم بالفعل.");
                                break;
                            case "DuplicateEmail":
                                errorMessages.Add("البريد الإلكتروني مستخدم بالفعل.");
                                break;
                            default:
                                errorMessages.Add("خطأ: " + error.Description);
                                break;
                        }
                    }

                    var finalMessage = string.Join(" - ", errorMessages);
                    return Ok(new { state = 0, message = finalMessage });
                }

            }

            return Ok(new { state = 0, message = "البيانات المدخلة غير صالحة، يرجى مراجعتها" });
        }


        /// <summary>
        /// عرض نموذج تعديل بيانات موظف حالي.
        /// </summary>
        /// <param name="id">معرف الموظف المراد تعديله.</param>
        /// <returns>عرض يحتوي على بيانات الموظف في نموذج التعديل، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Edit(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }
            var systemUser = new UsersClass().retriveUserEmployee(id ?? 0, _context);
            if (systemUser == null)
            {
                return NotFound();
            }
            ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality", systemUser.Nationality);
            return View(systemUser);
        }

        /// <summary>
        /// معالجة التعديلات المقدمة لبيانات موظف وحفظها.
        /// </summary>
        /// <param name="model">البيانات المحدثة للموظف.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة الموظفين؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(CModels.UserEmployeeModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var ob = _context.SystemUsers.Where(g=>g.Id==model.Id).FirstOrDefault();
                    if (ob != null)
                        if (_context.AspNetUsers.Where(p => (p.Email == model.Email || p.UserName == model.UserName) && p.Id != ob.AspId).Count() > 0)
                            return View(model);


                    ob.Name = model.Name;
                    ob.PhoneNo = model.PhoneNo;
                    ob.Email = model.Email;
                    ob.Salary = model.Salary;
                    ob.BirthDate = model.BirthDate.Value;
                    ob.IdentityNo = model.IdentityNo;
                    ob.Salary = model.Salary;

                    ob.Nationality = model.Nationality;
                    _context.Update(ob);

                    var userExists = await _userManager.FindByEmailAsync(model.Email);
                    userExists.Email = model.Email;
                    userExists.UserName = model.UserName;
                    await _userManager.UpdateAsync(userExists);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    return View(model);
                }
                return Ok(new { state = 7, message = _localization["opsuccess"].Value, url = "/Employees/Index" });
            }

            ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality", model.Nationality);
            return View(model);
        }



        /// <summary>
        /// عرض صفحة تأكيد حذف موظف.
        /// </summary>
        /// <param name="id">معرف الموظف المراد حذفه.</param>
        /// <returns>عرض يحتوي على تفاصيل الموظف لتأكيد الحذف، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Delete(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }

            var systemUser = new UsersClass().retriveUser(id ?? 0, _context);
            if (systemUser == null)
            {
                return NotFound();
            }
            return View(systemUser);
        }

        /// <summary>
        /// تنفيذ عملية حذف منطقي للموظف عن طريق تعيين علامة 'Deleted' إلى 'true'.
        /// </summary>
        /// <param name="id">معرف الموظف المراد حذفه.</param>
        /// <returns>بعد إتمام العملية بنجاح، يتم عرض صفحة قائمة الموظفين.</returns>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(long id)
        {
            if (_context.SystemUsers == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.SystemUsers'  is null.");
            }
            var systemUser = await _context.SystemUsers.FindAsync(id);
            if (systemUser != null)
            {
                systemUser.Deleted = true;
                _context.Update(systemUser);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// التحقق من وجود مستخدم في النظام بالمعرف المحدد.
        /// </summary>
        /// <param name="id">معرف المستخدم للتحقق منه.</param>
        /// <returns>إرجاع 'true' إذا كان المستخدم موجودًا، وإلا 'false'.</returns>
        private bool SystemUserExists(long id)
        {
            return (_context.SystemUsers?.Any(e => e.Id == id)).GetValueOrDefault();
        }

    }
}