<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HalalaPlus - Professional Dashboard</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    
    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css">
    
    <!-- Toastr -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Professional CSS -->
    <link rel="stylesheet" href="css/professional-style.css">
    
    <style>
        /* Additional Professional Styles */
        .professional-card {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            transition: var(--transition-normal);
            overflow: hidden;
        }
        
        .professional-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }
        
        .card-header-professional {
            padding: 1.5rem 2rem 1rem;
            border-bottom: 1px solid var(--border-color);
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
        }
        
        .card-body-professional {
            padding: 2rem;
        }
        
        .stat-card {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: 2rem;
            text-align: center;
            border: 1px solid var(--border-color);
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--radius-xl);
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: var(--font-size-sm);
        }
        
        .btn-professional {
            padding: 0.75rem 2rem;
            border-radius: var(--radius-lg);
            font-weight: 600;
            font-size: var(--font-size-sm);
            border: none;
            cursor: pointer;
            transition: var(--transition-fast);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }
        
        .btn-primary-professional {
            background: var(--primary-gradient);
            color: white;
        }
        
        .btn-primary-professional:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: white;
        }
        
        .btn-outline-professional {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }
        
        .btn-outline-professional:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .table-professional {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }
        
        .table-professional thead {
            background: var(--bg-secondary);
        }
        
        .table-professional th {
            padding: 1rem 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            border: none;
            font-size: var(--font-size-sm);
        }
        
        .table-professional td {
            padding: 1rem 1.5rem;
            border: none;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-secondary);
        }
        
        .table-professional tbody tr:hover {
            background: var(--bg-secondary);
        }
        
        .badge-professional {
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-md);
            font-size: var(--font-size-xs);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .badge-success {
            background: rgba(130, 214, 22, 0.1);
            color: var(--success-color);
        }
        
        .badge-warning {
            background: rgba(251, 207, 51, 0.1);
            color: var(--warning-color);
        }
        
        .badge-danger {
            background: rgba(234, 6, 6, 0.1);
            color: var(--danger-color);
        }
        
        .progress-professional {
            height: 8px;
            border-radius: var(--radius-md);
            background: var(--bg-tertiary);
            overflow: hidden;
        }
        
        .progress-bar-professional {
            height: 100%;
            background: var(--primary-gradient);
            border-radius: var(--radius-md);
            transition: var(--transition-normal);
        }
        
        /* Mobile Overlay */
        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition-fast);
        }
        
        .mobile-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        /* Language Toggle */
        .language-toggle {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            z-index: 1000;
            background: var(--primary-gradient);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            font-weight: 600;
            font-size: var(--font-size-sm);
            box-shadow: var(--shadow-lg);
            transition: var(--transition-fast);
        }
        
        .language-toggle:hover {
            transform: translateY(-50%) scale(1.1);
        }
        
        [dir="ltr"] .language-toggle {
            right: auto;
            left: 20px;
        }
    </style>
</head>
<body>
    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobileOverlay"></div>
    
    <!-- Language Toggle -->
    <button class="language-toggle" onclick="toggleLanguage()" id="langToggle">
        EN
    </button>
    
    <!-- Professional Sidebar -->
    <aside class="professional-sidebar" id="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
            <a href="#" class="sidebar-logo">
                <img src="assets/img/placeholder.svg" alt="Logo">
                <span class="sidebar-logo-text">HalalaPlus</span>
            </a>
        </div>
        
        <!-- Sidebar Navigation -->
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="#" class="nav-link active">
                    <div class="nav-icon">
                        <i class="material-icons-round">dashboard</i>
                    </div>
                    <span class="nav-text">لوحة التحكم</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <div class="nav-icon">
                        <i class="material-icons-round">people</i>
                    </div>
                    <span class="nav-text">المستخدمون</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <div class="nav-icon">
                        <i class="material-icons-round">inventory_2</i>
                    </div>
                    <span class="nav-text">المنتجات</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <div class="nav-icon">
                        <i class="material-icons-round">shopping_cart</i>
                    </div>
                    <span class="nav-text">الطلبات</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <div class="nav-icon">
                        <i class="material-icons-round">analytics</i>
                    </div>
                    <span class="nav-text">التقارير</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <div class="nav-icon">
                        <i class="material-icons-round">settings</i>
                    </div>
                    <span class="nav-text">الإعدادات</span>
                </a>
            </div>
        </nav>
        
        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
            <a href="#" class="sidebar-user">
                <div class="user-avatar">أم</div>
                <div class="user-info">
                    <div class="user-name">أحمد محمد</div>
                    <div class="user-role">مدير النظام</div>
                </div>
            </a>
        </div>
    </aside>
    
    <!-- Professional Header -->
    <header class="professional-header" id="header">
        <div class="header-content">
            <div class="header-left">
                <button class="mobile-menu-toggle" onclick="toggleMobileSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="breadcrumb-container">
                    <nav class="breadcrumb">
                        <div class="breadcrumb-item">
                            <a href="#">الرئيسية</a>
                        </div>
                        <div class="breadcrumb-item active">لوحة التحكم</div>
                    </nav>
                    <h1 class="page-title">لوحة التحكم الرئيسية</h1>
                </div>
            </div>
            
            <div class="header-right">
                <div class="header-search">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="البحث في النظام...">
                </div>
                
                <div class="header-actions">
                    <button class="header-action" title="الإشعارات">
                        <i class="material-icons-round">notifications</i>
                        <span class="notification-badge"></span>
                    </button>
                    
                    <button class="header-action" title="الرسائل">
                        <i class="material-icons-round">mail</i>
                        <span class="notification-badge"></span>
                    </button>
                    
                    <div class="user-dropdown">
                        <button class="user-trigger">
                            <div class="user-avatar-header">أم</div>
                            <span class="user-name-header">أحمد محمد</span>
                            <i class="material-icons-round">keyboard_arrow_down</i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        <div class="content-wrapper">
            
            <!-- Statistics Cards -->
            <div class="row mb-4" data-aos="fade-up">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="material-icons-round">people</i>
                        </div>
                        <div class="stat-number">2,847</div>
                        <div class="stat-label">إجمالي المستخدمين</div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--success-gradient);">
                            <i class="material-icons-round">trending_up</i>
                        </div>
                        <div class="stat-number">$45,678</div>
                        <div class="stat-label">إجمالي المبيعات</div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--info-gradient);">
                            <i class="material-icons-round">shopping_cart</i>
                        </div>
                        <div class="stat-number">1,234</div>
                        <div class="stat-label">الطلبات الجديدة</div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--warning-gradient);">
                            <i class="material-icons-round">star</i>
                        </div>
                        <div class="stat-number">4.8</div>
                        <div class="stat-label">متوسط التقييم</div>
                    </div>
                </div>
            </div>
            
            <!-- Main Cards -->
            <div class="row">
                <!-- Recent Orders -->
                <div class="col-xl-8 mb-4" data-aos="fade-right">
                    <div class="professional-card">
                        <div class="card-header-professional">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0 fw-bold">الطلبات الأخيرة</h5>
                                <a href="#" class="btn-outline-professional">
                                    عرض الكل
                                    <i class="material-icons-round">arrow_forward</i>
                                </a>
                            </div>
                        </div>
                        <div class="card-body-professional p-0">
                            <div class="table-responsive">
                                <table class="table table-professional mb-0">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>العميل</th>
                                            <th>المبلغ</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="fw-bold">#12345</td>
                                            <td>أحمد محمد علي</td>
                                            <td class="fw-bold">$250.00</td>
                                            <td><span class="badge-professional badge-success">مكتمل</span></td>
                                            <td>2024-01-15</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">#12346</td>
                                            <td>فاطمة أحمد</td>
                                            <td class="fw-bold">$180.50</td>
                                            <td><span class="badge-professional badge-warning">قيد المعالجة</span></td>
                                            <td>2024-01-14</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">#12347</td>
                                            <td>محمد علي</td>
                                            <td class="fw-bold">$320.75</td>
                                            <td><span class="badge-professional badge-danger">ملغي</span></td>
                                            <td>2024-01-13</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="col-xl-4 mb-4" data-aos="fade-left">
                    <div class="professional-card">
                        <div class="card-header-professional">
                            <h5 class="mb-0 fw-bold">الإجراءات السريعة</h5>
                        </div>
                        <div class="card-body-professional">
                            <div class="d-grid gap-3">
                                <a href="#" class="btn-primary-professional">
                                    <i class="material-icons-round">add</i>
                                    إضافة منتج جديد
                                </a>
                                <a href="#" class="btn-outline-professional">
                                    <i class="material-icons-round">people</i>
                                    إدارة المستخدمين
                                </a>
                                <a href="#" class="btn-outline-professional">
                                    <i class="material-icons-round">analytics</i>
                                    عرض التقارير
                                </a>
                                <a href="#" class="btn-outline-professional">
                                    <i class="material-icons-round">settings</i>
                                    إعدادات النظام
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Progress Section -->
            <div class="row">
                <div class="col-12" data-aos="fade-up">
                    <div class="professional-card">
                        <div class="card-header-professional">
                            <h5 class="mb-0 fw-bold">تقدم المشاريع</h5>
                        </div>
                        <div class="card-body-professional">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="fw-medium">تطوير الموقع</span>
                                        <span class="text-muted">85%</span>
                                    </div>
                                    <div class="progress-professional">
                                        <div class="progress-bar-professional" style="width: 85%"></div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="fw-medium">تطبيق الجوال</span>
                                        <span class="text-muted">60%</span>
                                    </div>
                                    <div class="progress-professional">
                                        <div class="progress-bar-professional" style="width: 60%; background: var(--success-gradient);"></div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="fw-medium">التسويق الرقمي</span>
                                        <span class="text-muted">40%</span>
                                    </div>
                                    <div class="progress-professional">
                                        <div class="progress-bar-professional" style="width: 40%; background: var(--warning-gradient);"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </main>
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Professional JavaScript -->
    <script src="js/professional.js"></script>
</body>
</html>
