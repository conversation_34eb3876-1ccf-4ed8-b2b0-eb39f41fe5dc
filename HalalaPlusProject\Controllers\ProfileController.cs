﻿using HalalaPlusProject.Entities;
using HalalaPlusProject.CustomClasses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    /// <summary>
    /// إدارة عمليات ملف تعريف المستخدم عبر الواجهة البرمجية (API)، وتحديداً تحديث صورة الملف الشخصي.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ProfileController : ControllerBase
    {
        private readonly IWebHostEnvironment _host;
        public ProfileController(IWebHostEnvironment _host)
        {

            this._host = _host;
        }

        /// <summary>
        /// معالجة عملية تحديث صورة الملف الشخصي للمستخدم.
        /// </summary>
        /// <param name="model">النموذج الذي يحتوي على ملف الصورة المراد تحديثه.</param>
        /// <returns>نتيجة `Ok` تحتوي على مسار الصورة الجديدة، أو رسالة خطأ في حالة عدم توفير صورة.</returns>
        [HttpPost]
        [Route("UpdateProfileImage")]


        public async Task<IActionResult> UpdatePersonalImage([FromForm] ProfileImage model)
        {
            if (model.image == null) { return Ok("No IMage"); }

            var Image = HandleImages.SaveImage(model.image, "img", _host);
            return Ok(new imagemodel() { image = "/img/" + Image });
        }
    }
}