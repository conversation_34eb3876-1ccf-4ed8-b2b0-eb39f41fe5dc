﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models
{
    public partial class GetProviderStatistics
    {


        //public int Id { get; set; }
        [Key]
        public int DiscountsCount { get; set; }
        public double TotalDiscountAmount { get; set; }
        public int OffersCount { get; set; }
        public double TotalOffersCount { get; set; }
        public int CodeCount { get; set; }
        public double TotalCodeCount { get; set; }
        public int UsedDiscountCodes { get; set; }
        public double TotalGrantedDiscount { get; set; }
        public int TotalGrantedPoints { get; set; }


    }
}

