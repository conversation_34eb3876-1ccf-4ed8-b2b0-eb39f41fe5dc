﻿@model HalalaPlusProject.CModels.UserEmployeeModel
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["edit"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["edit"]</h1>

<h4>@localizer["theemployee"]</h4>
<hr />
<div class="row">
    <div class="col-md-8">
        <form asp-action="Edit" class="submitfm" id="employeeForm">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
             
         <div class="row">
           <div class="col-md-5">
          <div class="form-group">
                        <label asp-for="Name" class="control-label">@localizer["name"]<span class="text-danger required-star">*</span></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" required class="text-danger"></span>
            </div>
            
            <div class="form-group">
                        <label asp-for="Nationality" class="control-label">@localizer["nationality"]<span class="text-danger required-star">*</span></label>
                 <select asp-for="Nationality" required  class="form-select" asp-items="ViewBag.National"></select>
                  <span asp-validation-for="Nationality" required class="text-danger"></span>
            </div>

              <div class="form-group">
                        <label asp-for="IdentityNo" class="control-label">@localizer["identityno"]<span class="text-danger required-star">*</span></label>
                <input asp-for="IdentityNo" class="form-control" />
                <span asp-validation-for="IdentityNo" class="text-danger"></span>
            </div>
            <div class="form-group">
                        <label asp-for="BirthDate" class="control-label">@localizer["birthdate"]<span class="text-danger required-star">*</span></label>
                <input asp-for="BirthDate" type="date" class="form-control" />
                <span asp-validation-for="BirthDate" class="text-danger"></span>
            </div>
     <div class="form-group">
                        <label asp-for="Salary" class="control-label">@localizer["salary"] <span class="text-danger required-star">*</span></label>
                <input asp-for="Salary" placeholder="2000" class="form-control" />
                <span asp-validation-for="Salary" class="text-danger"></span>
            </div>

          </div>

           <div class="col-md-5">
         
         <div class="form-group">
                        <label asp-for="PhoneNo" class="control-label">@localizer["phoneno"]<span class="text-danger required-star">*</span></label>
                <input asp-for="PhoneNo" class="form-control" />
                <span asp-validation-for="PhoneNo" class="text-danger"></span>
            </div>
            <div class="form-group">
                        <label asp-for="Email" class="control-label">@localizer["mail"]<span class="text-danger required-star">*</span></label>
                <input asp-for="Email" readonly class="form-control" />
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>
              <div class="form-group">
                        <label asp-for="UserName" class="control-label">@localizer["username"]<span class="text-danger required-star">*</span> </label>
                <input asp-for="UserName" readonly class="form-control" />
                <span asp-validation-for="UserName" class="text-danger"></span>
            </div>
              
            

          </div>
             </div>





            <br /><br />
           
            <div class="form-group">
                <button type="submit" value="Save" class="btn btn-primary">@localizer["save"]</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>


@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }

    <script>
        $(document).ready(function () {
            $("#PhoneNo").on("input", function () {
                var phoneValue = $(this).val();
                var phoneRegex = /^[\d\s\-\+\(\)]{10,10}$/;

                if (phoneValue && !phoneRegex.test(phoneValue)) {
                    $('span[data-valmsg-for="PhoneNo"]').text("رقم الهاتف غير صحيح");
                } else {
                    $('span[data-valmsg-for="PhoneNo"]').text("");
                }
            });

            $("#Email").on("input", function () {
                var emailValue = $(this).val();
            var emailRegex = /^[^\s@@]+@@[^\s@@]+\.[^\s@@]+$/;

                if (emailValue && !emailRegex.test(emailValue)) {
                    $('span[data-valmsg-for="Email"]').text("البريد الإلكتروني غير صحيح");
                } else {
                    $('span[data-valmsg-for="Email"]').text("");
                }
            });

            $("#employeeForm").on("submit", function (e) {
                var phoneValue = $("#PhoneNo").val();
                var emailValue = $("#Email").val();

                var phoneRegex = /^[\d\s\-\+\(\)]{8,15}$/;
                var emailRegex = /^[^\s@@]+@@[^\s@@]+\.[^\s@@]+$/;

                var valid = true;

                if (!phoneRegex.test(phoneValue)) {
                    $('span[data-valmsg-for="PhoneNo"]').text("رقم الهاتف غير صحيح");
                    valid = false;
                }
                if (!emailRegex.test(emailValue)) {
                    $('span[data-valmsg-for="Email"]').text("البريد الإلكتروني غير صحيح");
                    valid = false;
                }

                if (!valid) {
                    e.preventDefault();
                }
            });
        });
    </script>
    <script>
        $(document).ready(function () {
            // إضافة شرط مخصص للباسورد
            function validatePassword(password) {
                const minLength = 8;
                const hasUpper = /[A-Z]/.test(password);
                const hasLower = /[a-z]/.test(password);
                const hasNumber = /[0-9]/.test(password);
                const hasSpecial = /[\W_]/.test(password);

                return password.length >= minLength && hasUpper && hasLower && hasNumber && hasSpecial;
            }

            $("#PasswordInput").on("input", function () {
                var passwordValue = $(this).val();
                var errorSpan = $(this).closest(".form-group").find("span.text-danger");

                if (!passwordValue) {
                    errorSpan.text("من فضلك أدخل كلمة المرور");
                } else if (!validatePassword(passwordValue)) {
                    errorSpan.text("كلمة المرور يجب أن تكون 8 أحرف على الأقل وتحتوي على حرف كبير وصغير ورقم ورمز خاص.");
                } else {
                    errorSpan.text("");
                }
            });


            $("#employeeForm").on("submit", function (e) {
                var passwordValue = $("#PasswordInput").val();
                var errorSpan = $("#PasswordInput").closest(".form-group").find("span.text-danger");

                if (!validatePassword(passwordValue)) {
                    errorSpan.text("كلمة المرور يجب أن تكون قوية (8 أحرف على الأقل، حرف كبير وصغير، رقم ورمز خاص).");
                    e.preventDefault();
                }
            });
        });
    </script>

}
