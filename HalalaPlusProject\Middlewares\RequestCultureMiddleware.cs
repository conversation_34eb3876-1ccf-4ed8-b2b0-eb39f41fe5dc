﻿using HalalaPlusProject.Utils;
using Microsoft.AspNetCore.Localization;
using System.Globalization;

namespace HalalaPlusProject.Middlewares
{
    public class RequestCultureMiddleware
    {
        private readonly RequestDelegate _next;

        public RequestCultureMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        { 
            if (context.Request.Path.StartsWithSegments("/images") ||
                context.Request.Path.StartsWithSegments("/img") ||
                context.Request.Path.StartsWithSegments("/files") ||
                context.Request.Path.StartsWithSegments("/icons"))
            {
                await _next(context);
                return;
            }

            
            var cultureQuery = context.Request.Query["culture"].ToString();
            if (!string.IsNullOrEmpty(cultureQuery))
            {
                var culture1 = new CultureInfo(cultureQuery);
                CultureInfo.CurrentCulture = culture1;
                CultureInfo.CurrentUICulture = culture1;

                context.Response.Cookies.Append(
                    CookieRequestCultureProvider.DefaultCookieName,
                    CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture1)),
                    new CookieOptions { Expires = DateTimeOffset.UtcNow.AddYears(1) }
                );

                await _next(context);
                return;
            }

            // Check for existing cookie
            var cookieCulture = context.Request.Cookies[CookieRequestCultureProvider.DefaultCookieName];
            if (!string.IsNullOrEmpty(cookieCulture))
            {
                await _next(context);
                return;
            }
             
            var browserLanguage = context.Request.GetTypedHeaders().AcceptLanguage?
                .OrderByDescending(x => x.Quality ?? 1)
                .Select(x => x.Value.ToString())
                .FirstOrDefault() ?? "en-US";

            var culture = browserLanguage.StartsWith("ar") ? "ar-EG" : "en-US";

            context.Response.Cookies.Append(
                CookieRequestCultureProvider.DefaultCookieName,
                CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture)),
                new CookieOptions { Expires = DateTimeOffset.UtcNow.AddYears(1) }
            );

            CultureInfo.CurrentCulture = new CultureInfo(culture);
            CultureInfo.CurrentUICulture = new CultureInfo(culture);

            await _next(context);
        }
    }
}