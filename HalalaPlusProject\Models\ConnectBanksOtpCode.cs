﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class ConnectBanksOtpCode
{
    [Key]
    public long Id { get; set; }

    public long? UserId { get; set; }

    public int? BankId { get; set; }

    [Column("otpId")]
    public long? OtpId { get; set; }

    [Column("isAuth")]
    public bool IsAuth { get; set; }

    [Column("createDate", TypeName = "datetime")]
    public DateTime? CreateDate { get; set; }

    [ForeignKey("BankId")]
    [InverseProperty("ConnectBanksOtpCodes")]
    public virtual BanksAccount? Bank { get; set; }

    [ForeignKey("OtpId")]
    [InverseProperty("ConnectBanksOtpCodes")]
    public virtual Otplogin? Otp { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("ConnectBanksOtpCodes")]
    public virtual SystemUser? User { get; set; }
}
