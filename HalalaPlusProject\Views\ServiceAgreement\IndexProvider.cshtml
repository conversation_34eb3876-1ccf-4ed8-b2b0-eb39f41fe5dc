﻿@model HalalaPlusProject.Models.SystemUser
@{
    ViewData["Title"] = "اتفاقيات مقدمي الخدمات";
}

<h2 class="text-center mb-4">اتفاقيات مقدمي الخدمات</h2>

<table class="table table-bordered table-striped text-center">
    <thead>
        <tr>
            <th>الاسم</th>
            <th>النشاط</th>
            <th>المدينة</th>
            <th>رقم الجوال</th>
            <th>تحميل الاتفاقية</th>
        </tr>
    </thead>
    <tbody>
      
            <tr>
                <td>@Model.Name</td>
            <td>@Model.Activity</td>
            <td>@Model.City</td>
            <td>@Model.PhoneNo</td>
                <td>
                <a class="btn btn-success" target="_blank" asp-action="GeneratePdf" asp-route-id="@Model.Id">تحميل PDF</a>
                </td>
            </tr>
       
    </tbody>
</table>
