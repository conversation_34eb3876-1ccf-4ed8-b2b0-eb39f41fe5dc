﻿using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة الخصومات الخاصة بمقدمي الخدمة، بما في ذلك عرض وإنشاء وتعديل الخصومات.
    /// </summary>
    [Authorize]
    public class ProviderDiscountController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IStringLocalizer<ProviderDiscountController> _localization;

        /// <summary>
        /// إنشاء نسخة جديدة من وحدة التحكم مع حقن الخدمات المطلوبة.
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات.</param>
        /// <param name="_localization">خدمة الترجمة.</param>
        public ProviderDiscountController(HalalaPlusdbContext context, IStringLocalizer<ProviderDiscountController> _localization)
        {
            this._context = context;
            this._localization = _localization;
        }

        /// <summary>
        /// عرض قائمة بالخصومات الخاصة بمقدم الخدمة الحالي. إذا تم توفير معرف، يتم استخدامه لتعبئة نموذج التعديل.
        /// </summary>
        /// <param name="id">معرف الخصم المراد تعديله (اختياري).</param>
        /// <returns>عرض يحتوي على قائمة الخصومات وبيانات الخصم المحدد للتعديل.</returns>
        public async Task<IActionResult> Index(long? id = null)
        {
            var t = new CModels.DiscountsProviderList();
            if (id != null)
            {
                var temp = _context.DiscountsTables.FirstOrDefault(p => p.Id == id && p.Deleted != true &&
               p.UserId == _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id);
                if (temp != null)
                {
                    t.Id = temp.Id;
                    t.StartDate = DateTime.Parse(temp.StartDate.ToString());
                    t.EndDate = DateTime.Parse(temp.EndDate.ToString());
                    t.DiscountName = temp.DisCountName;
                    t.EnDiscountName = temp.EnDiscountName;
                    t.Discount = temp.Discount;
                    t.Conditions = temp.Conditions;
                }
            }
            var data = (from ob in _context.DiscountsTables
                        join user in _context.SystemUsers on ob.UserId equals user.Id
                        where ob.UserId == _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id

                        select new CModels.DiscountsModel
                        {
                            StartDate =  ob.StartDate ,
                            EndDate =  ob.EndDate ,
                            Id = ob.Id,
                            Discount = ob.Discount,
                            Conditions = ob.Conditions,
                            DiscountName = ob.DisCountName,
                            IsActive = ob.IsActive,
                            GrantType = (ob.GrantDiscount == true && ob.GrantPoints == true) ? _localization["discountandypoints"].Value : (ob.GrantDiscount == true && ob.GrantPoints == false) ? _localization["discountname"].Value : _localization["onlydiscount"].Value
                        }).ToList();
            t.ListDis = data;
            return View(t);
        }

        /// <summary>
        /// معالجة عملية إضافة أو تعديل خصم لمقدم الخدمة الحالي.
        /// </summary>
        /// <param name="model">البيانات الخاصة بالخصم المراد إضافته أو تعديله.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> AddEditDiscount(HalalaPlusProject.CModels.DiscountsModel model)
        {
            if (!ModelState.IsValid)
            {
                return Ok(new { state = 0, message = _localization["fillalldata"].Value });
            }
             
            if (model.EndDate <= model.StartDate)
            {
                return Ok(new { state = 0, message = "تاريخ النهاية يجب أن يكون بعد تاريخ البداية" });
            }

            var aspId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

            if (aspId == null)
                return Unauthorized(new { state = 0, message = "المستخدم غير معروف" });

            var userId = _context.SystemUsers.FirstOrDefault(p => p.AspId == aspId)?.Id;

            if (userId == null)
                return Unauthorized(new { state = 0, message = "المستخدم غير معروف" });

            DiscountsClass obj = new DiscountsClass();

            var result = obj.insert(model, _context, userId.Value);
             return Ok(new { state = 7, data = result, url = "/ProviderDiscount/Index" });
        }

        /// <summary>
        /// عرض صفحة إدارة النقاط الخاصة بمقدم الخدمة.
        /// </summary>
        /// <returns>عرض صفحة النقاط.</returns>
        public async Task<IActionResult> Points()
        {

            return View();
        }

    }
}