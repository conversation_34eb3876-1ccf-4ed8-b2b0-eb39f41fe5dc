﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    public class WebsiteModel
    {
        [Required]
        //[Display(Name = "الرؤية")]
        public string? vision { get; set; }
        [Required]
        //[Display(Name = "الرؤية")]
        public string? envision { get; set; }
        [Required]
        //[Display(Name = "التعريف")]
        public string? Defnition { get; set; } 
        [Required]
        //[Display(Name = "التعريف")]
        public string? enDefnition { get; set; }

        [Required]
        //[Display(Name = "التعريف")]
        public string? overview { get; set; }
        [Required]
        //[Display(Name = "التعريف")]
        public string? enoverview { get; set; }
         [Required]
        //[Display(Name = "التعريف")]
        public string? message { get; set; }
        [Required]
        //[Display(Name = "التعريف")]
        public string? enmessage { get; set; }

        //[Display(Name = "عدد العملاء")]
        public string? CustomersNo { get; set; }
  
        //[Display(Name = "عدد المدن")]
        public string? CitiesNo { get; set; }
        //[Display(Name = "مقدم خدمة")]
        public string? ProvidersNo { get; set; }
        
        //[Display(Name = "رابط فيسبوك")]
        public string? FaceookLink { get; set; }

         //[Display(Name = " رابط يوتيوب")]
        public string? YouTubeLink { get; set; }

         //[Display(Name = " رابط انستقرام")]
        public string? InstagramLink { get; set; }

         //[Display(Name = "رابط اكس")]
        public string? XLink { get; set; }
        public string? tiktok { get; set; }
        public string? whatsappLink { get; set; }
        public string? snapchatLink { get; set; }



        //[Display(Name = "الخبرات")]
        public string? Experts { get; set; }
        //[Display(Name = "رابط التطبيق قوقل ")]
        public string? AppGoogleLink { get; set; }
        //[Display(Name = "رابط التطبيق ابل")]
        public string? AppAppleLink { get; set; }
        //[Display(Name = "الهاتف")]
        public string? PhoneNo { get; set; }
        //[Display(Name = "الايميل")]
        public string? Email { get; set; }
        //[Display(Name = "العنوان")]
        public string? Place { get; set; }
        public List<WebsiteFAQSModel>? FAQSList { get; set; }
        public List<ArticlesModel>? ArticlesList { get; set; }
        public WebsiteFAQSModel? FAQS { get; set; }
        public ArticlesModel? Article { get; set; }
        public List<Models.ContactU>? ContactU { get; set; }
        public List<Models.MonyBoxsIcon>? Icons { get; set; }
        public MonyBoxIconsModel? Iconsob { get; set; }
        public WaletsConfig? waletsconfig { get; set; }

    }
    public class WaletsConfig
    {
        public double MaxLimit { get; set; }
        public double MinLimit { get; set; }

    }
    public class WebsiteFAQSModel {
        public int? Id { get; set; }
        [Required]
        //[Display(Name = "السؤال")]
        public string? Question { get; set; } 
        [Required]
        //[Display(Name = "السؤال")]
        public string? EnQuestion { get; set; }
        [Required]
        //[Display(Name = "اجابته")]
        public string? Details { get; set; }
        [Required]
        //[Display(Name = "اجابته")]
        public string? EnDetails { get; set; }
    }

    public class ArticlesModel
    {
        public int? Id { get; set; }
        [Required]
        //[Display(Name = "العنوان")]
        public string? title { get; set; }
        public string? entitle { get; set; }
        //[Required]
        //[Display(Name = "نبذة")]
        public string? link { get; set; }
        [Required]
        //[Display(Name = "المحتوى")]
        public string? Content { get; set; }
        [Required]
        //[Display(Name = "المحتوى")]
        public string? EnContent { get; set; }
        public IFormFile? Image { get; set; }
    }
}
