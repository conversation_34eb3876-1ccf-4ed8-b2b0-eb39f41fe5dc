﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class Package
{
    [Key]
    public int Id { get; set; }

    [StringLength(1500)]
    public string? Name { get; set; }

    [Column("characters")]
    public string? Characters { get; set; }

    public double? Price { get; set; }

    [StringLength(250)]
    public string? Period { get; set; }

    [StringLength(50)]
    public string? State { get; set; }

    [Column("isActive")]
    public bool? IsActive { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateOn { get; set; }

    [Column("masterId")]
    public long? MasterId { get; set; }

    public int? PackageDays { get; set; }

    [StringLength(1500)]
    public string? EnName { get; set; }

    public string? EnCharacters { get; set; }

    [StringLength(1500)]
    public string? EnPeriod { get; set; }

    public int MaxOffersAllowed { get; set; }

    public int MaxMonyBoxesAllowed { get; set; }

    [InverseProperty("Package")]
    public virtual ICollection<PackageFeature> PackageFeatures { get; set; } = new List<PackageFeature>();

    [InverseProperty("Pack")]
    public virtual ICollection<Subscription> Subscriptions { get; set; } = new List<Subscription>();
}
