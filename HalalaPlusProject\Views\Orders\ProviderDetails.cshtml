﻿@model HalalaPlusProject.CModels.RegisterProviderOrder
    @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = @localizer["orderdetails"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["orderdetails"]</h2>

<div>
    
    <div class="col-md-12">
             <div class="row">

     
             <div class="col-md-5">
                  <div class="form-group">
                    <label class="form-label">@localizer["orderno"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Id)</label>               
                    </div>
                  <div class="form-group">
                    <label class="form-label">@localizer["name"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Name)</label>               
                    </div>
                      <div class="form-group">
                    <label class="form-label">@localizer["phoneno"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.PhoneNumber)</label>               
                     </div>
                      <div class="form-group">
                    <label class="form-label"> @localizer["email"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Email)</label>               
                     </div>
                      
            </div>
               <div class="col-md-5">         
                       <div class="form-group">
                    <label class="form-label"> @localizer["serviceproviderrepresent"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.ServiceProviderRepresent)</label>               
                     </div>
                      <div class="form-group">
                    <label class="form-label">   @localizer["thecity"] </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.City)</label>               
                    </div>

                     <div class="form-group">
                    <label class="form-label">@localizer["activity"] </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Activity)</label>               
                     </div>
                     <div class="form-group">
                    <label class="form-label"> @localizer["date"] </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Date)</label>               
                     </div>
                    
            </div>
    </div>
    </div>

</div>
<div class="mt-2">
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>
