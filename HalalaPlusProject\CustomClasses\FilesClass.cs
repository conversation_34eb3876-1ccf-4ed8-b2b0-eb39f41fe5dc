﻿using HalalaPlusProject.CustomClasses;

namespace HalalaPlusProject.CustomClasses
{
    public class FilesClass
    {
        public bool insertOrdersFiles(List<IFormFile> files, long Id, string folder, IWebHostEnvironment _hosting, Models.HalalaPlusdbContext _dbContext)
        {
            try
            {
                //List<Models.FilesTable> allfile = new List<Models.FilesTable>();
                Models.FilesTable file = new Models.FilesTable();
                foreach (IFormFile f in files)
                {
                    file = new Models.FilesTable();
                    string oldname = f.FileName;
                    string NewName = HandleImages.SaveImage(f, folder, _hosting);
                    file.FileName = oldname;
                    file.FileLink = NewName;
                    file.UserId = Id;                    
                    _dbContext.FilesTables.Add(file);
                    _dbContext.SaveChanges();
                }
                //_dbContext.SaveChanges();
            }
            catch (Exception)
            {
                return false;
            }
            return true;
        }

        public string getFile(IWebHostEnvironment _hosting, Models.HalalaPlusdbContext _dbContext,long fileId) {
            
            string path = _dbContext.FilesTables.Find(fileId)?.FileLink;
            if (path != null && path.Length > 0) {
                string filePath = Path.Combine(_hosting.WebRootPath, path);

                byte[] imageBytes = File.ReadAllBytes(path);
                string base64String = Convert.ToBase64String(imageBytes);
                return base64String;
            }
            return null;
        }
        public bool insertOrdersFiles(List<IFormFile> files, long Id, string folder, IWebHostEnvironment _hosting, Models.HalalaPlusdbContext _dbContext, byte type)
        {
            try
            {
                //List<Models.FilesTable> allfile = new List<Models.FilesTable>();
                Models.FilesTable file = new Models.FilesTable();
                foreach (IFormFile f in files)
                {
                    file = new Models.FilesTable();
                    string oldname = f.FileName;
                    string NewName = HandleImages.SaveImage(f, folder, _hosting);
                    file.FileName = oldname;
                    file.FileLink = NewName;
                    file.UserId = Id;
                    file.FileType = type;
                    _dbContext.FilesTables.Add(file);
                    _dbContext.SaveChanges();
                }
                //_dbContext.SaveChanges();
            }
            catch (Exception)
            {
                return false;
            }
            return true;
        }

        public bool RemoveImage(long Id, string folder, IWebHostEnvironment _hosting, Models.HalalaPlusdbContext _dbContext) {
            var  path = _dbContext.FilesTables.Find(Id);
            if (path != null) {
                if (HandleImages.RemoveImage(path.FileLink, folder, _hosting))
                {
                    _dbContext.Remove(path);
                     _dbContext.SaveChanges();
                    return true;

                }
               
            }

            return false;

        }


        public bool insertOrdersFiles(List<IFormFile> files, int Id, string folder, IWebHostEnvironment _hosting, Models.HalalaPlusdbContext _dbContext,byte type)
        {


            try
            {
                //List<Models.FilesTable> allfile = new List<Models.FilesTable>();

                Models.FilesTable file = new Models.FilesTable();

                foreach (IFormFile f in files)
                {
                    file = new Models.FilesTable();
                    string oldname = f.FileName;
                    string NewName = HandleImages.SaveImage(f, folder, _hosting);
                    file.FileName = oldname;
                    file.FileLink = NewName;
                    
                    file.FileType = type;
                    if(type==0) file.ProviderId = Id;
                    else if (type == 1) file.TaskId = Id;

                    _dbContext.FilesTables.Add(file);
                    _dbContext.SaveChanges();
                }
                //_dbContext.SaveChanges();
            }
            catch (Exception)
            {

                return false;
            }
            return true;
        }


    }
}
