﻿@model HalalaPlusProject.Models.CountriesTable

@{
    ViewData["Title"] = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>تعديل</h1>

<h4>الدولة</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <div class="form-group">
                <label asp-for="Country" class="control-label">الدولة</label>
                <input asp-for="Country" class="form-control" />
                <span asp-validation-for="Country" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Nationality" class="control-label">الجنسية</label>
                <input asp-for="Nationality" class="form-control" />
                <span asp-validation-for="Nationality" class="text-danger"></span>
            </div>
            <div class="form-group">
                <button type="submit" value="Save" class="btn btn-primary" >حفظ</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">عودة للقائمة السابقة</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
