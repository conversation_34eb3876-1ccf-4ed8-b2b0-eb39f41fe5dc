﻿using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Razor.TagHelpers;
using System;

namespace HalalaPlusProject.TagHelpers
{
    [HtmlTargetElement("a", Attributes = "asp-controller, asp-action")]
    public class ActiveLinkTagHelper : TagHelper
    {
        [ViewContext]
        [HtmlAttributeNotBound]
        public ViewContext ViewContext { get; set; }

        [HtmlAttributeName("asp-controller")]
        public string Controller { get; set; }

        [HtmlAttributeName("asp-action")]
        public string Action { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            var currentController = ViewContext.RouteData.Values["controller"]?.ToString();
            var currentAction = ViewContext.RouteData.Values["action"]?.ToString();

            if (string.Equals(Controller, currentController, StringComparison.OrdinalIgnoreCase) &&
                string.Equals(Action, currentAction, StringComparison.OrdinalIgnoreCase))
            {
                var existingClass = output.Attributes.ContainsName("class")
                    ? output.Attributes["class"].Value.ToString()
                    : "";

                output.Attributes.SetAttribute("class", $"{existingClass} active");
            }
        }
    }   
}
