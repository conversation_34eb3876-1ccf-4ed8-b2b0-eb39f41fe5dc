/**
 * HalalaPlus Advanced Template - JavaScript
 * Enhanced functionality with modern libraries
 */

$(document).ready(function() {
    'use strict';

    // Initialize all components
    initializeComponents();
    initializeSidebar();
    initializeToastr();
    initializeDataTables();
    initializeDropzone();
    initializeAOS();
    initializeSweetAlert();
    bindEvents();
});

/**
 * Initialize all components
 */
function initializeComponents() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        dir: 'rtl',
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });

    // Smooth scrolling for anchor links
    $('a[href*="#"]').not('[href="#"]').not('[href="#0"]').click(function(event) {
        if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && 
            location.hostname == this.hostname) {
            var target = $(this.hash);
            target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
            if (target.length) {
                event.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 1000);
            }
        }
    });
}

/**
 * Initialize Sidebar functionality
 */
function initializeSidebar() {
    const iconNavbarSidenav = document.getElementById('iconNavbarSidenav');
    const iconSidenav = document.getElementById('iconSidenav');
    const sidenav = document.getElementById('sidenav-main');
    const body = document.getElementsByTagName('body')[0];
    const className = 'g-sidenav-pinned';

    // Toggle sidebar on mobile
    if (iconNavbarSidenav) {
        iconNavbarSidenav.addEventListener("click", toggleSidenav);
    }

    if (iconSidenav) {
        iconSidenav.addEventListener("click", toggleSidenav);
    }

    function toggleSidenav() {
        if (body.classList.contains(className)) {
            body.classList.remove(className);
            setTimeout(function() {
                sidenav.classList.remove('bg-white');
            }, 100);
            sidenav.classList.remove('bg-transparent');
        } else {
            body.classList.add(className);
            sidenav.classList.add('bg-white');
            sidenav.classList.remove('bg-transparent');
            if (iconSidenav) {
                iconSidenav.classList.remove('d-none');
            }
        }
    }

    // Close sidebar when clicking outside on mobile
    $(document).click(function(event) {
        if (window.innerWidth < 1200) {
            if (!$(event.target).closest('.sidenav, #iconNavbarSidenav').length) {
                if (body.classList.contains(className)) {
                    body.classList.remove(className);
                }
            }
        }
    });

    // Handle submenu toggles
    $('.nav-link[data-bs-toggle="collapse"]').click(function(e) {
        e.preventDefault();
        const target = $($(this).attr('data-bs-target'));
        const icon = $(this).find('.fa-chevron-down');
        
        if (target.hasClass('show')) {
            target.collapse('hide');
            icon.removeClass('rotate-180');
        } else {
            target.collapse('show');
            icon.addClass('rotate-180');
        }
    });
}

/**
 * Initialize Toastr notifications
 */
function initializeToastr() {
    toastr.options = {
        "closeButton": true,
        "debug": false,
        "newestOnTop": true,
        "progressBar": true,
        "positionClass": "toast-top-left",
        "preventDuplicates": false,
        "onclick": null,
        "showDuration": "300",
        "hideDuration": "1000",
        "timeOut": "5000",
        "extendedTimeOut": "1000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut",
        "rtl": true
    };
}

/**
 * Initialize DataTables
 */
function initializeDataTables() {
    if ($.fn.DataTable) {
        $('#projectsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json"
            },
            "responsive": true,
            "pageLength": 10,
            "order": [[0, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": -1 }
            ],
            "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
            "drawCallback": function() {
                // Reinitialize tooltips after table redraw
                $('[data-bs-toggle="tooltip"]').tooltip();
            }
        });
    }
}

/**
 * Initialize Dropzone
 */
function initializeDropzone() {
    if (typeof Dropzone !== 'undefined') {
        Dropzone.autoDiscover = false;
        
        const dropzoneElement = document.getElementById('dropzone-upload');
        if (dropzoneElement) {
            const myDropzone = new Dropzone("#dropzone-upload", {
                url: "/upload", // Change this to your upload endpoint
                maxFilesize: 10, // MB
                acceptedFiles: ".jpeg,.jpg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx",
                addRemoveLinks: true,
                dictDefaultMessage: "اسحب الملفات هنا أو انقر للرفع",
                dictRemoveFile: "حذف الملف",
                dictCancelUpload: "إلغاء الرفع",
                dictUploadCanceled: "تم إلغاء الرفع",
                dictInvalidFileType: "نوع الملف غير مدعوم",
                dictFileTooBig: "حجم الملف كبير جداً ({{filesize}}MB). الحد الأقصى: {{maxFilesize}}MB.",
                dictResponseError: "خطأ في الخادم: {{statusCode}}",
                
                init: function() {
                    this.on("success", function(file, response) {
                        showNotification('success', 'تم رفع الملف بنجاح', file.name);
                    });
                    
                    this.on("error", function(file, errorMessage) {
                        showNotification('error', 'خطأ في رفع الملف', errorMessage);
                    });
                    
                    this.on("addedfile", function(file) {
                        showNotification('info', 'تم إضافة الملف', file.name);
                    });
                }
            });
        }
    }
}

/**
 * Initialize AOS (Animate On Scroll)
 */
function initializeAOS() {
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            mirror: false,
            offset: 100
        });
    }
}

/**
 * Initialize SweetAlert2
 */
function initializeSweetAlert() {
    // Set default SweetAlert2 options
    if (typeof Swal !== 'undefined') {
        const swalWithBootstrapButtons = Swal.mixin({
            customClass: {
                confirmButton: 'btn btn-success me-2',
                cancelButton: 'btn btn-danger'
            },
            buttonsStyling: false,
            reverseButtons: true
        });
        
        // Make it globally available
        window.swalWithBootstrapButtons = swalWithBootstrapButtons;
    }
}

/**
 * Bind events
 */
function bindEvents() {
    // Demo notification buttons
    $(document).on('click', '.btn-demo-notification', function() {
        const type = $(this).data('type') || 'info';
        const title = $(this).data('title') || 'إشعار تجريبي';
        const message = $(this).data('message') || 'هذا إشعار تجريبي';
        showNotification(type, title, message);
    });

    // Demo SweetAlert buttons
    $(document).on('click', '.btn-demo-alert', function() {
        const type = $(this).data('type') || 'info';
        const title = $(this).data('title') || 'تنبيه تجريبي';
        const message = $(this).data('message') || 'هذا تنبيه تجريبي';
        showAlert(type, title, message);
    });

    // Search functionality
    $('#navbar input[type="text"]').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        if (searchTerm.length > 2) {
            // Implement search logic here
            console.log('البحث عن:', searchTerm);
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        const form = $(this);
        if (!validateForm(form)) {
            e.preventDefault();
            showNotification('error', 'خطأ في النموذج', 'يرجى التحقق من البيانات المدخلة');
        }
    });

    // Auto-hide alerts
    $('.alert').each(function() {
        const alert = $(this);
        if (alert.hasClass('alert-dismissible')) {
            setTimeout(function() {
                alert.fadeOut();
            }, 5000);
        }
    });

    // Loading states for buttons
    $(document).on('click', '.btn-loading', function() {
        const btn = $(this);
        const originalText = btn.html();
        
        btn.html('<span class="loading me-2"></span>جاري التحميل...');
        btn.prop('disabled', true);
        
        // Simulate loading (remove this in production)
        setTimeout(function() {
            btn.html(originalText);
            btn.prop('disabled', false);
        }, 2000);
    });
}

/**
 * Show notification using Toastr
 */
function showNotification(type, title, message) {
    if (typeof toastr !== 'undefined') {
        toastr[type](message, title);
    }
}

/**
 * Show alert using SweetAlert2
 */
function showAlert(type, title, message, callback) {
    if (typeof Swal !== 'undefined') {
        const config = {
            title: title,
            text: message,
            icon: type,
            confirmButtonText: 'موافق',
            cancelButtonText: 'إلغاء'
        };

        if (type === 'question') {
            config.showCancelButton = true;
            config.confirmButtonColor = '#cb0c9f';
            config.cancelButtonColor = '#ea0606';
        }

        Swal.fire(config).then((result) => {
            if (callback && typeof callback === 'function') {
                callback(result);
            }
        });
    }
}

/**
 * Validate form
 */
function validateForm(form) {
    let isValid = true;
    
    form.find('input[required], select[required], textarea[required]').each(function() {
        const field = $(this);
        const value = field.val().trim();
        
        if (!value) {
            field.addClass('is-invalid');
            isValid = false;
        } else {
            field.removeClass('is-invalid').addClass('is-valid');
        }
    });
    
    // Email validation
    form.find('input[type="email"]').each(function() {
        const field = $(this);
        const email = field.val().trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            field.addClass('is-invalid');
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * Format numbers with Arabic locale
 */
function formatNumber(number) {
    return new Intl.NumberFormat('ar-SA').format(number);
}

/**
 * Format currency
 */
function formatCurrency(amount, currency = 'SAR') {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

/**
 * Debounce function
 */
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction() {
        const context = this;
        const args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

/**
 * Utility functions
 */
const Utils = {
    // Show loading overlay
    showLoading: function() {
        if (!$('#loading-overlay').length) {
            $('body').append(`
                <div id="loading-overlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.5); z-index: 9999;">
                    <div class="text-center text-white">
                        <div class="loading mb-3" style="width: 3rem; height: 3rem; border-width: 4px;"></div>
                        <h5>جاري التحميل...</h5>
                    </div>
                </div>
            `);
        }
    },

    // Hide loading overlay
    hideLoading: function() {
        $('#loading-overlay').fadeOut(300, function() {
            $(this).remove();
        });
    },

    // Copy to clipboard
    copyToClipboard: function(text) {
        navigator.clipboard.writeText(text).then(function() {
            showNotification('success', 'تم النسخ', 'تم نسخ النص إلى الحافظة');
        }).catch(function() {
            showNotification('error', 'خطأ', 'فشل في نسخ النص');
        });
    },

    // Generate random ID
    generateId: function() {
        return Math.random().toString(36).substr(2, 9);
    },

    // Check if element is in viewport
    isInViewport: function(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
};

// Make Utils globally available
window.Utils = Utils;

// Handle window resize
$(window).resize(debounce(function() {
    // Reinitialize components that need resize handling
    if (typeof AOS !== 'undefined') {
        AOS.refresh();
    }
}, 250));

// Handle page visibility change
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        console.log('الصفحة مخفية');
    } else {
        console.log('الصفحة مرئية');
    }
});

// Export functions for global use
window.showNotification = showNotification;
window.showAlert = showAlert;
window.validateForm = validateForm;
window.formatNumber = formatNumber;
window.formatCurrency = formatCurrency;
