﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("WebhookResponse")]
public partial class WebhookResponse
{
    [Key]
    public long Id { get; set; }

    [Column("data")]
    public string? Data { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateAt { get; set; }

    public bool Processed { get; set; }
}
