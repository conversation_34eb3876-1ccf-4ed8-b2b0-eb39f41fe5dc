﻿@model IEnumerable<HalalaPlusProject.Models.SystemUser>
@{
    ViewData["Title"] = "اتفاقيات مقدمي الخدمات";
}

<h2 class="text-center mb-4">اتفاقيات مقدمي الخدمات</h2>

<table class="table table-bordered table-striped text-center">
    <thead>
        <tr>
            <th>الاسم</th>
            <th>النشاط</th>
            <th>المدينة</th>
            <th>رقم الجوال</th>
            <th>تحميل الاتفاقية</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var u in Model)
        {
            <tr>
                <td>@u.Name</td>
                <td>@u.Activity</td>
                <td>@u.City</td>
                <td>@u.PhoneNo</td>
                <td>
                    <a class="btn btn-success" target="_blank" asp-action="GeneratePdf" asp-route-id="@u.Id">تحميل PDF</a>
                </td>
            </tr>
        }
    </tbody>
</table>
