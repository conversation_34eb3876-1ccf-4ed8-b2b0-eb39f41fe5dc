﻿@model HalalaPlusProject.Models.RetriveMonyMonyView

@{
    Layout = null;  
}
<form asp-action="popup" enctype="multipart/form-data" class="submitfm">
    <input type="hidden" asp-for="Id" />

    <div class="form-group">
        <label>اسم الشخص</label>
        <input type="text" readonly value=" @Model.Name" class="form-control" />
    </div>

    <div class="form-group">
        <label>رقم العملية</label>
        <input asp-for="PaymentId" class="form-control" />
    </div>

    <div class="form-group">
        <label>صورة الايصال</label>
        <input name="recept" type="file" class="form-control" required />
    </div>

    <button type="submit" class="btn btn-success">حفظ</button>
</form>
<script>
$("form.submitfm").submit(function (e) {
    e.preventDefault();
    var formData = new FormData(this);

    $.ajax({
        url: $(this).attr("action"),
        type: "POST",
        data: formData,
        contentType: false,
        processData: false,
        success: function (data) {
            if (data.state === 7) {
                Swal.fire("نجاح", data.message, "success").then(() => {
                    $("#acceptModal").modal("hide");
                    window.location.reload();
                });
            } else {
                Swal.fire("خطأ", data.message, "error");
            }
        }
    });
});
</script>