﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class Subscription
{
    [Key]
    public long Id { get; set; }

    public long? UserId { get; set; }

    public int? PackId { get; set; }

    [Column("isSubscribed")]
    public bool? IsSubscribed { get; set; }

    [Column("state")]
    [StringLength(500)]
    public string? State { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? StartDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? EndDate { get; set; }

    [Column("price")]
    public double? Price { get; set; }

    [StringLength(250)]
    public string? ProcessNo { get; set; }

    [StringLength(100)]
    public string? PaymentId { get; set; }

    [ForeignKey("PackId")]
    [InverseProperty("Subscriptions")]
    public virtual Package? Pack { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("Subscriptions")]
    public virtual SystemUser? User { get; set; }
}
