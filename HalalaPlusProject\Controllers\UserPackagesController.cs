﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;
using HalalaPlusProject.CModels;
using HalalaPlusProject.Models; 

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// متحكم لإدارة باقات المستخدمين، بما في ذلك الإنشاء، العرض، التعديل، والحذف.
    /// </summary>
    [Authorize]
    public class UserPackagesController : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public UserPackagesController(HalalaPlusdbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// يعرض قائمة بجميع الباقات المتاحة.
        /// </summary>
        public async Task<IActionResult> Index()
        {
            return View(await _context.Packages.AsNoTracking().ToListAsync());
        }

        /// <summary>
        /// يعرض تفاصيل باقة محددة بناءً على المعرّف.
        /// </summary>
        /// <param name="id">معرّف الباقة.</param>
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null) return NotFound();

            var package = await _context.Packages
                .Include(p => p.PackageFeatures) // Eagerly load the features
                .AsNoTracking()
                .FirstOrDefaultAsync(m => m.Id == id);

            if (package == null) return NotFound();

            return View(package);
        }

        /// <summary>
        /// يعرض صفحة إنشاء باقة جديدة.
        /// </summary>
        public IActionResult Create()
        {
            return View(new PackageViewModel());
        }

        /// <summary>
        /// ينشئ باقة جديدة ويحفظها في قاعدة البيانات مع مميزاتها.
        /// </summary>
        /// <param name="viewModel">بيانات الباقة الجديدة.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(PackageViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                var package = new Package
                {
                    Name = viewModel.Name,
                    EnName = viewModel.EnName,
                    Characters = viewModel.Characters,
                    EnCharacters = viewModel.EnCharacters,
                    Price = viewModel.Price,
                    Period = viewModel.Period,
                    EnPeriod = viewModel.EnPeriod,
                    PackageDays = viewModel.PackageDays,
                    IsActive = viewModel.IsActive ?? true,
                    MaxMonyBoxesAllowed = viewModel.MaxMonyBoxesAllowed,
                    MaxOffersAllowed = viewModel.MaxOffersAllowed,
                    CreateOn = DateTime.Now
                };

                // Add the strongly-typed features with specific keys
                package.PackageFeatures.Add(new PackageFeature { FeatureKey = "MaxOffersAllowed", FeatureValue = viewModel.MaxOffersAllowed.ToString() });
                package.PackageFeatures.Add(new PackageFeature { FeatureKey = "MaxMonyBoxesAllowed", FeatureValue = viewModel.MaxMonyBoxesAllowed.ToString() });

                // Add any other dynamic features from the list
                if (viewModel.Features != null)
                {
                    foreach (var feature in viewModel.Features.Where(f => !string.IsNullOrWhiteSpace(f.Key) && !string.IsNullOrWhiteSpace(f.Value)))
                    {
                        package.PackageFeatures.Add(new PackageFeature
                        {
                            FeatureKey = feature.Key.Trim(),
                            FeatureValue = feature.Value.Trim()
                        });
                    }
                }

                _context.Add(package);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(viewModel);
        }

        /// <summary>
        /// يعرض صفحة تعديل باقة موجودة.
        /// </summary>
        /// <param name="id">معرّف الباقة المراد تعديلها.</param>
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null) return NotFound();

            var package = await _context.Packages
                .Include(p => p.PackageFeatures)
                .AsNoTracking()
                .FirstOrDefaultAsync(p => p.Id == id);

            if (package == null) return NotFound();

            // Map the entity to the view model for the edit form
            var viewModel = new PackageViewModel
            {
                Id = package.Id,
                Name = package.Name,
                EnName = package.EnName,
                Characters = package.Characters,
                EnCharacters = package.EnCharacters,
                Price = package.Price,
                Period = package.Period,
                EnPeriod = package.EnPeriod,
                PackageDays = package.PackageDays,
                MaxOffersAllowed = package.MaxOffersAllowed,
                MaxMonyBoxesAllowed = package.MaxMonyBoxesAllowed,
                IsActive = package.IsActive
            };

            // Find the specific features and populate the strongly-typed properties
            var offersFeature = package.PackageFeatures.FirstOrDefault(f => f.FeatureKey == "MaxOffersAllowed");
            if (offersFeature != null && int.TryParse(offersFeature.FeatureValue, out int maxOffers))
            {
                viewModel.MaxOffersAllowed = maxOffers;
            }

            var boxesFeature = package.PackageFeatures.FirstOrDefault(f => f.FeatureKey == "MaxMonyBoxesAllowed");
            if (boxesFeature != null && int.TryParse(boxesFeature.FeatureValue, out int maxBoxes))
            {
                viewModel.MaxMonyBoxesAllowed = maxBoxes;
            }

            // Populate the list with any *other* dynamic features
            viewModel.Features = package.PackageFeatures
                .Where(f => f.FeatureKey != "MaxOffersAllowed" && f.FeatureKey != "MaxMonyBoxesAllowed")
                .Select(f => new PackageFeatureViewModel { Key = f.FeatureKey, Value = f.FeatureValue })
                .ToList();

            return View(viewModel);
        }

        /// <summary>
        /// يحفظ التعديلات على باقة موجودة في قاعدة البيانات.
        /// </summary>
        /// <param name="id">معرّف الباقة المستهدفة.</param>
        /// <param name="viewModel">بيانات الباقة المحدثة.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, PackageViewModel viewModel)
        {
            if (id != viewModel.Id) return NotFound();

            if (ModelState.IsValid)
            {
                var packageToUpdate = await _context.Packages
                    .Include(p => p.PackageFeatures)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (packageToUpdate == null) return NotFound();

                // Update the main properties
                packageToUpdate.Name = viewModel.Name;
                packageToUpdate.EnName = viewModel.EnName;
                packageToUpdate.Characters = viewModel.Characters;
                packageToUpdate.EnCharacters = viewModel.EnCharacters;
                packageToUpdate.Price = viewModel.Price;
                packageToUpdate.Period = viewModel.Period;
                packageToUpdate.EnPeriod = viewModel.EnPeriod;
                packageToUpdate.PackageDays = viewModel.PackageDays;
                packageToUpdate.MaxMonyBoxesAllowed = viewModel.MaxMonyBoxesAllowed;
                packageToUpdate.MaxOffersAllowed = viewModel.MaxOffersAllowed;
                packageToUpdate.IsActive = viewModel.IsActive;

                // Simple way to update features: remove old ones, add new ones
                _context.PackageFeatures.RemoveRange(packageToUpdate.PackageFeatures);

                // Add the strongly-typed features
                packageToUpdate.PackageFeatures.Add(new PackageFeature { FeatureKey = "MaxOffersAllowed", FeatureValue = viewModel.MaxOffersAllowed.ToString() });
                packageToUpdate.PackageFeatures.Add(new PackageFeature { FeatureKey = "MaxMonyBoxesAllowed", FeatureValue = viewModel.MaxMonyBoxesAllowed.ToString() });

                // Add any other dynamic features
                if (viewModel.Features != null)
                {
                    foreach (var feature in viewModel.Features.Where(f => !string.IsNullOrWhiteSpace(f.Key) && !string.IsNullOrWhiteSpace(f.Value)))
                    {
                        packageToUpdate.PackageFeatures.Add(new PackageFeature
                        {
                            FeatureKey = feature.Key.Trim(),
                            FeatureValue = feature.Value.Trim()
                        });
                    }
                }

                try
                {
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PackageExists(packageToUpdate.Id)) return NotFound();
                    else throw;
                }
                return RedirectToAction(nameof(Index));
            }
            return View(viewModel);
        }

        /// <summary>
        /// يعرض صفحة تأكيد حذف (تعطيل) الباقة.
        /// </summary>
        /// <param name="id">معرّف الباقة المراد حذفها.</param>
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null) return NotFound();
            var package = await _context.Packages.FirstOrDefaultAsync(m => m.Id == id);
            if (package == null) return NotFound();
            return View(package);
        }

        /// <summary>
        /// يقوم بتعطيل الباقة (حذف ناعم) بدلاً من حذفها بشكل نهائي.
        /// </summary>
        /// <param name="id">معرّف الباقة المطلوب تعطيلها.</param>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var package = await _context.Packages.FindAsync(id);
            if (package != null)
            {
                package.IsActive = false;
                _context.Update(package);
                await _context.SaveChangesAsync();
            }
            return RedirectToAction(nameof(Index));
        }

        private bool PackageExists(int id)
        {
            return _context.Packages.Any(e => e.Id == id);
        }
    }
}