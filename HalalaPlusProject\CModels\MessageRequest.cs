﻿using System;
using System.ComponentModel.DataAnnotations;
using Google.Cloud.Firestore;

namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل طلب إرسال رسالة إلى جهاز معين باستخدام رمز الجهاز.
    /// </summary>
    public class MessageRequest
    {
        /// <summary>
        /// عنوان الرسالة.
        /// </summary>
        [Required(ErrorMessage = "العنوان مطلوب")]
        [Display(Name = "العنوان")]
        public string Title { get; set; }

        /// <summary>
        /// نص الرسالة.
        /// </summary>
        [Required(ErrorMessage = "النص مطلوب")]
        [Display(Name = "النص")]
        public string Body { get; set; }

        /// <summary>
        /// رمز الجهاز المستهدف.
        /// </summary>
        [Required(ErrorMessage = "رمز الجهاز مطلوب")]
        [Display(Name = "رمز الجهاز")]
        public string DeviceToken { get; set; }
    }

    /// <summary>
    /// يمثل طلب إرسال إشعار عام بدون تحديد جهاز.
    /// </summary>
    public class NotificationRequest
    {
        /// <summary>
        /// عنوان الإشعار.
        /// </summary>
        [Required(ErrorMessage = "العنوان مطلوب")]
        [Display(Name = "العنوان")]
        public string Title { get; set; }

        /// <summary>
        /// نص الإشعار.
        /// </summary>
        [Required(ErrorMessage = "النص مطلوب")]
        [Display(Name = "النص")]
        public string Body { get; set; }
    }
}
