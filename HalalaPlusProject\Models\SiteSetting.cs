﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class SiteSetting
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("appLinkApple")]
    [StringLength(500)]
    public string? AppLinkApple { get; set; }

    [Column("appLinkGoogle")]
    [StringLength(500)]
    public string? AppLinkGoogle { get; set; }

    [Column("vision")]
    public string? Vision { get; set; }

    public string? Defnition { get; set; }

    [Column("phone")]
    [StringLength(50)]
    public string? Phone { get; set; }

    [StringLength(50)]
    public string? Email { get; set; }

    [Column("place")]
    [StringLength(50)]
    public string? Place { get; set; }

    [StringLength(50)]
    public string? Experts { get; set; }

    [StringLength(20)]
    public string? Customers { get; set; }

    [StringLength(20)]
    public string? Providers { get; set; }

    [StringLength(20)]
    public string? Cities { get; set; }

    [Column("facebookLink")]
    [StringLength(500)]
    public string? FacebookLink { get; set; }

    [Column("instagramLink")]
    [StringLength(500)]
    public string? InstagramLink { get; set; }

    [Column("youtubeLink")]
    [StringLength(500)]
    public string? YoutubeLink { get; set; }

    [Column("twitterLink")]
    [StringLength(500)]
    public string? TwitterLink { get; set; }

    public string? PrivacyPolicy { get; set; }

    public string? TermsOfUse { get; set; }

    public string? Envision { get; set; }

    public string? EnDefnition { get; set; }

    public string? EnPlace { get; set; }

    public string? OverView { get; set; }

    public string? EnOverView { get; set; }

    [Column("ourMessage")]
    public string? OurMessage { get; set; }

    [Column("ourEnMessage")]
    public string? OurEnMessage { get; set; }

    [Column("whatsappLink")]
    [StringLength(100)]
    public string? WhatsappLink { get; set; }

    [Column("snapchatLink")]
    [StringLength(100)]
    public string? SnapchatLink { get; set; }

    [StringLength(100)]
    public string? TikTokLink { get; set; }

    public string? BusinessFeatures { get; set; }

    public string? EnBusinessFeatures { get; set; }
}
