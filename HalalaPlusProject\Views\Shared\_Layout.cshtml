﻿@using HalalaPlusProject.Areas.Identity.Data
@using Microsoft.AspNetCore.Identity
@inject SignInManager<HalalaPlusProjectUser> SignInManager
@inject UserManager<HalalaPlusProjectUser> UserManager
@using System.Globalization
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer

@{
    var isRTL = CultureInfo.CurrentCulture.Name.StartsWith("ar");
}
<!DOCTYPE html>
<html lang="en" dir="@(isRTL ? "RTL" : "LTR")" style="overflow-x: hidden;">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - HalalaPlus</title>
    @*<link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />*@
    @*<link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />*@
    @*<link rel="stylesheet" href="~/HalalaPlusProjectstyles.css" asp-append-version="true" />*@
    <!--     Fonts and icons     -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />
    <!-- Nucleo Icons -->
    <link href="~/assets/css/nucleo-icons.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/selectize.js/0.12.6/css/selectize.bootstrap3.min.css" integrity="sha256-ze/OEYGcFbPRmvCnrSeKbRTtjG4vGLHXgOqsyLFTRjg=" crossorigin="anonymous" />



    <link href="~/css/jquery-ui.css" rel="stylesheet" />
    <!-- Font Awesome Icons -->
    <link href="~/assets/css/nucleo-svg.css" rel="stylesheet" />
    <!-- CSS Files -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">

    @if (isRTL)
    {
        <link id="pagestyle" href="~/assets/css/soft-ui-dashboard.css?v=1.0.3" rel="stylesheet" asp-append-version="true" />
    }
    else
    {
        <link id="pagestyle" href="~/assets/css/soft-ui-dashboardltr.css?v=1.0.3" rel="stylesheet" asp-append-version="true" />
    }
    <link href="~/css/bootstrap-icons.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
    <link href="~/css/site.css" rel="stylesheet" />
    <link href="~/css/dataTables.min.css" rel="stylesheet" />
    <link href="~/css/sweetalert2.css" rel="stylesheet">
   
    <script src="~/js/sweetalert2.js"></script>
    <style>
        /*image logo start*/

        .mainInfo {
            background-color: #fff;
            border: .01rem round #e0e1ed;
            border-radius: 20px;
            color: #585CA1;
            width: 100%;
            height: 5em;
            box-shadow: 0px 0px 17px -5px rgba(0,0,0,0.75);
            margin-top: 3em;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .circle {
            position: relative;
            width: 8rem;
            height: 8rem;
            background: #fff;
            border-radius: 10%;
            box-shadow: 0px 0px 11px -5px rgba(0,0,0,0.65);
        }

            /*   .circle:before {
                        position: absolute;
                        content: "";
                        width: 15em;
                        height: 5em;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: #fff;
                    } */

            .circle img {
                position: absolute;
                max-width: 85%;
                border-radius: 10%;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 200;
            }

        .btnicons {
            width: auto !important;
            padding-right: 15px !important;
            padding-left: 15px !important;
        }
        .navbar-vertical .navbar-nav .nav-link > i{
            min-width: 1.3rem;
        }

        .navbar-vertical.navbar-expand-xs .navbar-nav .nav-link{
            font-size  : 18px;
        }

        .navbar-vertical .navbar-nav .nav-link{
            padding-left: 5px;
            padding-right: 5px;
        }
        /*image logo end*/
    </style>

    @*     <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" /> *@
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    @*  i can put it in the body later   <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"> *@
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">

</head>
<body class="g-sidenav-show bg-gray-100  @(isRTL ? "rtl" : "")">
    @*<header>*@
    <aside style="overflow: hidden;" class="sidenav navbar navbar-vertical navbar-expand-xs border-0 border-radius-xl my-3 fixed-end me-1 rotate-caret"
           id="sidenav-main">
        <div class="sidenav-header text-center mt-5 mb-4">
             @if (SignInManager.IsSignedIn(User) && User.IsInRole("Provider")){
<a title="الانتقال الى لوحة المعلومات - Dashboard" href="/Provider/Index">
                <img src="/assets/img/logo.png" style="max-height: 6rem;" class="navbar-brand-img h-900" alt="main_logo">
            </a>
             }
             else
            {
                 <a title="الانتقال الى لوحة المعلومات - Dashboard" href="/Home/Index">
                <img src="/assets/img/logo.png" style="max-height: 6rem;" class="navbar-brand-img h-900" alt="main_logo">
            </a>
            }
           
        </div>
        
        <div class="collapse navbar-collapse px-0 w-auto  max-height-vh-100 " style="overflow:auto;" id="sidenav-collapse-main">
            <ul class="navbar-nav">

                @if (SignInManager.IsSignedIn(User) && User.IsInRole("Admin"))
                {
                    @* <li class="nav-item">
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="CardReports" asp-action="Index">
                            <i class="bi bi-people me-2"></i>
                            <span class="nav-link-text me-1">CardReports</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="MyXlsxFiles" asp-action="Index">
                            <i class="bi bi-people me-2"></i>
                            <span class="nav-link-text me-1">رفع مقدمي الخدمات</span>
                        </a>
                    </li> *@
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Employees" asp-action="Index">
                            <i class="bi bi-people me-2"></i>
                            <span class="nav-link-text me-1">@localizer["theemployees"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Business" asp-action="Index">
                            <i class="bi bi-briefcase me-2"></i>
                            <span class="nav-link-text me-1">@localizer["Business"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Events" asp-action="Index">
                            <i class="bi bi-briefcase me-2"></i>
                            <span class="nav-link-text me-1">الفعاليات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Activities" asp-action="Index">
                            <i class="bi bi-check2-square me-2"></i>
                            <span class="nav-link-text me-1">@localizer["activities"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Tasks" asp-action="Index">
                            <i class="bi bi-list-task me-2"></i>
                            <span class="nav-link-text me-1">@localizer["tasks"]</span>
                        </a>
                    </li >
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="PaymentsRequests" asp-action="Index">
                            <i class="bi bi-list-task me-2"></i>
                            <span class="nav-link-text me-1">@localizer["PaymentsRequests"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Transcations" asp-action="Index">
                            <i class="bi bi-cash-coin me-2"></i>
                            <span class="nav-link-text me-1">@localizer["fininacalTranscations"]</span>
                        </a>
                    </li> 
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="ReplacePoints" asp-action="Index1">
                            <i class="bi bi-cash-coin me-2"></i>
                            <span class="nav-link-text me-1">طلبات استبدال النقاط</span>
                        </a>
                    </li> 
                    
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="GarentedDiscounts" asp-action="Report">
                            <i class="bi bi-cash-coin me-2"></i>
                            <span class="nav-link-text me-1">@localizer["GrantedTranscations"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Marketers" asp-action="Index">
                            <i class="bi bi-graph-up me-2"></i>
                            <span class="nav-link-text me-1">@localizer["markters"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="ServiceProvider" asp-action="Index">
                            <i class="bi bi-person-workspace me-2"></i>
                            <span class="nav-link-text me-1">@localizer["serviceproviders"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Investors" asp-action="Index">
                            <i class="bi bi-piggy-bank me-2"></i>
                            <span class="nav-link-text me-1">@localizer["investmentround"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="SpecialOffers" asp-action="Index">
                            <i class="bi bi-gift me-2"></i>
                            <span class="nav-link-text me-1">@localizer["offers"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="UserPackages" asp-action="Index">
                            <i class="bi bi-box-seam me-2"></i>
                            <span class="nav-link-text me-1">@localizer["packages"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="SystemUsers" asp-action="Index">
                            <i class="bi bi-people-fill me-2"></i>
                            <span class="nav-link-text me-1">@localizer["ClientsCollections"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Subscriptions" asp-action="Index">
                            <i class="bi bi-journal-check me-2"></i>
                            <span class="nav-link-text me-1">@localizer["subscriptions"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="GarentedDiscounts" asp-action="Index">
                            <i class="bi bi-percent me-2"></i>
                            <span class="nav-link-text me-1">@localizer["Customersavingspoints"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
    <a class="nav-link collapsed" href="#reportsSubmenu" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="reportsSubmenu">
        <i class="bi bi-bar-chart-line me-2"></i>
        <span class="nav-link-text me-1">@localizer["Reports"]</span>
        <i class="bi bi-chevron-down ms-auto"></i>
    </a>
    <div class="collapse" id="reportsSubmenu">
        <ul class="nav flex-column ms-3">
            <li class="nav-item">
                <a class="nav-link" asp-controller="Reports" asp-action="SystemPerformance">
                      @localizer["overallperformance"]
                </a>
            </li>
           <li class="nav-item">
                <a class="nav-link" asp-controller="Reports" asp-action="TopCustomersReport">
                     @localizer["MostEngagedCustomers"]
                </a>
            </li>  <li class="nav-item">
                 <a class="nav-link" asp-controller="Reports" asp-action="TopProvidersReport">
                     @localizer["MostEngagedDistributors"]
                </a>
            </li>
             <li class="nav-item">
                 <a class="nav-link" asp-controller="Reports" asp-action="SubscriptionsOverview">
                      @localizer["Recurringsubscriptions"]
                </a>
            </li>
             <li class="nav-item">
                 <a class="nav-link" asp-controller="Reports" asp-action="MonthlyGrowth">
                        @localizer["Monthlyrevenue"]
                </a>
            </li>
              <li class="nav-item">
                 <a class="nav-link" asp-controller="GarentedDiscounts" asp-action="Report">
                        @localizer["GrantingpointsReport"]
                </a>
            </li>
        </ul>
    </div>
</li>

                    
                  @*   <li class="nav-item">
                        <a class="nav-link" asp-controller="MonyBoxsPoints" asp-action="Index">
                            <i class="bi bi-currency-dollar me-2"></i>
                            <span class="nav-link-text me-1">اعدادات نقاط التوفير</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="WaletsConfigurations" asp-action="Index">
                            <i class="bi bi-stars me-2"></i>
                            <span class="nav-link-text me-1">اعدادات النقاط الترحيبية</span>
                        </a>
                    </li> *@
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="CustomerRewardSettings" asp-action="Index">
                            <i class="bi bi-stars me-2"></i>
                            <span class="nav-link-text me-1">@localizer["SettingsHalaCoinz"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Copun" asp-action="Index">
                            <i class="bi bi-ticket-perforated me-2"></i>
                            <span class="nav-link-text me-1">@localizer["coupons"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Countries" asp-action="Index">
                            <i class="bi bi-geo-alt me-2"></i>
                            <span class="nav-link-text me-1">@localizer["countriescities"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Agents" asp-action="Index">
                            <i class="bi bi-person-badge me-2"></i>
                            <span class="nav-link-text me-1">@localizer["customers"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Orders" asp-action="Index">
                            <i class="bi bi-truck me-2"></i>
                            <span class="nav-link-text me-1">@localizer["orderstracking"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="BanksAccounts" asp-action="Index">
                            <i class="bi bi-bank me-2"></i>
                            <span class="nav-link-text me-1">@localizer["bankaccount"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="PayingCompanies" asp-action="Index">
                            <i class="bi bi-credit-card-2-front me-2"></i>
                            <span class="nav-link-text me-1">@localizer["payingcom"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="CardOrder" asp-action="Index">
                            <i class="bi bi-person-vcard me-2"></i>
                            <span class="nav-link-text me-1">@localizer["waitinglist"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Categories" asp-action="Index">
                            <i class="bi bi-tags me-2"></i>
                            <span class="nav-link-text me-1">@localizer["cates"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Products" asp-action="Index">
                            <i class="bi bi-box2 me-2"></i>
                            <span class="nav-link-text me-1">@localizer["products"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Teams" asp-action="Index">
                            <i class="bi bi-people me-2"></i>
                            <span class="nav-link-text me-1">@(isRTL ? "الفريق" : "Team")</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Achievements" asp-action="Index">
                            <i class="bi bi-trophy me-2"></i>
                            <span class="nav-link-text me-1">@(isRTL ? "الانجازات" : "Achievements")</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="WebSite" asp-action="Index">
                            <i class="bi bi-globe me-2"></i>
                            <span class="nav-link-text me-1">@localizer["websitecontrol"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Message" asp-action="SendMessage">
                            <i class="bi bi-bell me-2"></i>
                            <span class="nav-link-text me-1">اشعارات فايربيس</span>
                        </a>
                    </li>
                } 
           
                @if (SignInManager.IsSignedIn(User) && User.IsInRole("Marketer"))
                {
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="MarketerUser" asp-action="Index">

                            <span class="nav-link-text me-1">@localizer["home"]</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link " asp-controller="MarketerUser" asp-action="CreateProvider">

                            <span class="nav-link-text me-1">@localizer["registerserviceprovider"]</span>
                        </a>
                    </li> 
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="MarketerUser" asp-action="Providers">

                            <span class="nav-link-text me-1">@localizer["serviceproviders"]</span>
                        </a>
                    </li>


                    <li class="nav-item">
                        <a class="nav-link " asp-controller="Marketers" asp-action="Customers">

                            <span class="nav-link-text me-1">@localizer["customerstracking"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="MarketerUser" asp-action="Finance">

                            <span class="nav-link-text me-1"> @localizer["financialtransactions"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="MarketerUser" asp-action="Tasks">

                            <span class="nav-link-text me-1">@localizer["taskstracking"]</span>
                        </a>
                    </li>
                }
              
                @if (SignInManager.IsSignedIn(User) && User.IsInRole("Employee"))
                {
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="EmployeeData" asp-action="Index">

                            <span class="nav-link-text me-1">@localizer["home"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="EmployeeData" asp-action="Create">

                            <span class="nav-link-text me-1">@localizer["addtasks"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="EmployeeData" asp-action="Tasks">

                            <span class="nav-link-text me-1">@localizer["taskstracking"]</span>
                        </a>
                    </li>
                    @* <li class="nav-item">
                         <a class="nav-link " asp-controller="EmployeeData" asp-action="Index">

                        <span class="nav-link-text me-1">الصلاحيات الممنوحة</span>
                      </a>
                    </li>*@
                }
            
                @if (SignInManager.IsSignedIn(User) && User.IsInRole("Provider"))
                {
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="ServiceAgreement" asp-action="IndexProvider">
                            <span class="nav-link-text me-1">اتفاقيات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="Provider" asp-action="Index">

                            <span class="nav-link-text me-1">@localizer["home"]</span>
                        </a>
                    </li>  <li class="nav-item">
                        <a class="nav-link " asp-controller="serviceProvider" asp-action="EditPersonalData">

                            <span class="nav-link-text me-1">@localizer["Editdata"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="serviceProvider" asp-action="ChangePassword">

                            <span class="nav-link-text me-1">@localizer["Changepassword"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="Employees" asp-action="Index">

                            <span class="nav-link-text me-1">@localizer["addemp"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="ProviderDiscount" asp-action="Index">

                            <span class="nav-link-text me-1">@localizer["addcoupons"]</span>
                        </a>
                    </li>

                    <li class="nav-item">

                        <a class="nav-link " asp-controller="Copun" asp-action="ProviderIndex">
                            <span class="nav-link-text me-1">@localizer["coupons"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="ProviderPoints" asp-action="Index">

                            <span class="nav-link-text me-1">@localizer["pointssettings"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link "  asp-controller="ChargeWalet" asp-action="Index">

                            <span class="nav-link-text me-1">@localizer["chargewalet"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="ReplacePoints" asp-action="Index">

                            <span class="nav-link-text me-1">@localizer["pointsreplaceorders"]</span>
                        </a>
                    </li>
                    @*    <li class="nav-item">
                        <a class="nav-link " href="../pages/consol.html">

                        <span class="nav-link-text me-1">إعدادات  النقاط</span>
                      </a>
                    </li>*@
                    <li class="nav-item">
                        <a class="nav-link "  asp-controller="ChargeWalet" asp-action="Index">

                            <span class="nav-link-text me-1">@localizer["returnremain"]</span>
                        </a>
                    </li>  <li class="nav-item">
                        <a class="nav-link " asp-controller="CustomDiscount" asp-action="DiscountsOrder">

                            <span class="nav-link-text me-1">@localizer["DiscountsOrder"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="CustomDiscount" asp-action="DiscountsOrder">

                            <span class="nav-link-text me-1">@localizer["grantdiscount"]</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="Statistics" asp-action="Index">

                            <span class="nav-link-text me-1">@localizer["statistics"]</span>
                        </a>
                    </li>

                }
                @if (SignInManager.IsSignedIn(User) && User.IsInRole("Customer"))
                {

                }
                @if (SignInManager.IsSignedIn(User) && User.IsInRole("Business"))
                {
                    <li class="nav-item">

                        <a class="nav-link " asp-controller="Business" asp-action="Index">

                            <span class="nav-link-text me-1">@localizer["Business"]</span>
                        </a>
                    </li>
                    <li class="nav-item">

                        <a class="nav-link " asp-controller="BusinessEmp" asp-action="Index">

                            <span class="nav-link-text me-1">الموظفين</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " asp-controller="ProviderDiscount" asp-action="Index">

                            <span class="nav-link-text me-1">@localizer["addcoupons"]</span>
                        </a>
                    </li>
                }

            </ul>

        </div>

    </aside>


    @*</header>*@
    @*<div class="container">*@
    <main class="main-content position-relative  h-100 mt-1 border-radius-lg overflow-hidden card" style="min-height: 750px;margin-bottom: 6rem;">
        <nav class="navbar navbar-main navbar-expand-lg px-0 mx-4 shadow-none border-radius-xl" id="navbarBlur"
             navbar-scroll="true" style="display: contents;">
            <div class="container-fluid py-1 px-3">

                <div class="collapse navbar-collapse mt-sm-0 mt-2 px-0" id="navbar">

                    <ul class="navbar-nav me-auto ms-0 justify-content-end">

                        <li class="nav-item d-xl-none pe-3 d-flex align-items-center">
                            <a href="javascript:;" class="nav-link text-body p-0" id="iconNavbarSidenav">
                                <div class="sidenav-toggler-inner">
                                    <i class="sidenav-toggler-line"></i>
                                    <i class="sidenav-toggler-line"></i>
                                    <i class="sidenav-toggler-line"></i>
                                </div>
                            </a>
                        </li>
                        <li class="nav-item"><partial name="_SelectLanguage" /> </li>
                       <li class="nav-item" title="تسجيل خروج">
    <form id="logoutForm" class="form-inline"
          asp-area="Identity"
          asp-page="/Account/Logout"
          asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })"
          onsubmit="return confirm('هل أنت متأكد من تسجيل الخروج؟');">
        <button id="logout" type="submit" class="nav-link btn btn-link">
            <img src="/img/Logout.png" />
        </button>
    </form>
</li>



                    </ul>
                </div>
            </div>
        </nav>
        <div class="container-fluid p-5" style="    padding-top: 0rem !important;">
            @RenderBody()
        </div>
    </main>
    @*</div>*@

    <footer class="footer pt-3  " style="bottom: 0; position: fixed; width: 100%;height: 71px; background: #fff;">
        <div class="container-fluid">
            <div class="row align-items-center justify-content-lg-between">
                <div class="col-lg-6 mb-lg-0 mb-4">
                    <div class="copyright text-center text-sm text-muted text-lg-start">

                        Made by
                        <a href="http://www.tkweentechno.com/">TAKWEEN</a>
                        For a better web.
                    </div>
                </div>
                @*   <div class="col-lg-6">
              <ul class="nav nav-footer justify-content-center justify-content-lg-end">
                <li class="nav-item">
                  <a href="https://www.creative-tim.com" class="nav-link text-muted" target="_blank">Creative Tim</a>
                </li>
                <li class="nav-item">
                  <a href="#" class="nav-link text-muted" target="_blank">About Us</a>
                </li>
                <li class="nav-item">
                  <a href="#" class="nav-link text-muted" target="_blank">Blog</a>
                </li>
                <li class="nav-item">
                  <a href="#" class="nav-link pe-0 text-muted" target="_blank">License</a>
                </li>
              </ul>
            </div> *@
            </div>
        </div>
    </footer>
    @* <script src="~/assets/js/core/popper.min.js"></script> *@
    <script src="~/assets/js/core/bootstrap.min.js"></script>
    @* <script src="~/assets/js/plugins/perfect-scrollbar.min.js"></script> *@
    @* <script src="~/assets/js/plugins/smooth-scrollbar.min.js"></script> *@
    @* <script src="~/assets/js/plugins/fullcalendar.min.js"></script> *@
    @* <script src="~/assets/js/plugins/chartjs.min.js"></script> *@
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/jquery-1.10.2.min.js"></script>
    <script src="~/js/jquery-ui.js"></script>
    @* <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script> *@

    @if (isRTL)
    {
        <script src="~/js/site.js" asp-append-version="true"></script>
    }
    else
    {
        <script src="~/js/ensite.js" asp-append-version="true"></script>
    }

    @* <script src="~/assets/js/plugins/choices.min.js"></script> *@
    @* <script src="https://polyfill.io/v3/polyfill.min.js?features=default"></script> *@



    @*<script src="~/js/map.js"></script>*@
    <!-- Github buttons -->
    @* <script async defer src="https://buttons.github.io/buttons.js"></script> *@
    <!-- Control Center for Soft Dashboard: parallax effects, scripts for the example pages etc -->
    <script src="~/assets/js/soft-ui-dashboard.min.js?v=1.0.3"></script>
    <script src="~/js/dataTables.min.js"></script>
    @* <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script> *@
    <script src="https://cdnjs.cloudflare.com/ajax/libs/selectize.js/0.12.6/js/standalone/selectize.min.js" integrity="sha256-+C0A5Ilqmu4QcSPxrlGpaZxJ04VjsRjKu+G82kl5UJk=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/localization/messages_ar.js"></script>

    <script>
        $(document).ready(function () {
            $('.customselect').selectize({
                sortField: 'text'
            });
        });

    </script>
    @await RenderSectionAsync("Scripts", required: false)
     @if (isRTL)
    {
        <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/localization/messages_ar.js"></script>
    }
</body>
</html>
