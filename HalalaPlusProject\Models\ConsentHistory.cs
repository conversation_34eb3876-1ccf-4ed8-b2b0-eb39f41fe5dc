﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("ConsentHistory")]
public partial class ConsentHistory
{
    [Key]
    public long Id { get; set; }

    public long? UserId { get; set; }

    public int? BankId { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateAt { get; set; }

    [StringLength(500)]
    public string? ConcentCaseType { get; set; }

    public string? ConcentId { get; set; }

    public bool? Authorised { get; set; }

    public bool? Revoked { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? RevokeDate { get; set; }

    [Column("transactionFromDateTime", TypeName = "datetime")]
    public DateTime? TransactionFromDateTime { get; set; }

    [Column("transactionToDateTime", TypeName = "datetime")]
    public DateTime? TransactionToDateTime { get; set; }

    [Column("expirationDateTime", TypeName = "datetime")]
    public DateTime? ExpirationDateTime { get; set; }

    [Column("status")]
    [StringLength(150)]
    public string? Status { get; set; }

    [Column("creationDateTime", TypeName = "datetime")]
    public DateTime? CreationDateTime { get; set; }

    [Column("statusUpdateDateTime", TypeName = "datetime")]
    public DateTime? StatusUpdateDateTime { get; set; }

    [ForeignKey("BankId")]
    [InverseProperty("ConsentHistories")]
    public virtual BanksAccount? Bank { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("ConsentHistories")]
    public virtual SystemUser? User { get; set; }

    [InverseProperty("Concent")]
    public virtual ICollection<UserBanksAccount> UserBanksAccounts { get; set; } = new List<UserBanksAccount>();
}
