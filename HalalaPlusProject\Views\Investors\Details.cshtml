﻿@model HalalaPlusProject.Models.InvestorsTable

@{
    ViewData["Title"] = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Details</h1>

<div>
    <h4>InvestorsTable</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.InvestorName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.InvestorName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.PhoneNumber)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.PhoneNumber)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Email)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Email)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.StocksNumber)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.StocksNumber)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.StocksValue)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.StocksValue)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.TransctionId)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.TransctionId)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.CreateAt)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CreateAt)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
