﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة الدول، وتوفير عمليات الإنشاء والقراءة والتحديث والحذف (CRUD).
    /// </summary>
    [Authorize]
    public class CountriesController : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public CountriesController(HalalaPlusdbContext context)
        {
            _context = context;
        }

        // GET: CountriesTables
        /// <summary>
        /// عرض قائمة بجميع الدول.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة الدول.</returns>
        public async Task<IActionResult> Index()
        {
            return _context.CountriesTables != null ?
                        View(await _context.CountriesTables.ToListAsync()) :
                        Problem("Entity set 'HalalaPlusdbContext.CountriesTables'  is null.");
        }

        // GET: CountriesTables/Details/5
        /// <summary>
        /// عرض التفاصيل الخاصة بدولة معينة.
        /// </summary>
        /// <param name="id">معرف الدولة المراد عرض تفاصيلها.</param>
        /// <returns>عرض يحتوي على تفاصيل الدولة، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null || _context.CountriesTables == null)
            {
                return NotFound();
            }

            var countriesTable = await _context.CountriesTables
                .FirstOrDefaultAsync(m => m.Id == id);
            if (countriesTable == null)
            {
                return NotFound();
            }

            return View(countriesTable);
        }

        // GET: CountriesTables/Create
        /// <summary>
        /// عرض نموذج إنشاء دولة جديدة.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج الإنشاء.</returns>
        public IActionResult Create()
        {
            return View();
        }

        // POST: CountriesTables/Create
        /// <summary>
        /// معالجة عملية إنشاء دولة جديدة وحفظها في قاعدة البيانات.
        /// </summary>
        /// <param name="countriesTable">كائن الدولة الذي يحتوي على البيانات الجديدة.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة الدول؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CountriesTable countriesTable)
        {
            if (ModelState.IsValid)
            {
                _context.Add(countriesTable);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(countriesTable);
        }

        // GET: CountriesTables/Edit/5
        /// <summary>
        /// عرض نموذج تعديل بيانات دولة حالية.
        /// </summary>
        /// <param name="id">معرف الدولة المراد تعديلها.</param>
        /// <returns>عرض يحتوي على بيانات الدولة في نموذج التعديل، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null || _context.CountriesTables == null)
            {
                return NotFound();
            }

            var countriesTable = await _context.CountriesTables.FindAsync(id);
            if (countriesTable == null)
            {
                return NotFound();
            }
            return View(countriesTable);
        }

        // POST: CountriesTables/Edit/5
        /// <summary>
        /// معالجة التعديلات المقدمة لدولة وحفظها في قاعدة البيانات.
        /// </summary>
        /// <param name="id">معرف الدولة التي يتم تعديلها.</param>
        /// <param name="countriesTable">كائن الدولة الذي يحتوي على البيانات المحدثة.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة الدول؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, CountriesTable countriesTable)
        {
            if (id != countriesTable.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(countriesTable);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CountriesTableExists(countriesTable.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(countriesTable);
        }

        // GET: CountriesTables/Delete/5
        /// <summary>
        /// عرض صفحة تأكيد حذف دولة.
        /// </summary>
        /// <param name="id">معرف الدولة المراد حذفها.</param>
        /// <returns>عرض يحتوي على تفاصيل الدولة لتأكيد الحذف، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null || _context.CountriesTables == null)
            {
                return NotFound();
            }

            var countriesTable = await _context.CountriesTables
                .FirstOrDefaultAsync(m => m.Id == id);
            if (countriesTable == null)
            {
                return NotFound();
            }

            return View(countriesTable);
        }

        // POST: CountriesTables/Delete/5
        /// <summary>
        /// تنفيذ عملية حذف الدولة من قاعدة البيانات بعد التأكيد.
        /// </summary>
        /// <param name="id">معرف الدولة المراد حذفها.</param>
        /// <returns>بعد إتمام العملية بنجاح، يتم عرض صفحة قائمة الدول.</returns>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            if (_context.CountriesTables == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.CountriesTables'  is null.");
            }
            var countriesTable = await _context.CountriesTables.FindAsync(id);
            if (countriesTable != null)
            {
                _context.CountriesTables.Remove(countriesTable);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// التحقق من وجود دولة في قاعدة البيانات بالمعرف المحدد.
        /// </summary>
        /// <param name="id">معرف الدولة للتحقق منه.</param>
        /// <returns>إرجاع 'true' إذا كانت الدولة موجودة، وإلا 'false'.</returns>
        private bool CountriesTableExists(int id)
        {
            return (_context.CountriesTables?.Any(e => e.Id == id)).GetValueOrDefault();
        }
    }
}