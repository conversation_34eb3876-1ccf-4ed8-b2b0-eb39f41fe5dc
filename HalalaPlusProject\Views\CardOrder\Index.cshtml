﻿@model IEnumerable<HalalaPlusProject.CModels.CardOrders>
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] =localizer["waitinglist"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<table id="tbl1" class="table">
    <thead>
        <tr>
            <th>
                @localizer["agentname"]
            </th>
            <th>
                @localizer["agentphone"]
            </th>
            <th>
                @localizer["memberno"]
            </th>
              <th>
               Date
            </th>
            <th>

            </th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.Name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.PhoneNo)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.MemberNo)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.JoinDate)
                </td>
                <td>
                    <button onclick="SendMessage('@item.Id')" class="btn btn-primary tablebtn">اعادة الارسال</button>
                </td>
               
            </tr>
        }
    </tbody>
</table>
@section Scripts {
    <script>
        let table = new DataTable('#tbl1');


         function SendMessage(id){

            var obj = new FormData();
            obj.append("Id", id);
            $.ajax({
                url: "/CardOrder/SendMessage",
                type: "POST",
                //dataType: "JSON",
                data: obj,
                processData: false,
                contentType: false,
                success: function (data) {
                    if (data.state == 1) {
                        Swal.fire({
                            title: 'نجـــاح',
                            type: 'success',
                            text: data.message,
                            confirmButtonText: "تم"
                        });

                    } else {
                        Swal.fire({
                            type: 'error',
                            title: 'خطاء...',
                            text: data.message,
                        })
                    }

                },
                error: function (xhr, desc, err) {
                    Swal.fire({
                        type: 'error',
                        title: 'خطاء...',
                        text: "هنالك خطاء تاكد من صحة البيانات",
                    })
                }
            });
                
                }
    </script>
}