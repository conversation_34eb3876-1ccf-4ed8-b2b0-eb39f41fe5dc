mapboxgl.accessToken =
  //"pk.eyJ1Ijoib3NhbWExOTk4IiwiYSI6ImNtZ2ZmMm9xZzA2eDMybXNjcmt1azFmMHMifQ.IvZPXry3tpG5AXyu7l9b3Q";
  "pk.eyJ1Ijoib3NhbWExOTk4IiwiYSI6ImNtOWk3eXd4MjBkbWcycHF2MDkxYmI3NjcifQ.2axcu5Sk9dx6GX3NtjjAvA";

var longitude = document.getElementById("map-longitude").value;
var latitude = document.getElementById("map-latitude").value;

if (longitude == "") {
  longitude = 46.6753;
}
if (latitude == "") {
  latitude = 24.7136;
}
// Setup Mapbox Location Handlers (General)
function setupMapboxLocationHandlers() {
  let mapInstance = null;
  let markerInstance = null;
  let geocoderInstance = null;

  // Initialize geocoder
  function initGeocoder() {
    if (geocoderInstance) {
      geocoderInstance.clear();
      document.getElementById("map-geocoder").innerHTML = "";
    }

    geocoderInstance = new MapboxGeocoder({
      accessToken: mapboxgl.accessToken,
      mapboxgl: mapboxgl,
      placeholder: "ابحث عن موقع...",
      language: "ar",
    });

    document
      .getElementById("map-geocoder")
      .appendChild(geocoderInstance.onAdd());

    geocoderInstance.on("result", function (e) {
      const coordinates = e.result.center;
      const address = e.result.place_name;

      document.getElementById("map-longitude").value = coordinates[0];
      document.getElementById("map-latitude").value = coordinates[1];

      if (mapInstance) {
        updateMapLocation(coordinates[1], coordinates[0]);
      }
    });
  }

  mapboxgl.setRTLTextPlugin(
    "https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-rtl-text/v0.2.3/mapbox-gl-rtl-text.js",
    null,
    true // تحميل فقط عند الحاجة (lazy load)
  );
  // Initialize map
  function initMap(lat = 24.7136, lng = 46.6753) {
    if (mapInstance) {
      mapInstance.remove();
    }

    mapInstance = new mapboxgl.Map({
      container: "container-map",
      style: "mapbox://styles/osama1998/cma8lcv6p00ha01s58rdb73zw",
      center: [lng, lat],
      zoom: 13,
    });

    markerInstance = new mapboxgl.Marker({ draggable: true })
      .setLngLat([lng, lat])
      .addTo(mapInstance);

    markerInstance.on("dragend", function () {
      const lngLat = markerInstance.getLngLat();
      document.getElementById("map-longitude").value = lngLat.lng;
      document.getElementById("map-latitude").value = lngLat.lat;

      reverseGeocode(lngLat.lng, lngLat.lat, "product-address");
    });

    mapInstance.on("click", function (e) {
      const coordinates = e.lngLat;
      markerInstance.setLngLat(coordinates);
      document.getElementById("map-longitude").value = coordinates.lng;
      document.getElementById("map-latitude").value = coordinates.lat;

      reverseGeocode(coordinates.lng, coordinates.lat, "product-address");
    });

    window.mapInstance = mapInstance;
  }

  setTimeout(() => {
    initMap(latitude, longitude);
    initGeocoder();
  }, 100);

  // Update map location
  function updateMapLocation(lat, lng) {
    if (mapInstance && markerInstance) {
      mapInstance.setCenter([lng, lat]);
      markerInstance.setLngLat([lng, lat]);
    }
  }

  // Reverse Geocode Helper
  function reverseGeocode(lng, lat, addressId) {
    fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${mapboxgl.accessToken}&language=ar`
    )
      .then((response) => response.json())
      .then((data) => {
        if (data.features && data.features.length > 0) {
          document.getElementById(addressId).value =
            data.features[0].place_name;
        }
      });
  }

  // Manual button click
  document
    .getElementById("map-manual-btn")
    ?.addEventListener("click", function () {
      document.getElementById("map-container").style.display = "block";
      document.getElementById("container-map").style.display = "block";

      setTimeout(() => {
        const lat =
          parseFloat(document.getElementById("map-latitude").value) || 24.7136;
        const lng =
          parseFloat(document.getElementById("map-longitude").value) || 46.6753;
        initMap(lat, lng);
        initGeocoder();
      }, 100);
    });

  // Current location button
  document
    .getElementById("map-getCurrentLocation")
    ?.addEventListener("click", function () {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function (position) {
          const lat = position.coords.latitude;
          const lng = position.coords.longitude;

          document.getElementById("map-latitude").value = lat;
          document.getElementById("map-longitude").value = lng;

          document.getElementById("map-container").style.display = "block";
          document.getElementById("container-map").style.display = "block";

          setTimeout(() => {
            initMap(lat, lng);
            initGeocoder();
          }, 100);

          reverseGeocode(lng, lat, "product-address");
        });
      }
    });

  // Toggle link input
  document
    .getElementById("map-toggle-link-input")
    ?.addEventListener("click", function () {
      const wrapper = document.getElementById("map-link-input-wrapper");
      if (wrapper.style.display === "none" || wrapper.style.display === "") {
        wrapper.style.display = "block";
      } else {
        wrapper.style.display = "none";
      }
    });

  // Parse map link
  document
    .getElementById("map-parse-link")
    ?.addEventListener("click", function () {
      const link = document.getElementById("map-map-link").value;
      const coordinates = extractCoordinatesFromLink(link);

      if (coordinates) {
        document.getElementById("map-latitude").value = coordinates.lat;
        document.getElementById("map-longitude").value = coordinates.lng;

        document.getElementById("map-container").style.display = "block";
        document.getElementById("container-map").style.display = "block";

        setTimeout(() => {
          initMap(coordinates.lat, coordinates.lng);
          initGeocoder();
        }, 100);

        reverseGeocode(coordinates.lng, coordinates.lat, "product-address");
      } else {
        alert("لم يتم العثور على إحداثيات صحيحة في الرابط");
      }
    });

  // Coordinate inputs change
  const latInput = document.getElementById("map-latitude");
  const lngInput = document.getElementById("map-longitude");

  function handleCoordinateChange() {
    const lat = parseFloat(latInput.value);
    const lng = parseFloat(lngInput.value);
    if (!isNaN(lat) && !isNaN(lng)) {
      updateMapLocation(lat, lng);
    }
  }

  latInput?.addEventListener("input", handleCoordinateChange);
  lngInput?.addEventListener("input", handleCoordinateChange);
}

// Extract coordinates from various map links
function extractCoordinatesFromLink(link) {
  const googlePatterns = [
    /@(-?\d+\.?\d*),(-?\d+\.?\d*)/,
    /q=(-?\d+\.?\d*),(-?\d+\.?\d*)/,
    /ll=(-?\d+\.?\d*),(-?\d+\.?\d*)/,
  ];

  for (let pattern of googlePatterns) {
    const match = link.match(pattern);
    if (match) {
      return { lat: parseFloat(match[1]), lng: parseFloat(match[2]) };
    }
  }

  return null;
}

// Initialize map handlers
document.addEventListener("DOMContentLoaded", setupMapboxLocationHandlers);
