﻿@model HalalaPlusProject.Models.BanksAccount
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] =localizer["details"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["details"]</h2>

<div class="col-md-12">
         <div class="row">

     
     <div class="col-md-5">
          <div class="form-group">
                <label class="form-label">@localizer["bankname"]</label>
                <label  class="form-control">@Model.BankName</label>               
            </div> <div class="form-group">
                <label class="form-label">@localizer["bankname"]</label>
                <label  class="form-control">@Model.EnBankName</label>               
            </div>
               <div class="form-group">
                <label class="form-label">@localizer["accountno"]</label>
                <label  class="form-control">@Model.AccountNumber</label>               
            </div>            
    </div>
       <div class="col-md-5">         
               <div class="form-group">
                <label class="form-label">@localizer["connectioncode"]</label>
                <label  class="form-control">@Model.ConnectionCode</label>               
             </div>
            <div class="form-group">
                <div class="row justify-content-center" style="margin-bottom:1px;">
                    <div class="circle ">
                        <img src="/img/@Model.Icon" id="bankicon">
                    </div>
                </div>

            </div>
    </div>

</div>
</div>
            
<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id">@localizer["edit"]</a> |
    <a asp-action="Index"> @localizer["backtolist"]</a>
</div>
