﻿@model HalalaPlusProject.Models.Product
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h3>@localizer["details"]</h3>
 
    <h4>Product</h4>
    <hr />
    <div class="col-md-12">
        <div class="row">


            <div class="col-md-5">

                <div class="form-group">
                    <label class="form-label">
            اسم المنتج
                    </label>
                    <label class="form-control">
            @Html.DisplayFor(model => model.Name)
                    </label>
                </div>
                <div class="form-group">
                    <label class="form-label">
            الوصف
                            </label>
                            <label class="form-control">
            @Html.DisplayFor(model => model.Description)
                    </label>
                </div>
                <div class="form-group">
                                <label class="form-label">
            صورة المنتج
                                    </label>
                                    <label class="form-control">
            <img src="~/imges/@Model.Image" alt="الصورة" style="width:150px; height:auto;" />
                                        </label>
                </div>
                <div class="form-group">
                                        <label class="form-label">
            اسم المنتج بالانجليزي
                                            </label>
                                            <label class="form-control">
            @Html.DisplayFor(model => model.Engname)
                    </label>
                </div>
                <div class="form-group">
                                                <label class="form-label">
            الوصف بالانجليزي
                                                    </label>
                                                    <label class="form-control">
            @Html.DisplayFor(model => model.Engdescription)
                    </label>
                </div>
                <div class="form-group">
                                                        <label class="form-label">
            @Html.DisplayNameFor(model => model.Active)
                                                            </label>
                                                            <label class="form-control">
            @if (Model.Active == true)
            {
                <span>فعال</span>
            }else
            {
                <span>غير فعال </span>
            }

                                                                </label>
                </div>
                <div class="form-group">
                                                                <label class="form-label">
            السعر
                                                                    </label>
                                                                    <label class="form-control">
            @Html.DisplayFor(model => model.Price)
                    </label>
                </div>
                <div class="form-group">
                                                                        <label class="form-label">
            الصنف
                                                                            </label>
                                                                            <label class="form-control">
            @Html.DisplayFor(model => model.CatagoryNavigation.Id)
                                                                                </label>
                                                                               
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id">@localizer["edit"]</a> |
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>

            </div>
        </div>
        </div>