﻿@model HalalaPlusProject.CModels.CoupunModelIndex
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["offers"];
}
<div class="row">
    <h3> @localizer["coupons"] </h3>
    <a asp-action="Create" style="float:left; margin-right: 10px; ">@localizer["addcoupon"] </a>
                </div>
<div class="tab">
    <button class="tablinks Active" onclick="openCity(event, 'OffersTab')">@localizer["currentcoupons"]</button>
    <button class="tablinks" onclick="openCity(event, 'stoppedOffersTab')">@localizer["expiredcoupons"]   </button>
  

</div>
<div id="OffersTab" class="tabcontent" style="display:block" >
<div class="row">
        <div class="col-12">
          <div class="card mb-4">
         
            <div class="card-body px-0 pt-0 pb-2">
              <div class="table-responsive p-0">
               
                <table id="tbl1"  class="table table-striped text-center">
                  <thead>
                    <tr>

                                    <th scope="col">@localizer["storename"]</th>
                                    <th scope="col">@localizer["storedetails"]</th>
                                    <th scope="col">@localizer["couponstart"]</th>
                                    <th scope="col">@localizer["couponends"] </th>
                                    <th scope="col">@localizer["state"]</th>
                                    <th scope="col">@localizer["more"]</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.ActiveCopuns) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.OverView)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.StartDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EndDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.state)
            </td>
           
            
            
            <td>

                                            <a href="javascript:void(0)"      onclick="confirmDisable(@item.Id)"     class="btn btn-primary">       @localizer["stop"]      </a>
                                            |
                                            <a onclick="Delete('/Copun/Delete?id='+@item.Id)" class="btn btn-primary" asp-route-id="@item.Id">@localizer["delete"]</a> |
                                            <a asp-action="Details" class="btn btn-primary" asp-route-id="@item.Id">@localizer["moore"]</a>
            </td>
        </tr>
}

             
             

                  


                    
                  </tbody>
               
                </table>

              </div>
              
            </div>
          </div>
        </div>
      </div>
      </div>
    <div id="stoppedOffersTab" class="tabcontent" >
<div class="row">
        <div class="col-12">
          <div class="card mb-4">
            
            <div class="card-body px-0 pt-0 pb-2">
              <div class="table-responsive p-0">
               
                <table id="tbl2"  class="table table-striped text-center">
                  <thead>
                    <tr>
                                    <th scope="col">@localizer["storename"]</th>
                                    <th scope="col">@localizer["storedetails"]</th>
                                    <th scope="col">@localizer["couponstart"]</th>
                                    <th scope="col">@localizer["couponends"] </th>
                                    <th scope="col">@localizer["state"]</th>
                                    <th scope="col">@localizer["more"]</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.stoppedCopuns) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.OverView)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.StartDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EndDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.state)
            </td>
           
            
            
            <td>

                                            <a asp-action="Details" class="btn btn-primary" asp-route-id="@item.Id">@localizer["more"]</a> |
                                        </td>
        </tr>
}

             
             

                  


                    
                  </tbody>
               
                </table>

              </div>
              
            </div>
          </div>
        </div>
      </div>
      </div>
    
                @section Scripts{
    <script>
  let table = new DataTable('#tbl1');
  let table2 = new DataTable('#tbl2');

    </script>
    <script>
        function confirmDisable(id) {
            if (confirm("هل أنت متأكد من إيقاف هذا الكوبون؟")) {
                fetch(`/Copun/Disable?id=${id}`, {
                    method: "POST"
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    if (data.state === 1) {
                        // إعادة تحميل الصفحة أو تحديث جدول البيانات
                        location.reload();
                    }
                })
                .catch(error => {
                    alert("حدث خطأ أثناء تنفيذ العملية.");
                    console.error(error);
                });
            }
        }
    </script>

}