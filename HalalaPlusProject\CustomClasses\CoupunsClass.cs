﻿using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Spreadsheet;
using HalalaPlusProject.CModels;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.ReportingServices.ReportProcessing.ReportObjectModel;
using NuGet.Packaging.Signing;

namespace HalalaPlusProject.CustomClasses
{
    public class CoupunsClass
    {
        public List<CoupunModel> retrive(HalalaPlusdbContext _context,bool isActive=true ,bool isDeleated=false,long? ids=null, bool isAdmin=true)
        {
            try
            {
                var query = _context.OffersAndCopuns
            .Where(ob => ob.IsActive == isActive
                      && ob.Deleted == isDeleated
                      && ob.RecType == "code");

           
                if (!isAdmin && ids.HasValue)
                {
                    query = query.Where(ob => ob.MasterId == ids.Value);
                }

                return query.Select(ob => new CModels.CoupunModel
                {
                    Name = ob.Name,
                            StartDate = ob.StartDate,
                            Id = ob.Id,
                            ProviderName = ob.StoreName,
                            Details = ob.Details,
                            EnDetails = ob.EnDetails,
                            EnOverView = ob.EnoverView,
                            EnProviderName = ob.EnStoreName,
                            ActivityName = (ob.Activity != null) ? _context.Activities.FirstOrDefault(p => p.Id == ob.Activity).Name : "",
                            ActivityNo = (ob.Activity != null) ? ob.Activity : 0,
                            Discount = (ob.Discount != null) ? ob.Discount : 0,
                            EndDate = ob.EndDate,
                            state = (ob.Deleted == true)?"محذوف": (ob.IsActive==true)? "نشط" : "موقف",
                            OverView = ob.OverView,
                            EnName = ob.EnName,
                            CoupunCode = ob.CopunCode,
                            Provider=ob.MasterId??0,
                            StoreLink=ob.StoreLink,
                            imgLink ="/images/"+ ob.Img,
                        }).OrderByDescending(p => p.Id).ToList();
            }
            catch (Exception ex)
            {Console.WriteLine(ex.Message); 
                return new List<CoupunModel>();
            }
        }
        public List<CoupunModel> retriveStoped(HalalaPlusdbContext _context, bool isActive = true, bool isDeleated = false, long? id = null, bool isAdmin=true)
        {
            try
            {

                var query = _context.OffersAndCopuns
           .Where(ob =>   ob.RecType == "code" 
           && ((ob.IsActive == false 
           && (ob.Deleted == false || ob.Deleted == true)) || ((ob.Deleted == true) && (ob.IsActive == false || ob.IsActive == true)))) ;

                 
                if (!isAdmin && id.HasValue)
                {
                    query = query.Where(ob => ob.MasterId == id.Value);
                }

                return query.Select(ob => new CModels.CoupunModel
                {
                     
                            Name = ob.Name,
                            StartDate = ob.StartDate,
                            Id = ob.Id,
                            ProviderName = ob.StoreName,
                            OverView = ob.OverView,
                            Details = ob.Details,
                            EnDetails = ob.EnDetails,
                            EnOverView = ob.EnoverView,
                            EnProviderName = ob.EnStoreName,
                            ActivityName = (ob.Activity != null) ? _context.Activities.FirstOrDefault(p => p.Id == ob.Activity).Name : "",
                            ActivityNo = (ob.Activity != null) ? ob.Activity : 0,
                            Discount = (ob.Discount != null) ? ob.Discount : 0,
                            EndDate = ob.EndDate,
                            state = (ob.Deleted == true) ? "محذوف" : (ob.IsActive == true) ? "نشط" : "موقف",
                            CoupunCode = ob.CopunCode,
                            EnName = ob.EnName,
                            
                            StoreLink = ob.StoreLink,
                            Provider=ob.MasterId??0,
                            imgLink = "/images/" + ob.Img,
                        }).OrderByDescending(p => p.Id).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return new List<CoupunModel>();
            }
        }
        public CoupunModel retriveCoupun(HalalaPlusdbContext _context,long id)
        {
            try
            {
                var temp =  _context.OffersAndCopuns.Where(o=>o.Id==id).Select(ob=>new CModels.CoupunModel
                {
                    Name = ob.Name,
                    StartDate = ob.StartDate,
                    Id = ob.Id,
                    ProviderName =ob.StoreName,
                    Details=ob.Details,
                    EnDetails=ob.EnDetails,
                    EnOverView = ob.EnoverView,
                    EnProviderName =ob.EnStoreName,
                    EnName =ob.EnName,
                    Provider = ob.MasterId ?? 0,
                    EndDate = ob.EndDate,
                    state = (ob.Deleted == true) ? "محذوف" : (ob.IsActive == true) ? "نشط" : "موقف",
                    OverView = ob.OverView,
                    CoupunCode = ob.CopunCode,
                    ActivityName = (ob.Activity != null) ? _context.Activities.FirstOrDefault(p => p.Id == ob.Activity).Name : "",
                    ActivityNo = (ob.Activity != null) ? ob.Activity : 0,
                    Discount = (ob.Discount != null) ? ob.Discount : 0,
                    StoreLink = ob.StoreLink,
                    imgLink = "/images/" + ob.Img,
                }).FirstOrDefault();
                return temp;
            }
            catch (Exception)
            {
                return new CoupunModel();
            }
        }


        public async Task<bool> disable(HalalaPlusdbContext _context, long id,bool activate)
        {
            try
            {
                var temp = _context.OffersAndCopuns.Find(id);
                if(temp==null) return false;
                temp.IsActive = activate;
                _context.Update(temp);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
        public async Task<bool> delete(HalalaPlusdbContext _context, long id)
        {
            try
            {
                var temp = _context.OffersAndCopuns.Find(id);
                if(temp==null) return false;
                temp.Deleted = true;
                _context.Update(temp);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
        public async Task<bool> insert(HalalaPlusdbContext _context, CModels.CoupunModel model,long id, bool isAdmin)
        {
            try
            {
                
                    var temp =new  Models.OffersAndCopun();
                temp.Name = model.Name;
                temp.EndDate = model.EndDate;
                temp.StartDate = model.StartDate;
                temp.StoreName = model.ProviderName;
                temp.EnStoreName = model.EnProviderName;
                temp.OverView = model.OverView;
                temp.EnoverView = model.EnOverView;
                temp.Details=model.Details;
                temp.EnDetails=model.EnDetails;
                temp.EnName = model.EnName;
                temp.IsActive = true;
                temp.Img = model.imgLink;
                temp.MasterId = isAdmin ? model.Provider : id;
                temp.Activity = model.ActivityNo;
                temp.Discount = model.Discount;
                temp.RecType = "code";
                temp.CopunCode = model.CoupunCode;
                temp.StoreLink=model.StoreLink;
                _context.Add(temp);
               await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
            
        }
        public async Task<bool> Edit(HalalaPlusdbContext _context, CModels.CoupunModel model, IWebHostEnvironment _hosting)
        {
            try
            {
                var temp = _context.OffersAndCopuns.Find(model.Id);
                if (model.Img != null)
                {
                    if (temp.Img != null)
                        HandleImages.RemoveImageRoot(temp.Img, "images", _hosting);
                    model.imgLink = HandleImages.SaveImage(model.Img, "images", _hosting);
                    temp.Img = model.imgLink;
                }
                if (model.EndDate < DateTime.Now)
                {
                    temp.IsActive = false;
                }
                else
                {
                    temp.IsActive = true;
                }
                temp.Name = model.Name;
                temp.EnName = model.EnName;
                temp.EndDate = model.EndDate;
                temp.StartDate = model.StartDate;
                temp.OverView = model.OverView;
                temp.EnoverView = model.EnOverView;
                temp.Details = model.Details;
                temp.EnDetails = model.EnDetails;
                //temp.IsActive = true;
                temp.StoreName = model.ProviderName;
                temp.EnStoreName = model.EnProviderName;
                temp.Activity = model.ActivityNo;
                temp.Discount = model.Discount;
                temp.MasterId = model.Provider;
                temp.CopunCode = model.CoupunCode;
                temp.StoreLink = model.StoreLink;
                _context.Update(temp);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }

        }
    }
}
