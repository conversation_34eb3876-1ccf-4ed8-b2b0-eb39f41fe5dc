﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة تصنيفات الأنشطة التجارية (Categories)، وتوفير عمليات الإنشاء والقراءة والتحديث والحذف (CRUD).
    /// </summary>
    [Authorize]
    public class CategoriesController : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public CategoriesController(HalalaPlusdbContext context)
        {
            _context = context;
        }

        // GET: Categories
        /// <summary>
        /// عرض قائمة بجميع التصنيفات.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة التصنيفات.</returns>
        public async Task<IActionResult> Index()
        {
            return _context.Categories != null ?
                        View(await _context.Categories.ToListAsync()) :
                        Problem("Entity set 'HalalaPlusdbContext.Categories'  is null.");
        }

        // GET: Categories/Details/5
        /// <summary>
        /// عرض التفاصيل الخاصة بتصنيف معين.
        /// </summary>
        /// <param name="id">معرف التصنيف المراد عرض تفاصيله.</param>
        /// <returns>عرض يحتوي على تفاصيل التصنيف، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null || _context.Categories == null)
            {
                return NotFound();
            }

            var category = await _context.Categories
                .FirstOrDefaultAsync(m => m.Id == id);
            if (category == null)
            {
                return NotFound();
            }

            return View(category);
        }

        // GET: Categories/Create
        /// <summary>
        /// عرض نموذج إنشاء تصنيف جديد.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج الإنشاء.</returns>
        public IActionResult Create()
        {
            return View();
        }

        // POST: Categories/Create
        /// <summary>
        /// معالجة عملية إنشاء تصنيف جديد وحفظه في قاعدة البيانات.
        /// </summary>
        /// <param name="category">كائن التصنيف الذي يحتوي على البيانات الجديدة.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة التصنيفات؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Name,EngName")] Category category)
        {
            if (ModelState.IsValid)
            {
                _context.Add(category);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(category);
        }

        // GET: Categories/Edit/5
        /// <summary>
        /// عرض نموذج تعديل بيانات تصنيف حالي.
        /// </summary>
        /// <param name="id">معرف التصنيف المراد تعديله.</param>
        /// <returns>عرض يحتوي على بيانات التصنيف في نموذج التعديل، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null || _context.Categories == null)
            {
                return NotFound();
            }

            var category = await _context.Categories.FindAsync(id);
            if (category == null)
            {
                return NotFound();
            }
            return View(category);
        }

        // POST: Categories/Edit/5
        /// <summary>
        /// معالجة التعديلات المقدمة لتصنيف وحفظها في قاعدة البيانات.
        /// </summary>
        /// <param name="id">معرف التصنيف الذي يتم تعديله.</param>
        /// <param name="category">كائن التصنيف الذي يحتوي على البيانات المحدثة.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة التصنيفات؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,EngName")] Category category)
        {
            if (id != category.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(category);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CategoryExists(category.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(category);
        }

        // GET: Categories/Delete/5
        /// <summary>
        /// عرض صفحة تأكيد حذف تصنيف.
        /// </summary>
        /// <param name="id">معرف التصنيف المراد حذفه.</param>
        /// <returns>عرض يحتوي على تفاصيل التصنيف لتأكيد الحذف، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null || _context.Categories == null)
            {
                return NotFound();
            }

            var category = await _context.Categories
                .FirstOrDefaultAsync(m => m.Id == id);
            if (category == null)
            {
                return NotFound();
            }

            return View(category);
        }

        // POST: Categories/Delete/5
        /// <summary>
        /// تنفيذ عملية حذف التصنيف من قاعدة البيانات بعد التأكيد.
        /// </summary>
        /// <param name="id">معرف التصنيف المراد حذفه.</param>
        /// <returns>بعد إتمام العملية بنجاح، يتم عرض صفحة قائمة التصنيفات.</returns>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            if (_context.Categories == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.Categories'  is null.");
            }
            var category = await _context.Categories.FindAsync(id);
            if (category != null)
            {
                _context.Categories.Remove(category);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// التحقق من وجود تصنيف في قاعدة البيانات بالمعرف المحدد.
        /// </summary>
        /// <param name="id">معرف التصنيف للتحقق منه.</param>
        /// <returns>إرجاع 'true' إذا كان التصنيف موجودًا، وإلا 'false'.</returns>
        private bool CategoryExists(int id)
        {
            return (_context.Categories?.Any(e => e.Id == id)).GetValueOrDefault();
        }
    }
}