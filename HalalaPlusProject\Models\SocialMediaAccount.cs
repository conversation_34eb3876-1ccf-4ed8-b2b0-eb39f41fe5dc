﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class SocialMediaAccount
{
    [Key]
    public int Id { get; set; }

    [StringLength(50)]
    public string? Name { get; set; }

    [StringLength(250)]
    public string? Link { get; set; }

    [StringLength(250)]
    public string? Icon { get; set; }

    [Column("Asp_Id")]
    public long? AspId { get; set; }

    [Column("userId")]
    public long? UserId { get; set; }

    [Column("P_Id")]
    public int? PId { get; set; }

    [StringLength(50)]
    public string? EnName { get; set; }

    [ForeignKey("PId")]
    [InverseProperty("SocialMediaAccounts")]
    public virtual Platform? PIdNavigation { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("SocialMediaAccounts")]
    public virtual SystemUser? User { get; set; }
}
