﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("PayingCompaniesTable")]
public partial class PayingCompaniesTable
{
    [Key]
    public int Id { get; set; }

    [StringLength(250)]
    public string? Name { get; set; }

    [StringLength(250)]
    public string? ConnectionCode { get; set; }

    [StringLength(500)]
    public string? OperationDetils { get; set; }

    [StringLength(250)]
    public string? EnName { get; set; }

    [StringLength(500)]
    public string? OperationDetails { get; set; }
}
