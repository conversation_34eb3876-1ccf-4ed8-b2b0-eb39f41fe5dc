﻿using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    public class BanksAccountsController : Controller
    {
        private readonly IWebHostEnvironment _hosting;
        private readonly HalalaPlusdbContext _context;
        public BanksAccountsController(HalalaPlusdbContext context, IWebHostEnvironment _hosting)
        {
            _context = context;
            this._hosting = _hosting;
        }
        public async Task<IActionResult> Index()
        {
            var list =  _context.BanksAccounts.ToList();                                     
            return View(list);
        }
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null || _context.BanksAccounts == null)
            {
                return NotFound();
            }

            var data = _context.BanksAccounts.Where(p=>p.Id==id).FirstOrDefault();
            if (data == null)
            {
                return NotFound();
            }

            return View(data);
        }
        public IActionResult Create()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Models.BanksAccount model,IFormFile img)
        {
            if (ModelState.IsValid)
            {
                var ob = new Models.BanksAccount();
                try
                {
                   
                    ob.BankName = model.BankName;
                    ob.EnBankName = model.EnBankName;
                    ob.AccountNumber = model.AccountNumber;
                    ob.ConnectionCode = model.ConnectionCode;
                    if (img != null) ob.Icon = HandleImages.SaveImage(img, "img", _hosting);
                    _context.BanksAccounts.Add(ob);
                    await _context.SaveChangesAsync();
                }
                catch( Exception ex)
                {
                    HandleImages.RemoveImage(ob.Icon, "img", _hosting);
                    return View(model);
                }
                return RedirectToAction(nameof(Index));
            }
            return View(model);
        }
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null || _context.BanksAccounts == null)
            {
                return NotFound();
            }

            var data = _context.BanksAccounts.Find(id);
            if (data == null)
            {
                return NotFound();
            }
            return View(data);
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(Models.BanksAccount model, IFormFile img)
        {
            model.Target = " ";
            model.State = "A";
            if (ModelState.IsValid)
            {
                var ob = _context.BanksAccounts.Find(model.Id);
                string old = ob.Icon;
                try
                {
                   
                    if (ob == null) return View(model);
                    ob.BankName = model.BankName;
                    ob.EnBankName = model.EnBankName;
                    ob.AccountNumber = model.AccountNumber;
                    ob.ConnectionCode = model.ConnectionCode;
                    if (img != null) ob.Icon = HandleImages.SaveImage(img, "img", _hosting);
                    _context.Update(ob);
                    await _context.SaveChangesAsync(); if (img != null && old != null) HandleImages.RemoveImage(ob.Icon, "img", _hosting);
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (img != null && old == null) HandleImages.RemoveImage(ob.Icon, "img", _hosting);
                    return View(model);
                }
                return RedirectToAction(nameof(Index));
            }
            return View(model);
        }
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null || _context.BanksAccounts == null)
            {
                return NotFound();
            }

            var data =  _context.BanksAccounts.Find(id);
            if (data == null)
            {
                return NotFound();
            }

            return View(data);
        }
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            if (_context.BanksAccounts == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.SystemUsers'  is null.");
            }
            var data = await _context.BanksAccounts.FindAsync(id);
            if (data != null)
            {
                _context.BanksAccounts.Remove(data);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool SystemUserExists(long id)
        {
            return (_context.BanksAccounts?.Any(e => e.Id == id)).GetValueOrDefault();
        }


    }
}
