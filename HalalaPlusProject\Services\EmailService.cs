﻿using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;
using HalalaPlusProject.Data;
using HalalaPlusProject.Utils;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;

namespace HalalaPlusProject.Services
{
    public interface IEmailService
    {
        public Task SendEmailAsync(string toEmail, string subject, string body);
        public Task SendNewEmailAsync(string toEmail, string subject, string recipientName, string subscriptionType, string password, string supportEmail);
    }

    public class EmailService : IEmailService
    {
        private readonly IConfiguration _configuration;

        public EmailService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task SendEmailAsync(string toEmail, string subject, string body)
        {
            var smtpClient = new SmtpClient(_configuration["EmailSettings:SmtpServer"])
            {
                Port = int.Parse(_configuration["EmailSettings:SmtpPort"]),
                Credentials = new NetworkCredential(
                    _configuration["EmailSettings:SenderEmail"],
                    _configuration["EmailSettings:SenderPassword"]
                ),
                EnableSsl = true
            };

            var mailMessage = new MailMessage
            {
                From = new MailAddress(_configuration["EmailSettings:SenderEmail"]),
                Subject = subject,
                Body = body,
                IsBodyHtml = true
            };

            mailMessage.To.Add(toEmail);

            await smtpClient.SendMailAsync(mailMessage);
        }

        public async Task SendNewEmailAsync(string toEmail, string subject, string recipientName, string subscriptionType, string password, string supportEmail)
        {
            try
            {
                var smtpClient = new SmtpClient(_configuration["EmailSettings:SmtpServer"])
                {
                    Port = int.Parse(_configuration["EmailSettings:SmtpPort"]),
                    Credentials = new NetworkCredential(
                           _configuration["EmailSettings:SenderEmail"],
                           _configuration["EmailSettings:SenderPassword"]
                       ),
                    EnableSsl = true
                };

                var mailMessage = new MailMessage
                {
                    From = new MailAddress(_configuration["EmailSettings:SenderEmail"]),
                    Subject = subject,
                    Body = this.GetSubscriptionConfirmationWithPassword(recipientName, subscriptionType, password, supportEmail, toEmail),
                    IsBodyHtml = true
                };

                mailMessage.To.Add(toEmail);

                await smtpClient.SendMailAsync(mailMessage);
            }
            catch (Exception ex)
            {
                new TelegramHAndler().SendSingleSMSwithoutasync(ex.Message);
                Console.WriteLine(ex.Message);
            }
        }

        //public string GetSubscriptionConfirmationWithPassword(string recipientName, string subscriptionType, string password, string supportEmail)
        //{
        //    return $@"
        //<html>
        //<body style='direction: rtl; text-align: right; font-family: Tahoma, Arial, sans-serif;'>
        //    <h2>عزيزي {recipientName}،</h2>
        //    <p>شكرًا لاشتراكك في {subscriptionType}!</p>
        //    <p>تم اشتراكك بنجاح وسوف يتم تأكيد الاشتراك خلال 24 ساعة .</p>
        //    <p>معلومات الحساب الخاصة بك:</p>
        //    <p><strong>كلمة المرور:</strong> {password}</p>
        //    <p>إذا كان لديك أي استفسارات، لا تتردد في التواصل معنا عبر البريد الإلكتروني: <a href='mailto:{supportEmail}'>{supportEmail}</a></p>
        //    <p>مع أطيب التحيات،<br>فريقنا</p>
        //</body>
        //</html>";
        //}

        public string GetSubscriptionConfirmationWithPassword(string recipientName, string subscriptionType, string password, string supportEmail,string userEmail)
        {
            return $@"
        <html>
        <body style='direction: rtl; text-align: right; font-family: Tahoma, Arial, sans-serif;'>
            <h2>عزيزي {recipientName}،</h2>
            <p>شكرًا لاختيارك منصة ايزي</p>
                    <p>يمكنك الدخول على حسابك عبر الرابط  <a href='https://admin.halalaplus.com'>دخول </a></p>
            <p>لقد تم اشتراكك بنجاح وسوف يتم تأكيد الاشتراك خلال 24 ساعة .</p>
            <p>معلومات الحساب الخاصة بك:</p>
            <p><strong>المستخدم:</strong> {userEmail}</p>
            <p><strong>كلمة المرور:</strong> {password}</p>
            <p>إذا كان لديك أي استفسارات، لا تتردد في التواصل معنا عبر البريد الإلكتروني: <a href='mailto:{supportEmail}'>{supportEmail}</a></p>
            <p>مع أطيب التحيات،<br>فريقنا</p>
        </body>
        </html>";
        }


    }

}
