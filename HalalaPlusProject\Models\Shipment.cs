﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("shipment")]
public partial class Shipment
{
    [Key]
    public long Id { get; set; }

    [Column("User_Id")]
    public long? UserId { get; set; }

    [Column("Shipment_Date", TypeName = "datetime")]
    public DateTime? ShipmentDate { get; set; }

    [Column("address")]
    [StringLength(250)]
    public string? Address { get; set; }

    [Column("city")]
    public int? City { get; set; }

    [Column("state")]
    [StringLength(50)]
    public string? State { get; set; }

    [Column("country")]
    public int? Country { get; set; }

    [Column("zip_code")]
    [StringLength(50)]
    public string? ZipCode { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("Shipments")]
    public virtual SystemUser? User { get; set; }
}
