﻿@model HalalaPlusProject.Models.Achievement

@{
    ViewData["Title"] = "انشاء انجاز";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<h4>انشاء انجاز</h4>
<hr />
<div class="row">
    <div class="col-md-12">
        <form asp-action="Create" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="Title" class="control-label">العنوان</label>
                        <input asp-for="Title" class="form-control" />
                        <span asp-validation-for="Title" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="EnTitle" class="control-label">العنوان انجليزي</label>
                        <input asp-for="EnTitle" class="form-control" />
                        <span asp-validation-for="EnTitle" class="text-danger"></span>
                    </div>
            </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="Details" class="control-label">التفاصيل</label>
                        <input asp-for="Details" class="form-control" />
                        <span asp-validation-for="Details" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="EnDetails" class="control-label">التفاصيل انجليزي</label>
                        <input asp-for="EnDetails" class="form-control" />
                        <span asp-validation-for="EnDetails" class="text-danger"></span>
                    </div>
            </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <div class="row justify-content-center" style="margin-bottom:1px;">
                            <div class="circle ">
                                <img src="" id="Achievementicon">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="file" class="control-label">الصورة</label>
                        <input name="file" type="file" id="file" accept="image/*" required onchange="ShowImagePreview(this, document.getElementById('Achievementicon'))" class="form-control" />
                    </div>
            </div>
            </div>
                <div class="form-group">
                    <button type="submit" value="Create" class="btn btn-primary" >حـفظ</button>
                </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">عودة للقائمة السابقة</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
