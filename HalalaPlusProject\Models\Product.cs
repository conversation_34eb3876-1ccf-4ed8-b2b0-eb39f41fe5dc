﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class Product
{
    [Key]
    public int Id { get; set; }

    [Column("SKU")]
    [StringLength(150)]
    public string? Sku { get; set; }

    [StringLength(250)]
    public string? Name { get; set; }

    [Column("description")]
    [StringLength(500)]
    public string? Description { get; set; }

    [Column("image")]
    [StringLength(250)]
    public string? Image { get; set; }

    [Column("engname")]
    [StringLength(250)]
    public string? Engname { get; set; }

    [Column("engdescription")]
    [StringLength(500)]
    public string? Engdescription { get; set; }

    [Column("active")]
    public bool? Active { get; set; }

    [Column("create_on", TypeName = "datetime")]
    public DateTime? CreateOn { get; set; }

    [Column("catagory")]
    public int? Catagory { get; set; }

    public double? Price { get; set; }

    [StringLength(100)]
    public string? ProductId { get; set; }

    [StringLength(100)]
    public string? CategoryId { get; set; }

    [Column(TypeName = "decimal(18, 0)")]
    public decimal? ProductPrice { get; set; }

    [StringLength(250)]
    public string? ProductImage { get; set; }

    [StringLength(10)]
    public string? ProductCurrency { get; set; }

    public double? VatPercentage { get; set; }

    public bool Available { get; set; }

    public double? SellPrice { get; set; }

    public int OptionalFieldsExist { get; set; }

    [StringLength(200)]
    public string? ParentProductId { get; set; }

    public bool IsFromTwelve { get; set; }

    public bool IsSubProduct { get; set; }

    public bool TopUp { get; set; }

    public string? Note { get; set; }

    public string? Fields { get; set; }

    public int? ParentId { get; set; }

    public string? Title { get; set; }

    public string? EnTitle { get; set; }

    public bool IsSandbox { get; set; }

    public string? Details { get; set; }

    [InverseProperty("Product")]
    public virtual ICollection<Cart> Carts { get; set; } = new List<Cart>();

    [ForeignKey("Catagory")]
    [InverseProperty("Products")]
    public virtual Category? CatagoryNavigation { get; set; }

    [InverseProperty("Product")]
    public virtual ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();

    [InverseProperty("Product")]
    public virtual ICollection<ProductDetail> ProductDetails { get; set; } = new List<ProductDetail>();

    [InverseProperty("Product")]
    public virtual ICollection<ProductOptionalField> ProductOptionalFields { get; set; } = new List<ProductOptionalField>();
}
