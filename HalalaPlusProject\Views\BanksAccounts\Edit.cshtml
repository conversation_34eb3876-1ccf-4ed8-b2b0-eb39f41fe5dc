﻿@model HalalaPlusProject.Models.BanksAccount
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = @localizer["edit"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h3>@localizer["edit"]</h3>

<h4>@localizer["bank"]</h4>
<hr />
<div class="row">
    <div class="col-md-8">
        <form asp-action="Edit" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="State" value="A" />
            <input type="hidden" asp-for="Target" value="0" />
             
         <div class="row">
           <div class="col-md-5">
          <div class="form-group">
                        <label asp-for="BankName" class="control-label">@localizer["bankname"]</label>
                <input asp-for="BankName" class="form-control" />
                <span asp-validation-for="BankName" required class="text-danger"></span>
            </div>
            <div class="form-group">
                        <label asp-for="EnBankName" class="control-label">EnBankName</label>
                        <input asp-for="EnBankName" class="form-control" />
                        <span asp-validation-for="EnBankName" required class="text-danger"></span>
            </div>
            
             <div class="form-group">
                        <label asp-for="AccountNumber" class="control-label">@localizer["accountno"]</label>
                <input asp-for="AccountNumber" class="form-control" />
                <span asp-validation-for="AccountNumber" required class="text-danger"></span>
            </div>
            
           

          </div>

           <div class="col-md-5">
         
         <div class="form-group">
                        <label asp-for="ConnectionCode" class="control-label">@localizer["connectioncode"]</label>
                <input asp-for="ConnectionCode" class="form-control" />
                <span asp-validation-for="ConnectionCode" class="text-danger"></span>
            </div>
                    <div class="form-group">
                        <div class="row justify-content-center" style="margin-bottom:1px;">
                            <div class="circle ">
                                <img src="/img/@Model.Icon" id="bankicon">
                            </div>
                        </div>

                    </div>
                    <div class="form-group">
                        <label class="control-label">Logo</label>
                        <input name="img" type="file" accept="image/*" onchange="ShowImagePreview(this, document.getElementById('bankicon'))" class="form-control" />
                        <span class="text-danger"></span>
                    </div>
              
            

          </div>
             </div>
 
        
         
        
         
     
           
            <div class="form-group">
                <button type="submit" value="Save" class="btn btn-primary">@localizer["save"]</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index"> @localizer["backtolist"]</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
