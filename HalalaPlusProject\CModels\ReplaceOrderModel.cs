﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    public class ReplaceOrderModel
    {
        //[Display(Name = "الرقم")]
        public long? Id { get; set; }

        //[Display(Name = "اسم العميل")]
        public string? CustomerName { get; set; }
        //[Display(Name = " عدد النقاط")]
        public string? PhoneNo { get; set; }
        public double? PointsNo { get; set; }

        //[Display(Name = "المبلغ المستحق")]
        public double? Amount { get; set; }
        public string? Status { get; set; }
        public DateTime? date { get; set; }

    }
}
