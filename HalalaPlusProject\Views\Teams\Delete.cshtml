﻿@model HalalaPlusProject.Models.Team

@{
    ViewData["Title"] = "حذف عضو";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>حـذف</h2>

<h3>هل تريد حذف العضو ؟</h3>
<div>
    
    <hr />
    <dl class="row">
        <div class="col-md-4">
            <dt class="col-sm-2">
                الاسم
            </dt>
            <dd class="col-sm-10">
                @Html.DisplayFor(model => model.Name)
            </dd>
            <dt class="col-sm-2">
                الاسم النجليزي
            </dt>
            <dd class="col-sm-10">
                @Html.DisplayFor(model => model.EnName)
            </dd>
        </div>
        <div class="col-md-4">
            <dt class="col-sm-2">
                الوظيفة
            </dt>
            <dd class="col-sm-10">
                @Html.DisplayFor(model => model.Major)
            </dd>
            <dt class="col-sm-2">
                الوظيفة انجليزي
            </dt>
            <dd class="col-sm-10">
                @Html.DisplayFor(model => model.EnMajor)
            </dd>
        </div>
        <div class="col-md-4">
            <dt class="col-sm-2">
                الصورة
            </dt>
            <dd class="col-sm-10">
                <img  width="100px" src="/img/@Model.Image"/>
                @Html.DisplayFor(model => model.Image)
            </dd>
        </div>
        
        
        
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-danger" >حـذف</button> |
        <a asp-action="Index">عودة للقائمة السابقة</a>
    </form>
</div>
