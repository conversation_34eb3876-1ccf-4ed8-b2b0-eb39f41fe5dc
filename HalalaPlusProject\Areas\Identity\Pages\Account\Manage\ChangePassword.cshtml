﻿@page
@model ChangePasswordModel
@{
    ViewData["Title"] = "Change password";
    ViewData["ActivePage"] = ManageNavPages.ChangePassword;
}

<h3>@ViewData["Title"]</h3>
<partial name="_StatusMessage" for="StatusMessage" />
<div class="row">
    <div class="col-md-6">
        <form id="change-password-form" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-floating">
                <input asp-for="Input.OldPassword" class="form-control" autocomplete="current-password" aria-required="true" />
                <label asp-for="Input.OldPassword" class="form-label"></label>
                <span asp-validation-for="Input.OldPassword" class="text-danger"></span>
            </div>
            <div class="form-floating">
                <input asp-for="Input.NewPassword" class="form-control" autocomplete="new-password" aria-required="true" />
                <label asp-for="Input.NewPassword" class="form-label"></label>
                <span asp-validation-for="Input.NewPassword" class="text-danger"></span>
            </div>
            <div class="form-floating">
                <input asp-for="Input.ConfirmPassword" class="form-control" autocomplete="new-password" aria-required="true" />
                <label asp-for="Input.ConfirmPassword" class="form-label"></label>
                <span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
            </div>
            <button type="submit" class="w-100 btn btn-lg btn-primary">Update password</button>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
