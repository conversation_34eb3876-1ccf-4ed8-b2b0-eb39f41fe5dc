﻿
using HalalaPlusProject.CModels;
using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.Entities
{

    public class OrginazationDetailsModel
    {
        public long Id { get; set; }
        [Display(Name = "اسم مقدم الخدمة")]

        public string? Name { get; set; }
        public string? EnName { get; set; }
        [Display(Name = "اسم المستخدم")]
        public string? UserName { get; set; }
        [Display(Name = "المدينة")]
        public string? City { get; set; }
        [Required]
        public string? overview { get; set; }
        [Required]
        public string? enoverview { get; set; }
        [Required]
        public string? bnifitfrompoints { get; set; } = null!;
        [Required]
        public string? enbnifitfrompoints { get; set; } = null!;
        [Required]
        public string? FirstColor { get; set; }
        [Required]
        public string? SecondColor { get; set; }
        [Required]
        public string? OffersIcon { get; set; }
        public bool HasCustomBrand { get; set; }
        public string? StoreLink { get; set; }
        [Required]
        [Display(Name = "النشاط")]
        public string? ActivityName { get; set; }
        //[Required]
        [Display(Name = "اسم الممثل")]
        public string? ServiceProviderRepresent { get; set; }
        [Display(Name = "رقم الجوال")]
        public string? PhoneNumber { get; set; }
        [Display(Name = "الايميل")]
        //[RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }
        //public string AspId { get; set; } = null!;
        [Display(Name = "رقم السجل التجاري")]
        public string BusnissNo { get; set; } = null!;
        [Display(Name = "لموقع x")]
        public string? Lat { get; set; } = null!;
        [Display(Name = "الموقع y")]
        public string? lng { get; set; } = null!;
        [Display(Name = "رقم المنشأة")]
        public string EnterprisePhoneNo { get; set; } = null!;
        [Display(Name = "الموقع")]
        public string Locatin { get; set; } = null!;
        [Display(Name = "الشعار")]
        public string? Logo { get; set; } = null!;
        [Display(Name = "المرفقات")]
        public List<Files>? Files { get; set; } = null!;
        public List<Files>? images { get; set; } = null!;
        [Display(Name = "الكاش باك")]
        public double? CashBack { get; set; }
        [Required]
        [Display(Name = "تاريخ التعاقد")]
        public DateTime? ContractDate { get; set; }
        [Display(Name = "{رقم العقد")]
        public string? ContractNo { get; set; }
        [Display(Name = "نهاية التعاقد")]
        public DateTime? ContractEndDate { get; set; }
        public List<SocialAccounts>? Accounts { get; set; } = null!;
        public List<Points> Points { get; set; } = null!;
        public Sales sales { get; set; } = null!;
        public List<DiscountsModel> DiscountsList { get; set; } = null!;
    }

    public class OrginazationgetEdit
    {
        public long Id { get; set; }
        [Required]
        //[Display(Name = "اسم مقدم الخدمة")]
        public string? Name { get; set; }
        public string? EnName { get; set; }
        //[Display(Name = "اسم المستخدم")]
        [Required]
        public string? FirstColor { get; set; }
        [Required]
        public string? SecondColor { get; set; }

        public IFormFile? OffersIcon { get; set; }
        public string? OffersIconLink { get; set; }
        public bool HasCustomBrand { get; set; }
        public string? StoreLink { get; set; }
        public string? UserName { get; set; }
        [Required]
        //[Display(Name = "المدينة")]
        public int? City { get; set; }
        [Required]
        public string? overview { get; set; }
        [Required]
        public string? enoverview { get; set; }
        [Required]
        public string? bnifitfrompoints { get; set; } = null!;
        [Required]
        public string? enbnifitfrompoints { get; set; } = null!;

        [Required]
        //[Display(Name = "النشاط")]
        public int? Activity { get; set; }
        //[Required]
        //[Display(Name = "اسم الممثل")]
        public string? ServiceProviderRepresent { get; set; }
        //[Display(Name = "رقم الجوال")]
        public string? PhoneNumber { get; set; }
        //[Display(Name = "الايميل")]
        [RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }
        //public string AspId { get; set; } = null!;
        //[Display(Name = "رقم السجل التجاري")]
        public string? BusnissNo { get; set; } 
        //[Display(Name = "رقم المنشأة")]
        public string? EnterprisePhoneNo { get; set; } 
        //[Display(Name = "الموقع")]
        public string? Locatin { get; set; } = null!;
        //[Display(Name = "لموقع x")]
        public string? Lat { get; set; } = null!;
        //[Display(Name = "الموقع y")]
        public string? lng { get; set; } = null!;
        //[Display(Name = "الشعار")]
        public IFormFile? Logo { get; set; } = null!;
        public string? LogoLink { get; set; } = null!;
        //[Display(Name = "المرفقات")]
        public List<IFormFile>? Files { get; set; } = null!;
        public List<Files>? FilesList { get; set; } = null!;
        public List<Files>? images { get; set; } = null!;
        public DateOnly? ContractDate { get; set; }
        //[Required]
        //[Display(Name = "نهاية التعاقد")]
        public DateOnly? ContractEndDate { get; set; }
        //[Display(Name = "رقم العقد")]
        public string? ContractNo { get; set; }

        public List<SocialAccounts>? Accounts { get; set; } = null!;
        public Points? Points { get; set; } = null;
        public List<Points>? allPoints { get; set; }
        public Sales? Sales { get; set; } = null!;
        public DiscountsModel? Discounts { get; set; } = null!;
        public List<DiscountsModel>? DiscountsList { get; set; } = null!;
        public socialMediaAccounts? AccountsData { get; set; } = null!;

    }
    public class ComplateBusinessData: OrginazationgetEdit
    {
      
        public string Password { get; set; }
    }
}

