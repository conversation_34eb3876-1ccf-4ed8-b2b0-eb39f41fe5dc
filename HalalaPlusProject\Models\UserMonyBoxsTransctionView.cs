﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class UserMonyBoxsTransctionView
{
    public long? MonyBoxId { get; set; }

    [Column(TypeName = "decimal(18, 0)")]
    public decimal Id { get; set; }

    public double? Credit { get; set; }

    public double? Debit { get; set; }

    [StringLength(50)]
    public string? PaymentId { get; set; }

    public long? UserId { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    public string MonyBox { get; set; } = null!;

    public double Target { get; set; }

    public string? Icon { get; set; }

    [Column("startDate", TypeName = "datetime")]
    public DateTime? StartDate { get; set; }

    [Column("endDate", TypeName = "datetime")]
    public DateTime? EndDate { get; set; }

    public double? Amount { get; set; }

    [Column("operationDate", TypeName = "datetime")]
    public DateTime? OperationDate { get; set; }

    public bool IsVerified { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? VerifayDate { get; set; }
}
