using HalalaPlusProject.CModels;
using HalalaPlusProject.Data;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    /// <summary>
    /// إدارة إعدادات مكافآت العملاء، مثل نقاط تسجيل الدخول، ومكافآت الشحن، وغيرها.
    /// </summary>
    public class CustomerRewardSettingsController : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public CustomerRewardSettingsController(HalalaPlusdbContext context)
        {
            _context = context;
        }

        // GET: CustomerRewardSettings
        /// <summary>
        /// استرداد وعرض إعدادات مكافآت العملاء الحالية.
        /// </summary>
        /// <returns>عرض يحتوي على تفاصيل إعدادات المكافآت.</returns>
        public async Task<IActionResult> Index()
        {
            var customerRewardSetting = await _context.CustomerRewardSettings.FirstOrDefaultAsync();
            if (customerRewardSetting == null)
            {
                //return NotFound();
                var viewModelZero = new CustomerRewardSettingViewModel
                {
                   // Id = 0,
                    LoginRewardPoints = 0,
                    IsLoginRewardActive = true,
                    ChargeRewardPoints = 0,
                    IsChargeRewardActive = true,
                    PiggyCreationBonus = 0,
                    IsPiggyCreationActive = true,
                    SavingsGoalReward = 0,
                    IsSavingsGoalActive = true,
                    DiscountUsageReward = 0,
                    IsDiscountUsageActive = true,
                    DigitalPurchaseReward = 0,
                    IsDigitalPurchaseActive = true,
                    PlanCreationBonus = 0,
                    IsPlanCreationActive = true,
                    EngagementReward = 0,
                    IsEngagementRewardActive = true,
                    PointValueSar = 0,
                    IsPointValueActive = true,
                    MaxTransferAmount = 0,
                    IsTransferLimitActive = true
                };

                return View(viewModelZero);
            }

            var viewModel = new CustomerRewardSettingViewModel
            {
                Id = customerRewardSetting.Id,
                LoginRewardPoints = customerRewardSetting.LoginRewardPoints ?? 0,
                IsLoginRewardActive = customerRewardSetting.IsLoginRewardActive,
                ChargeRewardPoints = customerRewardSetting.ChargeRewardPoints ?? 0,
                IsChargeRewardActive = customerRewardSetting.IsChargeRewardActive,
                PiggyCreationBonus = customerRewardSetting.PiggyCreationBonus ?? 0,
                IsPiggyCreationActive = customerRewardSetting.IsPiggyCreationActive,
                SavingsGoalReward = customerRewardSetting.SavingsGoalReward ?? 0,
                IsSavingsGoalActive = customerRewardSetting.IsSavingsGoalActive,
                DiscountUsageReward = customerRewardSetting.DiscountUsageReward ?? 0,
                IsDiscountUsageActive = customerRewardSetting.IsDiscountUsageActive,
                DigitalPurchaseReward = customerRewardSetting.DigitalPurchaseReward ?? 0,
                IsDigitalPurchaseActive = customerRewardSetting.IsDigitalPurchaseActive,
                PlanCreationBonus = customerRewardSetting.PlanCreationBonus ?? 0,
                IsPlanCreationActive = customerRewardSetting.IsPlanCreationActive,
                EngagementReward = customerRewardSetting.EngagementReward ?? 0,
                IsEngagementRewardActive = customerRewardSetting.IsEngagementRewardActive,
                PointValueSar = customerRewardSetting.PointValueSar ?? 0,
                IsPointValueActive = customerRewardSetting.IsPointValueActive,
                MaxTransferAmount = customerRewardSetting.MaxTransferAmount ?? 0,
                IsTransferLimitActive = customerRewardSetting.IsTransferLimitActive
            };

            //var viewModelZero = new CustomerRewardSettingViewModel
            //{
            //    Id = 0,
            //    LoginRewardPoints = 0,
            //    IsLoginRewardActive = true,
            //    ChargeRewardPoints = 0,
            //    IsChargeRewardActive = true,
            //    PiggyCreationBonus = 0,
            //    IsPiggyCreationActive = true,
            //    SavingsGoalReward = 0,
            //    IsSavingsGoalActive = true,
            //    DiscountUsageReward = 0,
            //    IsDiscountUsageActive = true,
            //    DigitalPurchaseReward = 0,
            //    IsDigitalPurchaseActive = true,
            //    PlanCreationBonus = 0,
            //    IsPlanCreationActive = true,
            //    EngagementReward = 0,
            //    IsEngagementRewardActive = true,
            //    PointValueSar = 0,
            //    IsPointValueActive = true,
            //    MaxTransferAmount = 0,
            //    IsTransferLimitActive = true
            //};

            return View(viewModel);
        }

        // GET: CustomerRewardSettings/Edit
        /// <summary>
        /// استرداد إعدادات مكافآت العملاء الحالية لعرضها في نموذج التعديل.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج تعديل إعدادات المكافآت.</returns>
        public async Task<IActionResult> Edit()
        {
            var customerRewardSetting = await _context.CustomerRewardSettings.FirstOrDefaultAsync();
            if (customerRewardSetting == null)
            {
                return NotFound();
            }

            var viewModel = new CustomerRewardSettingViewModel
            {
                Id = customerRewardSetting.Id,
                LoginRewardPoints = customerRewardSetting.LoginRewardPoints,
                IsLoginRewardActive = customerRewardSetting.IsLoginRewardActive,
                ChargeRewardPoints = customerRewardSetting.ChargeRewardPoints,
                IsChargeRewardActive = customerRewardSetting.IsChargeRewardActive,
                PiggyCreationBonus = customerRewardSetting.PiggyCreationBonus,
                IsPiggyCreationActive = customerRewardSetting.IsPiggyCreationActive,
                SavingsGoalReward = customerRewardSetting.SavingsGoalReward,
                IsSavingsGoalActive = customerRewardSetting.IsSavingsGoalActive,
                DiscountUsageReward = customerRewardSetting.DiscountUsageReward,
                IsDiscountUsageActive = customerRewardSetting.IsDiscountUsageActive,
                DigitalPurchaseReward = customerRewardSetting.DigitalPurchaseReward,
                IsDigitalPurchaseActive = customerRewardSetting.IsDigitalPurchaseActive,
                PlanCreationBonus = customerRewardSetting.PlanCreationBonus,
                IsPlanCreationActive = customerRewardSetting.IsPlanCreationActive,
                EngagementReward = customerRewardSetting.EngagementReward,
                IsEngagementRewardActive = customerRewardSetting.IsEngagementRewardActive,
                PointValueSar = customerRewardSetting.PointValueSar,
                IsPointValueActive = customerRewardSetting.IsPointValueActive,
                MaxTransferAmount = customerRewardSetting.MaxTransferAmount,
                IsTransferLimitActive = customerRewardSetting.IsTransferLimitActive
            };
            return View(viewModel);
        }

        // POST: CustomerRewardSettings/Edit
        /// <summary>
        /// معالجة التعديلات المقدمة لإعدادات مكافآت العملاء وحفظها في قاعدة البيانات.
        /// </summary>
        /// <param name="viewModel">البيانات المحدثة لإعدادات المكافآت.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل عملية الحفظ.</returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit([Bind("Id,LoginRewardPoints,IsLoginRewardActive,ChargeRewardPoints,IsChargeRewardActive,PiggyCreationBonus,IsPiggyCreationActive,SavingsGoalReward,IsSavingsGoalActive,DiscountUsageReward,IsDiscountUsageActive,DigitalPurchaseReward,IsDigitalPurchaseActive,PlanCreationBonus,IsPlanCreationActive,EngagementReward,IsEngagementRewardActive,PointValueSar,IsPointValueActive,MaxTransferAmount,IsTransferLimitActive")] CustomerRewardSettingViewModel viewModel)
        {
            //if (!ModelState.IsValid)
            //{
            //    return View(viewModel);
            //}

            try
            {


                var customerRewardSetting = await _context.CustomerRewardSettings.FirstOrDefaultAsync();
                if (customerRewardSetting == null)                   
                {
                    //  return NotFound();
                    var viewModelZero = new CustomerRewardSetting
                    {
                        LoginRewardPoints = 0,
                        IsLoginRewardActive = true,
                        ChargeRewardPoints = 0,
                        IsChargeRewardActive = true,
                        PiggyCreationBonus = 0,
                        IsPiggyCreationActive = true,
                        SavingsGoalReward = 0,
                        IsSavingsGoalActive = true,
                        DiscountUsageReward = 0,
                        IsDiscountUsageActive = true,
                        DigitalPurchaseReward = 0,
                        IsDigitalPurchaseActive = true,
                        PlanCreationBonus = 0,
                        IsPlanCreationActive = true,
                        EngagementReward = 0,
                        IsEngagementRewardActive = true,
                        PointValueSar = 0,
                        IsPointValueActive = true,
                        MaxTransferAmount = 0,
                        IsTransferLimitActive = true
                    };
                    _context.Add(viewModelZero);
                    await _context.SaveChangesAsync();
                    return Ok(new { state = 1, message = "تم الحفظ بنجاح" });
                }
                else
                {
                    customerRewardSetting.LoginRewardPoints = viewModel.LoginRewardPoints;
                    customerRewardSetting.IsLoginRewardActive = viewModel.IsLoginRewardActive;
                    customerRewardSetting.ChargeRewardPoints = viewModel.ChargeRewardPoints;
                    customerRewardSetting.IsChargeRewardActive = viewModel.IsChargeRewardActive;
                    customerRewardSetting.PiggyCreationBonus = viewModel.PiggyCreationBonus;
                    customerRewardSetting.IsPiggyCreationActive = viewModel.IsPiggyCreationActive;
                    customerRewardSetting.SavingsGoalReward = viewModel.SavingsGoalReward;
                    customerRewardSetting.IsSavingsGoalActive = viewModel.IsSavingsGoalActive;
                    customerRewardSetting.DiscountUsageReward = viewModel.DiscountUsageReward;
                    customerRewardSetting.IsDiscountUsageActive = viewModel.IsDiscountUsageActive;
                    customerRewardSetting.DigitalPurchaseReward = viewModel.DigitalPurchaseReward;
                    customerRewardSetting.IsDigitalPurchaseActive = viewModel.IsDigitalPurchaseActive;
                    customerRewardSetting.PlanCreationBonus = viewModel.PlanCreationBonus;
                    customerRewardSetting.IsPlanCreationActive = viewModel.IsPlanCreationActive;
                    customerRewardSetting.EngagementReward = viewModel.EngagementReward;
                    customerRewardSetting.IsEngagementRewardActive = viewModel.IsEngagementRewardActive;
                    customerRewardSetting.PointValueSar = viewModel.PointValueSar;
                    customerRewardSetting.IsPointValueActive = viewModel.IsPointValueActive;
                    customerRewardSetting.MaxTransferAmount = viewModel.MaxTransferAmount;
                    customerRewardSetting.IsTransferLimitActive = viewModel.IsTransferLimitActive;

                    _context.Update(customerRewardSetting);
                    await _context.SaveChangesAsync();
                    return Ok(new { state = 1, message = "تم الحفظ بنجاح" });
                }

            }
            catch (DbUpdateConcurrencyException)
            {
                if (!_context.CustomerRewardSettings.Any(e => e.Id == viewModel.Id))
                {
                    return Ok(new { state = 0, message = "فشل الحفظ" });
                }
                else
                {
                    return Ok(new { state = 0, message = "فشل الحفظ" });
                }
            }
        }
    }
}