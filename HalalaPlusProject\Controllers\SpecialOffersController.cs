﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using HalalaPlusProject.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// متحكم لإدارة العروض الخاصة، بما في ذلك عرضها، إنشائها، تعديلها، وحذفها.
    /// </summary>
    [Authorize]
    public class SpecialOffersController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IWebHostEnvironment _hosting;
        private readonly IStringLocalizer<SpecialOffersController> _localization;
        //private readonly IFirebaseNotificationHistoryService fire_notification;

        public SpecialOffersController(/*IFirebaseNotificationHistoryService firenotification, */HalalaPlusdbContext context, IWebHostEnvironment hosting, IStringLocalizer<SpecialOffersController> _localization)
        {
            _context = context;
            _hosting = hosting;
            this._localization = _localization;
            //fire_notification = firenotification;
        }

        /// <summary>
        /// يعرض صفحة قائمة العروض، مقسمة إلى عروض نشطة وعروض متوقفة.
        /// </summary>
        public IActionResult Index()
        {
            var ob = new CModels.SpecialOffersIndex();
            ob.SpecialOffers = new CustomClasses.SpecialOffersClass().retrive(_context, true, false);
            ob.StoppedOffers = new CustomClasses.SpecialOffersClass().retriveDeletedorUnactive(_context, false, true);
            return View(ob);
        }

        /// <summary>
        /// يعرض تفاصيل عرض خاص بناءً على المعرف.
        /// </summary>
        /// <param name="id">معرّف العرض الخاص.</param>
        public IActionResult Details(long? id)
        {
            var lang = Thread.CurrentThread.CurrentCulture.Name;
            var ob = new CustomClasses.SpecialOffersClass().retriveOffer(_context, id ?? 0);
            //ob.ProviderName = (lang.StartsWith("ar")) ? _context.SystemUsers.Where(o => o.Id == ob.Provider).FirstOrDefault().Name : _context.SystemUsers.Where(o => o.Id == ob.Provider).FirstOrDefault().EnName;
            if (ob.Provider != null && ob.Provider >= 1) ob.SysProvider = (lang.StartsWith("ar")) ? _context.SystemUsers.Where(o => o.Id == ob.Provider).FirstOrDefault().Name : _context.SystemUsers.Where(o => o.Id == ob.Provider).FirstOrDefault().EnName;
            return View(ob);
        }

        /// <summary>
        /// يعرض صفحة تعديل عرض خاص معبأة بالبيانات الحالية للعرض.
        /// </summary>
        /// <param name="id">معرّف العرض الخاص المراد تعديله.</param>
        public async Task<IActionResult> Edit(long? id)
        {
            var lang = Thread.CurrentThread.CurrentCulture.Name;
            var ob = new CustomClasses.SpecialOffersClass().retriveOffer(_context, id ?? 0);
            //ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name");
            ViewData["Provider"] = (lang.StartsWith("ar")) ? new SelectList(_context.SystemUsers.Where(o => o.AccountType == "Provider" && o.Status == "A"), "Id", "Name", ob.Provider) : new SelectList(_context.SystemUsers.Where(o => o.AccountType == "Provider" && o.Status == "A"), "Id", "EnName", ob.Provider);
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name", (ob?.ActivityNo != null) ? ob.ActivityNo : 0);
            if (ob.Provider != null && ob.Provider >= 1) ob.SysProvider = (lang.StartsWith("ar")) ? _context.SystemUsers.Where(o => o.Id == ob.Provider).FirstOrDefault().Name : _context.SystemUsers.Where(o => o.Id == ob.Provider).FirstOrDefault().EnName;
            return View(ob);
        }

        /// <summary>
        /// يعرض صفحة إنشاء عرض خاص جديد.
        /// </summary>
        public IActionResult Create()
        {
            var lang = Thread.CurrentThread.CurrentCulture.Name;
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name");
            ViewData["Provider"] = (lang.StartsWith("ar")) ? new SelectList(_context.SystemUsers.Where(o => o.AccountType == "Provider" && o.Status == "A"), "Id", "Name") : new SelectList(_context.SystemUsers.Where(o => o.AccountType == "Provider" && o.Status == "A"), "Id", "EnName");
            //ViewData["Emloyee"] = new SelectList(_context.SystemUsers.Where(p => p.AccountType == "Employee"), "AspId", "Name");
            return View();
        }

        /// <summary>
        /// يستقبل بيانات العرض الجديد، يحفظها في قاعدة البيانات، ويرسل إشعارًا للمستخدمين.
        /// </summary>
        /// <param name="model">بيانات العرض الخاص الجديد القادمة من النموذج.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CModels.SpecialOffers model)
        {
            try
            {
                if (!ModelState.IsValid) return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });

                if (model.Provider == null && string.IsNullOrEmpty(model.ProviderName))
                    return Ok(new { state = 0, message = "يجب تحديد مقدم الخصم" });
                if (model.Provider == null)
                    if (string.IsNullOrEmpty(model.ProviderName) || string.IsNullOrEmpty(model.ProviderName))
                        return Ok(new { state = 0, message = "يجب كتابة اسم المتجر عربي وانجليزي " });
                if (model.Img != null)
                {
                    model.imgLink = HandleImages.SaveImage(model.Img, "images", _hosting);
                }
                if (await new CustomClasses.SpecialOffersClass().insert(_context, model, _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id))
                {
                    try
                    {
                        //await this.fire_notification.SendNotification(model.OfferName, model.Details, null, "News");
                    }
                    catch (Exception)
                    {
                        //throw; do nothing
                    }

                    return Ok(new { state = 7, message = "تمت الاضافة بنجاح", Url = "Index" });
                }
                if (model.imgLink != null)
                    HandleImages.RemoveImageRoot(model.imgLink, "images", _hosting);
                return Ok(new { state = 0, message = _localization["errorwillsaving"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorwillsaving"].Value });
            }
            //string url = HandleImages.SaveImage(model.Logo, "Images", _hosting);


        }

        /// <summary>
        /// يستقبل البيانات المعدّلة للعرض الخاص ويقوم بتحديثها في قاعدة البيانات.
        /// </summary>
        /// <param name="model">بيانات العرض الخاص المحدثة.</param>
        [HttpPost]
        public async Task<IActionResult> Edit(CModels.SpecialOffers model)
        {
            try
            {

                if (model.Id == null) return Ok(new { state = 0, message = _localization["selectoffer"].Value });
                if (!ModelState.IsValid) return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });
                if (await new CustomClasses.SpecialOffersClass().Edit(_context, model, _hosting)) return Ok(new { state = 7, message = _localization["modefiedsuccessfuly"].Value, Url = "/SpecialOffers/Index" });
                return Ok(new { state = 0, message = _localization["errorwillsditing"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorwillsditing"].Value });
            }

        }

        /// <summary>
        /// يقوم بإيقاف (تعطيل) عرض خاص.
        /// </summary>
        /// <param name="model">نموذج يحتوي على معرّف العرض المطلوب إيقافه.</param>
        [HttpPost]
        public async Task<IActionResult> Disable(RemoveModel model)
        {
            try
            {

                if (model.id == null) return Ok(new { state = 0, message = _localization["selectoffer"].Value });

                if (await new CustomClasses.SpecialOffersClass().disable(_context, model.id ?? 0, false))
                    return Ok(new { state = 1, message = _localization["opsuccess"].Value });
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }

        }

        /// <summary>
        /// يقوم بإعادة تفعيل عرض خاص كان موقوفًا.
        /// </summary>
        /// <param name="id">معرّف العرض المطلوب تفعيله.</param>
        [HttpPost]
        public async Task<IActionResult> Activate(long? id)
        {
            try
            {

                if (id == null) return Ok(new { state = 0, message = _localization["selectoffer"].Value });

                if (await new CustomClasses.SpecialOffersClass().disable(_context, id ?? 0, true)) return Ok(new { state = 1, message = _localization["opsuccess"].Value });
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }

        }

        /// <summary>
        /// يقوم بحذف عرض خاص بشكل نهائي من قاعدة البيانات.
        /// </summary>
        /// <param name="id">معرّف العرض المطلوب حذفه.</param>
        [HttpPost]
        public async Task<IActionResult> Delete(long? id)
        {
            try
            {

                if (id == null) return Ok(new { state = 0, message = _localization["selectoffer"].Value });

                if (await new CustomClasses.SpecialOffersClass().delete(_context, id ?? 0))
                    return Ok(new { state = 1, message = _localization["opsuccess"].Value });
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }

        }

    }
}