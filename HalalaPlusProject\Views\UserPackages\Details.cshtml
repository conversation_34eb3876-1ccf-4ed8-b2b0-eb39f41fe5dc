﻿@model HalalaPlusProject.Models.Package

@{
    ViewData["Title"] = "تفاصيل";
}

<h1>تفاصيل</h1>

<div>
    <h4>الباقة</h4>
    <hr />
    <dl class="row">
        <div class="col-md-4">
            <dt>الاسم</dt>
            <dd>@Html.DisplayFor(model => model.Name)</dd>
            <dt>الرسومات</dt>
            <dd>@Html.DisplayFor(model => model.Characters)</dd>
            <dt>السعر</dt>
            <dd>@Html.DisplayFor(model => model.Price)</dd>
            <dt>الفترة</dt>
            <dd>@Html.DisplayFor(model => model.Period)</dd>
        </div>
        <div class="col-md-4">
            <dt>الحالة</dt>
            <dd>@Html.DisplayFor(model => model.State)</dd>
            <dt>نشط</dt>
            <dd>
                <select asp-for="IsActive">
                    <option value="true">نشط</option>
                    <option value="false">غير نشط</option>
                </select>
            </dd>
            <dt>تاريخ الإنشاء</dt>
            <dd>@Html.DisplayFor(model => model.CreateOn)</dd>
        </div>
        <div class="col-md-4">
            <dt>أيام الباقة</dt>
            <dd>@Html.DisplayFor(model => model.PackageDays)</dd>
        </div>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id">تعديل</a> |
    <a asp-action="Index">الرجوع إلى القائمة</a>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
