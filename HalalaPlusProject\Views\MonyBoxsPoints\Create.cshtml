﻿@model HalalaPlusProject.Models.MonyBoxsPoint

@{
    ViewData["Title"] = "إنشاء";
}

<h1>إنشاء</h1>

<h4>النقاط</h4>
<hr />
<div class="row">
    <div class="col-md-12">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="FromAmount" class="control-label">من القيمة</label>
                        <input asp-for="FromAmount" class="form-control" />
                        <span asp-validation-for="FromAmount" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ToAmount" class="control-label">إلى القيمة</label>
                        <input asp-for="ToAmount" class="form-control" />
                        <span asp-validation-for="ToAmount" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label asp-for="GrntedPoints" class="control-label">النقاط الممنوحة</label>
                        <input asp-for="GrntedPoints" class="form-control" />
                        <span asp-validation-for="GrntedPoints" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="CreateAt" class="control-label">تاريخ الإنشاء</label>
                        <input asp-for="CreateAt" class="form-control" />
                        <span asp-validation-for="CreateAt" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-check">
                        <label class="form-check-label">
                            <input class="form-check-input" asp-for="Deleted" /> محذوف
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <input type="submit" value="إنشاء" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">العودة إلى القائمة</a>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
