﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class MasajedDiscountView
{
    public long Id { get; set; }

    [Column("rate")]
    public double? Rate { get; set; }

    [Column("expire_at")]
    public DateOnly? ExpireAt { get; set; }

    [Column("code")]
    [StringLength(250)]
    public string? Code { get; set; }

    [Column("store_name")]
    [StringLength(500)]
    public string? StoreName { get; set; }

    [Column("category")]
    [StringLength(500)]
    public string? Category { get; set; }

    [Column("latitude")]
    [StringLength(50)]
    public string? Latitude { get; set; }

    [Column("longitude")]
    [StringLength(50)]
    public string? Longitude { get; set; }

    [StringLength(250)]
    public string? City { get; set; }
}
