﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class GetUsersWithActivitiesAndNationality
{
    [StringLength(500)]
    public string? Name { get; set; }

    public long Id { get; set; }

    [StringLength(100)]
    public string? IdentityNo { get; set; }

    [StringLength(50)]
    public string? PhoneNo { get; set; }

    [StringLength(50)]
    public string? Email { get; set; }

    [StringLength(1500)]
    public string? EnName { get; set; }

    [StringLength(20)]
    public string? AccountType { get; set; }

    [StringLength(256)]
    public string? UserName { get; set; }

    [StringLength(250)]
    public string? Nationality { get; set; }

    [StringLength(500)]
    public string? ActName { get; set; }

    [StringLength(250)]
    public string? ActEnName { get; set; }
}
