﻿using HalalaPlusProject.Areas.Identity.Data;
using HalalaPlusProject.CModels;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.HalalaClass;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace HalalaPlusProject.Controllers
{
    public class EventsPartecipntsController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IStringLocalizer<BusinessEmpController> _localization;
        private readonly UserManager<HalalaPlusProjectUser> _userManager;
        private readonly IConfiguration _configuration;
        public EventsPartecipntsController(HalalaPlusdbContext context, IConfiguration _configuration, UserManager<HalalaPlusProjectUser> userManager, IStringLocalizer<BusinessEmpController> _localization)
        {
            _context = context;
            _userManager = userManager;
            this._localization = _localization;
            this._configuration = _configuration;

            //_emailStore = emailStore;
        }

        /// <summary>
        /// عرض قائمة الموظفين المرتبطين بنشاط تجاري معين.
        /// </summary>
        /// <param name="id">معرف النشاط التجاري لعرض موظفيه. هذا الحقل اختياري.</param>
        /// <returns>عرض يحتوي على قائمة الموظفين.</returns>
        public IActionResult Index(int? id)
        {
            

                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value;
                var temp1 = _context.SystemUsers.Where(o => o.AspId == userId).FirstOrDefault();
                var temp = _context.SystemUsers.Where(o => o.OrgId == temp1.Id)
                .ToList().OrderByDescending(o => o.Id); ViewData["id"] = temp1.Id;
                return View(temp);
           

        }

        /// <summary>
        /// إعادة إرسال رسالة نصية تحتوي على رقم العضوية إلى مستخدم معين.
        /// </summary>
        /// <param name="model">البيانات التي تحتوي على معرف المستخدم لإعادة إرسال الرسالة إليه.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل عملية الإرسال.</returns>
        public IActionResult SendSingleMessage(EmpResend model)
        {
            try
            {
                var key = _configuration["SmsToken"];

                var emp = _context.SystemUsers.Find(model.Id);
                if (emp == null)
                {
                    return Ok(new { state = 0, message = "حدث خطاء" });
                }
                var t = _context.AspNetUsers.FirstOrDefault(o => o.Id == emp.AspId);

                var company = _context.SystemUsers.FirstOrDefault(o => o.Id == emp.OrgId);
                string phone = (t.PhoneNumber.StartsWith("9665")) ? t.PhoneNumber.Substring(3) : (t.PhoneNumber.StartsWith("05")) ? t.PhoneNumber.Substring(1) : t.PhoneNumber;
                new SMSHandler().SendHelloSMS(key, phone, string.Format(_localization["businessWelmsg"].Value, company.Name, t.MemberNo), "", -1);
                return Ok(new { state = 1, message = "تم ارسال الرسالة" });

            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = "حدث خطاء" });
            }

        }

        /// <summary>
        /// إعادة إرسال رسالة نصية تحتوي على رقم العضوية إلى مستخدم معين.
        /// </summary>
        /// <param name="model">البيانات التي تحتوي على معرف المستخدم لإعادة إرسال الرسالة إليه.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل عملية الإرسال.</returns>
        public IActionResult SendBulkMessage(EmpResend model)
        {
            try
            {
                var key = _configuration["SmsToken"];

                var com = _context.SystemUsers.Find(model.Id);
                if (com == null)
                {
                    return Ok(new { state = 0, message = "حدث خطاء" });
                }

                var emp = _context.SystemUsers.Where(o => o.OrgId == com.Id).ToList();
                foreach (var t in emp)
                {
                    var ss = _context.AspNetUsers.FirstOrDefault(o => o.Id == t.AspId);

                    string phone = (t.PhoneNo.StartsWith("9665")) ? t.PhoneNo.Substring(3) : (t.PhoneNo.StartsWith("05")) ? t.PhoneNo.Substring(1) : t.PhoneNo;
                    new SMSHandler().SendHelloSMS(key, phone, string.Format(_localization["businessWelmsg"].Value, com.Name, ss.MemberNo), "", -1);

                }
                return Ok(new { state = 1, message = "تم ارسال الرسالة" });

            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = "حدث خطاء" });
            }

        }

        /// <summary>
        /// إعادة إرسال رسالة نصية تحتوي على رقم العضوية إلى مستخدم معين.
        /// </summary>
        /// <param name="model">البيانات التي تحتوي على معرف المستخدم لإعادة إرسال الرسالة إليه.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل عملية الإرسال.</returns>
        public IActionResult SendSinglelongMessage(EmpResend model)
        {
            try
            {
                var key = _configuration["SmsToken"];

                var emp = _context.SystemUsers.Find(model.Id);
                if (emp == null)
                {
                    return Ok(new { state = 0, message = "حدث خطاء" });
                }
                var t = _context.AspNetUsers.FirstOrDefault(o => o.Id == emp.AspId);
                var tee = "عزيزي الموظف نود إعلامك بأنه تم اشتراكك في خدمة هلله بلس ضمن مبادرات ({0}) لدعم الموظفين، والتي تتيح لك الاستفادة من عروض وخصومات حصرية على مجموعة واسعة من المتاجر والخدمات. رقم اشتراكك: [{1}]للاستفادة من الخصومات، يُرجى تحميل تطبيق هلله بلس من الروابط التالية: 📱 لأجهزة أندرويد: [قريبا Google Play] الموقع الإلكتروني (https://www.halalaplus.com/) 📱 لأجهزة آيفون: [https://apps.apple.com/app/id6670226235] نأمل لك تجربة ممتعة ومليئة بالفوائد. مع أطيب التحيات،";


                var company = _context.SystemUsers.FirstOrDefault(o => o.Id == emp.OrgId);
                string phone = (t.PhoneNumber.StartsWith("9665")) ? t.PhoneNumber.Substring(3) : (t.PhoneNumber.StartsWith("05")) ? t.PhoneNumber.Substring(1) : t.PhoneNumber;
                new SMSHandler().SendHelloSMS(key, phone, string.Format(_localization["businessWellongmsg"].Value, company.Name, t.MemberNo), "", -1);
                return Ok(new { state = 1, message = "تم ارسال الرسالة" });

            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = "حدث خطاء" });
            }

        }
        /// <summary>
        /// إعادة إرسال رسالة نصية تحتوي على رقم العضوية إلى مستخدم معين.
        /// </summary>
        /// <param name="model">البيانات التي تحتوي على معرف المستخدم لإعادة إرسال الرسالة إليه.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل عملية الإرسال.</returns>
        public IActionResult Displaymessage(EmpResend model)
        {
            try
            {
                if (model.Id == 0)
                {
                    var msg = string.Format(_localization["businessWelmsg"].Value, "اسم الشركة", "رقم الاشتراك");
                    return Ok(new { state = 1, message = msg });
                }
                else
                {
                    var msg = string.Format(_localization["businessWellongmsg"].Value, "اسم الشركة", "رقم الاشتراك");
                    return Ok(new { state = 1, message = msg });

                }
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = "حدث خطاء" });
            }

        }



        /// <summary>
        /// إعادة إرسال رسالة نصية تحتوي على رقم العضوية إلى مستخدم معين.
        /// </summary>
        /// <param name="model">البيانات التي تحتوي على معرف المستخدم لإعادة إرسال الرسالة إليه.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل عملية الإرسال.</returns>
        public IActionResult SendBulklongMessage(EmpResend model)
        {
            try
            {
                var key = _configuration["SmsToken"];

                var com = _context.SystemUsers.Find(model.Id);
                if (com == null)
                {
                    return Ok(new { state = 0, message = "حدث خطاء" });
                }

                var emp = _context.SystemUsers.Where(o => o.OrgId == com.Id).ToList();
                foreach (var t in emp)
                {
                    var ss = _context.AspNetUsers.FirstOrDefault(o => o.Id == t.AspId);

                    string phone = (t.PhoneNo.StartsWith("9665")) ? t.PhoneNo.Substring(3) : (t.PhoneNo.StartsWith("05")) ? t.PhoneNo.Substring(1) : t.PhoneNo;
                    new SMSHandler().SendHelloSMS(key, phone, string.Format(_localization["businessWellongmsg"].Value, com.Name, ss.MemberNo), "", -1);

                }
                return Ok(new { state = 1, message = "تم ارسال الرسالة" });

            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = "حدث خطاء" });
            }

        }




        /// <summary>
        /// عرض نموذج إنشاء موظف جديد لنشاط تجاري.
        /// </summary>
        /// <param name="id">معرف النشاط التجاري الذي سينتمي إليه هذا الموظف.</param>
        /// <returns>عرض يحتوي على نموذج إنشاء الموظف.</returns>
        public IActionResult Create(int id)
        {
            //ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality");
            ViewData["id"] = id;
            return View();
        }

        /// <summary>
        /// معالجة عملية إنشاء موظف جديد. يتم إنشاء حساب مستخدم وربطه بالنشاط التجاري.
        /// </summary>
        /// <param name="model">بيانات الموظف الجديد.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة الموظفين؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CModels.BusineesEmployeesModel model)
        {
            if (ModelState.IsValid)
            {
                var userExists = await _userManager.FindByNameAsync(model.PhoneNo);
                if (userExists != null)
                {
                    //ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality", model.Nationality);
                    ModelState.AddModelError(string.Empty, _localization["emailused"].Value);
                    return View(model);
                }
                HalalaPlusProjectUser user = new()
                {
                    //Email = model.Email,
                    MemberNo = new RandomNumberClass().GenerateUniqueRandomNumber(_context),
                    SecurityStamp = Guid.NewGuid().ToString(),
                    PhoneNumber = model.PhoneNo,
                    UserName = model.PhoneNo,
                    FullName = model.Name
                };

                var result = await _userManager.CreateAsync(user);
                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(user, "BusinessEmp");
                    try
                    {
                        EmployeeClass ob = new EmployeeClass(_context);


                        //var temp1 = _context.SystemUsers.Where(o => o.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).FirstOrDefault();
                        if (!await ob.InsertBusnissEmp(model, user, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value, model.Id ?? 0))
                        {
                            await _userManager.DeleteAsync(user);
                            ModelState.AddModelError(string.Empty, _localization["errorwillsaving"].Value);
                            //ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality", model.Nationality);
                            return View(model);
                        }
                        user.EmailConfirmed = true;

                        await _userManager.UpdateAsync(user);
                        var key = _configuration["SmsToken"];

                        var company = _context.SystemUsers.Find(model.Id);
                        new SMSHandler().SendHelloSMS(key, model.PhoneNo, string.Format(_localization["businessWelmsg"].Value, company.Name, user.MemberNo), "", -1);

                        return RedirectToAction(nameof(Index), new { id = model.Id });
                    }
                    catch (Exception ex)
                    {
                        await _userManager.DeleteAsync(user);
                        ModelState.AddModelError(string.Empty, _localization["errorwillsaving"].Value);
                        //ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality", model.Nationality);
                        return View(model);
                    }
                }

            }
            ModelState.AddModelError(string.Empty, _localization["errorwillsaving"].Value);
            //ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality", model.Nationality);
            return View(model);
        }

        /// <summary>
        /// عرض المعلومات التفصيلية لموظف معين.
        /// </summary>
        /// <param name="id">معرف الموظف المراد عرض تفاصيله.</param>
        /// <returns>عرض يحتوي على تفاصيل الموظف، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Details(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }
            var systemUser = new UsersClass().retriveUser(id ?? 0, _context);
            if (systemUser == null)
            {
                return NotFound();
            }
            return View(systemUser);
        }

        /// <summary>
        /// عرض نموذج تعديل بيانات موظف حالي.
        /// </summary>
        /// <param name="id">معرف الموظف المراد تعديله.</param>
        /// <returns>عرض يحتوي على بيانات الموظف في نموذج التعديل، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Edit(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }
            var systemUser = new UsersClass().retriveUserEmployee(id ?? 0, _context);
            if (systemUser == null)
            {
                return NotFound();
            }
            ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality", systemUser.Nationality);
            return View(systemUser);
        }

        /// <summary>
        /// معالجة التعديلات المقدمة لملف تعريف موظف.
        /// </summary>
        /// <param name="model">بيانات الموظف المحدثة.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة الموظفين؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(CModels.BusineesEmployeesModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var ob = _context.SystemUsers.Find(model.Id);
                    if (ob != null)
                        if (_context.AspNetUsers.Where(p => (p.Email == model.PhoneNo || p.UserName == model.PhoneNo) && p.Id != ob.AspId).Count() > 0)
                            return View(model);


                    ob.Name = model.Name;
                    ob.PhoneNo = model.PhoneNo;
                    //ob.Email = model.Email;
                    //ob.Salary = model.Salary;
                    //ob.BirthDate = DateOnly.Parse(model.BirthDate.ToString());
                    //ob.IdentityNo = model.IdentityNo;
                    //ob.Salary = model.Salary;

                    //ob.Nationality = model.Nationality;
                    _context.Update(ob);

                    var userExists = await _userManager.FindByNameAsync(model.PhoneNo);
                    //userExists.Email = model.Email;
                    //userExists.UserName = model.UserName;
                    await _userManager.UpdateAsync(userExists);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    return View(model);
                }
                return RedirectToAction(nameof(Index));
            }

            //ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality", model.Nationality);
            return View(model);
        }

        /// <summary>
        /// عرض صفحة تأكيد حذف موظف.
        /// </summary>
        /// <param name="id">معرف الموظف المراد حذفه.</param>
        /// <returns>عرض يحتوي على تفاصيل الموظف لتأكيد الحذف، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Delete(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }

            var systemUser = new UsersClass().retriveUser(id ?? 0, _context);
            if (systemUser == null)
            {
                return NotFound();
            }
            return View(systemUser);
        }

        /// <summary>
        /// تنفيذ عملية حذف منطقي لموظف عن طريق تعيين علامة 'Deleted' إلى 'true'.
        /// </summary>
        /// <param name="id">معرف الموظف المراد حذفه.</param>
        /// <returns>بعد إتمام العملية بنجاح، يتم عرض صفحة قائمة الموظفين.</returns>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(long id)
        {
            if (_context.SystemUsers == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.SystemUsers'  is null.");
            }
            var systemUser = await _context.SystemUsers.FindAsync(id);
            if (systemUser != null)
            {
                systemUser.Deleted = true;
                _context.Update(systemUser);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// التحقق من وجود مستخدم في النظام بالمعرف المحدد.
        /// </summary>
        /// <param name="id">معرف المستخدم للتحقق منه.</param>
        /// <returns>إرجاع 'true' إذا كان المستخدم موجودًا، وإلا 'false'.</returns>
        private bool SystemUserExists(long id)
        {
            return (_context.SystemUsers?.Any(e => e.Id == id)).GetValueOrDefault();
        }


    }
}
