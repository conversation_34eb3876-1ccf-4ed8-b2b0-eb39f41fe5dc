﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("Category")]
public partial class Category
{
    [Key]
    public int Id { get; set; }

    [StringLength(150)]
    public string? Name { get; set; }

    [StringLength(50)]
    public string? EngName { get; set; }

    [StringLength(20)]
    public string? CategoryParentId { get; set; }

    [StringLength(250)]
    public string? AmazonImage { get; set; }

    public bool IsFromTwelve { get; set; }

    [StringLength(100)]
    public string? CatgoryId { get; set; }

    public bool IsSandBox { get; set; }

    [InverseProperty("Category")]
    public virtual ICollection<CatgoriesDetail> CatgoriesDetails { get; set; } = new List<CatgoriesDetail>();

    [InverseProperty("CatagoryNavigation")]
    public virtual ICollection<Product> Products { get; set; } = new List<Product>();
}
