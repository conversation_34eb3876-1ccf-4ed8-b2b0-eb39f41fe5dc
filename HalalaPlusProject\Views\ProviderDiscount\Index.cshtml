﻿
@model HalalaPlusProject.CModels.DiscountsProviderList
@using System.Globalization
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer


<h3>@localizer["discounts"]</h3>
<div class="tab">
    <button class="tablinks" onclick="openCity(event, 'AddDiscountTab')">@localizer["adddiscount"]</button>

    <button class="tablinks" onclick="openCity(event, 'DiscountsTab')">@localizer["alldiscount"] </button>

</div>
<div id="AddDiscountTab" class="tabcontent"  style="display:block">
 <div class="row">

    
    <div class="col-md-12">
            <form asp-controller="ProviderDiscount" asp-action="AddEditDiscount" class="submitfm" id="employeeForm">
               
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" name="Id" asp-for="Id" />
            
             <div class="row">
                    <div class="col-md-3">

                        <div class="form-group">
                            <label asp-for="EnDiscountName" class="control-label">
                                @localizer["EnDiscountName"]<span class="  required-star">*</span>
                            </label>
                            <input asp-for="EnDiscountName" class="form-control" id="EnDiscountName" maxlength="100" />
                            <span asp-validation-for="EnDiscountName" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="DiscountName" class="control-label">
                                @localizer["discountname"]<span class="  required-star">*</span>
                            </label>
                            <input asp-for="DiscountName" class="form-control" id="DiscountName" maxlength="100" />
                            <span asp-validation-for="DiscountName" class="text-danger"></span>
                        </div>

                      <div class="form-group">
                            <label asp-for="Discount" class="control-label">@localizer["discount"]  <span class="text-danger required-star">*</span></label>
                            <input asp-for="Discount" required class="form-control"   />
                        <span asp-validation-for="Discount"  class="text-danger"></span>
                    </div>



                 </div>
                  <div class="col-md-3">
                    <div class="form-group">
                            <label asp-for="StartDate" class="control-label">@localizer["startdate"]  <span class="text-danger required-star">*</span></label>
                            <input asp-for="StartDate" type="date" class="form-control" id="StartDate" />
                        <span asp-validation-for="StartDate"  class="text-danger"></span>
                    </div>
                      <div class="form-group">
                            <label asp-for="EndDate" class="control-label">@localizer["enddate"]  <span class="text-danger required-star">*</span></label>
                            <input asp-for="EndDate" type="date" class="form-control" id="EndDate" />
                        <span asp-validation-for="EndDate"  class="text-danger"></span>
                    </div>
                    </div>
                     <div class="col-md-3">
                            <div class="form-group">
                <label asp-for="GrantType" class="control-label"></label>
                <select asp-for="GrantType" required name="GrantType"  class ="form-select" >
                                <option value="1">@localizer["onlydiscount"]</option>
                                <option value="2">@localizer["onlypoints"]</option>
                                <option value="3">@localizer["discountandypoints"]</option>

                </select>           
                  <span asp-validation-for="GrantType" class="text-danger"></span>
            </div>
                        <div class="form-group">
                            <label asp-for="Conditions" class="control-label">@localizer["conditions"] </label>
                            <textarea asp-for="Conditions" name="Conditions" class="form-control" maxlength="500" oninput="updateCounter(this)"></textarea>
                            <span asp-validation-for="Conditions" class="text-danger"></span>
                            <small id="conditionsCounter" class="form-text text-muted">@localizer["charactersremaining"]</small>
                        </div>
                 </div>
                 </div>
                 <div class="form-group mt-2">
                    <button type="submit" value="Save" class="btn btn-primary">@localizer["save"]</button>
            </div>
            </form>
            </div>
           
            </div>
</div>

<div id="DiscountsTab" class="tabcontent" >
 <div class="row">

    
    <div class="col-md-12">

            @Html.Partial("_ProviderDisc",Model.ListDis)
</div>
</div>
</div>
 
 
@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        $(function () {
            function validateDates() {
                var startVal = $("#StartDate").val();
                var endVal = $("#EndDate").val();
                var endError = $("#EndDate").siblings("span.text-danger");

                endError.text(""); 

                if (startVal && endVal) {
                    var start = new Date(startVal);
                    var end = new Date(endVal);

                    if (end <= start) {
                        endError.text("تاريخ النهاية يجب أن يكون بعد تاريخ البداية");
                        return false;
                    }
                }
                return true;
            }

            $("#StartDate, #EndDate").on("blur change", function () {
                validateDates();
            });

            $("#employeeForm").on("submit", function (e) {
                if (!validateDates()) { 
                    e.preventDefault();
                }
            });
        });
    </script>


    <script>
        $(function () {
            function validateMaxLength(input, maxLength) {
                var value = $(input).val();
                var errorSpan = $(input).siblings("span.text-danger");

                errorSpan.text("");  

                if (value.length > maxLength) {
                    errorSpan.text("الحد الأقصى لعدد الحروف هو " + maxLength);
                    return false;
                }
                return true;
            }

            $("#EnDiscountName, #DiscountName").on("input blur", function () {
                validateMaxLength(this, 100);
            });

            $("#employeeForm").on("submit", function (e) {
                var valid1 = validateMaxLength("#EnDiscountName", 100);
                var valid2 = validateMaxLength("#DiscountName", 100);

                if (!valid1 || !valid2) {
                    e.preventDefault();
                }
            });
        });
         function updateCounter(textarea) {
            var max = textarea.getAttribute("maxlength");
            var length = textarea.value.length;
            var remaining = max - length;
            document.getElementById("conditionsCounter").innerText = "متبقي " + remaining + " حرف";
        }
    </script>
}

 