﻿@model HalalaPlusProject.CModels.Agents
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] =localizer["create"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}


  <div>
        <a  asp-action="Index"><img src="../assets/img/svgs/solid/arrow-right.svg" style="width: 30px;" alt=""></a>
    <h3>@localizer["addagent"]</h3>

      </div>
<div class="row">
    <div class="offset-md-2 col-md-8 col-sm-12">
        <form asp-action="Create" class="submitfm">
            <div class="row">
                  <div asp-validation-summary="ModelOnly" class="text-danger"></div>
             </div>
             
         <div class="row">
           <div class="col-md-5">
          <div class="form-group">
                        <label asp-for="Name" class="control-label">@localizer["name"]</label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" required class="text-danger"></span>
            </div>
            
            <div class="form-group">
                        <label asp-for="Nationality" class="control-label">@localizer["nationality"]</label>
                 <select asp-for="Nationality" required class ="form-select" asp-items="ViewBag.National"></select>
                  <span asp-validation-for="Nationality" required class="text-danger"></span>
            </div>

              <div class="form-group">
                        <label asp-for="IdentityNo" class="control-label">@localizer["identityno"]</label>
                <input asp-for="IdentityNo" class="form-control" />
                <span asp-validation-for="IdentityNo" class="text-danger"></span>
            </div>
            <div class="form-group">
                        <label asp-for="BirthDate" class="control-label"> @localizer["birthdate"]</label>
                <input asp-for="BirthDate" type="date" class="form-control" />
                <span asp-validation-for="BirthDate" class="text-danger"></span>
            </div>
              <div class="form-group">
                        <label asp-for="AccountNo" class="control-label"> @localizer["accountno"] </label>
                <input asp-for="AccountNo"  class="form-control" />
                <span asp-validation-for="AccountNo" class="text-danger"></span>
            </div>
        

          </div>

           <div class="col-md-5">
     
         <div class="form-group">
                        <label asp-for="PhoneNo" class="control-label">@localizer["phoneno"]</label>
                <input asp-for="PhoneNo" class="form-control" />
                <span asp-validation-for="PhoneNo" class="text-danger"></span>
            </div>
            <div class="form-group">
                        <label asp-for="Email" class="control-label"> @localizer["mail"]</label>
                <input asp-for="Email" class="form-control" />
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>
              <div class="form-group">
                        <label asp-for="UserName" class="control-label">@localizer["username"]</label>
                <input asp-for="UserName" class="form-control" />
                <span asp-validation-for="UserName" class="text-danger"></span>
            </div>
              <div class="form-group">
                        <label asp-for="Password" class="control-label"> @localizer["password"]</label>
                <input asp-for="Password" class="form-control" />
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>
            

          </div>
             </div>

           
           
           
            
           
          
       
       
            <div class="form-group">
                <button type="submit" value="Create" class="btn btn-primary">@localizer["create"]</button>
            </div>
        </form>
    </div>
</div>



@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
