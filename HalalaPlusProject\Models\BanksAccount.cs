﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("BanksAccount")]
public partial class BanksAccount
{
    [Key]
    public int Id { get; set; }

    [StringLength(150)]
    public string? BankName { get; set; }

    [StringLength(500)]
    public string? ConnectionCode { get; set; }

    [StringLength(150)]
    public string? AccountNumber { get; set; }

    [Column("target")]
    [StringLength(50)]
    public string Target { get; set; } = null!;

    [StringLength(250)]
    public string? Icon { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateOn { get; set; }

    [Column("state")]
    [StringLength(50)]
    public string State { get; set; } = null!;

    [Column("active")]
    public bool Active { get; set; }

    [Column("deleted")]
    public bool Deleted { get; set; }

    [StringLength(150)]
    public string? EnBankName { get; set; }

    [InverseProperty("Bank")]
    public virtual ICollection<ConnectBanksOtpCode> ConnectBanksOtpCodes { get; set; } = new List<ConnectBanksOtpCode>();

    [InverseProperty("Bank")]
    public virtual ICollection<ConsentHistory> ConsentHistories { get; set; } = new List<ConsentHistory>();

    [InverseProperty("Bank")]
    public virtual ICollection<UserBanksAccount> UserBanksAccounts { get; set; } = new List<UserBanksAccount>();
}
