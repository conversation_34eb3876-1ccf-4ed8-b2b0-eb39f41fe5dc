﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Reporting.NETCore;
using System;
using System.IO;
using System.IO.Compression;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using System.Text;
using HalalaPlusProject.Models;
// Add these for async operations
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using HalalaPlusProject.Utils;

namespace HalalaPlusApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CardReportsApiController : ControllerBase
    {
        private readonly IWebHostEnvironment _env;
        private readonly HalalaPlusdbContext _context;
        private readonly ILogger<CardReportsApiController> _logger;

        public CardReportsApiController(HalalaPlusdbContext context, ILogger<CardReportsApiController> logger, IWebHostEnvironment env)
        {
            _context = context;
            _logger = logger;
            _env = env;
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
        }


        [HttpGet("DownloadCard/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult DownloadCard(long id)
        {
            try
            {
                var user = _context.SystemUsers.Find(id);
                if (user == null)
                {
                    _logger.LogWarning($"Card generation failed: User with ID {id} not found.");
                    return NotFound($"User with ID {id} not found.");
                }

                var organization = _context.SystemUsers.Find(user.OrgId);

                var members = new[]
                {
                    new Member {
                        Organization = organization?.Name ?? "N/A",
                        MemberNo = user.Id.ToString(),
                        Name = user.Name,
                    }
                };

                _logger.LogInformation($"Starting card generation for user: {user.Name} (ID: {id})");
                string fileName = user.Name.Replace(" ", "_"); // Sanitize filename
                fileName = fileName.Replace('.', '-');

                var rdlcPath = Path.Combine(_env.ContentRootPath, "Resources", "MembershipCard.rdlc");

                var report = new LocalReport();
                report.LoadReportDefinition(System.IO.File.OpenRead(rdlcPath));
                report.EnableExternalImages = true;

                report.DataSources.Add(new ReportDataSource("MembershipCardData", members));

                string companyImageUrl;
                //string halalaImageUrl = $"{Request.Scheme}://{Request.Host}/halalaicon.png";
                string halalaImageUrl = $"https://admin.halalaplus.com/halalaicon.png";
                if (organization.Providerimages != null)
                {
                    companyImageUrl = $"https://admin.halalaplus.com/images/" + (organization.Providerimages);
                    // companyImageUrl = $"{Request.Scheme}://{Request.Host}/images/" + (organization.Providerimages);
                }
                else
                {
                    companyImageUrl = (halalaImageUrl);
                }
                _logger.LogInformation("print card rport halalaImageUrl  " + halalaImageUrl);
                _logger.LogInformation("print card rport companyImageUrl  " + companyImageUrl);

                // Add report parameter
                var parameters = new[]
                {
                           new ReportParameter("IconPath", halalaImageUrl),
                           new ReportParameter("CompanyIconPath", companyImageUrl)
                        };
                report.SetParameters(parameters);

                // You can add parameters here if your RDLC requires them
                // var parameters = new[] { ... };
                // report.SetParameters(parameters);

                byte[] pdf = report.Render("PDF");
                _logger.LogInformation($"Successfully rendered PDF for user: {user.Name}");

                string fileNameFormat = $"{fileName}.pdf";
                return File(pdf, "application/pdf", fileNameFormat);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"An error occurred while generating card for ID {id}.");
                return StatusCode(500, "An internal server error occurred. Please check the logs.");
            }
        }



        [HttpGet("GenerateZipOfCards/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GenerateZipOfCardsAsync(long id, CancellationToken cancellationToken)
        {
            // The File() result needs to take ownership to dispose of it later.
            MemoryStream memoryStream = null;
            try
            {
                var org = await _context.SystemUsers.FindAsync(new object[] { id }, cancellationToken);
                if (org == null)
                {
                    _logger.LogWarning($"Zip generation failed: Organization with ID {id} not found.");
                    return NotFound($"Organization with ID {id} not found.");
                }

                var tempdata = await _context.SystemUsers
                                             .Where(op => op.OrgId == id)
                                             .ToListAsync(cancellationToken);

                var members = tempdata.Select(member => new Member
                {
                    Organization = org.Name ?? org.Id.ToString(),
                    MemberNo = member.Id.ToString(),
                    Name = member.Name ?? member.Id.ToString(),
                    FileName = member.Name ?? member.Id.ToString()
                }).ToList();

                _logger.LogInformation($"Starting async zip generation for {members.Count} members.");

                var rdlcPath = Path.Combine(_env.ContentRootPath, "Resources", "MembershipCard.rdlc");
                byte[] reportDefinitionBytes = await System.IO.File.ReadAllBytesAsync(rdlcPath, cancellationToken);

                string companyImageUrl;
                string halalaImageUrl = $"https://admin.halalaplus.com/halalaicon.png";
                if (org.Providerimages != null)
                {
                    companyImageUrl = $"https://admin.halalaplus.com/images/" + (org.Providerimages);
                }
                else
                {
                    companyImageUrl = (halalaImageUrl);
                }

                var parameters = new[]
                {
                    new ReportParameter("IconPath", halalaImageUrl),
                    new ReportParameter("CompanyIconPath", companyImageUrl)
                };

                // Initialize the stream that we will return.
                memoryStream = new MemoryStream();

                // The 'using' on the archive is STILL ESSENTIAL.
                // It finalizes the zip file by writing the central directory when the block is exited.
                // The third parameter `true` tells it to leave the underlying memoryStream open.
                using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
                {
                    foreach (var member in members)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        var report = new LocalReport();
                        report.LoadReportDefinition(new MemoryStream(reportDefinitionBytes));
                        report.EnableExternalImages = true;
                        report.DataSources.Add(new ReportDataSource("MembershipCardData", new[] { member }));
                        report.SetParameters(parameters);

                        byte[] pdf = await Task.Run(() => report.Render("PDF"), cancellationToken);

                        string safeFileName = member.FileName.Replace('.', '_').Replace(' ', '_');
                        var zipEntry = archive.CreateEntry($"{safeFileName}.pdf");

                        using (var entryStream = zipEntry.Open())
                        {
                            await entryStream.WriteAsync(pdf, 0, pdf.Length, cancellationToken);
                        }
                    }
                } // The archive is finalized here, but memoryStream is still open and valid.

                // Reset the stream's position to the beginning for reading by the FileResult.
                memoryStream.Position = 0;

                string folderName = $"{org.Name.Replace(' ', '_')}_MembershipCards.zip";
                _logger.LogInformation("Successfully created zip archive.");

                // Return the FileStreamResult. It will now manage the stream's lifetime and dispose of it correctly.
                return File(memoryStream, "application/zip", folderName);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"Zip generation for organization ID {id} was cancelled.");
                // CRITICAL: We must manually dispose the stream if an exception occurs before returning it.
                await memoryStream.DisposeAsync();
                return StatusCode(499, "Request was cancelled by the client.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"An error occurred while generating the zip archive for organization ID {id}.");
                // CRITICAL: We must manually dispose the stream if an exception occurs before returning it.
                await memoryStream.DisposeAsync();
                return StatusCode(500, "An internal server error occurred. Please check the logs.");
            }
        }




        [HttpGet("GenerateZipOfCardsStreamedAsync/{id}")]
        public async Task GenerateZipOfCardsStreamedAsync(long id, CancellationToken cancellationToken)
        {
            try
            {
                var org = await _context.SystemUsers.FindAsync(new object[] { id }, cancellationToken);
                if (org == null)
                {
                    Response.StatusCode = 404;
                    await Response.WriteAsync($"Organization with ID {id} not found.", cancellationToken);
                    return;
                }

                // Prepare the HTTP response for streaming a zip file.
                string folderName = $"{org.Name.Replace(' ', '_')}_MembershipCards.zip";
                Response.ContentType = "application/zip";
                Response.Headers.Add("Content-Disposition", $"attachment; filename=\"{folderName}\"");

                var tempdata = await _context.SystemUsers
                                             .Where(op => op.OrgId == id)
                                             .ToListAsync(cancellationToken);

                var members = tempdata.Select(member => new Member
                {
                    Organization = org.Name ?? org.Id.ToString(),
                    MemberNo = member.Id.ToString(),
                    Name = member.Name ?? member.Id.ToString(),
                    FileName = member.Name ?? member.Id.ToString()
                }).ToList();

                _logger.LogInformation($"Starting STREAMED zip generation for {members.Count} members.");

                var rdlcPath = Path.Combine(_env.ContentRootPath, "Resources", "MembershipCard.rdlc");
                byte[] reportDefinitionBytes = await System.IO.File.ReadAllBytesAsync(rdlcPath, cancellationToken);

                string companyImageUrl;
                string halalaImageUrl = "https://admin.halalaplus.com/halalaicon.png";
                companyImageUrl = (org.Providerimages != null)
                    ? $"https://admin.halalaplus.com/images/{org.Providerimages}"
                    : halalaImageUrl;

                var parameters = new[]
                {
                    new ReportParameter("IconPath", halalaImageUrl),
                    new ReportParameter("CompanyIconPath", companyImageUrl)
                };

                // Create the ZipArchive directly on the response stream.
                // The `false` for leaveOpen is important: when the archive is disposed, it will close the response stream.
                using (var archive = new ZipArchive(Response.Body, ZipArchiveMode.Create, false))
                {
                    foreach (var member in members)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        var report = new LocalReport();
                        report.LoadReportDefinition(new MemoryStream(reportDefinitionBytes));
                        report.EnableExternalImages = true;
                        report.DataSources.Add(new ReportDataSource("MembershipCardData", new[] { member }));
                        report.SetParameters(parameters);

                        byte[] pdf = await Task.Run(() => report.Render("PDF"), cancellationToken);

                        string safeFileName = member.FileName.Replace('.', '_').Replace(' ', '_');
                        var zipEntry = archive.CreateEntry($"{safeFileName}.pdf", CompressionLevel.Optimal);

                        using (var entryStream = zipEntry.Open())
                        {
                            await entryStream.WriteAsync(pdf, 0, pdf.Length, cancellationToken);
                            // Flush the entry stream to the main response stream
                            await entryStream.FlushAsync(cancellationToken);
                        }

                        // Flush the response stream to send the chunk to the client immediately
                        await Response.Body.FlushAsync(cancellationToken);
                    }
                } // When this 'using' block ends, the archive is finalized and the response is completed.

                _logger.LogInformation("Successfully finished streaming zip archive.");
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"Zip generation stream for organization ID {id} was cancelled by the client.");
                // We cannot send a status code here because the response has already started.
                // The connection is simply closed.
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"A critical error occurred during the zip stream for organization ID {id}.");
                // We cannot send a status code here either. The client will receive a failed download.
            }
        }
        [HttpGet]
        [Route("ExportToExcel")]
        public IActionResult ExportToExcel(string search)
        {
            OfficeOpenXml.ExcelPackage.License.SetNonCommercialOrganization("halalplus");

            var payments = _context.PaymentsRequests
                .Include(p => p.User)
                .AsQueryable();

            if (!string.IsNullOrEmpty(search))
            {
                payments = payments.Where(p =>
                    p.User.Name.Contains(search) ||
                    p.User.PhoneNo.Contains(search));
            }

            var list = payments.ToList();

            using (var package = new OfficeOpenXml.ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("Payments");


                worksheet.Cells[1, 1].Value = "المستخدم";
                worksheet.Cells[1, 2].Value = "رقم الهاتف";
                worksheet.Cells[1, 3].Value = "المبلغ";
                worksheet.Cells[1, 4].Value = "نوع العملية";
                worksheet.Cells[1, 5].Value = "الحالة";
                worksheet.Cells[1, 6].Value = "تحقق";
                worksheet.Cells[1, 7].Value = "تاريخ الإنشاء";

                // البيانات
                int row = 2;
                foreach (var item in list)
                {

                    worksheet.Cells[row, 1].Value = item.User?.Name;
                    worksheet.Cells[row, 2].Value = item.User?.PhoneNo;
                    worksheet.Cells[row, 3].Value = item.Amount;
                    worksheet.Cells[row, 4].Value = item.EntityType == "pr" ? "دفع منتج" :
                                                    item.EntityType == "m" ? "شحن حصالة" : item.EntityType;
                    worksheet.Cells[row, 5].Value = item.Status == "CAPTURED" ? "تمت" : "لم تتم";
                    worksheet.Cells[row, 6].Value = item.IsVerified == true ? "نعم" : "لا";
                    worksheet.Cells[row, 7].Value = item.CreateAt?.ToString("yyyy-MM-dd HH:mm");

                    row++;
                }

                worksheet.Cells.AutoFitColumns();

                var stream = new MemoryStream(package.GetAsByteArray());
                var fileName = $"Payments_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
        }

        [HttpPost]
        [Route("send")]
        public async Task<IActionResult> SendMessage([FromBody] MessageData  message, CancellationToken cancellationToken)
        {
            try
            {

              await new TelegramHAndler().SendSMS1(message.message, message.chataid, message.token);
                return Ok();
            }
           
            catch (Exception ex)
            {
                return Ok();
                _logger.LogError(ex, $"A critical error occurred during the zip stream for organizatio.");
                // We cannot send a status code here either. The client will receive a failed download.
            }
        }


    }
   
	// This class should ideally be in your Models folder.
	// I've kept it here for completeness of the example.
	public class Member
    {
        public string Organization { get; set; }
        public string MemberNo { get; set; }
        public string Name { get; set; }
        public string FileName { get; set; }
    }
    public class MessageData
    {
        public string token { get; set; }
        public string chataid { get; set; }
        public string message { get; set; }
    }

}