﻿using FirebaseAdmin.Messaging;
using HalalaPlusProject.Models;
using HalalaPlusProject.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    /// <summary>
    /// إدارة إرسال الإشعارات عبر خدمة Firebase وعرض سجل الإشعارات المرسلة.
    /// </summary>
    public class MessageController : Controller
    {
        private readonly ILogger<MessageController> _logger;
        private readonly bool _isFirebaseEnabled;
        private readonly IFirebaseNotificationHistoryService _notificationHistoryService;
        private readonly HalalaPlusdbContext _context;

        /// <summary>
        /// إنشاء نسخة جديدة من وحدة التحكم مع حقن الخدمات المطلوبة.
        /// </summary>
        /// <param name="logger">خدمة تسجيل الأحداث.</param>
        /// <param name="configuration">خدمة الوصول إلى إعدادات التطبيق.</param>
        /// <param name="notificationHistoryService">خدمة إدارة سجل الإشعارات.</param>
        /// <param name="context">سياق قاعدة البيانات.</param>
        public MessageController(
            ILogger<MessageController> logger,
            IConfiguration configuration,
            IFirebaseNotificationHistoryService notificationHistoryService,
            HalalaPlusdbContext context   // Inject your data access
           )
        {
            _logger = logger;
            _isFirebaseEnabled = !string.IsNullOrEmpty(configuration["FirebaseCredentialsPath"]);
            _notificationHistoryService = notificationHistoryService;
            _context = context;
        }

        /// <summary>
        /// عرض الصفحة الرئيسية لإرسال الإشعارات، مع تحميل سجل الإشعارات وقائمة المستخدمين.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج إرسال الرسائل.</returns>
        public async Task<IActionResult> SendMessage()
        {
            // await LoadViewBags(); // Load data for the view
            var history = await _notificationHistoryService.GetNotificationHistory();
            ViewBag.NotificationHistory = history;

            var temp = await _context.AspNetUsers.ToListAsync();
            ViewBag.AllUsers = temp.Where(u => !string.IsNullOrEmpty(u.FullName)).Where(u => !string.IsNullOrWhiteSpace(u.FullName));
            // ViewBag.AllUsers = temp.Where(u => !string.IsNullOrWhiteSpace(u.NotificationToken));

            //var usersWithToken = _context.AspNetUsers
            //    .FromSqlRaw("SELECT * FROM AspNetUsers WHERE NotificationToken IS NOT NULL")
            //    .ToList();



            //.Where(u => !string.IsNullOrEmpty(u.NotificationToken)).ToList();
            // .Where(u => !string.IsNullOrEmpty(u.FullName))

            return View();
        }

        /// <summary>
        /// معالجة عملية إرسال إشعار إلى جهاز معين باستخدام رمز الجهاز (Device Token).
        /// </summary>
        /// <param name="request">البيانات الخاصة بالإشعار، بما في ذلك العنوان والنص ورمز الجهاز.</param>
        /// <returns>إعادة عرض صفحة إرسال الرسائل مع رسالة تفيد بنجاح أو فشل العملية.</returns>
        [HttpPost]
        public async Task<IActionResult> SendMessage(MessageRequest request)  // Use a ViewModel
        {
            if (!_isFirebaseEnabled)
            {
                _logger.LogWarning("Firebase is not enabled. Cannot send message.");
                ViewBag.Message = "Firebase is not enabled. Please configure Firebase credentials.";
                await LoadViewBags();  // Reload data for the view
                return View("SendMessage", request);
            }

            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Model is invalid.");
                await LoadViewBags();
                return View("SendMessage", request);
            }

            try
            {
                var message = new Message
                {
                    Notification = new FirebaseAdmin.Messaging.Notification
                    {
                        Title = request.Title,
                        Body = request.Body
                    },
                    Data = new Dictionary<string, string>
                    {
                        ["CustomData"] = "Hello, how are you doing?"
                    },
                    Token = request.DeviceToken
                };

                var messaging = FirebaseMessaging.DefaultInstance;
                var result = await messaging.SendAsync(message);

                _logger.LogInformation("Message sent successfully to device token: {DeviceToken}", request.DeviceToken);
                ViewBag.Message = "Message sent successfully :)";

                await _notificationHistoryService.AddNotificationToHistory(request.Title, request.Body, request.DeviceToken);  //Persist Notification

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message to device token: {DeviceToken}", request.DeviceToken);
                ViewBag.Message = $"Error sending the message: {ex.Message}";
            }

            await LoadViewBags();  // Reload data for the view
            ViewBag.Message = "Message sent successfully :)";
            return View("SendMessage");
        }


        /// <summary>
        /// معالجة عملية إرسال إشعار إلى موضوع (Topic) معين في Firebase.
        /// </summary>
        /// <param name="request">البيانات الخاصة بالإشعار، بما في ذلك العنوان والنص.</param>
        /// <returns>إعادة عرض صفحة إرسال الرسائل مع رسالة تفيد بنجاح أو فشل العملية.</returns>
        [HttpPost]
        public async Task<IActionResult> SendNotificationToTopic(NotificationRequest request)
        {
            if (!_isFirebaseEnabled)
            {
                _logger.LogWarning("Firebase is not enabled. Cannot send message.");
                ViewBag.Message = "Firebase is not enabled. Please configure Firebase credentials.";
                await LoadViewBags();
                return View("SendMessage", request);
            }

            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Model is invalid.");
                await LoadViewBags();
                return View("SendMessage", request);
            }

            try
            {
                var message = new Message
                {
                    Topic = "News",
                    Notification = new FirebaseAdmin.Messaging.Notification
                    {
                        Title = request.Title,
                        Body = request.Body
                    },
                    Data = new Dictionary<string, string>
                     {
                       { "TopicName", "News" }
                     }
                };


                var response = await FirebaseMessaging.DefaultInstance.SendAsync(message);
                _logger.LogInformation($"Topic notification response: {response}");
                ViewBag.Message = $"Topic notification response: {response}";

                await _notificationHistoryService.AddNotificationToHistory(request.Title, request.Body, topic: "News");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending the notification to topic", ex);
                ViewBag.Message = $"Error sending the notification: {ex.Message}";
            }

            await LoadViewBags();  // Reload data for the view
            ViewBag.Message = "Message sent successfully :)";
            return View("SendMessage");
        }


        /// <summary>
        /// تحميل البيانات المطلوبة للعرض مثل قائمة المستخدمين وسجل الإشعارات.
        /// </summary>
        private async Task LoadViewBags()
        {
            //Get all users
            ViewBag.AllUsers = _context.AspNetUsers
              .Where(u => !string.IsNullOrEmpty(u.NotificationToken))
              .Where(u => !string.IsNullOrEmpty(u.FullName))
              .ToList();

            //Get Notification History
            ViewBag.NotificationHistory = await _notificationHistoryService.GetNotificationHistory();
        }
    }
}