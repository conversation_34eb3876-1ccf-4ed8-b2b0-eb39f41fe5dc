﻿//using Microsoft.AspNetCore.Identity;
//using System.Text;

//namespace HalalaPlusProject.Utils
//{
//    public class CustomPhoneNumberTokenProvider<TUser> : PhoneNumberTokenProvider<TUser>
//    where TUser : class
//    {
//        //public override async Task<string> GenerateChangePhoneNumberTokenAsync(TUser user, string phoneNumber, UserManager<TUser> manager)
//        //{
//        //    // Implement your custom logic to generate a code with the desired length
//        //    int desiredLength = 6; // Change this to the desired code length
//        //    string code = GenerateNumericCode(desiredLength);

//        //    // You can also include any additional logic or transformations as needed

//        //    return await Task.FromResult(code);
//        //}

//        // Helper method to generate a numeric code with the desired length
//        private string GenerateNumericCode(int length)
//        {
//            var random = new Random();
//            var code = new StringBuilder(length);

//            for (int i = 0; i < length; i++)
//            {
//                code.Append(random.Next(0, 9));
//            }

//            return code.ToString();
//        }
//    }
//}
