﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("Order_Items")]
public partial class OrderItem
{
    [Key]
    public long Id { get; set; }

    public long? UserId { get; set; }

    public long? OrderId { get; set; }

    public int? ProductId { get; set; }

    [Column("quantity")]
    public int? Quantity { get; set; }

    public double? Price { get; set; }

    [ForeignKey("OrderId")]
    [InverseProperty("OrderItems")]
    public virtual Order? Order { get; set; }

    [ForeignKey("ProductId")]
    [InverseProperty("OrderItems")]
    public virtual Product? Product { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("OrderItems")]
    public virtual SystemUser? User { get; set; }
}
