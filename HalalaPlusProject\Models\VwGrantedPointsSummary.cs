﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class VwGrantedPointsSummary
{
    public long? UserId { get; set; }

    public long ReplaceOrderId { get; set; }

    public long? ProviderId { get; set; }

    [StringLength(500)]
    public string? ProviderName { get; set; }

    public bool Replaced { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? ReplaceOrderDate { get; set; }

    public int? TotalPoints { get; set; }

    public int? TotalGrants { get; set; }
}
