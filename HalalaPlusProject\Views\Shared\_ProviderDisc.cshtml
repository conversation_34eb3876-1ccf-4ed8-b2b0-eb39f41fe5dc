﻿
@model IEnumerable<HalalaPlusProject.CModels.DiscountsModel>

<div class="table-responsive p-0">
               
                <table  id="tbl1" class="table table-striped text-center">
                  <thead>
                    <tr>
                     
                      <th scope="col">اسم الخدمة(الخصم)</th>
                      <th scope="col">الخصم</th>
                      <th scope="col">بداية الخصم</th>
                      <th scope="col">نهاية الخصم</th>
                      <th scope="col">نوع الخصم</th>
                      <th scope="col">الشروط</th>
                      <th scope="col">خيارات</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model) {
        <tr id="<EMAIL>">
            <td>
                @Html.DisplayFor(modelItem => item.DiscountName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Discount)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.StartDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EndDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.GrantType)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Conditions)
            </td>
            <td>
                  <a asp-action="Index" class="btn btn-outline-danger tablebtn" asp-route-id="@item.Id">تعديل</a> | 
                  @if(item.IsActive==true)
                        {
                            <a onclick="setField('/ServiceProvider/Disable?id='+@item.Id)" class="btn  btn-outline-danger tablebtn">ايقاف</a>
                            
                                    }
                        else

                        {
                            <a onclick="setField('/ServiceProvider/Disable?id='+@item.Id)" class="btn btn-primary tablebtn">تفعيل</a>
                        }|
                    <a href="javascript:void(0);" 
   class="btn btn-outline-danger tablebtn"
   onclick="return confirmDelete('@item.Id')">حذف</a>
   <script>
   function confirmDelete(id) {
    event.preventDefault();  
    if (confirm("هل أنت متأكد من الحذف؟")) {
        deleteDiscount(id);
    }
    return false;
}</script>
                
            </td>
        </tr>
}
             
                  </tbody>
               
                </table>

              </div>
              @section Scripts{
    <script>
      

  let table = new DataTable('#tbl1');

    </script>
}