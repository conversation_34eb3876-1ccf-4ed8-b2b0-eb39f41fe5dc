﻿@model IEnumerable<HalalaPlusProject.CModels.CustomerViewModel>

@{
    ViewData["Title"] = "Customer Mony Box Transactions";
}

<link href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" rel="stylesheet" />
<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>

<h1>العملاء والحصالات</h1>

<div class="container mt-4">
    @foreach (var customer in Model)
    {
        <div class="card mb-4">
            <div class="card-header">
                <h2 class="mb-0">
                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#<EMAIL>" aria-expanded="true" aria-controls="<EMAIL>">
                        CustomerId: @customer.CustomerId - @customer.CustomerName
                    </button>
                </h2>
            </div>

            <div id="<EMAIL>" class="collapse">
                <div class="card-body">
                    <p><strong>Email:</strong> @customer.CustomerEmail</p>

                    @foreach (var monyBox in customer.MonyBoxes)
                    {
                        <div class="card mb-3">
                            <div class="card-header">
                                <h3 class="mb-0">
                                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#<EMAIL>" aria-expanded="true" aria-controls="<EMAIL>">
                                        Mony Box: @monyBox.Name
                                    </button>
                                </h3>
                            </div>

                            <div id="<EMAIL>" class="collapse">
                                <div class="card-body" style="color:cornflowerblue">
                                    <p><strong>User Id:</strong> @monyBox.UserId</p>
                                    <p><strong>Target:</strong> @monyBox.Target</p>
                                    <p><strong>Start Date:</strong> @monyBox.StartDate?.ToString("yyyy-MM-dd")</p>
                                    <p><strong>End Date:</strong> @monyBox.EndDate?.ToString("yyyy-MM-dd")</p>
                                    <p><strong>Amount:</strong> @monyBox.Amount</p>

@*                                     <ul>
                                        @foreach (var transaction in monyBox.MonyBoxTransactions)
                                        {
                                            <li style="color:cadetblue">
                                                <p><strong>Transaction Id:</strong> @transaction.Id</p>
                                                <p><strong>Credit:</strong> @transaction.Credit</p>
                                                <p><strong>Debit:</strong> @transaction.Debit</p>
                                                <p><strong>Operation Date:</strong> @transaction.OperationDate.Value.ToString("yyyy-MM-dd")</p>
                                                <p><strong>Is Verified:</strong> @(transaction.IsVerified ? "Yes" : "No")</p>
                                            </li>
                                        }
                                    </ul> *@

                                    <ul class="statistics-list" style="color:cadetblue">
                                        @foreach (var transaction in monyBox.MonyBoxTransactions)
                                        {
                                            <li>
                                                <span class="title-en">رقم العملية </span>
                                                <span class="value">@transaction.Id</span>
                                                <span class="title-ar"> Transaction Id </span>
                                            </li>
                                            <li>
                                                <span class="title-en"> دائن </span>
                                                <span class="value">@transaction.Credit</span>
                                                <span class="title-ar"> Credit </span>
                                            </li>
                                            <li>
                                                <span class="title-en"> مدين </span>
                                                <span class="value">@transaction.Debit</span>
                                                <span class="title-ar"> Debit </span>
                                            </li>
                                            <li>
                                                <span class="title-en">تاريخ العملية </span>
                                                <span class="value">@transaction.OperationDate.Value.ToString("yyyy-MM-dd")</span>
                                                <span class="title-ar"> Operation Date </span>
                                            </li>
                                            <li>
                                                <span class="title-en"> تم التحقق </span>
                                                <span class="value">@(transaction.IsVerified ? "Yes" : "No")</span>
                                                <span class="title-ar"> Is Verified </span>
                                            </li>
                                        }

                                    </ul>

                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    }
    

    </div>



@* @model IEnumerable<HalalaPlusProject.CModels.CustomerViewModel>  

@{
    ViewData["Title"] = "Customer Mony Box Transactions";
}

<h1>العملاء والحصالات</h1>

@foreach (var customer in Model)
{
    <h2>CustomerId: (@customer.CustomerId)</h2>
    <p>CustomerName: (@customer.CustomerName)</p>
    <p>Email: (@customer.CustomerEmail)</p>

    @foreach (var monyBox in customer.MonyBoxes)
    {
        <div style="border: 1px solid #ddd; padding: 10px; margin-bottom: 10px;color:cornflowerblue">
            <p><strong>Customer Id:</strong> @monyBox.UserId</p>
            <p><strong>Mony Box Name:</strong> @monyBox.Name</p>          
            <p><strong>Target:</strong> @monyBox.Target</p>
            <p><strong>Start Date:</strong> @monyBox.StartDate?.ToString("yyyy-MM-dd")</p>
            <p><strong>End Date:</strong> @monyBox.EndDate?.ToString("yyyy-MM-dd")</p>
            <p><strong>Amount:</strong> @monyBox.Amount</p>

            <ul>
                @foreach (var transaction in monyBox.MonyBoxTransactions)
                {
                    <li style="color:cadetblue">
                        <p><strong>Transaction Id:</strong> @transaction.Id</p>
                        <p><strong>Credit:</strong> @transaction.Credit</p>
                        <p><strong>Debit:</strong> @transaction.Debit</p>
                        <p><strong>Operation Date:</strong> @transaction.OperationDate.Value.ToString("yyyy-MM-dd")</p>
                        <p><strong>Is Verified:</strong> @(transaction.IsVerified ? "Yes" : "No")</p>
                    </li>
                }
            </ul>
        </div>
    }
}
 *@



    <style>
        .statistics-list {
            list-style-type: none;
            padding: 0;
        }

            .statistics-list li {
                display: flex;
                justify-content: space-between;
                padding: 5px 0;
            }

        .title-en {
            text-align: left;
            flex: 1;
            direction: ltr;
        }

        .value {
            text-align: center;
            flex: 1;
        }

        .title-ar {
            text-align: right;
            flex: 1;
            direction: rtl;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

/*          .container {
             max-width: 800px;
             margin: 0 auto;
             padding: 20px;
         } */

        h1 {
            text-align: center;
            margin-bottom: 20px;
        }

/*         .card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        } */

            .card span {
                display: block;
            }

        .title-ar {
            text-align: right;
            direction: rtl;
            font-weight: bold;
            flex: 1;
        }

        .value {
            text-align: center;
            flex: 1;
            font-size: 1.5em;
            font-weight: bold;
        }

        .title-en {
            text-align: left;
            direction: ltr;
            font-weight: bold;
            flex: 1;
        }

    </style>
