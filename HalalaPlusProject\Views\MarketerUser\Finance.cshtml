﻿@model IEnumerable<HalalaPlusProject.CModels.MarketerFinanceModel>
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["financialtransactions"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2> @ViewData["Title"]</h2>


<table id="tbl1" class="table">
    <thead>
             <tr>
            <th scope="col"> @localizer["deservedamount"]</th>
            <th scope="col">@localizer["amountspent"] </th>
            <th scope="col"> @localizer["remain"]</th>
                      
                     
                    </tr>
            
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Due)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Outcome)
            </td>
         
            
            <td>
                @Html.DisplayFor(modelItem => item.Remain)
            </td>
           
           
          
            
        </tr>
}
    </tbody>
</table>
@section Scripts{
    <script>
  let table = new DataTable('#tbl1');
  

    </script>
}
