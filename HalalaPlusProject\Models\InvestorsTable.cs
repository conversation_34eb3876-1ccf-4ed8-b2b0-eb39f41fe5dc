﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("InvestorsTable")]
public partial class InvestorsTable
{
    [Key]
    public long Id { get; set; }

    [StringLength(500)]
    public string? InvestorName { get; set; }

    [StringLength(500)]
    public string? PhoneNumber { get; set; }

    [StringLength(50)]
    public string? Email { get; set; }

    public int? StocksNumber { get; set; }

    public double? StocksValue { get; set; }

    [StringLength(500)]
    public string? TransctionId { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateAt { get; set; }
}
