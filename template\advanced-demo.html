<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HalalaPlus - Advanced Demo</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    
    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css">
    
    <!-- Toastr -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="g-sidenav-show bg-gray-100 rtl">
    
    <!-- Floating Action Button -->
    <button class="fab" onclick="showNotification('info', 'FAB', 'تم النقر على الزر العائم!')">
        <i class="fas fa-plus"></i>
    </button>
    
    <!-- Sidebar -->
    <aside class="sidenav navbar navbar-vertical navbar-expand-xs border-0 border-radius-xl my-3 fixed-end me-1 rotate-caret" id="sidenav-main">
        <div class="sidenav-header">
            <i class="fas fa-times p-3 cursor-pointer text-white opacity-5 position-absolute start-0 top-0 d-none d-xl-none" aria-hidden="true" id="iconSidenav"></i>
            <a class="navbar-brand m-0 text-center" href="index.html">
                <img src="assets/img/placeholder.svg" class="navbar-brand-img h-100" alt="main_logo" style="max-height: 3rem;">
                <span class="ms-1 font-weight-bold text-white">HalalaPlus</span>
            </a>
        </div>
        
        <hr class="horizontal light mt-0 mb-2">
        
        <div class="collapse navbar-collapse w-auto max-height-vh-100" id="sidenav-collapse-main">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link text-white" href="index.html">
                        <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                            <i class="material-icons opacity-10">dashboard</i>
                        </div>
                        <span class="nav-link-text ms-1">لوحة التحكم</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link text-white" href="components.html">
                        <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                            <i class="material-icons opacity-10">widgets</i>
                        </div>
                        <span class="nav-link-text ms-1">المكونات</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link text-white active bg-gradient-primary" href="advanced-demo.html">
                        <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                            <i class="material-icons opacity-10">auto_awesome</i>
                        </div>
                        <span class="nav-link-text ms-1">العرض المتقدم</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="sidenav-footer position-absolute w-100 bottom-0">
            <div class="mx-3">
                <a class="btn bg-gradient-primary mt-4 w-100" href="#" type="button">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </aside>
    
    <!-- Main Content -->
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <nav class="navbar navbar-main navbar-expand-lg px-0 mx-4 shadow-none border-radius-xl" id="navbarBlur" navbar-scroll="true">
            <div class="container-fluid py-1 px-3">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent mb-0 pb-0 pt-1 px-0 me-sm-6 me-5">
                        <li class="breadcrumb-item text-sm"><a class="opacity-5 text-dark" href="javascript:;">الصفحات</a></li>
                        <li class="breadcrumb-item text-sm text-dark active" aria-current="page">العرض المتقدم</li>
                    </ol>
                    <h6 class="font-weight-bolder mb-0">المكونات المتقدمة</h6>
                </nav>
                
                <div class="collapse navbar-collapse mt-sm-0 mt-2 me-md-0 me-sm-4" id="navbar">
                    <div class="ms-md-auto pe-md-3 d-flex align-items-center">
                        <div class="search-advanced">
                            <input type="text" class="form-control" placeholder="البحث المتقدم..." id="advancedSearch">
                            <i class="fas fa-search search-icon"></i>
                            <div class="search-results" id="searchResults">
                                <div class="search-result-item">نتيجة البحث 1</div>
                                <div class="search-result-item">نتيجة البحث 2</div>
                                <div class="search-result-item">نتيجة البحث 3</div>
                            </div>
                        </div>
                    </div>
                    
                    <ul class="navbar-nav justify-content-end">
                        <li class="nav-item d-xl-none ps-3 d-flex align-items-center">
                            <a href="javascript:;" class="nav-link text-body p-0" id="iconNavbarSidenav">
                                <div class="sidenav-toggler-inner">
                                    <i class="sidenav-toggler-line"></i>
                                    <i class="sidenav-toggler-line"></i>
                                    <i class="sidenav-toggler-line"></i>
                                </div>
                            </a>
                        </li>
                        
                        <li class="nav-item px-3 d-flex align-items-center">
                            <a href="javascript:;" class="nav-link text-body p-0 position-relative">
                                <i class="fa fa-bell cursor-pointer"></i>
                                <span class="notification-badge">5</span>
                            </a>
                        </li>
                        
                        <li class="nav-item d-flex align-items-center">
                            <a href="javascript:;" class="nav-link text-body font-weight-bold px-0">
                                <i class="fa fa-user me-sm-1"></i>
                                <span class="d-sm-inline d-none">أحمد محمد</span>
                                <span class="status-indicator status-online"></span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        
        <!-- Page Content -->
        <div class="container-fluid py-4">
            
            <!-- Advanced Cards Section -->
            <div class="row mb-4">
                <div class="col-12" data-aos="fade-up">
                    <h4 class="gradient-text mb-4">البطاقات المتقدمة</h4>
                </div>
                
                <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="card card-hover-lift glass">
                        <div class="card-body text-center">
                            <div class="icon icon-lg bg-gradient-primary mx-auto mb-3 pulse">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <h5 class="card-title">بطاقة زجاجية</h5>
                            <p class="card-text">بطاقة بتأثير الزجاج الضبابي مع انيميشن hover متقدم</p>
                            <button class="btn btn-primary btn-glow">زر متوهج</button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="card card-hover-lift">
                        <div class="card-body text-center">
                            <div class="icon icon-lg bg-gradient-success mx-auto mb-3">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h5 class="card-title">إحصائيات متقدمة</h5>
                            <p class="card-text">عرض البيانات بطريقة تفاعلية وجذابة</p>
                            <div class="progress mb-3">
                                <div class="progress-bar bg-gradient-success" style="width: 85%" role="progressbar">85%</div>
                            </div>
                            <small class="text-muted">معدل الإنجاز</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="card card-hover-lift">
                        <div class="card-body text-center">
                            <div class="icon icon-lg bg-gradient-warning mx-auto mb-3">
                                <i class="fas fa-star"></i>
                            </div>
                            <h5 class="card-title">تقييمات العملاء</h5>
                            <p class="card-text">نظام تقييم تفاعلي مع نجوم متحركة</p>
                            <div class="mb-3">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star-half-alt text-warning"></i>
                                <span class="ms-2">4.5/5</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Advanced Forms Section -->
            <div class="row mb-4">
                <div class="col-md-6" data-aos="fade-right">
                    <div class="card">
                        <div class="card-header pb-0">
                            <h6>نماذج متطورة</h6>
                        </div>
                        <div class="card-body">
                            <form>
                                <div class="form-floating-custom mb-3">
                                    <input type="text" class="form-control" id="floatingName" placeholder="الاسم">
                                    <label for="floatingName">الاسم الكامل</label>
                                </div>
                                
                                <div class="form-floating-custom mb-3">
                                    <input type="email" class="form-control" id="floatingEmail" placeholder="البريد">
                                    <label for="floatingEmail">البريد الإلكتروني</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">مستوى الخبرة</label>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-gradient-info" style="width: 70%" id="skillProgress">70%</div>
                                    </div>
                                    <input type="range" class="form-range" min="0" max="100" value="70" id="skillRange">
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="premiumSwitch">
                                        <label class="form-check-label" for="premiumSwitch">
                                            عضوية مميزة
                                            <span class="tooltip-custom" data-tooltip="احصل على مميزات إضافية">
                                                <i class="fas fa-info-circle text-info"></i>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn bg-gradient-primary btn-loading">
                                    حفظ البيانات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6" data-aos="fade-left">
                    <div class="card">
                        <div class="card-header pb-0">
                            <h6>حالات التحميل</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <h6 class="text-sm">Skeleton Loading</h6>
                                <div class="skeleton mb-2" style="height: 20px; border-radius: 4px;"></div>
                                <div class="skeleton mb-2" style="height: 20px; width: 80%; border-radius: 4px;"></div>
                                <div class="skeleton mb-2" style="height: 20px; width: 60%; border-radius: 4px;"></div>
                            </div>
                            
                            <div class="mb-4">
                                <h6 class="text-sm">أزرار التحميل</h6>
                                <button class="btn btn-primary me-2 mb-2" onclick="simulateLoading(this)">
                                    تحميل عادي
                                </button>
                                <button class="btn btn-success me-2 mb-2" onclick="simulateLoading(this)">
                                    <i class="fas fa-download me-2"></i>تحميل مع أيقونة
                                </button>
                            </div>
                            
                            <div class="mb-4">
                                <h6 class="text-sm">مؤشرات الحالة</h6>
                                <div class="d-flex align-items-center mb-2">
                                    <span class="status-indicator status-online"></span>
                                    <span class="ms-2">متصل</span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <span class="status-indicator status-away"></span>
                                    <span class="ms-2">بعيد</span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <span class="status-indicator status-busy"></span>
                                    <span class="ms-2">مشغول</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="status-indicator status-offline"></span>
                                    <span class="ms-2">غير متصل</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Interactive Elements -->
            <div class="row mb-4">
                <div class="col-12" data-aos="fade-up">
                    <div class="card">
                        <div class="card-header pb-0">
                            <h6>عناصر تفاعلية متقدمة</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-outline-primary w-100 scale-hover" onclick="copyToClipboard('نص تجريبي للنسخ')">
                                        <i class="fas fa-copy me-2"></i>نسخ النص
                                    </button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-outline-success w-100 scale-hover" onclick="Utils.showLoading(); setTimeout(Utils.hideLoading, 2000)">
                                        <i class="fas fa-spinner me-2"></i>إظهار التحميل
                                    </button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-outline-info w-100 scale-hover" onclick="toggleTheme()">
                                        <i class="fas fa-palette me-2"></i>تغيير المظهر
                                    </button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-outline-warning w-100 scale-hover" onclick="showAdvancedAlert()">
                                        <i class="fas fa-magic me-2"></i>تنبيه متقدم
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </main>
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="js/script.js"></script>
    
    <script>
        // Advanced demo functions
        function simulateLoading(button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="loading me-2"></span>جاري التحميل...';
            button.disabled = true;
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                showNotification('success', 'تم!', 'اكتملت العملية بنجاح');
            }, 3000);
        }
        
        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showNotification('success', 'تم النسخ!', 'تم نسخ النص إلى الحافظة');
                });
            } else {
                showNotification('error', 'خطأ!', 'المتصفح لا يدعم النسخ');
            }
        }
        
        function toggleTheme() {
            document.body.classList.toggle('dark-theme');
            showNotification('info', 'تم تغيير المظهر!', 'تم تبديل المظهر بنجاح');
        }
        
        function showAdvancedAlert() {
            Swal.fire({
                title: 'تنبيه متقدم!',
                text: 'هذا مثال على تنبيه متقدم مع خيارات متعددة',
                icon: 'question',
                showCancelButton: true,
                showDenyButton: true,
                confirmButtonText: 'موافق',
                denyButtonText: 'لا أوافق',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#cb0c9f',
                denyButtonColor: '#ea0606',
                cancelButtonColor: '#8392AB'
            }).then((result) => {
                if (result.isConfirmed) {
                    showNotification('success', 'ممتاز!', 'تم اختيار موافق');
                } else if (result.isDenied) {
                    showNotification('error', 'حسناً', 'تم اختيار عدم الموافقة');
                } else {
                    showNotification('info', 'تم الإلغاء', 'تم إلغاء العملية');
                }
            });
        }
        
        // Advanced search functionality
        $('#advancedSearch').on('input', function() {
            const searchTerm = $(this).val();
            if (searchTerm.length > 0) {
                $('#searchResults').show();
            } else {
                $('#searchResults').hide();
            }
        });
        
        // Skill range slider
        $('#skillRange').on('input', function() {
            const value = $(this).val();
            $('#skillProgress').css('width', value + '%').text(value + '%');
        });
        
        // Hide search results when clicking outside
        $(document).click(function(event) {
            if (!$(event.target).closest('.search-advanced').length) {
                $('#searchResults').hide();
            }
        });
    </script>
</body>
</html>
