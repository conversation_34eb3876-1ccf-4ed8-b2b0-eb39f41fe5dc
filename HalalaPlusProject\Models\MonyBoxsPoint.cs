﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class MonyBoxsPoint
{
    [Key]
    public int Id { get; set; }

    public double? FromAmount { get; set; }

    public double? ToAmount { get; set; }

    public int? GrntedPoints { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateAt { get; set; }

    public bool Deleted { get; set; }

    [InverseProperty("PointsNavigation")]
    public virtual ICollection<GrantedDiscount> GrantedDiscounts { get; set; } = new List<GrantedDiscount>();
}
