﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;

namespace HalalaPlusProject.Areas.Identity.Data;

// Add profile data for application users by adding properties to the HalalaPlusProjectUser class
public class HalalaPlusProjectUser : IdentityUser
{
    public string? RefreshToken { get; set; }
    public DateTime RefreshTokenExpiryTime { get; set; }
    public string? FullName { get; set; }
    public string? ShortScrap { get; set; }
    public string? Image { get; set; }
    public string? MemberNo { get; set; }
    public bool? Type { get; set; }
    public string? RegisterProgress { get; set; }
}

