﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class PaymentsRequest
{
    [Key]
    public string Id { get; set; } = null!;

    public long? UserId { get; set; }

    [StringLength(50)]
    public string? PaymentId { get; set; }

    [StringLength(100)]
    public string? RefrenceId { get; set; }

    [StringLength(100)]
    public string? TrackId { get; set; }

    public double? Amount { get; set; }

    [StringLength(100)]
    public string? TransId { get; set; }

    public long? MonyBoxId { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateAt { get; set; }

    public bool? IsVerified { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? VerfayDate { get; set; }

    [Column("status")]
    [StringLength(100)]
    public string? Status { get; set; }

    public string? Error { get; set; }

    [StringLength(50)]
    public string? EntityId { get; set; }

    [StringLength(20)]
    public string? EntityType { get; set; }

    [StringLength(100)]
    public string? Gateway { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("PaymentsRequests")]
    public virtual SystemUser? User { get; set; }
}
