﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    /// <summary>
    /// متحكم لعرض بيانات عملاء النظام وتفاصيل صناديقهم المالية.
    /// </summary>
    public class SystemUsersController : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public SystemUsersController(HalalaPlusdbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// يعرض قائمة بالعملاء الذين يمتلكون صناديق مالية، مع ملخص لعدد الصناديق وإجمالي المبالغ.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة بنماذج عرض العملاء.</returns>
        public async Task<IActionResult> Index()
        {
            var systemUsers = await _context.SystemUsers.Where(op => op.AccountType == "Customer" && op.UsersMonyBoxes.Any())
                .Include(op => op.UsersMonyBoxes).Include(op => op.MonyBoxTransactions)
                .ToListAsync();
            List<CustomerViewModel> listCustomers = new List<CustomerViewModel>();
            foreach (var customer in systemUsers)
            {
                //user.Status = user.UsersMonyBoxes.Count.ToString();
                //user.Amount = user.UsersMonyBoxes.Sum(op => op.Amount);
                var totalAmount = customer.UsersMonyBoxes.Sum(op => op.Amount);
                var temp = new CustomerViewModel
                {
                    CustomerId = customer.Id,
                    CustomerEmail = customer.Email,
                    CustomerName = customer.Name,
                    CustomerPhone = customer.PhoneNo,
                    CustomerMonyBoxsCount = customer.UsersMonyBoxes.Count(),
                    CustomerTotalAmount = "" + ((totalAmount == null) ? (00) : (totalAmount.Value)).ToString("N2")
                };

                listCustomers.Add(temp);
            }

            return View(listCustomers);
            //var customers = _context.SystemUsers.Include(s => s.Asp);
            //return View(await customers.ToListAsync());
        }

        /// <summary>
        /// يعرض تفاصيل عميل محدد، بما في ذلك قائمة بصناديقه المالية.
        /// </summary>
        /// <param name="id">معرّف العميل المطلوب عرض تفاصيله.</param>
        /// <returns>عرض يحتوي على تفاصيل العميل وصناديقه.</returns>
        public async Task<IActionResult> Details(long? id)
        {
            var customer = await _context.SystemUsers.Where(op => op.Id == id)
              .Include(op => op.UsersMonyBoxes).Include(op => op.MonyBoxTransactions).FirstAsync();

            List<CustomerViewModel> listCustomers = new List<CustomerViewModel>();
            var totalAmount = customer.UsersMonyBoxes.Sum(op => op.Amount);
            var temp = new CustomerViewModel
            {
                CustomerId = customer.Id,
                CustomerEmail = customer.Email,
                CustomerName = customer.Name,
                CustomerPhone = customer.PhoneNo,
                CustomerMonyBoxsCount = customer.UsersMonyBoxes.Count(),
                CustomerTotalAmount = "" + ((totalAmount == null) ? (00) : (totalAmount.Value)).ToString("N2")
            };
            // temp.MonyBoxes = await _context.UsersMonyBoxs.Where(op => op.UserId == temp.CustomerId && op.MonyBoxTransactions.Any()).ToListAsync();
            temp.MonyBoxes = customer.UsersMonyBoxes.ToList();
            //foreach (var item in temp.MonyBoxes)
            //{
            //   // item.MonyBoxTransactions = await _context.MonyBoxTransactions.Where(op => op.MonyBoxId == item.Id).ToListAsync();
            //     item.MonyBoxTransactions = item.MonyBoxTransactions.ToList();
            //}

            listCustomers.Add(temp);


            return View(listCustomers);
        }

    }
}