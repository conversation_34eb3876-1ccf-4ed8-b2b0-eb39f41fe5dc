﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class AspNetUsersWithSystemUsersView
{
    [StringLength(450)]
    public string Id { get; set; } = null!;

    public string? FullName { get; set; }

    public string? ShortScrap { get; set; }

    public string? Image { get; set; }

    [StringLength(256)]
    public string? UserName { get; set; }

    [StringLength(256)]
    public string? Email { get; set; }

    public bool EmailConfirmed { get; set; }

    public string? PasswordHash { get; set; }

    public string? PhoneNumber { get; set; }

    public bool Deleted { get; set; }

    public string? MemberNo { get; set; }

    [StringLength(50)]
    public string? RegisterProgress { get; set; }

    public bool HasCustomBrand { get; set; }

    public string? NotificationToken { get; set; }

    public bool EnableNotification { get; set; }

    [Column("PIN")]
    public int? Pin { get; set; }

    public bool FirstLogin { get; set; }

    public bool NeedOtp { get; set; }

    public string? DeleteReason { get; set; }

    [StringLength(50)]
    public string? DeleteEmail { get; set; }

    public long UserId { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    public int? Nationality { get; set; }

    [StringLength(100)]
    public string? IdentityNo { get; set; }

    public int? City { get; set; }

    public string? ServiceProviderRepresent { get; set; }

    public int? Activity { get; set; }

    [StringLength(450)]
    public string? BusinessNo { get; set; }

    [StringLength(450)]
    public string? EnterPrisePhoneNo { get; set; }

    [StringLength(20)]
    public string? AccountType { get; set; }

    [StringLength(1)]
    [Unicode(false)]
    public string? Status { get; set; }

    public bool? UserDeleted { get; set; }

    [StringLength(50)]
    public string? PhoneNo { get; set; }
}
