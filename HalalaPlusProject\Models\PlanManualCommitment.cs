﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class PlanManualCommitment
{
    [Key]
    public int ManualCommitmentsId { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? Amount { get; set; }

    public DateOnly? ExpenseDate { get; set; }

    [StringLength(500)]
    public string? Notes { get; set; }

    public int? MainTermsId { get; set; }

    public int? SubTermsId { get; set; }

    [StringLength(100)]
    public string? CreatedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [StringLength(100)]
    public string? UpdatedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    public bool? Deleted { get; set; }

    [StringLength(100)]
    public string? DeletedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? DeletedAt { get; set; }

    [ForeignKey("MainTermsId")]
    [InverseProperty("PlanManualCommitments")]
    public virtual PlanManualMainTerm? MainTerms { get; set; }

    [InverseProperty("ManualCommitments")]
    public virtual ICollection<PlanExpense> PlanExpenses { get; set; } = new List<PlanExpense>();

    [ForeignKey("SubTermsId")]
    [InverseProperty("PlanManualCommitments")]
    public virtual PlanManualSubTerm? SubTerms { get; set; }
}
