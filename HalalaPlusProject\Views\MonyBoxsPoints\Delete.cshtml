﻿@model HalalaPlusProject.Models.MonyBoxsPoint

@{
    ViewData["Title"] = "حذف";
}

<h1>حذف</h1>

<h3>هل أنت متأكد أنك تريد حذف هذا؟</h3>
<div>
    <h4>النقاط</h4>
    <hr />
    <dl class="row">
        <div class="col-md-3">
            <dt>من القيمة</dt>
            <dd>@Html.DisplayFor(model => model.FromAmount)</dd>
        </div>
        <div class="col-md-3">
            <dt>إلى القيمة</dt>
            <dd>@Html.DisplayFor(model => model.ToAmount)</dd>
        </div>
        <div class="col-md-3">
            <dt>النقاط الممنوحة</dt>
            <dd>@Html.DisplayFor(model => model.GrntedPoints)</dd>
        </div>
        <div class="col-md-3">
            <dt>تاريخ الإنشاء</dt>
            <dd>@Html.DisplayFor(model => model.CreateAt)</dd>
        </div>
        <div class="col-md-3">
            <dt>محذوف</dt>
            <dd>@Html.DisplayFor(model => model.Deleted)</dd>
        </div>
    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="حذف" class="btn btn-danger" /> |
        <a asp-action="Index">العودة إلى القائمة</a>
    </form>
</div>
