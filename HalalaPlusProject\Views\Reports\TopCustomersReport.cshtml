﻿@model IEnumerable<HalalaPlusProject.Models.vw_TopCustomersReport>

@{
    ViewData["Title"] = "تقرير العملاء";
}

<h2>تقرير العملاء الأكثر تفاعلاً</h2>

<table class="table table-bordered table-striped">
    <thead class="table-dark">
        <tr>
            <th>الرقم</th>
            <th>اسم العميل</th>
            <th>الدولة</th>
            <th>عدد الطلبات</th>
            <th>إجمالي المدفوعات</th>
            <th>متوسط قيمة الطلب</th>
            <th>آخر طلب</th>
            <th>عدد الأيام منذ آخر طلب</th>
            <th>الحالة</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.CustomerId</td>
                <td>@item.CustomerName</td>
                <td>@item.Country</td>
                <td>@item.TotalOrders</td>
                <td>@item.TotalPayments </td>
                <td>@item.AvgOrderValue </td>
                <td>@(item.LastOrderDate?.ToString("yyyy-MM-dd") ?? "-")</td>
                <td>@item.DaysSinceLastOrder</td>
            
            </tr>
        }
    </tbody>
</table>
