﻿using HalalaPlusProject.Areas.Identity.Data;
using HalalaPlusProject.CustomClasses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System.Text;
using System.Text.Encodings.Web;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// يتحكم في إدارة مزود الخدمة: عرض الإحصائيات، الموظفين، المهام، والخصومات.
    /// </summary>
    [Authorize]
    public class ProviderController : Controller
    {
        Models.HalalaPlusdbContext _context;
        IWebHostEnvironment _hosting;
        private readonly UserManager<HalalaPlusProjectUser> _userManager;
        private readonly IEmailSender _emailSender;
        private readonly IStringLocalizer<ProviderController> _localization;

        public ProviderController(Models.HalalaPlusdbContext context, IWebHostEnvironment hosting,
            UserManager<HalalaPlusProjectUser> userManager, IEmailSender emailSender,
            IStringLocalizer<ProviderController> _localization)
        {
            this._context = context;
            this._localization = _localization;
            this._hosting = hosting;
            _userManager = userManager;
            _emailSender = emailSender;
        }

        /// <summary>
        /// يعرض صفحة الإحصائيات الخاصة بمزود الخدمة.
        /// </summary>
        public async Task<IActionResult> Index()
        {
            var systemUser = new UsersClass().retriveUserDetails(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value, _context);
            var data = await _context.GetProviderStatistics
                 .FromSqlRaw("EXEC GetProviderStatistics @ProviderId = {0}", _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id)
                 .ToListAsync();

             systemUser.GetProviderStatistics = data.FirstOrDefault();
            //return View(systemUser);
            return View(systemUser);
        }

        /// <summary>
        /// يعرض قائمة المسوقين المرتبطين بمزود الخدمة.
        /// </summary>
        public async Task<IActionResult> Task()
        {
            return View(new UsersClass().retrive("Marketer", _context));
        }

        /// <summary>
        /// يعرض صفحة منح الخصومات للمسوقين.
        /// </summary>
        public async Task<IActionResult> GrantDiscount()
        {
            return View(new UsersClass().retrive("Marketer", _context));
        }

        /// <summary>
        /// يعرض قائمة الموظفين المرتبطين بمزود الخدمة الحالي.
        /// </summary>
        public async Task<IActionResult> Employees()
        {
            return View(new UsersClass().retrive("Employee", _context, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value));
        }

        /// <summary>
        /// يعرض نموذج تعديل بيانات موظف معين.
        /// </summary>
        /// <param name="id">معرف الموظف.</param>
        public async Task<IActionResult> Edit(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }
            var systemUser = new UsersClass().retriveUserEmployee(id ?? 0, _context);
            if (systemUser == null)
            {
                return NotFound();
            }
            ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality", systemUser.Nationality);
            return View(systemUser);
        }

        /// <summary>
        /// يقوم بتحديث بيانات موظف بعد التحقق من البريد الإلكتروني واسم المستخدم.
        /// </summary>
        /// <param name="model">نموذج بيانات الموظف المعدلة.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(CModels.UserEmployeeModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var ob = _context.SystemUsers.Find(model.Id);
                    if (ob != null)
                        if (_context.AspNetUsers.Where(p => (p.Email == model.Email || p.UserName == model.UserName) && p.Id != ob.AspId).Count() > 0)
                            return Ok(new { state = 0, message = _localization["emailused"].Value });

                    ob.Name = model.Name;
                    ob.PhoneNo = model.PhoneNo;
                    ob.Email = model.Email;
                    ob.Salary = model.Salary;
                    ob.BirthDate =DateOnly.Parse(model.BirthDate.ToString()) ;
                    ob.IdentityNo = model.IdentityNo;
                    ob.Salary = model.Salary;
                    ob.Nationality = model.Nationality;
                    _context.Update(ob);

                    var userExists = await _userManager.FindByEmailAsync(model.Email);
                    userExists.Email = model.Email;
                    userExists.UserName = model.UserName;
                    await _userManager.UpdateAsync(userExists);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    return Ok(new { state = 0, message = _localization["anerroroccured"].Value });
                }
                return Ok(new { state = 7, message = _localization["addedsuccessfuly"].Value, Url = "/Provider/Employees" });
            }

            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// يعرض نموذج إضافة موظف جديد.
        /// </summary>
        public IActionResult Create()
        {
            ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality");
            return View();
        }

        /// <summary>
        /// ينشئ موظفًا جديدًا ويضيفه إلى قاعدة البيانات ويرسل رسالة تأكيد بالبريد الإلكتروني.
        /// </summary>
        /// <param name="model">نموذج بيانات الموظف الجديد.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CModels.EmployeesModel model)
        {
            if (ModelState.IsValid)
            {
                var userExists = await _userManager.FindByEmailAsync(model.Email);
                if (userExists != null)
                    return Ok(new { state = 0, message = _localization["emailused"].Value });

                HalalaPlusProjectUser user = new()
                {
                    Email = model.Email,
                    SecurityStamp = Guid.NewGuid().ToString(),
                    PhoneNumber = model.PhoneNo,
                    UserName = model.UserName,
                    FullName = model.Name
                };

                var passwordValidator = new PasswordValidator<HalalaPlusProjectUser>();
                var result = await passwordValidator.ValidateAsync(_userManager, user, model.Password);

                if (result.Succeeded)
                {
                    result = await _userManager.CreateAsync(user, model.Password);
                    if (result.Succeeded)
                    {
                        var result1 = await _userManager.AddToRoleAsync(user, "Employee");
                        if (result1.Succeeded)
                        {
                            EmployeeClass ob = new EmployeeClass(_context);
                            ob.Insert(model, user, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value);

                            var code = await _userManager.GenerateEmailConfirmationTokenAsync(user);
                            var userId = await _userManager.GetUserIdAsync(user);
                            code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));

                            var callbackUrl = Url.Page(
                                "/Account/ConfirmEmail",
                                pageHandler: null,
                                values: new { area = "Identity", userId = userId, code = code, returnUrl = "~/" },
                                protocol: Request.Scheme);

                            await _emailSender.SendEmailAsync(model.Email, "Confirm your email",
                                $"Please confirm your account by <a href='{HtmlEncoder.Default.Encode(callbackUrl)}'>clicking here</a>.");

                            code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(code));
                            await _userManager.ConfirmEmailAsync(user, code);

                            return Ok(new { state = 1, message = _localization["addedsuccessfuly"].Value });
                        }
                    }
                }

                return Ok(new { state = 0, message = _localization["anerroroccured"].Value });
            }

            ViewData["National"] = new SelectList(_context.CountriesTables, "Id", "Nationality");
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }
    }
}
