﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Models;

namespace HalalaPlusProject.CustomClasses
{
    public class OrdersClass
    {
        public List<OrdersModel> retrive( HalalaPlusdbContext _context,string state="new")
        {
            try
            {
                var temp = _context.CardsOrders.Where(o => o.OrderState == state).Select(order => new CModels.OrdersModel
                        {
                            OrderNo = order.Id.ToString(),
                            UserOrdered = order.Name,
                            Id = order.Id,
                            OrderType = "طلب بطاقة",
                            OrderTypeNo = 1,
                            PhoneNo=order.PhoneNo,
                            date=order.Orderdate,
                            DetailLink="CardDetails"

                        }).ToList();
                 var list=  _context.SystemUsers.Where(p => p.Orderstate == state).Select(o => new OrdersModel 
                        { OrderNo = o.Id.ToString(),
                            OrderType="طلب اعتماد مقدم خدمة",
                            OrderTypeNo=3,
                            Id=o.Id,
                            PhoneNo=o.PhoneNo,
                            UserOrdered=o.Name ,
                             date = o.Orderdate,
                     DetailLink = "ProviderDetails"
                 }).ToList();
                var all =temp.Union(list);

                var list2= _context.DiscountsTables.Where(p => p.Orderstate == state).Select(o => new OrdersModel
                        {
                            OrderNo = o.Id.ToString(),
                            OrderType = "طلب اعتماد خصم",
                            OrderTypeNo = 2,
                            Id = o.Id,
                            PhoneNo=o.User.PhoneNo,
                            UserOrdered = o.User.Name,
                            date = o.Orderdate,
                    DetailLink = "DiscountDetails"
                }).ToList();

                var allelements=all.Union(list2);
                return allelements.ToList();
            }
            catch (Exception)
            {
                return new List<OrdersModel>();
            }

        }

        public async Task<bool> SetCardOrder(HalalaPlusdbContext _context,long id,string state) {
            try
            {
                var temp = _context.CardsOrders.Find(id);
                if (temp != null)
                {
                    temp.OrderState = state;
                    _context.Update(temp);
                   await  _context.SaveChangesAsync();
                    return true;
                }

            }
            catch { return false; }
            return false;
        }
        public async Task<bool> SetProviderOrder(HalalaPlusdbContext _context, long id, string state)
        {
            try
            {
                var temp = _context.SystemUsers.Find(id);
                if (temp != null)
                {
                    temp.Orderstate =state;
                    if(state== "rejected")
                    temp.Asp.EmailConfirmed = false;
                    else temp.Asp.EmailConfirmed = true;
                    _context.Update(temp);
                    await _context.SaveChangesAsync();
                    return true;
                }

            }
            catch { return false; }
            return false;
        }
        public async Task<bool> SetDiscountOrder(HalalaPlusdbContext _context, long id, string state)
        {
            try
            {
                var temp = _context.DiscountsTables.Find(id);
                if (temp != null)
                {
                    temp.Orderstate = state;
                    _context.Update(temp);
                    await _context.SaveChangesAsync();
                    return true;
                }

            }
            catch { return false; }
            return false;
        }

    }
}
