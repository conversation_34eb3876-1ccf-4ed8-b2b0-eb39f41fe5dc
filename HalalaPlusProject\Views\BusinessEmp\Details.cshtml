﻿@model HalalaPlusProject.CModels.UserDetailsModel
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["details"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h3>@localizer["details"]</h3>

<h4>@localizer["theemployee"]</h4>
    <hr />
         <div class="col-md-12">
         <div class="row">

     
     <div class="col-md-5">

          <div class="form-group">
                <label  class="form-label">@localizer["name"]</label>
                <label  class="form-control">@Model.Name</label>               
            </div>
               <div class="form-group">
                <label  class="form-label">@localizer["nationality"]</label>
                <label  class="form-control">@Model.Nationality</label>               
            </div>
               <div class="form-group">
                <label class="form-label">@localizer["identityno"]</label>
                <label  class="form-control">@Model.IdentityNo</label>               
            </div>
              <div class="form-group">
                <label class="form-label"> @localizer["birthdate"]</label>
                <label  class="form-control">@Model.BirthDate</label>               
            </div>
              <div class="form-group">
                <label class="form-label">@localizer["salary"]</label>
                <label  class="form-control">@Model.Salary</label>               
            </div>

    @*<dl class="row">
   
        
     
        <dt class = "col-sm-2">
         تاريخ الميلاد
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.BirthDate)
        </dd>
        
        <dt class = "col-sm-2">
        الراتب
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Salary)
        </dd>
        
         </dl>*@
        </div>
         <div class="col-md-5">

             <div class="form-group">
                <label class="form-label">@localizer["phoneno"]</label>
                <label  class="form-control">@Model.PhoneNo</label>               
            </div>
            <div class="form-group">
                <label class="form-label">@localizer["mail"]</label>
                <label  class="form-control">@Model.Email</label>               
            </div>
            <div class="form-group">
                <label class="form-label"> @localizer["username"]</label>
                <label  class="form-control">@Model.UserName</label>               
            </div>

@*    <dl class="row">
        <dt class = "col-sm-2">
          رقم الجوال
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.PhoneNo)
        </dd>
        <dt class = "col-sm-2">
          البريد الالكتروني
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Email)
        </dd>
        <dt class = "col-sm-2">
         اسم المستخدم
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.UserName)
        </dd>
        
        
     
     
    </dl>*@
</div>
          </div>  </div>
<div>
    <a asp-action="Edit" class="btn btn-primary px-5" asp-route-id="@Model?.Id">@localizer["edit"]</a> |
    <a asp-action="Delete" class="btn btn-primary px-5" asp-route-id="@Model?.Id">@localizer["delete"]</a> |
    <a class="btn btn-primary px-5" asp-action="Index">@localizer["backtolist"]</a>
</div>
