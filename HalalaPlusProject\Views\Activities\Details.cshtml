﻿@model HalalaPlusProject.Models.Activity
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["details"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["details"]</h1>

<div>
    <h4>@localizer["theactivity"]</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @localizer["activityname"]
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt class="col-sm-2">
            @localizer["activityname"]
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.EnName)
        </dd>
        <dt class="col-sm-2">
            @localizer["discribe"]
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Describtion)
        </dd>
        <dt class="col-sm-2">
            @localizer["discribe"]
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.EnDescribtion)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id"> @localizer["edite"]</a> |
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>
