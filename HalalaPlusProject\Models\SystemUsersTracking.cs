﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("SystemUsersTracking")]
public partial class SystemUsersTracking
{
    [Key]
    public int Id { get; set; }

    public string? Details { get; set; }

    [StringLength(50)]
    public string? Status { get; set; }

    public long? UserId { get; set; }

    [StringLength(450)]
    public string? MasterId { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateAt { get; set; }

    [StringLength(450)]
    public string? CreateUserId { get; set; }
}
