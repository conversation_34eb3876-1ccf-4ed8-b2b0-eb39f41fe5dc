﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class Notification
{
    [Key]
    public long Id { get; set; }

    [Column("title")]
    [StringLength(250)]
    public string? Title { get; set; }

    [Column("body")]
    public string? Body { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("target")]
    [StringLength(50)]
    public string? Target { get; set; }

    [Column("target_Id")]
    public long? TargetId { get; set; }

    [Column("isRead")]
    public bool? IsRead { get; set; }
}
