﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class ActivationLog
{
    [Key]
    public int Id { get; set; }

    [Column("Asp_Id")]
    [StringLength(450)]
    public string? AspId { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? DisableDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? ActivateDate { get; set; }

    [StringLength(450)]
    public string? DmasterId { get; set; }

    [StringLength(450)]
    public string? ActmasterId { get; set; }
}
