﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HalalaPlusProject.Models;
using HalalaPlusProject.CustomClasses;
using Microsoft.Extensions.Localization;
using HalalaPlusProject.CModels;
using Microsoft.AspNetCore.Authorization;
using System.Globalization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة المنتجات، وتوفير عمليات الإنشاء والقراءة والتحديث والحذف (CRUD).
    /// </summary>
    [Authorize]
    public class ProductsController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IWebHostEnvironment _hosting;
        private readonly IStringLocalizer<ProductsController> _localization;
        private readonly ILogger<ProductsController> _logger;
        public ProductsController(HalalaPlusdbContext context, ILogger<ProductsController> logger, IWebHostEnvironment _hosting, IStringLocalizer<ProductsController> _localization)
        {
            _context = context;
            this._logger = logger;  
            this._hosting = _hosting;
            this._localization = _localization;
        }

        // GET: Products
        /// <summary>
        /// عرض قائمة بجميع المنتجات مع أسعارها وتصنيفاتها.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة بالمنتجات.</returns>
        public IActionResult Index()
        {
            var halalaPlusdbContext = _context.Products.Where(o=>o.IsFromTwelve).Include(p => p.CatagoryNavigation).ToList();
            var temp = halalaPlusdbContext.Select(p => new CModels.ProductIndeexModel { Price = p.Price, Id = p.Id, Name = p.Name, EnName = p.Engname, Catagory = p.CatagoryNavigation?.Name }).ToList();
            return View(temp);
        }

        // GET: Products/Details/5
        /// <summary>
        /// عرض التفاصيل الخاصة بمنتج معين.
        /// </summary>
        /// <param name="id">معرف المنتج المراد عرض تفاصيله.</param>
        /// <returns>عرض يحتوي على تفاصيل المنتج، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null || _context.Products == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.CatagoryNavigation)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (product == null)
            {
                return NotFound();
            }

            return View(product);
        }

        // GET: Products/Create
        /// <summary>
        /// عرض نموذج إنشاء منتج جديد.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج الإنشاء.</returns>
        public IActionResult Create()
        {
            ViewData["Catagory"] = new SelectList(_context.Categories, "Id", "Name");
            return View();
        }

        /// <summary>
        /// تنشيط منتج".
        /// </summary>
        /// <param name="id">معرف المنتج.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        public async Task<IActionResult> ActiveProduct(int id )
        {
            if (id !=0)
            {
                try
                {
                    var product = await _context.Products.FindAsync(id);
                    if (product != null)
                    {
                        product.Active = true; 
                        _context.Products.Update(product);
                    }

                    await _context.SaveChangesAsync();
                    return Ok(new { state =1, message = "تم التنشيط بنجاح" });
                }
                catch (Exception ex) { 
                    return Ok(new { state = 0, message ="لم يتم التنشيط بنجاح" }); }
                   
            }
            return Ok(new { state = 0, message = "يجب تحديد منتج" });
        }
          /// <summary>
        /// ايقاف منتج".
        /// </summary>
        /// <param name="id">معرف المنتج.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        public async Task<IActionResult> stopProduct(int id )
        {
            if (id !=0)
            {
                try
                {
                    var product = await _context.Products.FindAsync(id);
                    if (product != null)
                    {
                        product.Active = false;  
                        _context.Products.Update(product);
                    }

                    await _context.SaveChangesAsync();
                    return Ok(new { state =1, message = "تم ايقاف المنتج بنجاح" });
                }
                catch (Exception ex) { 
                    return Ok(new { state = 0, message = "فشل ايقاف المنتج" }); }
                   
            }
            return Ok(new { state = 0, message = "يجب تحديد منتج" });
        }


        // POST: Products/Create
        /// <summary>
        /// معالجة عملية إنشاء منتج جديد، بما في ذلك حفظ الصورة.
        /// </summary>
        /// <param name="product">البيانات الخاصة بالمنتج الجديد.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CModels.ProductModel product)
        {
            if (ModelState.IsValid)
            {
                var ob = new Models.Product()
                {

                    Catagory = product.Catagory,
                    Name = product.Name,
                    Description = product.Description,
                    Engdescription = product.Engdescription,
                    Active = true,
                    Details = product.Details,
                    CreateOn = DateTime.Now,
                    Engname = product.Engname,
                   

                }; 
                product.SellPrice = product.SellPrice?.Replace('٫', '.');
                product.Price = product.Price?.Replace('٫', '.');
                if (product.SellPrice != null)
                {
                    ob.SellPrice = double.Parse(product.SellPrice.Replace('٫', '.'), CultureInfo.GetCultureInfo("en-US"));
                }
                if (product.Price != null)
                {
                    ob.Price = double.Parse(product.Price.Replace('٫', '.'), CultureInfo.GetCultureInfo("en-US"));
                }

                try
                {
                    ob.Image = HandleImages.SaveImage(product.file, "images", _hosting);
                    _context.Add(product);
                    await _context.SaveChangesAsync();
                    return Ok(new { state = 7, message = _localization["opsuccess"].Value, url = "/Products/Index" });
                }
                catch (Exception ex)
                {
                    if (ob.Image != null) HandleImages.RemoveImage(ob.Image, "images", _hosting);
                    Ok(new { state = 1, message = _localization["validateallparamaters"].Value });

                }

                return RedirectToAction(nameof(Index));
            }
            return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });
        }

        // GET: Products/Edit/5
        /// <summary>
        /// عرض نموذج تعديل بيانات منتج حالي.
        /// </summary>
        /// <param name="id">معرف المنتج المراد تعديله.</param>
        /// <returns>عرض يحتوي على بيانات المنتج في نموذج التعديل، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null || _context.Products == null)
            {
                return NotFound();
            }

            var product = await _context.Products.FindAsync(id);
            if (product == null)
            {
                return NotFound();
            }
            var ob = new ProductModel()
            {
                Catagory = product.Catagory,
                Name = product.Name,
                Description = product.Description,
                Engdescription = product.Engdescription,
                Active = true,
                //SellPrice = product.SellPrice,  
                CreateOn = DateTime.Now,
                Engname = product.Engname,
                Price = product.Price.ToString()?.Replace('٫', '.'),
                SellPrice = product.SellPrice.ToString()?.Replace('٫', '.'),   
                Image = "/images/" + product.Image
            };
            
            ViewData["Catagory"] = new SelectList(_context.Categories, "Id", "Name", product.Catagory);
            return View(ob);
        }

        // POST: Products/Edit/5
        /// <summary>
        /// معالجة التعديلات المقدمة لمنتج وحفظها في قاعدة البيانات.
        /// </summary>
        /// <param name="product">البيانات المحدثة للمنتج.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة المنتجات؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(ProductModel product)
        {
            var ob = await _context.Products.FindAsync(product.Id);
            string oldpath = ob.Image;
            if (ob == null)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {



                ob.Catagory = product.Catagory;
                ob.Name = product.Name;
                ob.Description = product.Description;
                ob.Engdescription = product.Engdescription;
                ob.Active = true;
                ob.Details=product.Details;
                ob.Details = product.Details;   
                ob.CreateOn = DateTime.Now;
                ob.Engname = product.Engname;
                if (product.SellPrice != null) product.SellPrice = product.SellPrice.Replace('٫', '.');
                if (product.Price != null) product.Price = product.Price?.Replace('٫', '.');

                if (product.SellPrice != null)
                {
                    ob.SellPrice = double.Parse(product.SellPrice.Replace('٫', '.'), CultureInfo.GetCultureInfo("en-US"));
                }
                if (product.Price != null)
                {
                    ob.Price = double.Parse(product.Price.Replace('٫', '.'), CultureInfo.GetCultureInfo("en-US"));
                }
                try
                {
                    if (product.file != null) ob.Image = HandleImages.SaveImage(product.file, "images", _hosting);
                    _context.Update(ob);
                    await _context.SaveChangesAsync();
                    if (product.file != null && oldpath != null) HandleImages.RemoveImage(oldpath, "images", _hosting);
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ProductExists(product.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["Catagory"] = new SelectList(_context.Categories, "Id", "Name", product.Catagory);
            return View(product);
        }

        // GET: Products/Delete/5
        /// <summary>
        /// عرض صفحة تأكيد حذف منتج.
        /// </summary>
        /// <param name="id">معرف المنتج المراد حذفه.</param>
        /// <returns>عرض يحتوي على تفاصيل المنتج لتأكيد الحذف، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null || _context.Products == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.CatagoryNavigation)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (product == null)
            {
                return NotFound();
            }

            return View(product);
        }
      


        // POST: Products/Delete/5
        /// <summary>
        /// تنفيذ عملية حذف المنتج من قاعدة البيانات بعد التأكيد.
        /// </summary>
        /// <param name="id">معرف المنتج المراد حذفه.</param>
        /// <returns>بعد إتمام العملية بنجاح، يتم عرض صفحة قائمة المنتجات.</returns>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            if (_context.Products == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.Products'  is null.");
            }
            var product = await _context.Products.FindAsync(id);
            if (product != null)
            {
                product.Active = false; // بدلاً من الحذف الفعلي، نقوم بتعطيل المنتج 
                _context.Products.Update(product);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// التحقق من وجود منتج في قاعدة البيانات بالمعرف المحدد.
        /// </summary>
        /// <param name="id">معرف المنتج للتحقق منه.</param>
        /// <returns>إرجاع 'true' إذا كان المنتج موجودًا، وإلا 'false'.</returns>
        private bool ProductExists(int id)
        {
            return (_context.Products?.Any(e => e.Id == id)).GetValueOrDefault();
        }
    }
}