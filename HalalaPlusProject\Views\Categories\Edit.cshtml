﻿@model HalalaPlusProject.Models.Category

@{
    ViewData["Title"] = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<h4>تعديل صنف</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <div class="form-group">
                <label asp-for="Name" class="control-label">اسم الصنف</label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="EngName" class="control-label">اسم الصنف انجليزي</label>
                <input asp-for="EngName" class="form-control" />
                <span asp-validation-for="EngName" class="text-danger"></span>
            </div>
            <div class="form-group mt-2">
                <button type="submit" value="Create" class="btn btn-primary">حفظ</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">عودة</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
