﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("CombinedStatement")]
public partial class CombinedStatement
{
    [Key]
    public long Id { get; set; }

    [Column("AccountNO")]
    public long? AccountNo { get; set; }

    [StringLength(50)]
    public string? Name { get; set; }

    public long? UserId { get; set; }

    [Column("state")]
    [StringLength(50)]
    public string? State { get; set; }

    public bool Deleted { get; set; }

    public long? MainAccount { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateOn { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? UpdatedOn { get; set; }

    public double? Balance { get; set; }

    [Column("temp1")]
    [StringLength(500)]
    public string? Temp1 { get; set; }

    [Column("temp2")]
    [StringLength(500)]
    public string? Temp2 { get; set; }

    public bool? Active { get; set; }

    [Column("isAuth")]
    public bool? IsAuth { get; set; }

    public int? BankId { get; set; }

    [StringLength(50)]
    public string? EnName { get; set; }

    [InverseProperty("Acc")]
    public virtual ICollection<CombinedStatementTransction> CombinedStatementTransctions { get; set; } = new List<CombinedStatementTransction>();
}
