﻿@model HalalaPlusProject.CModels.UserDetails
@using System.Globalization
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer

@{
    ViewData["Title"] = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<div class="row">
         <div class="col-md-6">
         <div class="row">
              <div class="col-md-12" align="center">
                <a class="nav-link" asp-controller="Provider" asp-action="Index">
                    <img style="width:100px; margin-bottom:5px;" src="@Model.Logo" /></a>

              </div>
               <div class="col-md-12" align="center">
                     <label style="font-size:1.25rem; " class="form-label">@Model.Name</label>      
              </div>
              </div>
      <div class="row mt-4" >
     <div class="col-md-12">
          <div class="form-group" align="center">
                    <label style="font-size:1rem; width:39%; text-align:right;" class="form-label">@localizer["theactivity"] :</label>
                <label style="font-size:1.25rem; width:49%; text-align:right;"  class="form-label">@Model.ActivityName
                </label>               
            </div>
               <div class="form-group" align="center">
                    <label style="font-size:1rem; width:39%; text-align:right;" class="form-label"> @localizer["busnissno"]:</label>
                <label style="font-size:1.25rem; width:49%; text-align:right;"   class="form-label">@Model.BusnissNo</label>               
            </div>
               <div class="form-group" align="center">
                    <label style="font-size:1rem; width:39%; text-align:right;" class="form-label">@localizer["Phonenumber"]:</label>
                <label style="font-size:1.25rem; width:49%; text-align:right;"  class="form-label">@Model.EnterprisePhoneNo</label>               
            </div>
              <div class="form-group" align="center">
                    <label style="font-size:1rem; width:39%; text-align:right;" class="form-label" align="center">@localizer["serviceproviderrepresent"] :</label>
                <label style="font-size:1.25rem; width:49%; text-align:right;"  class="form-label">@Model.ServiceProviderRepresent</label>               
            </div>


      
 <div class="form-group" align="center">
                    <label style="font-size:1rem; width:39%; text-align:right;" class="form-label" align="center"> @localizer["phone"]:</label>
                <label  style="font-size:1.25rem; width:49%; text-align:right;" class="form-label">@Model.PhoneNo</label>               
            </div>
            <div class="form-group" align="center">
                    <label style="font-size:1rem; width:39%; text-align:right;" class="form-label" align="center"> @localizer["mail"]:</label>
                <label style="font-size:1.25rem; width:49%; text-align:right;"  class="form-label">@Model.Email</label>               
            </div>
            
             <div class="form-group" align="center">
                    <label style="font-size:1rem; width:39%; text-align:right;" class="form-label">@localizer["Startdateofdiscounts"]:</label>
                <label style="font-size:1.25rem; width:49%; text-align:right;"  class="form-label">@Model.startDate</label>               
            </div>

             <div class="form-group" align="center">
                    <label style="font-size:1rem; width:39%; text-align:right;" class="form-label"> @localizer["enddateofdiscounts"]:</label>
                <label style="font-size:1.25rem; width:49%; text-align:right;"   class="form-label">@Model.endDate</label>               
            </div>
             <div class="form-group" align="center">
                    <label style="font-size:1rem; width:39%; text-align:right;" class="form-label">@localizer["Cashbackforpiggybank"]:</label>
                    <label style="font-size:1.25rem; width:49%; text-align:right;" class="form-label">@Model.CashBack</label>
                </div>
             <div class="form-group" align="center">
                    <label style="font-size:1rem; width:39%; text-align:right;" class="form-label"> @localizer["username"]:</label>
                    <label style="font-size:1.25rem; width:49%; text-align:right;" class="form-label">@Model.UserName</label>
                </div>




</div>
          </div>  </div>
    <div class="col-md-6 ">
        <canvas id="statsChart" width="400" height="400"></canvas>
    </div>
</div>
 

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        const statsLabels = [
            "@localizer["Numberofdiscounts"]",
            "@localizer["Valueofdiscounts"]",
            "@localizer["Number of offers"]",
            "@localizer["valueofoffers"]",
            "@localizer["Number of discount codes"]",
            "@localizer["Valueofdiscountcodes"]",
            "@localizer["Numberofcodesused"]",
            "@localizer["Pointsawarded"]",
            "@localizer["Actualdiscounts"]"
        ];

        const statsData = [
    @Model.GetProviderStatistics.DiscountsCount ?? 0,
    @Model.GetProviderStatistics.TotalDiscountAmount ?? 0,
    @Model.GetProviderStatistics.OffersCount ?? 0,
    @Model.GetProviderStatistics.TotalOffersCount ?? 0,
    @Model.GetProviderStatistics.CodeCount ?? 0,
    @Model.GetProviderStatistics.TotalCodeCount ?? 0,
    @Model.GetProviderStatistics.UsedDiscountCodes ?? 0,
    @Model.GetProviderStatistics.TotalGrantedPoints ?? 0,
    @Model.GetProviderStatistics.TotalGrantedDiscount ?? 0
        ];

        const ctx = document.getElementById('statsChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: statsLabels,
                datasets: [{
                    label: 'إحصائيات المزود',
                    data: statsData,
                    backgroundColor: [
                        '#FF6384', '#36B5EB', '#FFCE56', '#4BC0C0',
                        '#9966FF', '#FF9F40', '#66BB6A', '#AB47BC', '#29B6F6'
                    ],
                    borderColor: '#fff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                cutout: '60%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: { size: 14 }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function (context) {
                                return `${context.label}: ${context.formattedValue}`;
                            }
                        }
                    }
                }
            }
        });
    });
</script>

