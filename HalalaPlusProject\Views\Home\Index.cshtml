﻿@{
    ViewData["Title"] = "Home Page";
}
@using HalalaPlusProject.Areas.Identity.Data
@using Microsoft.AspNetCore.Identity
@inject SignInManager<HalalaPlusProjectUser> SignInManager
@inject UserManager<HalalaPlusProjectUser> UserManager
@using Microsoft.AspNetCore.Mvc.Localization
@model HalalaPlusProject.CModels.UserStatistic

@inject IViewLocalizer localizer

<div class="text-center">
    @* <h3 class="display-4">@localizer["welcome"]</h3> *@
    @* <span>Arabian Standard Time: @ViewBag.CurrentMyDateTimeUtcZ.ToString("F")</span> *@
    @*     <div class="container">
            <span>Local Time: @ViewBag.CurrentDateTime.ToString("F")</span>
            <span>UTC Time: @ViewBag.CurrentDateTimeUtc.ToString("F")</span>
            <span>Arabian Standard Time: @ViewBag.CurrentMyDateTimeUtc</span>
            <span>Arabian Standard Time: @ViewBag.CurrentMyDateTimeUtcZ.ToString("F")</span>
    </div> *@

        <h2>لوحة المعلومات - Dashboard</h2>
        <div class="dashboard">
            <div class="chart-container"><canvas id="chart1" width="400" height="200"></canvas></div>
            <div class="chart-container"><canvas id="chart2" width="400" height="200"></canvas></div>
        </div>
    </div>

    <div >
        <h4 style="text-align:center;">الاحـصـائـيـات</h4>
        <ul class="statistics-list">
            <li>
                <span class="title-en">إجمالي عدد حصالات المستخدمين</span>
                <span class="value">@Model.TotalUsersMonyBoxs</span>
                <span class="title-ar">Total Users MonyBoxs</span>
            </li>
            <li>
                <span class="title-en">إجمالي  مبالغ حصالات المستخدمين</span>
                <span class="value">@Model.TotalAmount.ToString("N2")</span>
                <span class="title-ar">Total Amount</span>
            </li>
            <li>
                <span class="title-en">إجمالي عدد مقدمي الخدمات </span>
                <span class="value">@Model.Providers</span>
                <span class="title-ar">Services Provides</span>
            </li>
            <li>
                <span class="title-en">إجمالي توفير المستخدمين</span>
                <span class="value">@Model.TotalDiscount.ToString("N2")</span>
                <span class="title-ar">Total Discount</span>
            </li>
            <li>
                <span class="title-en">إجمالي عدد زيارات الموقع</span>
                <span class="value">@Model.visitors</span>
                <span class="title-ar">Site Visits</span>
            </li>
            <li>
                <span class="title-en">عدد المستخدمين المسجلين</span>
                <span class="value">@Model.ActiveUsers</span>
                <span class="title-ar">Application Users</span>
            </li> <li>
                <span class="title-en">كل596 المستخدمين </span>
            <span class="value">@Model.AllUsers</span>
                <span class="title-ar">All Users</span>
            </li>

        </ul>
    </div>


<style>
    

    body {
        font-family: 'Roboto', sans-serif;
        background-color: #f5f5f5;
        color: #333;
    }

    .container {
        max-width: 900px;
        margin: 0 auto;
        padding: 20px;
    }

    .text-center {
        text-align: center;
    }

    .dashboard {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    .chart-container {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .statistics-list {
        list-style-type: none;
        padding: 0;
    }

    .statistics-list li {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #ddd;
        align-items: center;
    }

    .title-en {
        font-weight: 500;
        flex: 1;
        text-align: right;
    }
    .title-ar {
        font-weight: 500;
        flex: 1;
        text-align: left;
    }

    .value {
        flex:1;
        text-align: center;
        font-size: 1.2em;
        font-weight: bold;
    }

    h2 {
        font-size: 1.75em;
        color: #007bff;
        margin-bottom: 20px;
    }

    h4 {
        font-size: 1.5em;
        color: #007bff;
        margin-top: 30px;
    }

    .card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .card span {
        display: block;
    }
</style>

<script>
    var tempData = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.chartData)),
        tempLast7Days = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.Last7Days)),
        tempLast7Months = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.Last7Months)),
        calcTotalDiscountData = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.TotalDiscountData))

    document.addEventListener('DOMContentLoaded', (event) => {
        const createLineChart = (ctx, data, label) => {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: tempLast7Days,
                    datasets: [{
                        label: label,
                        data: tempData,
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                        pointBorderColor: '#fff',
                        pointRadius: 5,
                        pointHoverRadius: 7,
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        };

        const createBarChart = (ctx, data, label) => {
            new Chart(ctx, {
                type: 'bar',
                data: {
                    // labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
                    labels: tempLast7Months,
                    datasets: [{
                        label: label,
                        data: calcTotalDiscountData,
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        };

        // Chart data for one month
        const chartData1 = [12, 15, 10, 8,1,2,3];
        const chartData2 = [3.2, 6.34, 8.23, 4.22, 7.21, 2.90, 9.23];


        createLineChart(document.getElementById('chart1').getContext('2d'), chartData1, ' زوار الـمـوقـع آخر 7 ايام حسب التاريخ');
        createBarChart(document.getElementById('chart2').getContext('2d'), chartData2, ' تـوفـيـر الـعـمـلاء آخر 7 شهور');
        });
    </script>


