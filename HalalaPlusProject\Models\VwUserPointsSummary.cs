﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class VwUserPointsSummary
{
    public long? UserId { get; set; }

    public long? ProviderId { get; set; }

    public double? TotalPoints { get; set; }

    public double? DesrveAmount { get; set; }

    public bool? Accept { get; set; }

    [Column("state")]
    [StringLength(500)]
    public string State { get; set; } = null!;

    [Column(TypeName = "datetime")]
    public DateTime? OrderDate { get; set; }

    public long OrderId { get; set; }

    [StringLength(500)]
    public string ProviderName { get; set; } = null!;

    public bool? Replaced { get; set; }
}
