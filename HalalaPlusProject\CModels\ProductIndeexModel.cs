﻿namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل منتجًا في صفحة العرض مع تفاصيله الأساسية.
    /// </summary>
    public class ProductIndeexModel
    {
        /// <summary>
        /// معرف المنتج.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// اسم المنتج باللغة العربية.
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// اسم المنتج باللغة الإنجليزية.
        /// </summary>
        public string? EnName { get; set; }

        /// <summary>
        /// اسم الفئة التي ينتمي إليها المنتج.
        /// </summary>
        public string? Catagory { get; set; }

        /// <summary>
        /// سعر المنتج.
        /// </summary>
        public double? Price { get; set; }
        /// <summary>
        /// سعر المنتج.
        /// </summary>
        public double? SellPrice { get; set; }
        public bool active { get; set; }
    }
}
