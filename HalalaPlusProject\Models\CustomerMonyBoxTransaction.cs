﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class CustomerMonyBoxTransaction
{
    public long CustomerId { get; set; }

    [StringLength(500)]
    public string? CustomerName { get; set; }

    [StringLength(50)]
    public string? CustomerPhone { get; set; }

    [StringLength(50)]
    public string? CustomerEmail { get; set; }

    [StringLength(20)]
    public string? AccountType { get; set; }

    public string? MonyBoxes { get; set; }
}
