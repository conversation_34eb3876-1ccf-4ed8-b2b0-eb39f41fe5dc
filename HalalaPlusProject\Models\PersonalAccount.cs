﻿using System;
using System.Collections.Generic;

namespace HalalaPlusProject.Models;

public partial class PersonalAccount
{
    public int AccountId { get; set; }

    public long? UserId { get; set; }

    public decimal? Salary { get; set; }

    public string? Notes { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? CreatedAt { get; set; }

    public string? UpdatedBy { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public bool? Deleted { get; set; }

    public string? DeletedBy { get; set; }

    public DateTime? DeletedAt { get; set; }

    public string? CalendarType { get; set; }

    public byte? SalaryDeliverDate { get; set; }

    public virtual ICollection<AdditionalIncome> AdditionalIncomes { get; set; } = new List<AdditionalIncome>();

    public virtual SystemUser? User { get; set; }
}
