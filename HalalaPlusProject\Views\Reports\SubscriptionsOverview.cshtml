﻿@using System.Globalization
@model IEnumerable<HalalaPlusProject.Models.vw_SubscriptionsOverview>

@{
    ViewData["Title"] = " تقرير الاشتراكات الدورية";
}

<h2> تقرير الاشتراكات الدورية </h2>
<div class="row">
    <div class="col-12">
        <div class="card mb-4">

            <div class="card-body px-0 pt-0 pb-2">
                <div class="table-responsive p-0">
<table id="tbl1" class="table"> 
                            <thead>
                                <tr>
                                    <th>الباقة</th>
                                    <th>المشتركون النشطون</th>
                                    <th>الجدد هذا الشهر</th>
                                    <th>إجمالي الإيرادات</th>
                                    <th>نسبة التسرب (%)</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var row in Model)
                                {
                                    <tr>
                                        <td>@row.PackageName</td>
                                        <td>@row.ActiveSubscribers</td>
                                        <td>@row.NewSubscribersThisMonth</td>
                                    <td>@row.TotalRevenueFromPackage.ToString("C2", new CultureInfo("ar-SA"))</td>
                                        <td>@row.ChurnRate</td>
                                    </tr>
                                }
                            </tbody>
                        </table>

                </div>
            </div>
        </div>
    </div>
@section Scripts{
        <script>
            let table = new DataTable('#tbl1');

        </script>
}
