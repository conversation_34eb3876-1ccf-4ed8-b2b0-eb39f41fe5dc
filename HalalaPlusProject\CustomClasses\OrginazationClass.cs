﻿namespace HalalaPlusProject.CustomClasses
{
    public class OrginazationClass
    {
        Models.HalalaPlusdbContext _context;
        IWebHostEnvironment _hosting;
        public OrginazationClass(Models.HalalaPlusdbContext context, IWebHostEnvironment hosting)
        {
            this._context = context;
            this._hosting = hosting;
        }
        public bool Insert(Entities.CreateOrginazationModel model, Areas.Identity.Data.HalalaPlusProjectUser user, string MasterId)
        {

            try
            {
                _context.Database.BeginTransaction();
                var ob = new Models.SystemUser();
                ob.Name = model.Name;
                ob.EnName = model.EnName;
                ob.ServiceProviderRepresent = model.ServiceProviderRepresent;
                ob.PhoneNo = model.PhoneNumber;
                ob.EnterPrisePhoneNo = model.EnterprisePhoneNo;
                ob.Activity = model.Activity;
                ob.BusinessNo = model.BusnissNo;
                ob.Email = model.Email;
                ob.StoreLink = model.StoreLink;
                ob.Lat = model.Lat;
                ob.Lng = model.lng;
                ob.OverView = model.overview;
                ob.EnOverView = model.enoverview;
                ob.Benefitfrompoints = model.bnifitfrompoints;
                ob.EnBenefitfrompoints = model.enbnifitfrompoints;
                ob.City = model.City;
                ob.Logo = user.Image;
                ob.AccountType = "Orginazation";
                ob.Status = "A";
                ob.FirstColor = model.FirstColor;
                ob.SecondColor = model.SecondColor;
               if(model.OffersIcon!=null)
                ob.OffesLogo= HandleImages.SaveImage(model.OffersIcon, "images", _hosting);
                ob.AspId = user.Id;
                ob.MasterId = MasterId;
                ob.ContractNo = model.ContractNo;
                ob.Deleted = false;
                ob.CashBack = model.CashBack;
              if(model.ContractDate!=null)  ob.ContractDate =DateOnly.Parse( model.ContractDate.ToString());
                
                ob.DateofJoin = DateTime.Now;
                if (model.ContractEndDate != null) ob.ContractEndDate =DateOnly.Parse( model.ContractEndDate.ToString());
                _context.Add(ob);
                _context.SaveChanges();
                if (model.Files != null)
                    new FilesClass().insertOrdersFiles(model.Files, ob.Id, "Files", _hosting, _context, 1);
                if (model.Images != null)
                    new FilesClass().insertOrdersFiles(model.Files, ob.Id, "images", _hosting, _context, 5);
                _context.Database.CommitTransaction();
                return true;
            }
            catch (Exception)
            {
                _context.Database.RollbackTransaction();
                return false;
            }

        }
 public bool InsertEvent(Entities.CreateEventModel model, Areas.Identity.Data.HalalaPlusProjectUser user, string MasterId)
        {

            try
            {
                _context.Database.BeginTransaction();
                var ob = new Models.SystemUser();
                ob.Name = model.Name;
                ob.EnName = model.EnName;
                //ob.ServiceProviderRepresent = model.ServiceProviderRepresent;
                ob.PhoneNo = model.PhoneNumber;
                //ob.EnterPrisePhoneNo = model.EnterprisePhoneNo;
                //ob.Activity = model.Activity;
                //ob.BusinessNo = model.BusnissNo;
                ob.Email = model.Email;
                ob.StoreLink = model.StoreLink;
                ob.Lat = model.Lat;
                ob.Lng = model.lng;
                //ob.OverView = model.overview;
                //ob.EnOverView = model.enoverview;
                //ob.Benefitfrompoints = model.bnifitfrompoints;
                //ob.EnBenefitfrompoints = model.enbnifitfrompoints;
                ob.City = model.City;
                ob.Logo = user.Image;
                ob.AccountType = "event";
                ob.Status = "A";
                //ob.FirstColor = model.FirstColor;
                //ob.SecondColor = model.SecondColor;
               if(model.OffersIcon!=null)
                ob.OffesLogo= HandleImages.SaveImage(model.OffersIcon, "images", _hosting);
                ob.AspId = user.Id;
                ob.MasterId = MasterId;
                ob.ContractNo = model.ContractNo;
                ob.Deleted = false;
                //ob.CashBack = model.CashBack;
              if(model.ContractDate!=null)  ob.ContractDate =DateOnly.Parse( model.ContractDate.ToString());
                
                ob.DateofJoin = DateTime.Now;
                if (model.ContractEndDate != null) ob.ContractEndDate =DateOnly.Parse( model.ContractEndDate.ToString());
                _context.Add(ob);
                _context.SaveChanges();
                if (model.Files != null)
                    new FilesClass().insertOrdersFiles(model.Files, ob.Id, "Files", _hosting, _context, 1);
                if (model.Images != null)
                    new FilesClass().insertOrdersFiles(model.Files, ob.Id, "images", _hosting, _context, 5);
                _context.Database.CommitTransaction();
                return true;
            }
            catch (Exception)
            {
                _context.Database.RollbackTransaction();
                return false;
            }

        }

    }
}
