﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class LoginDevice
{
    [Key]
    public long Id { get; set; }

    public long? UserI { get; set; }

    [StringLength(500)]
    public string? DeviceName { get; set; }

    [Column("DeviceIP")]
    [StringLength(500)]
    public string? DeviceIp { get; set; }

    [Column("DeviceMAC")]
    [StringLength(500)]
    public string? DeviceMac { get; set; }

    [StringLength(500)]
    public string? DeviceVersion { get; set; }

    [StringLength(500)]
    public string? DeviceSystem { get; set; }

    [Column("isCurrentDevice")]
    public bool? IsCurrentDevice { get; set; }

    [Column("state")]
    [StringLength(50)]
    public string? State { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? AddDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? RemoveDate { get; set; }

    [Column("authenticated")]
    public bool Authenticated { get; set; }
}
