﻿@model IEnumerable<HalalaPlusProject.Models.MonyBoxsIcon>
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@foreach (var item in Model)
{<div id="<EMAIL>" class="col-md-3 col-sm-4 mb-lg-0 mb-4">

        

        <div class="card">
            <div class="card-header mx-4 p-3 text-center">
                <div class="row justify-content-center">
                    <div class="circle ">
                        <img id="<EMAIL>" src="@item.Link">
                    </div>
                </div>
                <div class="row">
                    <h6 id="<EMAIL>" class="text-center mb-0"> @item.Name</h6>
                </div>
            </div>
            <div class="card-body pt-0 p-3 text-center">



                <button onclick="EditIcon('@item.Id','@item.Name','@item.Link');" class="btn btn-outline-primary btnicons btn-sm m-1 mb-0">@localizer["email"] </button>

                <button onclick="handleicon('@item.Id')"  class="btn btn-outline-primary btnicons btn-sm mb-0 m-1">@localizer["delete"]</button>
               
            </div>
        </div>

</div>

}