﻿@model HalalaPlusProject.Models.CountriesTable

@{
    ViewData["Title"] = "Create";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>إنشاء</h1>

<h4>دولة</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="Country" class="control-label">اسم الدولة</label>
                <input asp-for="Country" required class="form-control" />
                <span asp-validation-for="Country" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Nationality" class="control-label">أسم الجنسية الخاصة بالدولة</label>
                <input asp-for="Nationality" required class="form-control" />
                <span asp-validation-for="Nationality" class="text-danger"></span>
            </div>
            <div class="form-group">
                <button type="submit" value="Create" class="btn btn-primary" >إنشاء</button>
            </div>
        </form>
    </div>
    <div class="col-md-4">
        

        </div>

</div>

<div>
    <a asp-action="Index">عودة للقائمة السابقة</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
