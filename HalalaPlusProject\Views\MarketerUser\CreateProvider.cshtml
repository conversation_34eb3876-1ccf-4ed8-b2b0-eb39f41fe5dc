@model HalalaPlusProject.CModels.ServiceProvidersCreate
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["createprovider"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<link href="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css" rel="stylesheet" />
<link href="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v4.7.2/mapbox-gl-geocoder.css"
    rel="stylesheet" />
<link href="~/css/mapbox.css" rel="stylesheet" />
@*<div>
    <a href="~/ServiceProvider/index"><img src="../assets/img/svgs/solid/arrow-right.svg" style="width: 40px;" alt=""></a>
    <h3> مقدمي الخدمات</h3>

</div>*@
<div class="row">
    <div class="col-md-12">
        <form asp-controller="MarketerUser" asp-action="CreateProvider" enctype="multipart/form-data" id="ProvidersFrm" >
            <div class="row">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label asp-for="Name" class="control-label">@localizer["name"]</label>
                        <input asp-for="Name" class="form-control" />
                        <span asp-validation-for="Name"  class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Logo" class="control-label">@localizer["logo"]</label>
                        <input asp-for="Logo" accept="image/*" class="form-control" />
                        <span asp-validation-for="Logo" class="text-danger"></span>
                    </div>


                    <div class="form-group">
                        <label asp-for="Activity" class="control-label">@localizer["activity"]</label>
                        @*<select asp-for="Activity" required class ="form-control" asp-items="ViewBag.Activity"></select>*@
                        <select name="Activity" id="Activity" class ="form-select" >
                            @foreach (var item in ViewBag.Activity) {
                                <option value="@item.Value">@item.Text </option>
                            }
                        </select>
                  <span asp-validation-for="Activity" class="text-danger"></span>
            </div>
            
              <div class="form-group">
                        <label asp-for="City" class="control-label">@localizer["thecity"]</label>
                <select asp-for="City"  class ="form-select" asp-items="ViewBag.City"></select>           
                  <span asp-validation-for="City" class="text-danger"></span>
            </div>
              <div class="form-group">
                        <label asp-for="BusnissNo" class="control-label">رقم السجل التجاري</label>
                <input asp-for="BusnissNo"  class="form-control" />
                <span asp-validation-for="BusnissNo" class="text-danger"></span>
            </div>
            <div class="form-group">
                        <label asp-for="BranchsNo" class="control-label">عدد الفروع</label>
                <input asp-for="BranchsNo"  class="form-control" />
                        <span asp-validation-for="BranchsNo" class="text-danger"></span>
            </div>
            <div class="form-group">
                        <label asp-for="EnterprisePhoneNo" class="control-label">@localizer["enterprisephoneno"]</label>
                <input asp-for="EnterprisePhoneNo"  class="form-control" />
                <span asp-validation-for="EnterprisePhoneNo" class="text-danger"></span>
            </div>
          </div>


            <div class="col-md-3">
                  <div class="form-group">
                        <label asp-for="CashBack" class="control-label">@localizer["cashback"]</label>
                <input asp-for="CashBack"  class="form-control" />
                <span asp-validation-for="CashBack" class="text-danger"></span>
            </div>
            <div class="form-group">
                        <label asp-for="ContractNo" class="control-label">@localizer["contractno"]</label>
                <input asp-for="ContractNo"   class="form-control" />
                <span asp-validation-for="ContractNo" class="text-danger"></span>
           @*  </div>
                 <div class="form-group">
                        <label asp-for="ContractDate" class="control-label">@localizer["contractstartdate"]</label>
                <input asp-for="ContractDate" type="date"  class="form-control" />
                <span asp-validation-for="ContractDate" class="text-danger"></span>
            </div>
           <div class="form-group">
                        <label asp-for="ContractEndDate " class="control-label">@localizer["contractenddate"]</label>
                        <input asp-for="ContractEndDate" type="date" class="form-control" />
                <span asp-validation-for="ContractEndDate" class="text-danger"></span>
            </div> *@
          
            
           
             

              <div class="form-group">
                        <label asp-for="Files" class="control-label">@localizer["attatchments"]</label>
                <input asp-for="Files"  multiple class="form-control" />
                <span asp-validation-for="Files" class="text-danger"></span>
            </div>
           
             
            </div>
            <div class="col-md-3">
                 <div class="form-group">
                        <label asp-for="ServiceProviderRepresent" class="control-label">@localizer["serviceproviderrepresent"]</label>
                <input asp-for="ServiceProviderRepresent"  class="form-control" />
                <span asp-validation-for="ServiceProviderRepresent" class="text-danger"></span>
            </div>
            <div class="form-group">
                        <label asp-for="ActorDescription" class="control-label">صفته</label>
                        <input asp-for="ActorDescription" class="form-control" />
                        <span asp-validation-for="ActorDescription" class="text-danger"></span>
            </div>
                 <div class="form-group">
                        <label asp-for="PhoneNumber" class="control-label">@localizer["phoneno"]</label>
                <input asp-for="PhoneNumber"  class="form-control" />
                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
            </div>
                  <div class="form-group">
                        <label asp-for="Email" class="control-label">@localizer["mail"]</label>
                <input asp-for="Email" required class="form-control" />
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>
            @* <div class="form-group">
                        <label asp-for="UserName" class="control-label">@localizer["username"]</label>
                <input asp-for="UserName" required class="form-control" />
                <span asp-validation-for="UserName" class="text-danger"></span>
            </div> *@
             <div class="form-group">
                        <label asp-for="Password" class="control-label">@localizer["password"]</label>
                <input asp-for="Password" required class="form-control" />
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>
            </div>
            
          <div class="col-md-3">

                  @*   <div class="form-group">
                        <label asp-for="LocationLink" class="control-label">@localizer["lat"]<span style="font-size:20pt; color: red;">&nbsp*</span></label>
                        <input asp-for="LocationLink" required class="form-control" />
                        <span asp-validation-for="LocationLink" class="text-danger"></span>
                    </div> *@

                    @* <div class="form-group">
                        <label asp-for="Lat" class="control-label">@localizer["lat"]</label>
                    <input asp-for="Lat"  class="form-control" />
                    <span asp-validation-for="Lat" class="text-danger"></span>
                    </div>
               
                  <div class="form-group">
                                <label asp-for="lng" class="control-label">@localizer["lng"]</label>
                        <input asp-for="lng"  class="form-control" />
                        <span asp-validation-for="lng" class="text-danger"></span>
                    </div> *@
            <div class="form-group" id="map">
        
            </div>

            </div>
            <div class="col-md-3">
             <div class="mb-3">
                        <label for="location">* Location</label>

                        <div class="input-group mb-2">
                            <div class="form-control p-0" id="map-geocoder"></div>

                            <button type="button" title="تحليل رابط موقع" id="map-toggle-link-input"
                                class="input-group-text bg-white">
                                <i class="bi bi-link text-secondary"></i>
                            </button>

                            <button type="button" title="إدخال يدوي" id="map-manual-btn"
                                class="input-group-text bg-white">
                                <i class="bi bi-globe text-secondary"></i>
                            </button>

                            <button type="button" title="موقعي الحالي" id="map-getCurrentLocation"
                                class="input-group-text bg-white">
                                <i class="bi bi-geo-alt text-secondary"></i>
                            </button>
                        </div>

                        <div id="map-link-input-wrapper" class="mt-2" style="display: none;">
                            <div class="">
                                <input type="text" id="map-map-link" class="form-control"
                                    placeholder="ألصق رابط الموقع هنا" />
                                <button type="button" id="map-parse-link" class="btn btn-outline-secondary btn-sm mt-2">
                                    تحليل الرابط
                                </button>
                            </div>
                        </div>

                        <!-- Map Container -->
                        <div id="map-container" class="position-relative rounded overflow-hidden border"
                            style="height: 300px; ">

                            <div class="row mb-2 position-absolute top-0 start-0 m-2 z-3" style="z-index: 1000;">
                                <div class="col">
                                    <input asp-for="Lng" type="number" name="Lng" step="any" id="map-longitude"
                                        class="form-control" placeholder="Longitude" />
                                </div>
                                <div class="col">
                                    <input asp-for="Lat" type="number" name="Lat" step="any" id="map-latitude"
                                        class="form-control" placeholder="Lat" />
                                </div>
                            </div>



                            <div id="container-map" class="w-100 h-100"></div>

                        </div>

                        <span class="longitude-error text-danger text-error"></span>
                        <span class="latitude-error text-danger text-error"></span>
                    </div>
            </div>


            </div>


          
           
          
          
           
           
            <div class="mt-2">
                <button type="submit" value="Create" class="btn btn-primary">@localizer["save"]</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>

@section Scripts {



    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
        <script src="https://polyfill.io/v3/polyfill.min.js?features=default"></script>
        <script>(g=>{var h,a,k,p="The Google Maps JavaScript API",c="google",l="importLibrary",q="__ib__",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),r=new Set,e=new URLSearchParams,u=()=>h||(h=new Promise(async(f,n)=>{await (a=m.createElement("script"));e.set("libraries",[...r]+"");for(k in g)e.set(k.replace(/[A-Z]/g,t=>"_"+t[0].toLowerCase()),g[k]);e.set("callback",c+".maps."+q);a.src=`https://maps.${c}apis.com/maps/api/js?`+e;d[q]=f;a.onerror=()=>h=n(Error(p+" could not load."));a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}));d[l]?console.warn(p+" only loads once. Ignoring:",g):d[l]=(f,...n)=>r.add(f)&&u().then(()=>d[l](f,...n))})
        ({key: "AIzaSyB41DRUbKWJHPxaFjMAwdrzWzbVKartNGg", v: "weekly"});</script>
    @* <script>
    async function initMap() {
  // Request needed libraries.
  const { Map } = await google.maps.importLibrary("maps");
  const myLatlng = { lat:  20.99265387585728, lng:40.54420880475375};
  const map = new google.maps.Map(document.getElementById("map"), {
    zoom: 6,
    center: myLatlng,
  });
  // Create the initial InfoWindow.
  let infoWindow = new google.maps.InfoWindow({
    content: "Click the map to get Lat/Lng!",
    position: myLatlng,
    name:'KSA'
  });
  //var marker = new google.maps.Marker({  
  //                  position: myLatlng,  
  //                  map: map,  
  //                  title: 'Halala'  
  //              }); 
  infoWindow.open(map);
  // Configure the click listener.
  map.addListener("click", (mapsMouseEvent) => {
    console.log("clicked:",myLatlng);
    // Close the current InfoWindow.
    infoWindow.close();
    // Create a new InfoWindow.
    infoWindow = new google.maps.InfoWindow({
      position: mapsMouseEvent.latLng,
    });
    infoWindow.setContent(
      JSON.stringify(mapsMouseEvent.latLng.toJSON(), null, 2),
    );
    infoWindow.open(map);
  });
}

initMap();
  </script> *@
}
<script src="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js"></script>
<script src="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v4.7.2/mapbox-gl-geocoder.min.js"></script>

<script src="~/js/mapbox.js"></script>