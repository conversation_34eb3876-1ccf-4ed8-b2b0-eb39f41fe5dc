﻿@using System.Globalization
@model IEnumerable<HalalaPlusProject.Models.vw_TopProvidersReport>

@{
    ViewData["Title"] = "تقرير العملاء";
}

<h2>تقرير الموزعين الأكثر تفاعلاً</h2>
<div class="row">
    <div class="col-12">
        <div class="card mb-4">

            <div class="card-body px-0 pt-0 pb-2">
                <div class="table-responsive p-0">
<table id="tbl1" class="table">
    <thead class="table-dark">
        <tr>
            <th>معرّف</th>
            <th>اسم المزود</th>
            <th>المدينة</th>
            <th>النشاط</th>
            <th>عدد الخصومات</th>
            <th>إجمالي قيمة الخصومات</th>
            <th>عدد العروض</th>
            <th>إجمالي قيمة العروض</th>
            <th>عدد أكواد الخصم</th>
            <th>إجمالي قيمة أكواد الخصم</th>
            <th>الأكواد المستخدمة</th>
            <th>النقاط الممنوحة</th>
            <th>قيمة الخصومات الممنوحة</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var r in Model)
        {
            <tr>
                <td>@r.ProviderId</td>
                <td>@r.ProviderName</td>
                <td>@r.CityName</td>
                <td>@r.ActivityName</td>
                <td>@r.DiscountsCount</td>
                <td>@r.DiscountsTotalValue.ToString("C2", new CultureInfo("ar-SA"))</td>
                <td>@r.OffersCount</td>
                <td>@r.OffersTotalValue.ToString("C2", new CultureInfo("ar-SA"))</td>
                <td>@r.CouponCodesCount</td>
                <td>@r.CouponCodesTotalValue.ToString("C2", new CultureInfo("ar-SA"))</td>
                <td>@r.UsedCouponCodesCount</td>
                <td>@r.TotalPointsGranted</td>
                <td>@r.TotalGrantedDiscountsValue.ToString("C2", new CultureInfo("ar-SA"))</td>
            </tr>
        }
    </tbody>
    </table>

                </div>
            </div>
        </div>
    </div>
@section Scripts{
        <script>
            let table = new DataTable('#tbl1');

        </script>
}
