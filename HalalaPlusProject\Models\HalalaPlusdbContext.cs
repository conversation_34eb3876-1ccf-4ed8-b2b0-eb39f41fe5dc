﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class HalalaPlusdbContext : DbContext
{
    public HalalaPlusdbContext()
    {
    }

    public HalalaPlusdbContext(DbContextOptions<HalalaPlusdbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Achievement> Achievements { get; set; }

    public virtual DbSet<ActivationLog> ActivationLogs { get; set; }

    public virtual DbSet<ActivitiesCitiesView> ActivitiesCitiesViews { get; set; }

    public virtual DbSet<Activity> Activities { get; set; }

    public virtual DbSet<AdditionalIncome> AdditionalIncomes { get; set; }

    public virtual DbSet<AnonymousUserActivity> AnonymousUserActivities { get; set; }

    public virtual DbSet<Article> Articles { get; set; }

    public virtual DbSet<AspNetRole> AspNetRoles { get; set; }

    public virtual DbSet<AspNetRoleClaim> AspNetRoleClaims { get; set; }

    public virtual DbSet<AspNetUser> AspNetUsers { get; set; }

    public virtual DbSet<AspNetUserClaim> AspNetUserClaims { get; set; }

    public virtual DbSet<AspNetUserLogin> AspNetUserLogins { get; set; }

    public virtual DbSet<AspNetUserToken> AspNetUserTokens { get; set; }

    public virtual DbSet<AspNetUsersWithSystemUsersView> AspNetUsersWithSystemUsersViews { get; set; }

    public virtual DbSet<BankAccountTransction> BankAccountTransctions { get; set; }

    public virtual DbSet<BanksAccount> BanksAccounts { get; set; }

    public virtual DbSet<CardsOrder> CardsOrders { get; set; }

    public virtual DbSet<Cart> Carts { get; set; }

    public virtual DbSet<Category> Categories { get; set; }

    public virtual DbSet<CatgoriesDetail> CatgoriesDetails { get; set; }

    public virtual DbSet<Checkout> Checkouts { get; set; }

    public virtual DbSet<CitiesTable> CitiesTables { get; set; }

    public virtual DbSet<CombinedStatement> CombinedStatements { get; set; }

    public virtual DbSet<CombinedStatementTransction> CombinedStatementTransctions { get; set; }

    public virtual DbSet<ConnectBanksOtpCode> ConnectBanksOtpCodes { get; set; }

    public virtual DbSet<ConsentHistory> ConsentHistories { get; set; }

    public virtual DbSet<ContactU> ContactUs { get; set; }

    public virtual DbSet<CountriesTable> CountriesTables { get; set; }

    public virtual DbSet<CouponsOffersProvidersView> CouponsOffersProvidersViews { get; set; }

    public virtual DbSet<CouponsOffersView> CouponsOffersViews { get; set; }

    public virtual DbSet<CoupounsProviderActivitiesView> CoupounsProviderActivitiesViews { get; set; }

    public virtual DbSet<CustomerDiscount> CustomerDiscounts { get; set; }

    public virtual DbSet<CustomerDiscountsView> CustomerDiscountsViews { get; set; }

    public virtual DbSet<CustomerGrantedDiscountsView> CustomerGrantedDiscountsViews { get; set; }

    public virtual DbSet<CustomerMonyBoxTransaction> CustomerMonyBoxTransactions { get; set; }

    public virtual DbSet<CustomerPlan> CustomerPlans { get; set; }

    public virtual DbSet<CustomerRewardSetting> CustomerRewardSettings { get; set; }

    public virtual DbSet<DiscountsProvidersActivitiesView> DiscountsProvidersActivitiesViews { get; set; }

    public virtual DbSet<DiscountsSale> DiscountsSales { get; set; }

    public virtual DbSet<DiscountsTable> DiscountsTables { get; set; }

    public virtual DbSet<FaqsTable> FaqsTables { get; set; }

    public virtual DbSet<FilesTable> FilesTables { get; set; }

    public virtual DbSet<GetUsersWithActivitiesAndNationality> GetUsersWithActivitiesAndNationalities { get; set; }

    public virtual DbSet<GrantDiscount> GrantDiscounts { get; set; }

    public virtual DbSet<GrantedDiscount> GrantedDiscounts { get; set; }

    public virtual DbSet<GrantingHistory> GrantingHistories { get; set; }

    public virtual DbSet<InvestorsTable> InvestorsTables { get; set; }

    public virtual DbSet<LoginDevice> LoginDevices { get; set; }

    public virtual DbSet<MasajedDiscountView> MasajedDiscountViews { get; set; }

    public virtual DbSet<MonyBoxAccount> MonyBoxAccounts { get; set; }

    public virtual DbSet<MonyBoxTransaction> MonyBoxTransactions { get; set; }

    public virtual DbSet<MonyBoxsIcon> MonyBoxsIcons { get; set; }

    public virtual DbSet<MonyBoxsPoint> MonyBoxsPoints { get; set; }

    public virtual DbSet<Notification> Notifications { get; set; }

    public virtual DbSet<NotificationsGroup> NotificationsGroups { get; set; }

    public virtual DbSet<OffersActivitiesView> OffersActivitiesViews { get; set; }

    public virtual DbSet<OffersAndCopun> OffersAndCopuns { get; set; }

    public virtual DbSet<Order> Orders { get; set; }

    public virtual DbSet<OrderItem> OrderItems { get; set; }

    public virtual DbSet<Otplogin> Otplogins { get; set; }

    public virtual DbSet<Otpsm> Otpsms { get; set; }

    public virtual DbSet<Package> Packages { get; set; }

    public virtual DbSet<PackageFeature> PackageFeatures { get; set; }

    public virtual DbSet<PayingCompaniesTable> PayingCompaniesTables { get; set; }

    public virtual DbSet<Payment> Payments { get; set; }

    public virtual DbSet<PaymentsRequest> PaymentsRequests { get; set; }

    public virtual DbSet<Plan> Plans { get; set; }

    public virtual DbSet<PlanAllocation> PlanAllocations { get; set; }

    public virtual DbSet<PlanAutoMainCommitmentType> PlanAutoMainCommitmentTypes { get; set; }

    public virtual DbSet<PlanAutoMonthlyCommitment> PlanAutoMonthlyCommitments { get; set; }

    public virtual DbSet<PlanExpense> PlanExpenses { get; set; }

    public virtual DbSet<PlanManualCommitment> PlanManualCommitments { get; set; }

    public virtual DbSet<PlanManualMainTerm> PlanManualMainTerms { get; set; }

    public virtual DbSet<PlanManualSubTerm> PlanManualSubTerms { get; set; }

    public virtual DbSet<PlanPersonalAccount> PlanPersonalAccounts { get; set; }

    public virtual DbSet<Platform> Platforms { get; set; }

    public virtual DbSet<PointReplaceOrder> PointReplaceOrders { get; set; }

    public virtual DbSet<PointSetting> PointSettings { get; set; }

    public virtual DbSet<PointsTable> PointsTables { get; set; }

    public virtual DbSet<Product> Products { get; set; }

    public virtual DbSet<ProductDetail> ProductDetails { get; set; }

    public virtual DbSet<ProductOptionalField> ProductOptionalFields { get; set; }

    public virtual DbSet<ProviderStatisticsView> ProviderStatisticsViews { get; set; }

    public virtual DbSet<ProvidersCustomBrand> ProvidersCustomBrands { get; set; }

    public virtual DbSet<RetriveMonyBoxAmount> RetriveMonyBoxAmounts { get; set; }

    public virtual DbSet<RetriveMonyMonyView> RetriveMonyMonyViews { get; set; }

    public virtual DbSet<ServiceProvider> ServiceProviders { get; set; }

    public virtual DbSet<Shipment> Shipments { get; set; }

    public virtual DbSet<SiteSetting> SiteSettings { get; set; }

    public virtual DbSet<SocialMediaAccount> SocialMediaAccounts { get; set; }

    public virtual DbSet<Subscription> Subscriptions { get; set; }

    public virtual DbSet<SystemUser> SystemUsers { get; set; }

    public virtual DbSet<SystemUsersTracking> SystemUsersTrackings { get; set; }

    public virtual DbSet<TaskesTable> TaskesTables { get; set; }

    public virtual DbSet<Team> Teams { get; set; }

    public virtual DbSet<UserBanksAccount> UserBanksAccounts { get; set; }

    public virtual DbSet<UserMonyBoxsTransctionView> UserMonyBoxsTransctionViews { get; set; }

    public virtual DbSet<UserSubscriptionsView> UserSubscriptionsViews { get; set; }

    public virtual DbSet<UsersMonyBox> UsersMonyBoxs { get; set; }

    public virtual DbSet<UsersPaymentsOrdersView> UsersPaymentsOrdersViews { get; set; }

    public virtual DbSet<UsersWithDiscountsView> UsersWithDiscountsViews { get; set; }

    public virtual DbSet<VwGrantedPointsSummary> VwGrantedPointsSummaries { get; set; }

    public virtual DbSet<VwMonthlyGrowthRevenue> VwMonthlyGrowthRevenues { get; set; }

    public virtual DbSet<VwOverallSystemPerformance> VwOverallSystemPerformances { get; set; }

    public virtual DbSet<VwSubscriptionsOverview> VwSubscriptionsOverviews { get; set; }

    public virtual DbSet<VwTopCustomersReport> VwTopCustomersReports { get; set; }

    public virtual DbSet<VwTopProvidersReport> VwTopProvidersReports { get; set; }

    public virtual DbSet<VwUserPointsSummary> VwUserPointsSummaries { get; set; }

    public virtual DbSet<Walet> Walets { get; set; }

    public virtual DbSet<WaletTransction> WaletTransctions { get; set; }

    public virtual DbSet<WaletsConfiguration> WaletsConfigurations { get; set; }

    public virtual DbSet<Webhook> Webhooks { get; set; }

    public virtual DbSet<WebhookResponse> WebhookResponses { get; set; }
    public DbSet<GetProviderStatistics> GetProviderStatistics { get; set; }
    public DbSet<vw_OverallSystemPerformance> vw_OverallSystemPerformance { get; set; }
    public DbSet<vw_TopProvidersReport> vw_TopProvidersReport { get; set; }
    public DbSet<vw_TopCustomersReport> vw_TopCustomersReport { get; set; }
    public DbSet<vw_SubscriptionsOverview> vw_SubscriptionsOverview { get; set; }
    public DbSet<vw_MonthlyGrowthRevenue> vw_MonthlyGrowthRevenue { get; set; }
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
        => optionsBuilder.UseSqlServer("Server=tcp:***************,1433;Database=halalaTestdb;User Id=HalalaLive;Password=***************;Encrypt=False;TrustServerCertificate=True;Command Timeout=300;");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ActivitiesCitiesView>(entity =>
        {
            entity.ToView("ActivitiesCitiesView");

            entity.Property(e => e.Status).IsFixedLength();
        });

        modelBuilder.Entity<AdditionalIncome>(entity =>
        {
            entity.HasKey(e => e.IncomeId).HasName("PK__Addition__60DFC60CCF90D952");

            entity.Property(e => e.CreatedAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Deleted).HasDefaultValue(false);

            entity.HasOne(d => d.Account).WithMany(p => p.AdditionalIncomes)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Additiona__Accou__4242D080");
        });

        modelBuilder.Entity<AnonymousUserActivity>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Anonymou__3214EC071CEDC489");
        });

        modelBuilder.Entity<AspNetRole>(entity =>
        {
            entity.HasIndex(e => e.NormalizedName, "RoleNameIndex")
                .IsUnique()
                .HasFilter("([NormalizedName] IS NOT NULL)");
        });

        modelBuilder.Entity<AspNetUser>(entity =>
        {
            entity.HasIndex(e => e.NormalizedUserName, "UserNameIndex")
                .IsUnique()
                .HasFilter("([NormalizedUserName] IS NOT NULL)");

            entity.Property(e => e.EnableNotification).HasDefaultValue(true);

            entity.HasMany(d => d.Roles).WithMany(p => p.Users)
                .UsingEntity<Dictionary<string, object>>(
                    "AspNetUserRole",
                    r => r.HasOne<AspNetRole>().WithMany().HasForeignKey("RoleId"),
                    l => l.HasOne<AspNetUser>().WithMany().HasForeignKey("UserId"),
                    j =>
                    {
                        j.HasKey("UserId", "RoleId");
                        j.ToTable("AspNetUserRoles");
                        j.HasIndex(new[] { "RoleId" }, "IX_AspNetUserRoles_RoleId");
                    });
        });

        modelBuilder.Entity<AspNetUsersWithSystemUsersView>(entity =>
        {
            entity.ToView("AspNetUsersWithSystemUsersView");

            entity.Property(e => e.Status).IsFixedLength();
        });

        modelBuilder.Entity<BankAccountTransction>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();

            entity.HasOne(d => d.IdNavigation).WithOne(p => p.BankAccountTransction)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_BankAccountTransctions_UserBanksAccounts");
        });

        modelBuilder.Entity<BanksAccount>(entity =>
        {
            entity.Property(e => e.State).HasDefaultValue("A");
            entity.Property(e => e.Target).HasDefaultValue("control");
        });

        modelBuilder.Entity<CardsOrder>(entity =>
        {
            entity.Property(e => e.Orderdate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<Cart>(entity =>
        {
            entity.HasOne(d => d.Product).WithMany(p => p.Carts).HasConstraintName("FK_Cart_Products");

            entity.HasOne(d => d.User).WithMany(p => p.Carts).HasConstraintName("FK_Cart_SystemUsers");
        });

        modelBuilder.Entity<CatgoriesDetail>(entity =>
        {
            entity.HasOne(d => d.Category).WithMany(p => p.CatgoriesDetails).HasConstraintName("FK_CatgoriesDetails_Category");
        });

        modelBuilder.Entity<Checkout>(entity =>
        {
            entity.HasKey(e => e.SessionId).HasName("PK__Checkout__C9F492903F4ABC02");

            entity.Property(e => e.CreatedAt).HasDefaultValueSql("(sysutcdatetime())");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("(sysutcdatetime())");
        });

        modelBuilder.Entity<CitiesTable>(entity =>
        {
            entity.HasOne(d => d.CIdNavigation).WithMany(p => p.CitiesTables).HasConstraintName("FK_CitiesTable_Countries_Table");
        });

        modelBuilder.Entity<CombinedStatementTransction>(entity =>
        {
            entity.HasOne(d => d.Acc).WithMany(p => p.CombinedStatementTransctions).HasConstraintName("FK_CombinedStatementTransctions_CombinedStatement");
        });

        modelBuilder.Entity<ConnectBanksOtpCode>(entity =>
        {
            entity.HasOne(d => d.Bank).WithMany(p => p.ConnectBanksOtpCodes).HasConstraintName("FK_ConnectBanksOtpCodes_BanksAccount");

            entity.HasOne(d => d.Otp).WithMany(p => p.ConnectBanksOtpCodes).HasConstraintName("FK_ConnectBanksOtpCodes_OTPLogins");

            entity.HasOne(d => d.User).WithMany(p => p.ConnectBanksOtpCodes).HasConstraintName("FK_ConnectBanksOtpCodes_SystemUsers");
        });

        modelBuilder.Entity<ConsentHistory>(entity =>
        {
            entity.HasOne(d => d.Bank).WithMany(p => p.ConsentHistories).HasConstraintName("FK_ConsentHistory_BanksAccount");

            entity.HasOne(d => d.User).WithMany(p => p.ConsentHistories).HasConstraintName("FK_ConsentHistory_SystemUsers");
        });

        modelBuilder.Entity<CouponsOffersProvidersView>(entity =>
        {
            entity.ToView("CouponsOffersProvidersView");

            entity.Property(e => e.Status).IsFixedLength();
        });

        modelBuilder.Entity<CouponsOffersView>(entity =>
        {
            entity.ToView("CouponsOffersView");
        });

        modelBuilder.Entity<CoupounsProviderActivitiesView>(entity =>
        {
            entity.ToView("CoupounsProviderActivitiesView");

            entity.Property(e => e.Status).IsFixedLength();
        });

        modelBuilder.Entity<CustomerDiscount>(entity =>
        {
            entity.Property(e => e.CustomerStatus).HasDefaultValue("new");

            entity.HasOne(d => d.Discount).WithMany(p => p.CustomerDiscounts).HasConstraintName("FK_CustomerDiscount_DiscountsTables");

            entity.HasOne(d => d.DiscountOffer).WithMany(p => p.CustomerDiscounts).HasConstraintName("FK_CustomerDiscount_OffersAndCopuns");

            entity.HasOne(d => d.User).WithMany(p => p.CustomerDiscounts).HasConstraintName("FK_CustomerDiscount_SystemUsers");
        });

        modelBuilder.Entity<CustomerDiscountsView>(entity =>
        {
            entity.ToView("CustomerDiscountsView");
        });

        modelBuilder.Entity<CustomerGrantedDiscountsView>(entity =>
        {
            entity.ToView("CustomerGrantedDiscountsView");
        });

        modelBuilder.Entity<CustomerMonyBoxTransaction>(entity =>
        {
            entity.ToView("CustomerMonyBoxTransactions");

            entity.Property(e => e.CustomerId).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<CustomerPlan>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Customer__3214EC0707C18E55");

            entity.Property(e => e.StartDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.Customer).WithMany(p => p.CustomerPlans)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CustomerPlans_SystemUsers");

            entity.HasOne(d => d.Plan).WithMany(p => p.CustomerPlans)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CustomerPlans_Plans");
        });

        modelBuilder.Entity<CustomerRewardSetting>(entity =>
        {
            entity.Property(e => e.IsChargeRewardActive).HasDefaultValue(true);
            entity.Property(e => e.IsDigitalPurchaseActive).HasDefaultValue(true);
            entity.Property(e => e.IsDiscountUsageActive).HasDefaultValue(true);
            entity.Property(e => e.IsEngagementRewardActive).HasDefaultValue(true);
            entity.Property(e => e.IsLoginRewardActive).HasDefaultValue(true);
            entity.Property(e => e.IsPiggyCreationActive).HasDefaultValue(true);
            entity.Property(e => e.IsPlanCreationActive).HasDefaultValue(true);
            entity.Property(e => e.IsPointValueActive).HasDefaultValue(true);
            entity.Property(e => e.IsSavingsGoalActive).HasDefaultValue(true);
            entity.Property(e => e.IsTransferLimitActive).HasDefaultValue(true);
        });

        modelBuilder.Entity<DiscountsProvidersActivitiesView>(entity =>
        {
            entity.ToView("DiscountsProvidersActivitiesView");

            entity.Property(e => e.Status).IsFixedLength();
        });

        modelBuilder.Entity<DiscountsSale>(entity =>
        {
            entity.HasOne(d => d.Discount).WithMany().HasConstraintName("FK_DiscountsSales_DiscountsTables");

            entity.HasOne(d => d.User).WithMany().HasConstraintName("FK_DiscountsSales_SystemUsers");
        });

        modelBuilder.Entity<DiscountsTable>(entity =>
        {
            entity.Property(e => e.GrantDiscount).HasDefaultValue(true);
            entity.Property(e => e.Orderdate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.Master).WithMany(p => p.DiscountsTableMasters).HasConstraintName("FK_DiscountsTables_SystemUsers1");

            entity.HasOne(d => d.User).WithMany(p => p.DiscountsTableUsers).HasConstraintName("FK_DiscountsTables_SystemUsers");
        });

        modelBuilder.Entity<FilesTable>(entity =>
        {
            entity.HasOne(d => d.Task).WithMany(p => p.FilesTables).HasConstraintName("FK_FilesTable_TaskesTable");
        });

        modelBuilder.Entity<GetUsersWithActivitiesAndNationality>(entity =>
        {
            entity.ToView("GetUsersWithActivitiesAndNationalities");
        });

        modelBuilder.Entity<GrantDiscount>(entity =>
        {
            entity.HasOne(d => d.User).WithMany(p => p.GrantDiscounts).HasConstraintName("FK_GrantDiscounts_SystemUsers");
        });

        modelBuilder.Entity<GrantedDiscount>(entity =>
        {
            entity.Property(e => e.OrderState).HasDefaultValue("new");
            entity.Property(e => e.Purpose).HasDefaultValue("Other");

            entity.HasOne(d => d.DiscountNavigation).WithMany(p => p.GrantedDiscounts).HasConstraintName("FK_GrantedDiscounts_DiscountsTables");

            entity.HasOne(d => d.MonyBox).WithMany(p => p.GrantedDiscounts).HasConstraintName("FK_GrantedDiscounts_UsersMonyBoxs");

            entity.HasOne(d => d.PointsNavigation).WithMany(p => p.GrantedDiscounts).HasConstraintName("FK_GrantedDiscounts_MonyBoxsPoints");

            entity.HasOne(d => d.Provider).WithMany(p => p.GrantedDiscountProviders).HasConstraintName("FK_GrantedDiscounts_SystemUsersProvider");

            entity.HasOne(d => d.User).WithMany(p => p.GrantedDiscountUsers).HasConstraintName("FK_GrantedDiscounts_SystemUsers");
        });

        modelBuilder.Entity<GrantingHistory>(entity =>
        {
            entity.ToView("GrantingHistory");
        });

        modelBuilder.Entity<LoginDevice>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_LoginDivces");
        });

        modelBuilder.Entity<MasajedDiscountView>(entity =>
        {
            entity.ToView("MasajedDiscountView");
        });

        modelBuilder.Entity<MonyBoxAccount>(entity =>
        {
            entity.HasOne(d => d.MonyBox).WithMany(p => p.MonyBoxAccounts).HasConstraintName("FK_MonyBoxAccounts_UsersMonyBoxs");

            entity.HasOne(d => d.User).WithMany(p => p.MonyBoxAccounts).HasConstraintName("FK_MonyBoxAccounts_SystemUsers");
        });

        modelBuilder.Entity<MonyBoxTransaction>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();

            entity.HasOne(d => d.Account).WithMany(p => p.MonyBoxTransactions).HasConstraintName("FK_MonyBoxTransactions_MonyBoxAccounts");

            entity.HasOne(d => d.MonyBox).WithMany(p => p.MonyBoxTransactions).HasConstraintName("FK_MonyBoxTransactions_UsersMonyBoxs");

            entity.HasOne(d => d.User).WithMany(p => p.MonyBoxTransactions).HasConstraintName("FK_MonyBoxTransactions_SystemUsers");
        });

        modelBuilder.Entity<MonyBoxsIcon>(entity =>
        {
            entity.Property(e => e.IsActive).HasDefaultValue(true);
        });

        modelBuilder.Entity<OffersActivitiesView>(entity =>
        {
            entity.ToView("OffersActivitiesView");
        });

        modelBuilder.Entity<OffersAndCopun>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_SpecialOffers");

            entity.Property(e => e.RecType).HasDefaultValue("offer");
        });

        modelBuilder.Entity<Order>(entity =>
        {
            entity.Property(e => e.OrderType).HasDefaultValue("default");

            entity.HasOne(d => d.User).WithMany(p => p.Orders).HasConstraintName("FK_Order_SystemUsers");
        });

        modelBuilder.Entity<OrderItem>(entity =>
        {
            entity.HasOne(d => d.Order).WithMany(p => p.OrderItems).HasConstraintName("FK_Order_Items_Order");

            entity.HasOne(d => d.Product).WithMany(p => p.OrderItems).HasConstraintName("FK_Order_Items_Products");

            entity.HasOne(d => d.User).WithMany(p => p.OrderItems).HasConstraintName("FK_Order_Items_SystemUsers");
        });

        modelBuilder.Entity<Otplogin>(entity =>
        {
            entity.Property(e => e.Purpose).HasDefaultValue("R");
        });

        modelBuilder.Entity<PackageFeature>(entity =>
        {
            entity.HasOne(d => d.Package).WithMany(p => p.PackageFeatures).HasConstraintName("FK_PackageFeatures_Packages");
        });

        modelBuilder.Entity<PaymentsRequest>(entity =>
        {
            entity.HasOne(d => d.User).WithMany(p => p.PaymentsRequests).HasConstraintName("FK_PaymentsRequests_SystemUsers");
        });

        modelBuilder.Entity<Plan>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Plans__3214EC0711B3ED04");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsCustom).HasDefaultValue(false);
            entity.Property(e => e.IsDeleted).HasDefaultValue(false);
        });

        modelBuilder.Entity<PlanAllocation>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__PlanAllo__3214EC075732A018");

            entity.HasOne(d => d.MainCommitmentTypes).WithMany(p => p.PlanAllocations)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PlanAllocations_MainCategories");

            entity.HasOne(d => d.Plan).WithMany(p => p.PlanAllocations)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PlanAllocations_Plans");
        });

        modelBuilder.Entity<PlanAutoMainCommitmentType>(entity =>
        {
            entity.HasKey(e => e.TypeId).HasName("PK__Commitme__516F03B5A0F66F60");
        });

        modelBuilder.Entity<PlanAutoMonthlyCommitment>(entity =>
        {
            entity.HasKey(e => e.CommitmentId).HasName("PK__MonthlyC__5360E8776C0F93F7");

            entity.Property(e => e.AutoRepeat).HasDefaultValue(true);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Deleted).HasDefaultValue(false);
            entity.Property(e => e.Reminder).HasDefaultValue(false);

            entity.HasOne(d => d.MainCommitmentTypes).WithMany(p => p.PlanAutoMonthlyCommitments).HasConstraintName("FK_MonthlyCommitments_MainCommitmentTypes");
        });

        modelBuilder.Entity<PlanExpense>(entity =>
        {
            entity.HasKey(e => e.ExpensesPlanId).HasName("PK_ExpensesPlan");

            entity.Property(e => e.Deleted).IsFixedLength();

            entity.HasOne(d => d.ManualCommitments).WithMany(p => p.PlanExpenses).HasConstraintName("FK_ExpensesPlan_ManualCommitments");

            entity.HasOne(d => d.PlanAutoMonthlyCommitments).WithMany(p => p.PlanExpenses).HasConstraintName("FK_ExpensesPlan_ExpensesPlan1");

            entity.HasOne(d => d.Plan).WithMany(p => p.PlanExpenses).HasConstraintName("FK_ExpensesPlan_Plans");
        });

        modelBuilder.Entity<PlanManualCommitment>(entity =>
        {
            entity.HasKey(e => e.ManualCommitmentsId).HasName("PK__Expenses__1445CFD377412DCB");

            entity.Property(e => e.CreatedAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Deleted).HasDefaultValue(false);

            entity.HasOne(d => d.MainTerms).WithMany(p => p.PlanManualCommitments).HasConstraintName("FK_Expenses_MainCategories");

            entity.HasOne(d => d.SubTerms).WithMany(p => p.PlanManualCommitments).HasConstraintName("FK_Expenses_SubCategories");
        });

        modelBuilder.Entity<PlanManualMainTerm>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MainCate__3214EC07BE3CD599");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsDeleted).HasDefaultValue(false);
        });

        modelBuilder.Entity<PlanManualSubTerm>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__SubCateg__3214EC0741F86406");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsDeleted).HasDefaultValue(false);

            entity.HasOne(d => d.MainCategory).WithMany(p => p.PlanManualSubTerms).HasConstraintName("FK_SubCategories_MainCategories");
        });

        modelBuilder.Entity<PlanPersonalAccount>(entity =>
        {
            entity.HasKey(e => e.AccountId).HasName("PK__Personal__349DA5A6667AEE85");

            entity.Property(e => e.CreatedAt).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Deleted).HasDefaultValue(false);

            entity.HasOne(d => d.User).WithMany(p => p.PlanPersonalAccounts).HasConstraintName("FK_PersonalAccount_SystemUsers");
        });

        modelBuilder.Entity<PointReplaceOrder>(entity =>
        {
            entity.HasOne(d => d.Provider).WithMany(p => p.PointReplaceOrderProviders).HasConstraintName("FK_PointReplaceOrders_SystemUsersProvider");

            entity.HasOne(d => d.User).WithMany(p => p.PointReplaceOrderUsers).HasConstraintName("FK_PointReplaceOrders_SystemUsersUser");
        });

        modelBuilder.Entity<PointsTable>(entity =>
        {
            entity.HasOne(d => d.Discount).WithMany(p => p.PointsTables).HasConstraintName("FK_PointsTables_DiscountsTables");

            entity.HasOne(d => d.User).WithMany(p => p.PointsTables).HasConstraintName("FK_PointsTables_SystemUsers");
        });

        modelBuilder.Entity<Product>(entity =>
        {
            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.Available).HasDefaultValue(true);
            entity.Property(e => e.OptionalFieldsExist).HasDefaultValue(1);
            entity.Property(e => e.SellPrice).HasDefaultValue(0.0);

            entity.HasOne(d => d.CatagoryNavigation).WithMany(p => p.Products).HasConstraintName("FK_Products_Category");
        });

        modelBuilder.Entity<ProductDetail>(entity =>
        {
            entity.HasOne(d => d.Product).WithMany(p => p.ProductDetails).HasConstraintName("FK_ProductDetails_Products");
        });

        modelBuilder.Entity<ProductOptionalField>(entity =>
        {
            entity.HasOne(d => d.Product).WithMany(p => p.ProductOptionalFields).HasConstraintName("FK_ProductOptionalFields_Products");
        });

        modelBuilder.Entity<ProviderStatisticsView>(entity =>
        {
            entity.ToView("ProviderStatisticsView");

            entity.Property(e => e.ProviderId).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<ProvidersCustomBrand>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.HasOne(d => d.User).WithMany(p => p.ProvidersCustomBrands).HasConstraintName("FK_ProvidersCustomBrand_AspNetUsers");
        });

        modelBuilder.Entity<RetriveMonyBoxAmount>(entity =>
        {
            entity.Property(e => e.Status).HasDefaultValue("wait");

            entity.HasOne(d => d.User).WithMany(p => p.RetriveMonyBoxAmounts).HasConstraintName("FK_RetriveMonyBoxAmounts_SystemUsers");
        });

        modelBuilder.Entity<RetriveMonyMonyView>(entity =>
        {
            entity.ToView("RetriveMonyMonyView");
        });

        modelBuilder.Entity<ServiceProvider>(entity =>
        {
            entity.HasOne(d => d.ActivityNavigation).WithMany(p => p.ServiceProviders).HasConstraintName("FK_ServiceProviders_Activities");

            entity.HasOne(d => d.Asp).WithMany(p => p.ServiceProviders).HasConstraintName("FK_AspNetUsers_ServiceProviders_RoleId");
        });

        modelBuilder.Entity<Shipment>(entity =>
        {
            entity.HasOne(d => d.User).WithMany(p => p.Shipments).HasConstraintName("FK_shipment_SystemUsers");
        });

        modelBuilder.Entity<SocialMediaAccount>(entity =>
        {
            entity.HasOne(d => d.PIdNavigation).WithMany(p => p.SocialMediaAccounts).HasConstraintName("FK_SocialMediaAccounts_Platforms");

            entity.HasOne(d => d.User).WithMany(p => p.SocialMediaAccounts).HasConstraintName("FK_SocialMediaAccounts_SystemUsers");
        });

        modelBuilder.Entity<Subscription>(entity =>
        {
            entity.HasOne(d => d.Pack).WithMany(p => p.Subscriptions).HasConstraintName("FK_Subscriptions_Packages");

            entity.HasOne(d => d.User).WithMany(p => p.Subscriptions).HasConstraintName("FK_Subscriptions_SystemUsers");
        });

        modelBuilder.Entity<SystemUser>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_SystemUsersTable");

            entity.Property(e => e.DiscountCode).IsFixedLength();
            entity.Property(e => e.OrderStatus).HasDefaultValue("pending");
            entity.Property(e => e.Orderdate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Status)
                .HasDefaultValue("A")
                .IsFixedLength();

            entity.HasOne(d => d.Asp).WithMany(p => p.SystemUsers)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SystemUsers_AspNetUsers");
        });

        modelBuilder.Entity<TaskesTable>(entity =>
        {
            entity.Property(e => e.Deleted).HasDefaultValue(false);

            entity.HasOne(d => d.Asp).WithMany(p => p.TaskesTables).HasConstraintName("FK_TaskesTable_AspNetUsers");
        });

        modelBuilder.Entity<UserBanksAccount>(entity =>
        {
            entity.HasOne(d => d.Bank).WithMany(p => p.UserBanksAccounts).HasConstraintName("FK_UserBanksAccounts_BanksAccount");

            entity.HasOne(d => d.Concent).WithMany(p => p.UserBanksAccounts).HasConstraintName("FK_UserBanksAccounts_ConsentHistory1");

            entity.HasOne(d => d.MainAccountNavigation).WithMany(p => p.InverseMainAccountNavigation).HasConstraintName("FK_UserBanksAccounts_UserMainBanksAccounts");

            entity.HasOne(d => d.User).WithMany(p => p.UserBanksAccounts).HasConstraintName("FK_UserBanksAccounts_SystemUsers");
        });

        modelBuilder.Entity<UserMonyBoxsTransctionView>(entity =>
        {
            entity.ToView("UserMonyBoxsTransctionView");
        });

        modelBuilder.Entity<UserSubscriptionsView>(entity =>
        {
            entity.ToView("UserSubscriptionsView");
        });

        modelBuilder.Entity<UsersMonyBox>(entity =>
        {
            entity.Property(e => e.Status).HasDefaultValue("Active");

            entity.HasOne(d => d.User).WithMany(p => p.UsersMonyBoxes).HasConstraintName("FK_UsersMonyBoxs_SystemUsers");
        });

        modelBuilder.Entity<UsersPaymentsOrdersView>(entity =>
        {
            entity.ToView("UsersPaymentsOrdersView");
        });

        modelBuilder.Entity<UsersWithDiscountsView>(entity =>
        {
            entity.ToView("UsersWithDiscountsView");

            entity.Property(e => e.Status).IsFixedLength();
        });

        modelBuilder.Entity<VwGrantedPointsSummary>(entity =>
        {
            entity.ToView("vw_GrantedPointsSummary");
        });

        modelBuilder.Entity<VwMonthlyGrowthRevenue>(entity =>
        {
            entity.ToView("vw_MonthlyGrowthRevenue");
        });

        modelBuilder.Entity<VwOverallSystemPerformance>(entity =>
        {
            entity.ToView("vw_OverallSystemPerformance");
        });

        modelBuilder.Entity<VwSubscriptionsOverview>(entity =>
        {
            entity.ToView("vw_SubscriptionsOverview");
        });

        modelBuilder.Entity<VwTopCustomersReport>(entity =>
        {
            entity.ToView("vw_TopCustomersReport");
        });

        modelBuilder.Entity<VwTopProvidersReport>(entity =>
        {
            entity.ToView("vw_TopProvidersReport");
        });

        modelBuilder.Entity<VwUserPointsSummary>(entity =>
        {
            entity.ToView("vw_UserPointsSummary");
        });

        modelBuilder.Entity<Walet>(entity =>
        {
            entity.HasOne(d => d.User).WithMany(p => p.Walets).HasConstraintName("FK_Walet_SystemUsers");
        });

        modelBuilder.Entity<WaletTransction>(entity =>
        {
            entity.HasOne(d => d.DistUserNavigation).WithMany(p => p.WaletTransctionDistUserNavigations).HasConstraintName("FK_WaletTransctions_SystemUsersDist");

            entity.HasOne(d => d.User).WithMany(p => p.WaletTransctionUsers).HasConstraintName("FK_WaletTransctions_SystemUsers");

            entity.HasOne(d => d.Walet).WithMany(p => p.WaletTransctions)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WaletTransctions_Walet");
        });

        modelBuilder.Entity<WaletsConfiguration>(entity =>
        {
            entity.Property(e => e.GranChargeCashback).HasDefaultValue(true);
            entity.Property(e => e.GrantCachBack).HasDefaultValue(true);
            entity.Property(e => e.GrantMonyBoxsChargePoints).HasDefaultValue(true);
            entity.Property(e => e.IsWecomePointsActive).HasDefaultValue(true);
            entity.Property(e => e.SetMaxlimtForAllMonyBoxs).HasDefaultValue(true);
        });

        modelBuilder.Entity<Webhook>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Webhook__3214EC07D9DEB5BB");

            entity.Property(e => e.Timestamp).HasDefaultValueSql("(sysutcdatetime())");

            entity.HasOne(d => d.CheckoutSession).WithMany(p => p.Webhooks).HasConstraintName("FK_Webhook_Checkout");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
