﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class CustomerDiscountsView
{
    [StringLength(500)]
    public string? DisCountName { get; set; }

    [StringLength(1500)]
    public string? EnDiscountName { get; set; }

    public long Id { get; set; }

    public long? UserId { get; set; }

    [StringLength(50)]
    public string? DiscountCode { get; set; }

    public double? Amount { get; set; }

    public double? DiscountRate { get; set; }

    public long? DiscountId { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? Discountdate { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    [StringLength(1500)]
    public string? EnName { get; set; }

    public long? MasterId { get; set; }

    [StringLength(450)]
    public string? Icon { get; set; }

    [Column("recType")]
    [StringLength(20)]
    public string? RecType { get; set; }

    [Column("shareUrl")]
    [StringLength(1)]
    [Unicode(false)]
    public string ShareUrl { get; set; } = null!;
}
