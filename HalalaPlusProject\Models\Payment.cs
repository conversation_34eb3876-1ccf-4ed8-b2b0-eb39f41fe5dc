﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("Payment")]
public partial class Payment
{
    [Key]
    public long Id { get; set; }

    [Column("Payment_Date", TypeName = "datetime")]
    public DateTime? PaymentDate { get; set; }

    [Column("Payment_Method")]
    [StringLength(50)]
    public string? PaymentMethod { get; set; }

    [Column("amount")]
    public double? Amount { get; set; }

    [Column("Order_Id")]
    public long? OrderId { get; set; }

    public long? UserId { get; set; }
}
