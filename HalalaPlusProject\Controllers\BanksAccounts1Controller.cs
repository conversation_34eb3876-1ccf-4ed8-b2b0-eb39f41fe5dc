﻿using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    public class BanksAccounts1Controller : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public BanksAccounts1Controller(HalalaPlusdbContext context)
        {
            _context = context;
        }

        // GET: BanksAccounts1
        public async Task<IActionResult> Index()
        {
              return _context.BanksAccounts != null ? 
                          View(await _context.BanksAccounts.ToListAsync()) :
                          Problem("Entity set 'HalalaPlusdbContext.BanksAccounts'  is null.");
        }

        // GET: BanksAccounts1/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null || _context.BanksAccounts == null)
            {
                return NotFound();
            }

            var banksAccount = await _context.BanksAccounts
                .FirstOrDefaultAsync(m => m.Id == id);
            if (banksAccount == null)
            {
                return NotFound();
            }

            return View(banksAccount);
        }

        // GET: BanksAccounts1/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: BanksAccounts1/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,BankName,ConnectionCode,AccountNumber")] BanksAccount banksAccount)
        {
            if (ModelState.IsValid)
            {
                _context.Add(banksAccount);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(banksAccount);
        }

        // GET: BanksAccounts1/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null || _context.BanksAccounts == null)
            {
                return NotFound();
            }

            var banksAccount = await _context.BanksAccounts.FindAsync(id);
            if (banksAccount == null)
            {
                return NotFound();
            }
            return View(banksAccount);
        }

        // POST: BanksAccounts1/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,BankName,ConnectionCode,AccountNumber")] BanksAccount banksAccount)
        {
            if (id != banksAccount.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(banksAccount);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!BanksAccountExists(banksAccount.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(banksAccount);
        }

        // GET: BanksAccounts1/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null || _context.BanksAccounts == null)
            {
                return NotFound();
            }

            var banksAccount = await _context.BanksAccounts
                .FirstOrDefaultAsync(m => m.Id == id);
            if (banksAccount == null)
            {
                return NotFound();
            }

            return View(banksAccount);
        }

        // POST: BanksAccounts1/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            if (_context.BanksAccounts == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.BanksAccounts'  is null.");
            }
            var banksAccount = await _context.BanksAccounts.FindAsync(id);
            if (banksAccount != null)
            {
                _context.BanksAccounts.Remove(banksAccount);
            }
            
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool BanksAccountExists(int id)
        {
          return (_context.BanksAccounts?.Any(e => e.Id == id)).GetValueOrDefault();
        }
    }
}
