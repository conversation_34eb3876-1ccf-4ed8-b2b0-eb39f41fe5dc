﻿@{
    ViewData["Title"] = "استيراد ملف اكسل";
    var table = ViewBag.Table as System.Data.DataTable;
}

<h2>استيراد ملف اكسل</h2>

@if (TempData["Message"] != null)
{
    <div class="alert alert-info">@TempData["Message"]</div>
}

<form asp-action="ImportExcel" asp-controller="MyXlsxFiles" method="post" enctype="multipart/form-data" class="mb-3">
	<label class="form-label">اختر ملف اكسل:</label>
    <input type="file" name="file" accept=".xlsx" class="form-control mb-2" required />

    <label class="form-label">اختر ايقونة</label>
    <input type="file" name="logo" accept="images/*" class="form-control mb-2" required />
    <button type="submit" class="btn btn-primary">رفع</button>
</form>

<hr />

@if (table != null && table.Rows.Count > 0)
{
    <h3>Preview Imported Data</h3>

    <style>
        .scrollable-container {
            max-height: 500px;
            overflow: auto;
            border: 1px solid #ccc;
        }

        .scrollable-container table {
            min-width: max-content;
        }
    </style>

    <div class="scrollable-container mt-3">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    @foreach (System.Data.DataColumn col in table.Columns)
                    {
                        <th>@col.ColumnName</th>
                    }
                </tr>
            </thead>
            <tbody>
                @foreach (System.Data.DataRow row in table.Rows)
                {
                    <tr>
                        @foreach (var item in row.ItemArray)
                        {
                            <td>@item</td>
                        }
                    </tr>
                }
            </tbody>
        </table>
    </div>
}
else
{
    <p class="text-muted">لم يتم عرض اي بيانات</p>
}
