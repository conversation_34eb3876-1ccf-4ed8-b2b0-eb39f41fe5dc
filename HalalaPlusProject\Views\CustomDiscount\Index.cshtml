﻿@model HalalaPlusProject.CModels.GrantDiscountModel

@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["grantdiscount"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["grantdiscount"]</h2>

<hr />
<div class="row">


    <div class="col-md-12">
        <form asp-action="GrantDiscount" class="submitfm" novalidate>
            <div class="row">
                <div class="col-md-6">
          <div class="form-group">
                        <label class="control-label">@localizer["phoneno"]   </label>
                <input name="PhoneNo" id="PhoneNoSearch" autocomplete="off" type="text" class="form-control mb-4 text-box single-line ui-autocomplete-input" />


                    </div>
            <div class="row">
                  <div asp-validation-summary="ModelOnly" class="text-danger"></div>
             </div>
                  
                   @*  <div class="form-group">

                    <label class="control-label">@localizer["customerno"]</label>
                <label id="CustomNo" class="form-control"></label>
               
            </div> *@
          <div class="form-group">
               <input type="hidden" asp-for="Id" />
                        <label asp-for="phoneNo" class="control-label">@localizer["customerphoneno"] <span class="text-danger required-star">*</span></label>
                        <input asp-for="phoneNo" class="form-control" data-val="true"
                               data-val-required="رقم الهاتف مطلوب" />
                <span asp-validation-for="phoneNo"  class="text-danger"></span>
            </div>
                    <div class="form-group">
                        <label asp-for="product" class="control-label"> @localizer["products"]  <span class="text-danger required-star">*</span></label>
                        <select asp-for="product"   class="form-select" data-val="true"
                               data-val-required="المنتج مطلوب" >
                            <option value="-1">@localizer["chose"]</option>
                            @foreach (var item in ViewBag.Products)
                            {
                                <option value="@item.Value">@item.Text </option>
                            }
                        </select>
                        @*<select asp-for="product" required  class ="form-select" asp-items="ViewBag.Products"></select>*@
                        <span asp-validation-for="product" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-6">
           

              <div class="form-group">
                        <label asp-for="amount" class="control-label">@localizer["amount"]    <span class="text-danger required-star">*</span></label>
                        <input asp-for="amount" onkeyup="getPrecent()" required class="form-control" data-val="true"
                               data-val-required="المبلـغ  مطلوب" />
                <span asp-validation-for="amount" class="text-danger"></span>
            </div>
               <div class="form-group">
                        <label asp-for="discount" class="control-label"> @localizer["discount"]    <span class="text-danger required-star">*</span></label>
                        <input asp-for="discount" required class="form-control" data-val="true"
                               data-val-required="الخصم  مطلوب" />
                <span asp-validation-for="discount " class="text-danger"></span>
            </div>
               <div class="form-group">
                    <label asp-for="rate" class="control-label">@localizer["rate"]  <span class="text-danger required-star">*</span></label>
                        <input asp-for="rate"  class="form-control" data-val="true"
                               data-val-required="النسبـة مطلوب"   class="form-control" />
                <span asp-validation-for="rate" class="text-danger"></span>
                    </div>
                </div>
            </div>
             <div class="form-group mt-2">
                    <button type="submit" value="Create" class="btn btn-primary">@localizer["grant"]</button>
            </div>
            </form>
        </div>
</div>
 

@section Scripts {
    <script>
       
    </script>
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}