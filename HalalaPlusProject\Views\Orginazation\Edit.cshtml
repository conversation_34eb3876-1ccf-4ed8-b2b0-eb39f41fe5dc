﻿@model HalalaPlusProject.Entities.OrginazationgetEdit
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["edit"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["edit"]</h1>

<h4>@localizer["servceproviders"]</h4>
<hr />

<!-- Tab links -->
<div class="tab">
    <button class="tablinks active" id="tab1" onclick="openCity(event, 'DataTab')">@localizer["personaldata"]</button>
    <button class="tablinks" id="tab2" onclick="openCity(event, 'PointsTab')">@localizer["points"]  </button>
    <button class="tablinks" id="tab3" onclick="openCity(event, 'DiscountsTab')">@localizer["discounts"]</button>
    <button class="tablinks" id="tab4" onclick="openCity(event, 'AccountsTab')">@localizer["socialaccounts"]</button>

</div>

<!-- Tab content -->


<div id="AccountsTab" class="tabcontent" >
 <div class="row">

    
    <div class="col-md-4">
        <form asp-action="AddEditAccount" class="submitfm" >
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" name="Id"   asp-for="AccountsData.Id" />
            <input type="hidden" name="userId" id="userId" value="@Model.Id" />
             <div class="row">
                   <div class="col-md-12">
                    <div class="form-group">
                            <label asp-for="AccountsData.SiteName" class="control-label">@localizer["platformname"]</label>
                        <input asp-for="AccountsData.SiteName" name="SiteName" class="form-control" />
                        <span asp-validation-for="AccountsData.SiteName" required class="text-danger"></span>
                    </div>
                      <div class="form-group">
                            <label asp-for="AccountsData.Link" class="control-label">@localizer["platformlink"]</label>
                        <input asp-for="AccountsData.Link" name="Link" class="form-control" />
                        <span asp-validation-for="AccountsData.Link" required class="text-danger"></span>
                    </div>
                    </div>
                    </div>
                     <div class="form-group mt-2">
                    <button type="submit" value="Save" class="btn btn-primary">@localizer["save"]</button>
            </div>
                    </form>
     </div>
      <div class="col-md-8">
          <div class="col-md-12">
                 <div class="table-responsive p-0">
               
                <table class="table table-striped text-center">
                  <thead>
                    <tr>


                                <th scope="col">@localizer["platformname"]</th>
                                <th scope="col">@localizer["platformlink"]</th>
                                <th scope="col"> @localizer["options"]</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.Accounts) {
                <tr id="@item.Id">
            
           
                    <td>
                        @Html.DisplayFor(modelItem => item.Name)
                    </td>
                    <td>
                          @Html.DisplayFor(modelItem => item.Link) 
                    </td>
                    <td>
                                        <a onclick="EditSite(@item.Id)" class="btn btn-outline-info tablebtn">@localizer["edit"]</a> |
                                        <a href="#" class="btn btn-outline-danger tablebtn" onclick="DeleteAccount('@item.Id')">@localizer["delete"]</a>
                    </td>
                </tr>
}                  
                  </tbody>              
                </table>
              </div>
              
            </div>
   </div>
   </div>
      </div>


<div id="PointsTab" class="tabcontent" >
 <div class="row">

    
    <div class="row">
         <div class="col-md-3 col-sm-6 col-xs-12">
                  <form asp-action="AddEditSales" class="submitfm">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" name="Id"   asp-for="Sales.Id" />
            <input type="hidden" name="userId" id="userId" value="@Model.Id" />
             <div class="row">
                   <div class="col-md-12">
                            <h5>@localizer["sales"]</h5>
                    <div class="form-group">
                                <label asp-for="Sales.SalesNo" class="control-label">@localizer["salesno"]</label>
                        <input asp-for="Sales.SalesNo" required name="SalesNo" class="form-control" />
                        <span asp-validation-for="Sales.SalesNo"  class="text-danger"></span>
                    </div>
                      <div class="form-group">
                                <label asp-for="Sales.DeservePoints" class="control-label">@localizer["deservpoints"]</label>
                        <input asp-for="Sales.DeservePoints" required name="DeservePoints" class="form-control" />
                        <span asp-validation-for="Sales.DeservePoints"  class="text-danger"></span>
                    </div>

                    </div>
                         <div class="form-group mt-2">
                            <button type="submit" value="Save" class="btn btn-primary">@localizer["save"]</button>
                     </div>

                 </div>
                  
                 
            </form>
         </div>
         <div class="col-md-3 col-sm-6 col-xs-12">
                  <form asp-action="AddEditPoints" class="submitfm">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" name="Id"   asp-for="Points.Id" />
            <input type="hidden" name="userId" id="userId" value="@Model.Id" />
             <div class="row">
                   
                  <div class="col-md-12">
                            <h5>@localizer["points"]</h5>
                    <div class="form-group">
                                <label asp-for="Points.PointNo" class="control-label">@localizer["pointsno"]</label>
                        <input asp-for="Points.PointNo" required name="PointNo"  class="form-control" />
                        <span asp-validation-for="Points.PointNo"  class="text-danger"></span>
                    </div>
                      <div class="form-group">
                                <label asp-for="Points.Prize" class="control-label">@localizer["prize"]</label>
                        <input asp-for="Points.Prize" name="Prize" required class="form-control" />
                        <span asp-validation-for="Points.Prize"  class="text-danger"></span>
                    </div>
                    <div class="form-group">
                                <label asp-for="Points.PointsConditions" class="control-label">@localizer["conditions"]</label>
                        <textarea asp-for="Points.PointsConditions" required name="PointsConditions" class="form-control" ></textarea>
                        <span asp-validation-for="Points.PointsConditions"  class="text-danger"></span>
                    </div>
                    <div class="form-group mt-2">
                                <button type="submit" value="Save" class="btn btn-primary">@localizer["save"]</button>
            </div>

                 </div>
                 </div>
                 
            </form>
         </div>
     <div class="col-md-6 col-sm-12 col-xs-12">
               <div class="table-responsive p-0">
               
                <table class="table table-striped text-center">
                  <thead>
                    <tr>


                                <th scope="col">@localizer["pointsno"] </th>
                                <th scope="col">@localizer["deservpoints"]</th>
                                <th scope="col"> @localizer["condtions"]</th>
                                <th scope="col"> @localizer["options"]</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.allPoints) {
        <tr>
            
           
            <td>
                @Html.DisplayFor(modelItem => item.PointNo)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Prize)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.PointsConditions)
            </td>
          
             <td>
                                        <a onclick="EditPoints(@item.Id,this)" class="btn btn-outline-info tablebtn">@localizer["edit"]</a> |
                                        <a onclick="DeletePoints(@item.Id)" href="#" class="btn btn-outline-danger tablebtn">@localizer["delete"]</a>
              
            </td>
        </tr>
}                  
                  </tbody>              
                </table>
              </div>
        </div>
            </div>
            </div>
</div>
<div id="DiscountsTab" class="tabcontent" >
 <div class="col-md-12">

    
    <div class="row">
        <form asp-action="AddEditDiscount" class="submitfm">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" name="Id" asp-for="Discounts.Id" />
            <input type="hidden" name="userId" id="userId" value="@Model.Id" />
             <div class="row">
                 
                   <div class="col-md-3">
                    <div class="form-group">
                            <label asp-for="Discounts.DiscountName" n class="control-label">@localizer["discountname"]</label>
                        <input asp-for="Discounts.DiscountName" name="DiscountName" class="form-control" />
                        <span asp-validation-for="Discounts.DiscountName" required class="text-danger"></span>
                    </div> <div class="form-group">
                            <label asp-for="Discounts.EnDiscountName" n class="control-label">اسم الخصم انجليزي</label>
                            <input asp-for="Discounts.EnDiscountName" required name="EnDiscountName" class="form-control" />
                            <span asp-validation-for="Discounts.EnDiscountName" required class="text-danger"></span>
                    </div>
                      <div class="form-group">
                            <label asp-for="Discounts.Discount" class="control-label">@localizer["discount"]</label>
                        <input asp-for="Discounts.Discount" name="Discount" class="form-control" />
                        <span asp-validation-for="Discounts.Discount" required class="text-danger"></span>
                    </div> 
                   @* <div class="form-group">
                        <label asp-for="Discounts.GrantType" class="control-label"></label>
                        <input asp-for="Discounts.GrantType" name="GrantType" class="form-control" />
                        <span asp-validation-for="Discounts.GrantType"  class="text-danger"></span>
                    </div>*@
                      <div class="form-group">
                            <label asp-for="Discounts.GrantType" class="control-label">@localizer["discounttype"]</label>
                <select asp-for="Discounts.GrantType" name="GrantType"  class ="form-select" >
                                <option value="1">@localizer["onlydiscount"]</option>
                                <option value="2">@localizer["onlypoints"] </option>
                                <option value="3"> @localizer["discountandypoints"]</option>

                </select>           
                  <span asp-validation-for="Discounts.GrantType" class="text-danger"></span>
            </div>
                 </div>
                  <div class="col-md-3">
                      
                    <div class="form-group">
                            <label asp-for="Discounts.StartDate" class="control-label">@localizer["startdate"]</label>
                        <input asp-for="Discounts.StartDate"   type="date" name="StartDate" class="form-control" />
                        <span asp-validation-for="Discounts.StartDate"  class="text-danger"></span>
                    </div>
                      <div class="form-group">
                            <label asp-for="Discounts.EndDate" class="control-label">@localizer["enddate"]</label>
                        <input asp-for="Discounts.EndDate" type="date" name="EndDate" class="form-control" />
                        <span asp-validation-for="Discounts.EndDate"  class="text-danger"></span>
                    </div>
                    </div>
                     <div class="col-md-3">
                    <div class="form-group">
                            <label asp-for="Discounts.Conditions" class="control-label">@localizer["conditions"]</label>
                        <textarea asp-for="Discounts.Conditions" name="Conditions" class="form-control" ></textarea>
                        <span asp-validation-for="Discounts.Conditions"  class="text-danger"></span>
                    </div>
                    <div class="form-group">
                            <label asp-for="Discounts.EnConditions" class="control-label">@localizer["conditions"]</label>
                            <textarea asp-for="Discounts.EnConditions" name="EnConditions" class="form-control"></textarea>
                            <span asp-validation-for="Discounts.EnConditions" required class="text-danger"></span>
                    </div>  
                  
                 </div>
                 </div>
                 <div class="form-group mt-2">
                    <button type="submit" value="Save" class="btn btn-primary">@localizer["save"]</button>
            </div>
            </form>
            </div>
            <div class="col-md-12">
               @Html.Partial("_discounts",Model.DiscountsList)
               
            </div>
            </div>
</div>

<div id="DataTab" class="tabcontent" style="display:block">
  
<div class="row">

    
    <div class="col-md-12">
        <form asp-action="Edit" class="submitfm">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
             
         
       <div class="row">
                <div class="col-md-3">
                        <div class="form-group">
                            <label asp-for="ContractNo" class="control-label">@localizer["contractno"]</label>
                            <input asp-for="ContractNo" class="form-control" />
                            <span asp-validation-for="ContractNo" class="text-danger"></span>
                        </div>
                    <div class="form-group">
                            <label asp-for="Name" class="control-label">@localizer["serviceprovidername"]</label>
                            <input asp-for="Name" required class="form-control" />
                        <span asp-validation-for="Name"  class="text-danger"></span>
                    </div>
                        <div class="form-group">
                            <label asp-for="EnName" class="control-label">اسم مقدم الخدمة انجليزي</label>
                            <input asp-for="EnName" required class="form-control" />
                            <span asp-validation-for="EnName" class="text-danger"></span>
                        </div>
                       

                    <div class="form-group">
                            <label asp-for="Activity" class="control-label">@localizer["activity"]</label>
                        <select name="Activity" id="Activity" class ="form-select" >
                            @foreach (var item in ViewBag.Activity) {
                                <option value="@item.Value">@item.Text </option>
                            }
                        </select>
                            <span asp-validation-for="Activity" class="text-danger"></span>
                        </div> 
                        <div class="form-group">
                            <label asp-for="ServiceProviderRepresent" class="control-label">@localizer["representname"]</label>
                            <input asp-for="ServiceProviderRepresent" required class="form-control" />
                            <span asp-validation-for="ServiceProviderRepresent" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="overview" class="control-label">@localizer["storeoverview"]</label>
                            <textarea asp-for="overview" class="form-control"></textarea>
                            <span asp-validation-for="overview" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="enoverview" class="control-label">@localizer["enstoreoverview"]</label>
                            <textarea asp-for="enoverview" class="form-control"></textarea>
                            <span asp-validation-for="enoverview" class="text-danger"></span>
                        </div>
            </div>
            
 <div class="col-md-3">
      

           @*   <div class="form-group">
                            <label asp-for="UserName" class="control-label">@localizer["username"]</label>
                <input asp-for="UserName" required class="form-control" />
                <span asp-validation-for="UserName" class="text-danger"></span>
            </div> *@

                        <div class="form-group">
                            <label asp-for="City" class="control-label">@localizer["thecity"]</label>
                            <select asp-for="City" required class="form-control" asp-items="ViewBag.City"></select>
                            <span asp-validation-for="City" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="PhoneNumber" class="control-label">@localizer["phoneno"]</label>
                            <input asp-for="PhoneNumber" required class="form-control" />
                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="EnterprisePhoneNo" class="control-label">@localizer["enterprisephoneno"]</label>
                            <input asp-for="EnterprisePhoneNo"  class="form-control" />
                            <span asp-validation-for="EnterprisePhoneNo" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="BusnissNo" class="control-label">@localizer["busnissno"]</label>
                            <input asp-for="BusnissNo" required class="form-control" />
                            <span asp-validation-for="BusnissNo" class="text-danger"></span>
                        </div>
                                <div class="form-group">
                                    <label asp-for="bnifitfrompoints" class="control-label">@localizer["bnifitfrompoints"]</label>
                                    <textarea asp-for="bnifitfrompoints" class="form-control"></textarea>
                                    <span asp-validation-for="bnifitfrompoints" class="text-danger"></span>
                                </div>

                        
                                <div class="form-group">
                                    <label asp-for="enbnifitfrompoints" class="control-label">@localizer["enbnifitfrompoints"]</label>
                                    <textarea asp-for="enbnifitfrompoints" class="form-control"></textarea>
                                    <span asp-validation-for="enbnifitfrompoints" class="text-danger"></span>
                                </div>
                        <div class="form-group">
                            <label asp-for="StoreLink" class="control-label">@localizer["storelink"]</label>
                            <input asp-for="StoreLink" required class="form-control"/>
                            <span asp-validation-for="StoreLink" class="text-danger"></span>
                        </div>
                   
                   
             </div>


              <div class="col-md-3">
                        <div class="form-group">
                            <label asp-for="Files" class="control-label">@localizer["attatchments"]</label>
                            <input asp-for="Files" multiple class="form-control" />
                            <span asp-validation-for="Files" class="text-danger"></span>
                        </div>
      
           
             <div class="form-group">
                            <label asp-for="ContractDate" class="control-label">@localizer["contractstartdate"]</label>
                <input asp-for="ContractDate"  type="date" required class="form-control" />
                <span asp-validation-for="ContractDate" class="text-danger"></span>
            </div>
             <div class="form-group">
                <label asp-for="ContractEndDate" class="control-label">@localizer["contractenddate"]</label>
                <input asp-for="ContractEndDate"  type="date" required class="form-control" />
                <span asp-validation-for="ContractEndDate" class="text-danger"></span>
            </div>
            

                        <div class="form-group mt-1">

                            <img style="width:168px" src="@Model.LogoLink" />
                            
                        </div>
                        <div class="form-group mt-1">

                            <label asp-for="Logo" class="control-label ">@localizer["logo"]</label>
                            <input asp-for="Logo" class="form-control" />
                            <span asp-validation-for="Logo" class="text-danger"></span>
                        </div>
                        <div class="form-group mt-1">

                            <img style="width:168px" src="@Model.OffersIconLink" />

                        </div>
                        <div class="form-group mt-1">

                            <label asp-for="OffersIcon" class="control-label ">@localizer["OffersIcon"]</label>
                            <input asp-for="OffersIcon" class="form-control" />
                            <span asp-validation-for="OffersIcon" class="text-danger"></span>
                        </div>
            
            </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label asp-for="FirstColor" class="control-label">@localizer["FirstColor"]</label>
                            <input asp-for="FirstColor" class="form-control" placeholder="Example f7f7f7" />
                            <span asp-validation-for="FirstColor" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="SecondColor" class="control-label">@localizer["SecondColor"]</label>
                            <input asp-for="SecondColor" class="form-control" placeholder="Example f7f7f7" />
                            <span asp-validation-for="SecondColor" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="Lat" class="control-label">@localizer["lat"]</label>
                            <input asp-for="Lat" class="form-control" />
                            <span asp-validation-for="Lat" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="lng" class="control-label">@localizer["lng"]</label>
                            <input asp-for="lng" class="form-control mb-3" />
                            <span asp-validation-for="lng" class="text-danger"></span>
                        </div>
                        <div class="form-group">
                            <div style="height: 400px; max-width: 700px;" id="map">
                            </div>
                        </div>

                        @*        <div class="form-group">
                        <label asp-for="Locatin" class="control-label"></label>
                        <input asp-for="Locatin" required class="form-control" />
                        <span asp-validation-for="Locatin" class="text-danger"></span>
                        </div> *@

                    </div>

             </div>
 
         <div class="row mt-4">

               
               <div class="col-md-6">
                 <div class="table-responsive p-0">
               
                <table class="table table-striped text-center">
                  <thead>
                    <tr>


                                        <th scope="col">@localizer["filename"]</th>
                                        <th scope="col">@localizer["display"]</th>
                                        <th scope="col"> @localizer["options"]</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.FilesList) {
        <tr>
            
           
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>

                                                <a class="view" target="_blank" href="@item.Link">@localizer["displayfile"] </a>
            </td>
             <td>

                                                <a onclick="DeleteFile('@item.Id')" href="#" class="btn btn-outline-danger tablebtn">@localizer["delete"]</a>
              
            </td>
        </tr>
}                  
                  </tbody>              
                </table>
              </div>
              
            </div>
             <div class="col-md-6">
                 <div class="table-responsive p-0">
               
                <table class="table table-striped text-center">
                  <thead>
                    <tr>


                                        <th scope="col">@localizer["images"]</th>
                                        <th scope="col">@localizer["display"]</th>
                                        <th scope="col"> @localizer["options"]</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.images) {
        <tr>
            
           
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>

                                                <a class="view" target="_blank" href="@item.Link">@localizer["displayimages"] </a>
            </td>
             <td>

                                                <a onclick="DeleteFile('@item.Id')" href="#" class="btn btn-outline-danger tablebtn">@localizer["delete"]</a>
              
            </td>
        </tr>
}                  
                  </tbody>              
                </table>
              </div>
              
            </div>

         </div>
         
        
         
     
           
            <div class="form-group mt-2">
                    <button type="submit" value="Save" class="btn btn-primary">@localizer["save"]</button>
            </div>
        </form>


    </div>

    </div>
    </div>
  


<div class="row">
    <div class="col-md-12">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />

            </form>
            </div>
            
        
  
        </div>
<div>



    <a asp-action="Index"> @localizer["backtolist"] </a>
    
</div>


@*
<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModalCenter">
  Launch demo modal
</button>*@





<script>
    (g => { var h, a, k, p = "The Google Maps JavaScript API", c = "google", l = "importLibrary", q = "__ib__", m = document, b = window; b = b[c] || (b[c] = {}); var d = b.maps || (b.maps = {}), r = new Set, e = new URLSearchParams, u = () => h || (h = new Promise(async (f, n) => { await (a = m.createElement("script")); e.set("libraries", [...r] + ""); for (k in g) e.set(k.replace(/[A-Z]/g, t => "_" + t[0].toLowerCase()), g[k]); e.set("callback", c + ".maps." + q); a.src = `https://maps.${c}apis.com/maps/api/js?` + e; d[q] = f; a.onerror = () => h = n(Error(p + " could not load.")); a.nonce = m.querySelector("script[nonce]")?.nonce || ""; m.head.append(a) })); d[l] ? console.warn(p + " only loads once. Ignoring:", g) : d[l] = (f, ...n) => r.add(f) && u().then(() => d[l](f, ...n)) })
        ({ key: "AIzaSyBRDq842x31n-rAmjPQ7hZotsZaGvbjI_U", v: "weekly" });</script>

@* 
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBRDq842x31n-rAmjPQ7hZotsZaGvbjI_U&language=ar&callback=initMap&libraries=&v=weekly"
        async></script> *@
        <script>
    async function initMap() {

        // Request needed libraries.
        const { Map } = await google.maps.importLibrary("maps");
        const myLatlng1 = { lat: 20.99265387585728, lng: 40.54420880475375 };
        const myLatlng = { lat: @Model.Lat, lng: @Model.lng };
        
        const map = new google.maps.Map(document.getElementById("map"), {
            zoom: 6,
            center: myLatlng,
        });
        // var marker1 = new google.maps.Marker({
        //     position: myLatlng,
        //     map: map,
        //     title:'hh'
        // });
        // Create the initial InfoWindow.
        let infoWindow = new google.maps.InfoWindow({
            // content: "Click the map to get Lat/Lng!",
            position: myLatlng,
            name: 'KSA'
        });
        var marker = new google.maps.Marker({
            position: myLatlng,
            map: map,
            title: 'Location'
        });


        // Configure the click listener.
        map.addListener("click", (mapsMouseEvent) => {
            infoWindow.open(map);
            document.getElementById('Lat').value = mapsMouseEvent.latLng.lat();
            document.getElementById('lng').value = mapsMouseEvent.latLng.lng();
            // console.log("clicked:",myLatlng);
            // Close the current InfoWindow.
            var result = {
                lat: mapsMouseEvent.latLng.lat(),
                lng: mapsMouseEvent.latLng.lng()
            };
            // marker1 = new google.maps.Marker({
            //     position: result,
            //     map: map,
            //     title: 'ss'
            // });
            infoWindow.close();
            // Create a new InfoWindow.
            infoWindow = new google.maps.InfoWindow({
                position: mapsMouseEvent.latLng,
            });
            infoWindow.setContent(
                JSON.stringify(mapsMouseEvent.latLng.toJSON(), null, 2),
            );
            infoWindow.open(map);
        });
    }

    initMap();
</script>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
