﻿using HalalaPlusProject.Models;

namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل تفاصيل الخصومات الممنوحة للعميل ضمن حصالة معينة.
    /// </summary>
    public class CustomerGrantedDiscountsViewModel
    {
        /// <summary>
        /// معرف العميل.
        /// </summary>
        public long CustomerId { get; set; }

        /// <summary>
        /// اسم العميل.
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// رقم هاتف العميل.
        /// </summary>
        public string CustomerPhone { get; set; }

        /// <summary>
        /// معرف الحصالة المرتبطة بالعميل.
        /// </summary>
        public long MonyBoxId { get; set; }

        /// <summary>
        /// اسم الحصالة.
        /// </summary>
        public string MonyBoxName { get; set; }

        /// <summary>
        /// عدد الخصومات الممنوحة للعميل.
        /// </summary>
        public int CustomerGrantedDiscountsCount { get; set; }

        /// <summary>
        /// إجمالي المبلغ الممنوح للعميل.
        /// </summary>
        public string CustomerTotalAmount { get; set; }

        /// <summary>
        /// قائمة الخصومات الممنوحة للعميل.
        /// </summary>
        public List<GrantedDiscount> GrantedDiscountUsers { get; set; }
    }

    /// <summary>
    /// يمثل خصمًا ممنوحًا لمستخدم معين.
    /// </summary>
    public class GrantedDiscountViewModel
    {
        /// <summary>
        /// معرف الخصم.
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// معرف المستخدم الذي حصل على الخصم.
        /// </summary>
        public long? UserId { get; set; }

        /// <summary>
        /// قيمة الخصم الممنوح.
        /// </summary>
        public double? Amount { get; set; }

        /// <summary>
        /// تاريخ منح الخصم.
        /// </summary>
        public DateTime? GrantDate { get; set; }

        /// <summary>
        /// عدد النقاط المرتبطة بالخصم.
        /// </summary>
        public int? Points { get; set; }

        /// <summary>
        /// يحدد ما إذا تم استبدال الخصم أم لا.
        /// </summary>
        public bool Replaced { get; set; }

        /// <summary>
        /// حالة الطلب المرتبط بالخصم.
        /// </summary>
        public string OrderState { get; set; } = null!;

        /// <summary>
        /// معرف الحصالة المرتبطة بالخصم.
        /// </summary>
        public long? MonyBoxId { get; set; }
    }

    public class GrantedDiscountReportVM
    {
        public string PurposeCode { get; set; }
        public string PurposeName { get; set; }
        public int Count { get; set; }          
        public int TotalPoints { get; set; }     
        public double TotalAmount { get; set; }  
    }

}
