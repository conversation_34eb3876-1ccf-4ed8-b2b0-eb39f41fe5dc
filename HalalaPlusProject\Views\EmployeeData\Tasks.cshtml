﻿@model IEnumerable<HalalaPlusProject.CModels.Tasks>

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>المهام</h2>

<p>
    <a  asp-action="Create"> إضافة مهمة</a>
</p>
<table class="table">
    <thead>
             <tr>                   
                      <th scope="col">المهمة</th>
                      <th scope="col">الموظف</th>                        
                      <th scope="col">تاريخ البداية</th>
                       <th scope="col">تاريخ النهاية</th>
                      <th scope="col">المزيد</th>
                    </tr>
            
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.TaskName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Employee)
            </td>
         
            <td>
                @Html.DisplayFor(modelItem => item.StartDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EndDate)
            </td>
          
           
           
          
            <td>
                <a asp-action="Details"   asp-route-id="@item.Id">المزيد..</a>
            </td>
        </tr>
}
    </tbody>
</table>

