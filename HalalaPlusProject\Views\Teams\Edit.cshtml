﻿@model HalalaPlusProject.Models.Team

@{
    ViewData["Title"] = "تعديل عضو";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<h4>@ViewData["Title"]</h4>
<hr />
<div class="row">

    <form asp-action="Edit" enctype="multipart/form-data" class="row">
        <div class="col-md-4">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="Name" class="control-label">الاسم</label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="EnName" class="control-label">الاسم الانجليزي</label>
                <input asp-for="EnName" class="form-control" />
                <span asp-validation-for="EnName" class="text-danger"></span>
            </div>
               <div class="form-group">
                <label asp-for="Major" class="control-label">الوظيفة</label>
                <input asp-for="Major" class="form-control" />
                <span asp-validation-for="Major" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="EnMajor" class="control-label">الوظيفة انجليزي</label>
                <input asp-for="EnMajor" class="form-control" />
                <span asp-validation-for="EnMajor" class="text-danger"></span>
            </div>
        </div>

        <div class="col-md-4">
            <div class="form-group">
                <label asp-for="Overview" class="control-label">النبذة</label>

                <textarea asp-for="Overview" rows="4" class="form-control"></textarea>
                <span asp-validation-for="Overview" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Enoverview" class="control-label">النبذة انجليزي</label>
                <textarea asp-for="Enoverview" rows="4" class="form-control"></textarea>
                <span asp-validation-for="Enoverview" class="text-danger"></span>
            </div>

        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label asp-for="Image" class="control-label">صورة</label>
                <input asp-for="Image" type="file" accept="image/*" class="form-control" />
                <span asp-validation-for="Image" class="text-danger"></span>
            </div>
        </div>
        <div class="form-group mt-5">
            <button type="submit" value="Edit" class="btn btn-primary">حفظ</button>
        </div>
    </form>
</div>

<div>
    <a asp-action="Index">عودة للقائمة السابقة</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
