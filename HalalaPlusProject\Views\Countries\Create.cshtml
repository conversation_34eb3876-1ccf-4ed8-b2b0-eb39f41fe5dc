﻿@model HalalaPlusProject.Models.CountriesTable
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["create"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["create"]</h2>

<h4>@localizer["country"]</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="Country" class="control-label"> @localizer["countryname"]</label>
                <input asp-for="Country" required class="form-control" />
                <span asp-validation-for="Country" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="EnCountry" class="control-label"> اسم المدينة انجليزي</label>
                <input asp-for="EnCountry" required class="form-control" />
                <span asp-validation-for="EnCountry" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Nationality" class="control-label">@localizer["countrynationalty"]</label>
                <input asp-for="Nationality" required class="form-control" />
                <span asp-validation-for="Nationality" class="text-danger"></span>
            </div> 
            <div class="form-group">
                <label asp-for="EnNationality" class="control-label"> الجنسية انجليزي</label>
                <input asp-for="EnNationality" required class="form-control" />
                <span asp-validation-for="EnNationality" class="text-danger"></span>
            </div>
            <div class="form-group">
                <button type="submit" value="Create" class="btn btn-primary">@localizer["create"]</button>
            </div>
        </form>
    </div>
    <div class="col-md-4">
        

        </div>

</div>

<div>
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
