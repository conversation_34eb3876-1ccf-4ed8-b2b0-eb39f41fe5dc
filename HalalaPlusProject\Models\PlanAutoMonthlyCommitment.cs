﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class PlanAutoMonthlyCommitment
{
    [Key]
    public int CommitmentId { get; set; }

    [StringLength(200)]
    public string? Name { get; set; }

    public int? MainCommitmentTypesId { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? Amount { get; set; }

    public byte? DueDay { get; set; }

    [StringLength(10)]
    public string? CalendarType { get; set; }

    public bool? Reminder { get; set; }

    public bool? AutoRepeat { get; set; }

    [StringLength(100)]
    public string? CreatedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [StringLength(100)]
    public string? UpdatedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    public bool? Deleted { get; set; }

    [StringLength(100)]
    public string? DeletedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? DeletedAt { get; set; }

    [ForeignKey("MainCommitmentTypesId")]
    [InverseProperty("PlanAutoMonthlyCommitments")]
    public virtual PlanAutoMainCommitmentType? MainCommitmentTypes { get; set; }

    [InverseProperty("PlanAutoMonthlyCommitments")]
    public virtual ICollection<PlanExpense> PlanExpenses { get; set; } = new List<PlanExpense>();
}
