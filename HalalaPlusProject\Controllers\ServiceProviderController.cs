﻿using DocumentFormat.OpenXml.Office2010.Excel;
using HalalaPlusProject.Areas.Identity.Data;
using HalalaPlusProject.CModels;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using HalalaPlusProject.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

//using NPOI.HSSF.UserModel;
//using NPOI.SS.UserModel;
//using NPOI.XSSF.UserModel;
//using System.Reflection;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// متحكم لإدارة مقدمي الخدمات، ويشمل عمليات الإنشاء، العرض، التعديل، والحذف، بالإضافة إلى إدارة التفاصيل المتعلقة بهم مثل الملفات والخصومات.
    /// </summary>
    [Authorize]
    public class ServiceProviderController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly UserManager<HalalaPlusProjectUser> _userManager;
        private readonly IWebHostEnvironment _hosting; private readonly SignInManager<HalalaPlusProjectUser> _signInManager;
        private readonly IStringLocalizer<ServiceProviderController> _localization;
        public ServiceProviderController(HalalaPlusdbContext context,
                         SignInManager<HalalaPlusProjectUser> signInManager, IStringLocalizer<ServiceProviderController> _localization, UserManager<HalalaPlusProjectUser> userManager, IWebHostEnvironment hosting)
        {
            _context = context;
            _userManager = userManager;
            _hosting = hosting; _signInManager = signInManager;

            this._localization = _localization;
        }

        /// <summary>
        /// يعرض قائمة بجميع مقدمي الخدمات.
        /// </summary>
        public IActionResult Index()
        {
            var temp = new UsersClass().retrive("Provider", _context);
            return View(temp);
        }
        //[AllowAnonymous]
        //[HttpGet]
        //public IActionResult impotexcel()
        //{
        //    var temp = new UsersClass().retrive("Provider", _context);
        //    return View(temp);
        //}
        //[AllowAnonymous]
        //[HttpPost]
        //public async Task< IActionResult> impotexcel(IFormFile file)
        //{

        //    ExcelPackage.LicenseContext = LicenseContext.NonCommercial; // Required for EPPlus from v5+

        //    var people = new List<Person>();

        //    using (var stream = new MemoryStream())
        //    {
        //        await file.CopyToAsync(stream);
        //        using (var package = new ExcelPackage(stream))
        //        {
        //            var worksheet = package.Workbook.Worksheets[0]; // First worksheet
        //            int rowCount = worksheet.Dimension.Rows;

        //            for (int row = 2; row <= rowCount; row++) // Skip header
        //            {
        //                var person = new Person
        //                {
        //                    Id = int.Parse(worksheet.Cells[row, 1].Text),
        //                    Name = worksheet.Cells[row, 2].Text,
        //                    Age = int.Parse(worksheet.Cells[row, 3].Text)
        //                };

        //                people.Add(person);
        //            }
        //        }
        //    }
        //    return Ok();
        //}
        //public ActionResult impotexcel(IFormFile file)
        //{

        //    //IFormFile file = Request.Form.Files[0];
        //    string folderName = "Upload";
        //    string webRootPath = _hosting.WebRootPath;
        //    string newPath = Path.Combine(webRootPath, folderName);
        //    StringBuilder sb = new StringBuilder();
        //    if (!Directory.Exists(newPath))
        //    {
        //        Directory.CreateDirectory(newPath);
        //    }
        //    var people = new List<Person>();
        //    if (file.Length > 0)
        //    {
        //        string sFileExtension = Path.GetExtension(file.FileName).ToLower();
        //        ISheet sheet;
        //        string fullPath = Path.Combine(newPath, file.FileName);
        //        using (var stream = new FileStream(fullPath, FileMode.Create))
        //        {
        //            file.CopyTo(stream);
        //            stream.Position = 0;
        //            if (sFileExtension == ".xls")
        //            {
        //                HSSFWorkbook hssfwb = new HSSFWorkbook(stream); //This will read the Excel 97-2000 formats  
        //                sheet = hssfwb.GetSheetAt(0); //get first sheet from workbook  
        //            }
        //            else
        //            {
        //                XSSFWorkbook hssfwb = new XSSFWorkbook(stream); //This will read 2007 Excel format  
        //                sheet = hssfwb.GetSheetAt(0); //get first sheet from workbook   
        //            }
        //            IRow headerRow = sheet.GetRow(0); //Get Header Row
        //            int cellCount = headerRow.LastCellNum;

        //            for (int i = (sheet.FirstRowNum + 1); i <= sheet.LastRowNum; i++) //Read Excel File
        //            {
        //                IRow row = sheet.GetRow(i);
        //                if (row == null) continue;
        //                if(row.Cells.All(d => d.CellType == CellType.Blank)) continue;
        //                var person = new Person
        //                {
        //                    Id = int.Parse(row.GetCell(0).ToString()),
        //                    Name = row.GetCell(1).ToString(),
        //                    Age = int.Parse(row.GetCell(2).ToString())
        //                };
        //                people.Add(person);
        //            }

        //        }
        //    }
        //    return this.Content("");
        //}



        /// <summary>
        /// يعرض الخدمات المرتبطة بمقدم خدمة معين.
        /// </summary>
        /// <param name="id">معرّف مقدم الخدمة.</param>
        public IActionResult Services(long? id)
        {
            return View(new UsersClass().retrive("Provider", _context));
        }

        /// <summary>
        /// يعرض نموذج إنشاء مقدم خدمة جديد.
        /// </summary>
        [HttpGet]
        public IActionResult Create()
        {
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name");
            ViewData["City"] = new SelectList(_context.CitiesTables.Where(p => p.CId == 1), "Id", "City");
            return View(new ServiceProvidersCreate());
        }

        /// <summary>
        /// يعيد عرض نموذج الإنشاء مع البيانات المدخلة في حالة عدم صلاحية النموذج.
        /// </summary>
        /// <param name="model">بيانات النموذج.</param>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> Createnew(CModels.ServiceProvidersCreate model)
        {
            if (ModelState.IsValid)
            {
            }
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name");
            ViewData["City"] = new SelectList(_context.CitiesTables.Where(p => p.CId == 1), "Id", "City");
            return View(model);
        }

        /// <summary>
        /// يعرض صفحة الخريطة.
        /// </summary>
        public async Task<IActionResult> getMap()
        {

            return View();

        }

        /// <summary>
        /// ينشئ مقدم خدمة جديد مع حساب مستخدم مرتبط به في النظام.
        /// </summary>
        /// <param name="model">بيانات مقدم الخدمة الجديد القادمة من النموذج.</param>
        //[HttpPost]
        ////[ValidateAntiForgeryToken]
        //public async Task<IActionResult> Create(CModels.ServiceProvidersCreate model)
        //{
        //    if (true)
        //    {
        //        //TelegramHandler1.Instance.SendSingleSMSwithoutasync(J);
        //        model.PhoneNumber = (model.PhoneNumber.StartsWith("05")) ? "966" + model.PhoneNumber.Substring(1) : (model.PhoneNumber.StartsWith("5")) ? "966" + model.PhoneNumber : model.PhoneNumber;
        //        var userExists = await _userManager.FindByNameAsync(model.PhoneNumber);
        //        if (userExists != null) return Ok(new { state = 0, message = _localization["usedphone"].Value });
        //        userExists = await _userManager.FindByNameAsync(model.PhoneNumber);
        //        if (userExists != null) return Ok(new { state = 0, message = _localization["usedusername"].Value });
        //        string url = HandleImages.SaveImage(model.Logo, "images", _hosting);

        //        HalalaPlusProjectUser user = new()
        //        {
        //            Email = model.Email,
        //            SecurityStamp = Guid.NewGuid().ToString(),
        //            PhoneNumber = model.PhoneNumber,
        //            Image = url,
        //            UserName = model.PhoneNumber
        //        };
        //        var result = await _userManager.CreateAsync(user, model.Password);
        //        if (result.Succeeded)
        //        {
        //            try
        //            {
        //                await _userManager.AddToRoleAsync(user, "Provider");
        //                ProvidersClass ob = new ProvidersClass(_context, _hosting);
        //                if (!ob.Insert(model, user, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value))
        //                {
        //                    await _userManager.DeleteAsync(user); 
        //                    TelegramHandler1.Instance.SendSingleSMSwithoutasync("error while saving the Provider");
        //                    return Ok(new { state = 0, message = _localization["errorwillsaving1"].Value });

        //                }
        //                return Ok(new { state = 7, message = _localization["addedsuccessfuly"].Value, Url = "Index" });
        //            }
        //            catch (Exception ex)
        //            {
        //                TelegramHandler1.Instance.SendSingleSMSwithoutasync(ex.Message + "stack treace\t" + ex.StackTrace);
        //                await _userManager.DeleteAsync(user);
        //                return Ok(new { state = 0, message = _localization["errorwillsaving1"].Value });

        //            }
        //        }


        //    }

        //    return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });
        //}

        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CModels.ServiceProvidersCreate model)
        {
            if (!ModelState.IsValid)
            {
                return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });
            }

            model.PhoneNumber = (model.PhoneNumber.StartsWith("05")) ? "966" + model.PhoneNumber.Substring(1) : (model.PhoneNumber.StartsWith("5")) ? "966" + model.PhoneNumber : model.PhoneNumber;
            var existingUser = await _userManager.FindByNameAsync(model.PhoneNumber);

            var creatorId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            ProvidersClass ob = new ProvidersClass(_context, _hosting);

            // If the user ALREADY EXISTS
            if (existingUser != null)
            {
                // Check if this user is already a provider with the same name to avoid duplicates.
                if (ob.IsUserAlreadyProvider(existingUser.Id, model.Name))
                {
                    return Ok(new { state = 0, message = _localization["userisalreadyprovider"].Value });
                }

                if (!await ob.Insert(model, existingUser, creatorId))
                {
                    // The provider profile failed to save for some reason.
                    return Ok(new { state = 0, message = _localization["errorwillsaving1"].Value });
                }

                TelegramHandler1.Instance.SendSingleSMSwithoutasync($"Provider profile added to existing user: {model.PhoneNumber}");
                return Ok(new { state = 7, message = _localization["addedsuccessfuly"].Value, Url = "Index" });
            }
            // If the user DOES NOT EXIST
            else
            {
                HalalaPlusProjectUser newUser = new()
                {
                    Email = model.Email,
                    SecurityStamp = Guid.NewGuid().ToString(),
                    PhoneNumber = model.PhoneNumber,
                    UserName = model.PhoneNumber,
                    Image = null // We will set the image after it's saved to disk
                };

                var result = await _userManager.CreateAsync(newUser, model.Password);
                if (!result.Succeeded)
                {
                    // If user creation fails return the errors.
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return Ok(new { state = 0, message = errors });
                }

                // If anything fails from here on, we must delete the user we just created.
                string imageUrl = null;
                try
                {
                    // Save image and update user
                    imageUrl = HandleImages.SaveImage(model.Logo, "images", _hosting);
                    newUser.Image = imageUrl;
                    await _userManager.UpdateAsync(newUser);

                    // Add user to the "Provider" role
                    await _userManager.AddToRoleAsync(newUser, "Provider");

                    // Insert the provider-specific data
                    if (!await ob.Insert(model, newUser, creatorId))
                    {
                        throw new Exception("Failed to insert provider data into the database.");
                    }

                    TelegramHandler1.Instance.SendSingleSMSwithoutasync($"New user and provider created: {model.PhoneNumber}");
                    return Ok(new { state = 7, message = _localization["addedsuccessfuly"].Value, Url = "Index" });
                }
                catch (Exception ex)
                {
                    TelegramHandler1.Instance.SendSingleSMSwithoutasync("CRITICAL ERROR: " + ex.Message + "\nSTACK TRACE:\t" + ex.StackTrace);

                    // Delete the image if it was saved
                    if (!string.IsNullOrEmpty(imageUrl))
                    {
                        HandleImages.RemoveImage(imageUrl, "images", _hosting);
                    }

                    // Delete the user that we just created
                    await _userManager.DeleteAsync(newUser);

                    return Ok(new { state = 0, message = _localization["errorwillsaving1"].Value });
                }
            }
        }


        /// <summary>
        /// يقوم بتعطيل (إيقاف) حساب مقدم خدمة.
        /// </summary>
        /// <param name="id">معرّف مقدم الخدمة.</param>
        [HttpPost]
        public async Task<IActionResult> Disable(long? id)
        {
            try
            {

                if (id == null) return Ok(new { state = 0, message = _localization["errorinoperation"].Value });

                if (await new CustomClasses.ServiceProvidersClass().disables(_context, id ?? 0)) return Ok(new { state = 1, message = _localization["opsuccess"].Value });
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }

        }

        /// <summary>
        /// يقوم بتعطيل خصم معين تابع لمقدم الخدمة.
        /// </summary>
        /// <param name="id">معرّف الخصم.</param>
        [HttpPost]
        public async Task<IActionResult> DisableDiscount(long? id)
        {
            try
            {

                if (id == null) return Ok(new { state = 0, message = _localization["selectoffer"].Value });

                if (await new CustomClasses.DiscountsClass().disable(_context, id ?? 0)) return Ok(new { state = 1, message = _localization["opsuccess"].Value });
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }

        }


        /// <summary>
        /// يعرض تفاصيل كاملة لمقدم خدمة معين، بما في ذلك ملفاته وحساباته ونقاطه ومبيعاته.
        /// </summary>
        /// <param name="id">معرّف مقدم الخدمة.</param>
        public async Task<IActionResult> Details(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }
            var systemUser = (from user in _context.SystemUsers
                              where user.Id == id && user.AccountType == "Provider" && user.Deleted != true
                              select new CModels.ServiceProvidersDetails
                              {
                                  PhoneNumber = user.PhoneNo,
                                  Email = user.Email,
                                  Id = user.Id,
                                  Name = user.Name,
                                  BusnissNo = user.BusinessNo,
                                  ActivityName = _context.Activities.Where(p => p.Id == user.Activity).FirstOrDefault().Name,
                                  City = _context.CitiesTables.Where(p => p.Id == user.City).FirstOrDefault().City,
                                  EnterprisePhoneNo = user.EnterPrisePhoneNo,
                                  ServiceProviderRepresent = user.ServiceProviderRepresent,
                                  Locatin = user.Location,
                                  ContractNo = user.ContractNo,
                                  CashBack = user.CashBack,
                                  Lng = user.Lng ?? "46.70213857938796",
                                  Lat = user.Lat ?? "24.67592860338076",
                                  UserName = user.Asp.UserName,
                                  ContractDate = (user.ContractDate != null) ? DateTime.Parse(user.ContractDate.ToString()) : DateTime.MinValue,
                                  ContractEndDate = (user.ContractEndDate != null) ? DateTime.Parse(user.ContractEndDate.ToString()) : DateTime.MinValue,
                                  Logo = "/images/" + user.Logo,
                                  EnName = user.EnName,
                                  overview = user.OverView,
                                  enoverview = user.EnOverView,
                                  bnifitfrompoints = user.Benefitfrompoints,
                                  enbnifitfrompoints = user.EnBenefitfrompoints,
                                  StoreLink = user.StoreLink

                              }).FirstOrDefault();
            if (systemUser == null)
            {
                return NotFound();
            }
            systemUser.Files = _context.FilesTables.Where(p => p.FileType == 1 && p.UserId == systemUser.Id).Select(i => new Files { Id = i.Id, Name = i.FileName, Link = "/Files/" + i.FileLink }).ToList();
            systemUser.images = _context.FilesTables.Where(p => p.FileType == 5 && p.UserId == systemUser.Id).Select(i => new Files { Id = i.Id, Name = i.FileName, Link = "/images/" + i.FileLink }).ToList();
            systemUser.Accounts = await new SocialAccount().retrive(systemUser.Id, _context);
            systemUser.Points = await new PointsClass().retrive(systemUser.Id, _context);//await new PointsClass().retrive(systemUser.Id, _context);
            systemUser.sales = await new PointsClass().retriveSales(systemUser.Id, _context);
            systemUser.DiscountsList = await new DiscountsClass().retrive(systemUser.Id, _context);
            return View(systemUser);
        }

        /// <summary>
        /// يعرض نموذج تعديل بيانات مقدم خدمة معين معبأ ببياناته الحالية.
        /// </summary>
        /// <param name="id">معرّف مقدم الخدمة.</param>
        public async Task<IActionResult> Edit(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }
            var systemUser = (from user in _context.SystemUsers
                              where user.Id == id && user.AccountType == "Provider" && user.Deleted != true
                              select new CModels.ServiceProvidersgetEdit
                              {
                                  PhoneNumber = user.PhoneNo,
                                  Email = user.Email,
                                  Id = user.Id,
                                  Name = user.Name,
                                  EnName = user.EnName,
                                  BusnissNo = user.BusinessNo,
                                  Activity = user.Activity,
                                  City = user.City,
                                  EnterprisePhoneNo = user.EnterPrisePhoneNo,
                                  ServiceProviderRepresent = user.ServiceProviderRepresent,
                                  //Locatin = user.Location,
                                  ContractDate = user.ContractDate,
                                  ContractEndDate = user.ContractEndDate,
                                  LogoLink = "/images/" + user.Logo,
                                  UserName = user.Asp.UserName,
                                  ContractNo = user.ContractNo,
                                  Lng = user.Lng ?? "46.70213857938796",
                                  Lat = user.Lat ?? "24.67592860338076",
                                  overview = user.OverView,
                                  enoverview = user.EnOverView,
                                  bnifitfrompoints = user.Benefitfrompoints,
                                  enbnifitfrompoints = user.EnBenefitfrompoints,
                                  StoreLink = user.StoreLink
                              }).FirstOrDefault();
            if (systemUser == null)
            {
                return NotFound();
            }
            systemUser.FilesList = _context.FilesTables.Where(p => p.FileType == 1 && p.UserId == systemUser.Id).Select(i => new Files { Link = "/Files/" + i.FileLink, Name = i.FileName, Id = i.Id }).ToList();
            systemUser.images = _context.FilesTables.Where(p => p.FileType == 5 && p.UserId == systemUser.Id).Select(i => new Files { Link = "images" + i.FileLink, Name = i.FileName, Id = i.Id }).ToList();
            systemUser.Accounts = await new SocialAccount().retrive(systemUser.Id, _context);
            //systemUser.Points =await new PointsClass().retrive(systemUser.Id, _context);
            systemUser.Points = new Points();
            systemUser.Sales = await new PointsClass().retriveSales(systemUser.Id, _context);
            systemUser.allPoints = await new PointsClass().retrive(systemUser.Id, _context);
            systemUser.DiscountsList = await new DiscountsClass().retrive(systemUser.Id, _context);
            systemUser.Discounts = new DiscountsModel(); // Initialize empty discount model for the form
            systemUser.AccountsData = new socialMediaAccounts(); // Initialize empty social media account model for the form
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name", systemUser.Activity);
            ViewData["City"] = new SelectList(_context.CitiesTables.Where(p => p.CId == 1), "Id", "City", systemUser.City);
            return View(systemUser);
        }

        /// <summary>
        /// يستقبل البيانات المعدّلة لمقدم الخدمة ويقوم بتحديثها في قاعدة البيانات.
        /// </summary>
        /// <param name="model">البيانات المحدثة لمقدم الخدمة.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(CModels.ServiceProvidersEdit model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var ob = _context.SystemUsers.Find(model.Id);
                    if (ob != null)
                    {
                        if (_context.SystemUsers.Where(p => p.PhoneNo == model.PhoneNumber && p.Id != ob.Id && p.AccountType == "Provider").Count() > 0)
                            return Ok(new { state = 0, message = _localization["usedphone"].Value });
                    }

                    ob.Name = model.Name;
                    ob.ServiceProviderRepresent = model.ServiceProviderRepresent;
                    //ob.Location = model.Locatin;
                    ob.PhoneNo = model.PhoneNumber;
                    ob.EnterPrisePhoneNo = model.EnterprisePhoneNo;
                    ob.Activity = model.Activity;
                    ob.EnName = model.EnName;
                    ob.Lng = model.Lng;
                    ob.Lat = model.Lat;


                    ob.ContractDate = model.ContractDate;
                    ob.ContractEndDate = model.ContractEndDate;
                    ob.BusinessNo = model.BusnissNo;
                    ob.StoreLink = model.StoreLink;
                    ob.OverView = model.overview;
                    ob.EnOverView = model.enoverview;
                    ob.Benefitfrompoints = model.bnifitfrompoints;
                    ob.EnBenefitfrompoints = model.enbnifitfrompoints;
                    ob.ContractNo = model.ContractNo;
                    ob.City = model.City;

                    // if (model.LocationLink != null)
                    // {
                    //     ProvidersClass pc = new ProvidersClass();
                    //     var t = await pc.getLocation(model.LocationLink);
                    //     if (t.Item1)
                    //     {
                    //         ob.Lat = t.Item2;
                    //         ob.Lng = t.Item3;
                    //     }
                    //     else
                    //     {
                    //         ob.Lat = null;
                    //         ob.Lng = null;
                    //     }
                    // }
                    if (model.Logo != null)
                    {
                        if (ob.Logo != null)
                            HandleImages.RemoveImageRoot(ob.Logo, "images", _hosting);
                        ob.Logo = HandleImages.SaveImage(model.Logo, "images", _hosting);
                    }
                    if (model.Files != null)
                    {
                        new FilesClass().insertOrdersFiles(model.Files, ob.Id, "Files", _hosting, _context, 1);
                    }
                    _context.Update(ob);
                    _context.SaveChanges();
                    var userExists = await _userManager.FindByNameAsync(model.PhoneNumber);
                    if (userExists != null)
                    {
                        userExists.PhoneNumber = model.PhoneNumber;
                        userExists.UserName = model.PhoneNumber;
                        await _userManager.UpdateAsync(userExists);
                        await _context.SaveChangesAsync();
                    }
                }
                catch (DbUpdateConcurrencyException)
                {
                    return Ok(new { state = 0, message = _localization["errorwillsaving1"].Value });
                }
                return Ok(new { state = 7, message = _localization["modefiedsuccessfuly"].Value, Url = "/ServiceProvider/Index" });
            }
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name");
            ViewData["City"] = new SelectList(_context.CitiesTables.Where(p => p.CId == 1), "Id", "City");
            return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });
        }

        /// <summary>
        /// يحذف حساب تواصل اجتماعي مرتبط بمقدم الخدمة.
        /// </summary>
        /// <param name="id">معرّف حساب التواصل الاجتماعي.</param>
        public async Task<IActionResult> DeleteAccount(int? id)
        {
            return Ok(await new SocialAccount().delete(id ?? 0, _context, _localization));
        }

        /// <summary>
        /// يحذف ملفًا مرتبطًا بمقدم الخدمة من الخادم وقاعدة البيانات.
        /// </summary>
        /// <param name="id">معرّف الملف.</param>
        public async Task<IActionResult> DeleteFile(string? id)
        {
            if (id == null) return Ok(new { state = 0, message = _localization["datanotdeleted"].Value });
            if (new FilesClass().RemoveImage(long.Parse(id), "Files", _hosting, _context))
            {
                return Ok(new { state = 1, message = _localization["deletessuccessfuly"].Value });
            }
            else
                return Ok(new { state = 0, message = _localization["datanotdeleted"].Value });

        }

        /// <summary>
        /// ينشئ حساب تواصل اجتماعي جديد لمقدم الخدمة.
        /// </summary>
        /// <param name="model">بيانات حساب التواصل الاجتماعي.</param>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateSite(HalalaPlusProject.CModels.socialMediaAccounts model)
        {
            if (ModelState.IsValid)
            {
                return Ok(await new SocialAccount().insert(model, model.userId, _context, _localization));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// يعدّل حساب تواصل اجتماعي قائم لمقدم الخدمة.
        /// </summary>
        /// <param name="model">بيانات حساب التواصل الاجتماعي المحدثة.</param>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> Site(HalalaPlusProject.CModels.socialMediaAccounts model)
        {
            if (ModelState.IsValid && model.Id != null)
            {
                return Ok(await new SocialAccount().update(model, _context, _localization));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// يضيف أو يعدل إعدادات النقاط لمقدم الخدمة.
        /// </summary>
        /// <param name="model">بيانات إعدادات النقاط.</param>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> AddEditPoints(CModels.Points model)
        {
            if (ModelState.IsValid)
            {
                PointsClass obj = new PointsClass();
                return Ok(await obj.insert(model, _context, model.userId, _localization));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// يضيف أو يعدل بيانات المبيعات لمقدم الخدمة.
        /// </summary>
        /// <param name="model">بيانات المبيعات.</param>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> AddEditSales(CModels.Sales model)
        {
            if (ModelState.IsValid)
            {
                PointsClass obj = new PointsClass();
                return Ok(await obj.insertSales(model, _context, model.userId, _localization));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// يضيف أو يعدل خصمًا لمقدم الخدمة.
        /// </summary>
        /// <param name="model">بيانات الخصم.</param>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        [HttpPost]
        public async Task<IActionResult> AddEditDiscount(HalalaPlusProject.CModels.DiscountsModel model)
        {
            if (!ModelState.IsValid)
            {
                return Ok(new { state = 0, message = _localization["fillalldata"].Value });
            }

            DiscountsClass obj = new DiscountsClass();
            var result = await obj.insert(
                model,
                _localization,
                _context,
                model.userId,
                User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value,
                false
            );

            // ✅ check if inserted or not
            var stateProp = result.GetType().GetProperty("state")?.GetValue(result, null);

            if (stateProp is int state && state == 5)
            {
                return Ok(new { state = 7, message = _localization["opsuccess"].Value, url = "/ServiceProvider/Edit/" + model.userId });


            }
            else
            {
                return Ok(new { state = 0, message = "لم يتم الحفظ" });
            }
        }

        /// <summary>
        /// يضيف أو يعدل حساب تواصل اجتماعي لمقدم الخدمة.
        /// </summary>
        /// <param name="model">بيانات حساب التواصل الاجتماعي.</param>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> AddEditAccount(HalalaPlusProject.CModels.socialMediaAccounts model)
        {
            if (ModelState.IsValid)
            {
                if (model.Id == null || model.Id == 0)
                    return Ok(await new SocialAccount().insert(model, model.userId, _context, _localization));
                else
                    return Ok(await new SocialAccount().update(model, _context, _localization));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// يحذف خصمًا معينًا بشكل نهائي.
        /// </summary>
        /// <param name="Id">معرّف الخصم المراد حذفه.</param>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteDiscount(long Id)
        {
            if (ModelState.IsValid)
            {
                DiscountsClass obj = new DiscountsClass();
                return Ok(await obj.delete(Id, _context, _localization));
            }
            return Ok(new { state = 0, message = _localization["datanotdeleted"].Value });
        }

        /// <summary>
        /// يعرض صفحة تأكيد حذف مقدم الخدمة.
        /// </summary>
        /// <param name="id">معرّف مقدم الخدمة المراد حذفه.</param>
        public async Task<IActionResult> Delete(long? id)
        {
            if (id == null || _context.SystemUsers.Where(p => p.AccountType == "Provider") == null)
            {
                return NotFound();
            }
            var systemUser = await _context.SystemUsers.FindAsync(id);

            if (systemUser == null)
            {
                return NotFound();
            }
            return View(systemUser);
        }

        /// <summary>
        /// يقوم بتنفيذ الحذف (الناعم) لمقدم الخدمة بعد التأكيد.
        /// </summary>
        /// <param name="id">معرّف مقدم الخدمة.</param>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(long id)
        {
            if (_context.SystemUsers == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.SystemUsers'  is null.");
            }
            var systemUser = await _context.SystemUsers.FindAsync(id);
            if (systemUser != null)
            {
                systemUser.Deleted = true;
                _context.Update(systemUser);
            }
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        public ActionResult ChangePassword()
        {
            return View(new ChangePasswordViewModel());
        }

        // POST
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> ChangePassword(HalalaPlusProject.CModels.ChangePasswordViewModel model)
        {
            if (!ModelState.IsValid)
                return View(model);

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var result = await _userManager.ChangePasswordAsync(user, model.CurrentPassword, model.NewPassword);

            if (result.Succeeded)
            {
                await _signInManager.RefreshSignInAsync(user);
                ViewBag.Message = "Your password has been changed.";
                return Ok(new { state = 1, message = _localization["addedsuccessfuly"].Value });
            }

            return Ok(new { state = 0, message = _localization["errorwillsaving1"].Value });
            //return View(model);
        }
        //ServiceProvider/EditPersonalData/52745
        public async Task<IActionResult> EditPersonalData()
        {
            var id = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value;

            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }
            var systemUser = (from user in _context.SystemUsers
                              where user.AspId == id && user.AccountType == "Provider" && user.Deleted != true
                              select new CModels.ServiceProvidersgetEdit
                              {
                                  PhoneNumber = user.PhoneNo,
                                  Email = user.Email,
                                  Id = user.Id,
                                  Name = user.Name,
                                  EnName = user.EnName,
                                  BusnissNo = user.BusinessNo,
                                  Activity = user.Activity,
                                  City = user.City,
                                  EnterprisePhoneNo = user.EnterPrisePhoneNo,
                                  ServiceProviderRepresent = user.ServiceProviderRepresent,
                                  //Locatin = user.Location,
                                  ContractDate = user.ContractDate,
                                  ContractEndDate = user.ContractEndDate,
                                  LogoLink = "/images/" + user.Logo,
                                  UserName = user.Asp.UserName,
                                  ContractNo = user.ContractNo,
                                  Lng = user.Lng ?? "46.70213857938796",
                                  Lat = user.Lat ?? "24.67592860338076",
                                  overview = user.OverView,
                                  enoverview = user.EnOverView,
                                  bnifitfrompoints = user.Benefitfrompoints,
                                  enbnifitfrompoints = user.EnBenefitfrompoints,
                                  StoreLink = user.StoreLink
                              }).FirstOrDefault();
            if (systemUser == null)
            {
                return NotFound();
            }
            systemUser.FilesList = _context.FilesTables.Where(p => p.FileType == 1 && p.UserId == systemUser.Id).Select(i => new Files { Link = "/Files/" + i.FileLink, Name = i.FileName, Id = i.Id }).ToList();
            systemUser.images = _context.FilesTables.Where(p => p.FileType == 5 && p.UserId == systemUser.Id).Select(i => new Files { Link = "images" + i.FileLink, Name = i.FileName, Id = i.Id }).ToList();
            systemUser.Accounts = await new SocialAccount().retrive(systemUser.Id, _context);
            //systemUser.Points =await new PointsClass().retrive(systemUser.Id, _context);
            systemUser.Points = new Points();
            systemUser.Sales = await new PointsClass().retriveSales(systemUser.Id, _context);
            systemUser.allPoints = await new PointsClass().retrive(systemUser.Id, _context);
            systemUser.DiscountsList = await new DiscountsClass().retrive(systemUser.Id, _context);
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name", systemUser.Activity);
            ViewData["City"] = new SelectList(_context.CitiesTables.Where(p => p.CId == 1), "Id", "City", systemUser.City);
            return View(systemUser);
        }

        /// <summary>
        /// يستقبل البيانات المعدّلة لمقدم الخدمة ويقوم بتحديثها في قاعدة البيانات.
        /// </summary>
        /// <param name="model">البيانات المحدثة لمقدم الخدمة.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditPersonalData(CModels.ServiceProvidersEdit model)
        { 
            if (ModelState.IsValid)
            {
                try
                {
                    var ob = _context.SystemUsers.Find(model.Id);
                    if (ob != null)
                    {
                        if (_context.SystemUsers.Where(p => p.PhoneNo == model.PhoneNumber && p.Id != ob.Id && p.AccountType == "Provider").Count() > 0)
                            return Ok(new { state = 0, message = _localization["usedphone"].Value });
                    }

                    ob.Name = model.Name;
                    ob.ServiceProviderRepresent = model.ServiceProviderRepresent;
                    //ob.Location = model.Locatin;
                    ob.PhoneNo = model.PhoneNumber;
                    ob.Email = model.Email;
                    ob.EnterPrisePhoneNo = model.EnterprisePhoneNo;
                    ob.Activity = model.Activity;
                    ob.EnName = model.EnName;
                    
                    ob.ContractDate = model.ContractDate;
                    ob.ContractEndDate = model.ContractEndDate;
                    ob.BusinessNo = model.BusnissNo;
                    ob.StoreLink = model.StoreLink;
                    ob.OverView = model.overview;
                    ob.EnOverView = model.enoverview;
                    ob.Benefitfrompoints = model.bnifitfrompoints;
                    ob.EnBenefitfrompoints = model.enbnifitfrompoints;
                    ob.ContractNo = model.ContractNo;
                    ob.City = model.City;
                    if (model.Logo != null)
                    {
                        if (ob.Logo != null)
                            HandleImages.RemoveImageRoot(ob.Logo, "images", _hosting);
                        ob.Logo = HandleImages.SaveImage(model.Logo, "images", _hosting);
                    }
                    if (model.Files != null)
                    {
                        new FilesClass().insertOrdersFiles(model.Files, ob.Id, "Files", _hosting, _context, 1);
                    }
                    _context.Update(ob);
                    _context.SaveChanges();
                    var userExists = await _userManager.FindByNameAsync(model.PhoneNumber);
                    if (userExists != null)
                    {
                        userExists.PhoneNumber = model.PhoneNumber;
                        userExists.UserName = model.PhoneNumber;
                        await _userManager.UpdateAsync(userExists);
                        await _context.SaveChangesAsync();
                    }
                }
                catch (DbUpdateConcurrencyException)
                {
                    return Ok(new { state = 0, message = _localization["errorwillsaving1"].Value });
                }
                return Ok(new { state = 7, message = _localization["modefiedsuccessfuly"].Value, Url = "/Provider/Index" });
            }
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name");
            ViewData["City"] = new SelectList(_context.CitiesTables.Where(p => p.CId == 1), "Id", "City");
            return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });
        }
    }
    }