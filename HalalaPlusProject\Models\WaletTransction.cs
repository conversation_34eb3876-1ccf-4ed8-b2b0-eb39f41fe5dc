﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class WaletTransction
{
    [Key]
    public long Id { get; set; }

    public long? UserId { get; set; }

    [Column("outAmount")]
    public double? OutAmount { get; set; }

    public double? InAmount { get; set; }

    public long? DistUser { get; set; }

    [Column("OPType")]
    [StringLength(50)]
    public string? Optype { get; set; }

    [StringLength(500)]
    public string? Note { get; set; }

    public long WaletId { get; set; }

    [ForeignKey("DistUser")]
    [InverseProperty("WaletTransctionDistUserNavigations")]
    public virtual SystemUser? DistUserNavigation { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("WaletTransctionUsers")]
    public virtual SystemUser? User { get; set; }

    [ForeignKey("WaletId")]
    [InverseProperty("WaletTransctions")]
    public virtual Walet Walet { get; set; } = null!;
}
