﻿namespace HalalaPlusProject.Services
{
    public interface IDashboardActivityService
    {
        public double GetActivityTotalDiscounts(int select);
        public double GetActivityTotalDiscountsByMonth(int select);
        int GetActivityCount(int select);
        int GetActivityCountForDay(int select);
        int GetActivityCountForMonth(int month);
        List<string> GetDateOfLast7Days();
        List<string> GetDateOfLast7Months();
        public DateTime GetTheRightDateTime(int count = 0);

    }

}
