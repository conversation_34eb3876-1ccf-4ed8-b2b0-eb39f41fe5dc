﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    /// <summary>
    /// إدارة عرض الخصومات الممنوحة للعملاء في حصالاتهم.
    /// </summary>
    public class GarentedDiscountsController : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public GarentedDiscountsController(HalalaPlusdbContext context)
        {
            _context = context;
        }
        private Dictionary<string, string> PurposeMap = new Dictionary<string, string>
    {
        { "Register", "نقاط مكافأة تسجيل الدخول" },
        { "ChargeMonyBox", "نقاط مكافأة الشحن" },
        { "CreateMonyBox", "مكافأة إنشاء الحصالة" },
        { "SavingGoal", "مكافأة تحقيق هدف الإدخار" },
        { "UseDiscount", "مكافأة استخدام الخصومات" },
        { "PurchaseProduct", "مكافأة المشتريات الرقمية" },
        { "Createplan", "مكافأة إنشاء الخطة المالية" },
        { "InviteUser", "مكافأة التفاعل وارسال دعوة" }
    };

        public async Task<IActionResult> Report()
        {
            var grouped = await _context.GrantedDiscounts
                .GroupBy(g => g.Purpose)
                .Select(s => new
                {
                    Purpose = s.Key,
                    Count = s.Count(),
                    TotalPoints = s.Sum(x => x.Points) ?? 0,
                    TotalAmount = s.Sum(x => x.Amount) ?? 0
                }).ToListAsync();

            ViewBag.config = _context.CustomerRewardSettings.FirstOrDefault();

            var result = PurposeMap.Select(p => new GrantedDiscountReportVM
            {
                PurposeCode = p.Key,
                PurposeName = p.Value,
                Count = grouped.FirstOrDefault(x => x.Purpose == p.Key)?.Count ?? 0,
                TotalPoints = grouped.FirstOrDefault(x => x.Purpose == p.Key)?.TotalPoints ?? 0,
                TotalAmount = grouped.FirstOrDefault(x => x.Purpose == p.Key)?.TotalPoints /(double) ViewBag.config.PointValueSar ?? 0
            }).ToList();

            ViewBag.config = _context.CustomerRewardSettings.FirstOrDefault();

            return View(result);
        }

        // GET: GarentedDiscounts
        //public async Task<IActionResult> Index()
        //{
        //    var systemUsers = await _context.UsersMonyBoxs.Where(op => op.GrantedDiscounts.Any())
        //        .Include(op => op.GrantedDiscounts)
        //        .Include(op => op.User)
        //        .ToListAsync();
        //    List<CustomerGrantedDiscountsViewModel> listCustomers = new List<CustomerGrantedDiscountsViewModel>();
        //    foreach (var customer in systemUsers)
        //    {
        //        var totalAmount = customer.GrantedDiscounts.Sum(op => op.Amount);
        //        var temp = new CustomerGrantedDiscountsViewModel
        //        {
        //            CustomerId = customer.User.Id,
        //            MonyBoxName = customer.Name,
        //            MonyBoxId = customer.Id,
        //            CustomerName =  customer.User.Name,
        //            CustomerPhone =customer.User.PhoneNo,
        //            CustomerGrantedDiscountsCount = customer.GrantedDiscounts.Count(),
        //            CustomerTotalAmount = "" + ((totalAmount == null) ? (00) : (totalAmount.Value)).ToString("N2")
        //        };
        //        listCustomers.Add(temp);
        //    }

        //    return View(listCustomers);
        //}

        /// <summary>
        /// عرض قائمة مجمعة للخصومات الممنوحة للعملاء.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة الخصومات الممنوحة.</returns>
        public async Task<IActionResult> Index()
        {
            var systemUsers = await _context.CustomerGrantedDiscountsViews.ToListAsync();
            return View(systemUsers);
        }

        /// <summary>
        /// عرض التفاصيل الخاصة بالخصومات الممنوحة لحصالة (صندوق أموال) معينة.
        /// </summary>
        /// <param name="id">معرف الحصالة المراد عرض تفاصيلها.</param>
        /// <returns>عرض يحتوي على قائمة مفصلة بالخصومات الممنوحة لهذه الحصالة.</returns>
        public async Task<IActionResult> Details(long? id)
        {
            var customer = await _context.UsersMonyBoxs.Where(op => op.Id == id)
              .Include(op => op.GrantedDiscounts)
              .Include(op => op.User)
              .FirstAsync();

            List<CustomerGrantedDiscountsViewModel> listCustomers = new List<CustomerGrantedDiscountsViewModel>();
            var totalAmount = customer.GrantedDiscounts.Sum(op => op.Amount);
            var temp = new CustomerGrantedDiscountsViewModel
            {
                CustomerId = customer.User.Id,
                MonyBoxName = customer.Name,
                CustomerName = customer.User.Name,
            };
            temp.GrantedDiscountUsers = customer.GrantedDiscounts.ToList();
            temp.GrantedDiscountUsers.ForEach(discount =>
            discount.OrderState = (discount.Replaced == true) ? "تم التحويل" : (discount.OrderState == "new") ? "تم الطلب" : "تم رفع الطلب");

            listCustomers.Add(temp);
            return View(listCustomers);
        }

    }
}