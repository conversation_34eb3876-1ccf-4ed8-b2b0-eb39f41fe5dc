﻿using System.Net;
using System.Net.Mail;

namespace HalalaPlusProject.Service
{
    public class MailClass
    {

        public async Task<bool> sendImail(CModels.MailModel mail) {
            try
            {
                string fromMail = "<EMAIL>";
                string fromPassword = /*info.password;*/"tzhdcrzlkxncznor";
                MailMessage message = new MailMessage();
                message.From = new MailAddress(fromMail);
                message.Subject = mail.Subject;
                message.To.Add(new MailAddress(mail.Email));
                message.Body = mail.body;
                message.IsBodyHtml = true;
                var smtpClient = new SmtpClient("smtp.gmail.com")
                {
                    Port = 587,
                    Credentials = new NetworkCredential(fromMail, fromPassword),
                    EnableSsl = true,
                };
                smtpClient.Send(message);
                return true;

            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }
}
