﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class CustomerPlan
{
    [Key]
    public long Id { get; set; }

    public long CustomerId { get; set; }

    public int PlanId { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime StartDate { get; set; }

    public bool IsClosed { get; set; }

    [ForeignKey("CustomerId")]
    [InverseProperty("CustomerPlans")]
    public virtual SystemUser Customer { get; set; } = null!;

    [ForeignKey("PlanId")]
    [InverseProperty("CustomerPlans")]
    public virtual Plan Plan { get; set; } = null!;
}
