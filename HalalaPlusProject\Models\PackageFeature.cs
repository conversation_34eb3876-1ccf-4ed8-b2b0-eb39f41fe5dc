﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Index("PackageId", "FeatureKey", Name = "UX_PackageFeatures_PackageId_FeatureKey", IsUnique = true)]
public partial class PackageFeature
{
    [Key]
    public int Id { get; set; }

    public int PackageId { get; set; }

    [StringLength(100)]
    public string FeatureKey { get; set; } = null!;

    [StringLength(255)]
    public string FeatureValue { get; set; } = null!;

    [ForeignKey("PackageId")]
    [InverseProperty("PackageFeatures")]
    public virtual Package Package { get; set; } = null!;
}
