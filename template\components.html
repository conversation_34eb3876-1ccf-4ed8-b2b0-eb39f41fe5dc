<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HalalaPlus - Components Demo</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    
    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css">
    
    <!-- Toastr -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    
    <!-- Dropzone -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/6.0.0-beta.2/dropzone.min.css">
    
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="g-sidenav-show bg-gray-100 rtl">
    
    <!-- Sidebar -->
    <aside class="sidenav navbar navbar-vertical navbar-expand-xs border-0 border-radius-xl my-3 fixed-end me-1 rotate-caret" id="sidenav-main">
        <div class="sidenav-header">
            <i class="fas fa-times p-3 cursor-pointer text-white opacity-5 position-absolute start-0 top-0 d-none d-xl-none" aria-hidden="true" id="iconSidenav"></i>
            <a class="navbar-brand m-0 text-center" href="index.html">
                <img src="assets/img/placeholder.svg" class="navbar-brand-img h-100" alt="main_logo" style="max-height: 3rem;">
                <span class="ms-1 font-weight-bold text-white">HalalaPlus</span>
            </a>
        </div>
        
        <hr class="horizontal light mt-0 mb-2">
        
        <div class="collapse navbar-collapse w-auto max-height-vh-100" id="sidenav-collapse-main">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link text-white" href="index.html">
                        <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                            <i class="material-icons opacity-10">dashboard</i>
                        </div>
                        <span class="nav-link-text ms-1">لوحة التحكم</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link text-white active bg-gradient-primary" href="components.html">
                        <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                            <i class="material-icons opacity-10">widgets</i>
                        </div>
                        <span class="nav-link-text ms-1">المكونات</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="sidenav-footer position-absolute w-100 bottom-0">
            <div class="mx-3">
                <a class="btn bg-gradient-primary mt-4 w-100" href="#" type="button">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </aside>
    
    <!-- Main Content -->
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <nav class="navbar navbar-main navbar-expand-lg px-0 mx-4 shadow-none border-radius-xl" id="navbarBlur" navbar-scroll="true">
            <div class="container-fluid py-1 px-3">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent mb-0 pb-0 pt-1 px-0 me-sm-6 me-5">
                        <li class="breadcrumb-item text-sm"><a class="opacity-5 text-dark" href="javascript:;">الصفحات</a></li>
                        <li class="breadcrumb-item text-sm text-dark active" aria-current="page">المكونات</li>
                    </ol>
                    <h6 class="font-weight-bolder mb-0">عرض المكونات</h6>
                </nav>
                
                <div class="collapse navbar-collapse mt-sm-0 mt-2 me-md-0 me-sm-4" id="navbar">
                    <ul class="navbar-nav justify-content-end">
                        <li class="nav-item d-xl-none ps-3 d-flex align-items-center">
                            <a href="javascript:;" class="nav-link text-body p-0" id="iconNavbarSidenav">
                                <div class="sidenav-toggler-inner">
                                    <i class="sidenav-toggler-line"></i>
                                    <i class="sidenav-toggler-line"></i>
                                    <i class="sidenav-toggler-line"></i>
                                </div>
                            </a>
                        </li>
                        
                        <li class="nav-item d-flex align-items-center">
                            <a href="javascript:;" class="nav-link text-body font-weight-bold px-0">
                                <i class="fa fa-user me-sm-1"></i>
                                <span class="d-sm-inline d-none">أحمد محمد</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        
        <!-- Page Content -->
        <div class="container-fluid py-4">
            
            <!-- Buttons Section -->
            <div class="row mb-4">
                <div class="col-12" data-aos="fade-up">
                    <div class="card">
                        <div class="card-header pb-0">
                            <h6>الأزرار والإشعارات</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-sm">أزرار الإشعارات</h6>
                                    <button class="btn btn-success btn-demo-notification me-2 mb-2" 
                                            data-type="success" 
                                            data-title="نجح!" 
                                            data-message="تم تنفيذ العملية بنجاح">
                                        إشعار نجاح
                                    </button>
                                    <button class="btn btn-danger btn-demo-notification me-2 mb-2" 
                                            data-type="error" 
                                            data-title="خطأ!" 
                                            data-message="حدث خطأ أثناء تنفيذ العملية">
                                        إشعار خطأ
                                    </button>
                                    <button class="btn btn-warning btn-demo-notification me-2 mb-2" 
                                            data-type="warning" 
                                            data-title="تحذير!" 
                                            data-message="يرجى الانتباه لهذا التحذير">
                                        إشعار تحذير
                                    </button>
                                    <button class="btn btn-info btn-demo-notification me-2 mb-2" 
                                            data-type="info" 
                                            data-title="معلومات" 
                                            data-message="هذه معلومات مفيدة">
                                        إشعار معلومات
                                    </button>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6 class="text-sm">أزرار التنبيهات</h6>
                                    <button class="btn btn-primary btn-demo-alert me-2 mb-2" 
                                            data-type="success" 
                                            data-title="تم بنجاح!" 
                                            data-message="تم حفظ البيانات بنجاح">
                                        تنبيه نجاح
                                    </button>
                                    <button class="btn btn-secondary btn-demo-alert me-2 mb-2" 
                                            data-type="error" 
                                            data-title="خطأ!" 
                                            data-message="فشل في حفظ البيانات">
                                        تنبيه خطأ
                                    </button>
                                    <button class="btn btn-outline-primary btn-demo-alert me-2 mb-2" 
                                            data-type="question" 
                                            data-title="تأكيد الحذف" 
                                            data-message="هل أنت متأكد من حذف هذا العنصر؟">
                                        تنبيه تأكيد
                                    </button>
                                    <button class="btn btn-outline-secondary btn-loading me-2 mb-2">
                                        زر مع تحميل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Forms Section -->
            <div class="row mb-4">
                <div class="col-md-6" data-aos="fade-right">
                    <div class="card">
                        <div class="card-header pb-0">
                            <h6>النماذج المتقدمة</h6>
                        </div>
                        <div class="card-body">
                            <form id="demoForm">
                                <div class="input-group input-group-outline mb-3">
                                    <label class="form-label">الاسم الكامل</label>
                                    <input type="text" class="form-control" required>
                                </div>
                                
                                <div class="input-group input-group-outline mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" required>
                                </div>
                                
                                <div class="mb-3">
                                    <select class="form-select select2" data-placeholder="اختر المدينة">
                                        <option></option>
                                        <option value="riyadh">الرياض</option>
                                        <option value="jeddah">جدة</option>
                                        <option value="dammam">الدمام</option>
                                        <option value="mecca">مكة المكرمة</option>
                                        <option value="medina">المدينة المنورة</option>
                                    </select>
                                </div>
                                
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="notifications">
                                    <label class="form-check-label" for="notifications">
                                        تفعيل الإشعارات
                                    </label>
                                </div>
                                
                                <button type="submit" class="btn bg-gradient-primary">حفظ البيانات</button>
                                <button type="reset" class="btn btn-outline-secondary">إعادة تعيين</button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6" data-aos="fade-left">
                    <div class="card">
                        <div class="card-header pb-0">
                            <h6>البطاقات التفاعلية</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6 mb-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <div class="icon icon-lg bg-gradient-primary mx-auto mb-3">
                                                <i class="fas fa-users"></i>
                                            </div>
                                            <h5 class="card-title">1,234</h5>
                                            <p class="card-text text-sm">إجمالي المستخدمين</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-6 mb-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <div class="icon icon-lg bg-gradient-success mx-auto mb-3">
                                                <i class="fas fa-chart-line"></i>
                                            </div>
                                            <h5 class="card-title">$45,678</h5>
                                            <p class="card-text text-sm">إجمالي المبيعات</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-6 mb-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <div class="icon icon-lg bg-gradient-info mx-auto mb-3">
                                                <i class="fas fa-shopping-cart"></i>
                                            </div>
                                            <h5 class="card-title">567</h5>
                                            <p class="card-text text-sm">الطلبات الجديدة</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-6 mb-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <div class="icon icon-lg bg-gradient-warning mx-auto mb-3">
                                                <i class="fas fa-star"></i>
                                            </div>
                                            <h5 class="card-title">4.8</h5>
                                            <p class="card-text text-sm">متوسط التقييم</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Data Table Section -->
            <div class="row mb-4">
                <div class="col-12" data-aos="fade-up">
                    <div class="card">
                        <div class="card-header pb-0">
                            <h6>جدول البيانات المتقدم</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="advancedTable">
                                    <thead>
                                        <tr>
                                            <th>الاسم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>المدينة</th>
                                            <th>تاريخ التسجيل</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>أحمد محمد</td>
                                            <td><EMAIL></td>
                                            <td>الرياض</td>
                                            <td>2024-01-15</td>
                                            <td><span class="badge bg-success">نشط</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>فاطمة أحمد</td>
                                            <td><EMAIL></td>
                                            <td>جدة</td>
                                            <td>2024-01-10</td>
                                            <td><span class="badge bg-warning">معلق</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>محمد علي</td>
                                            <td><EMAIL></td>
                                            <td>الدمام</td>
                                            <td>2024-01-05</td>
                                            <td><span class="badge bg-danger">غير نشط</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Progress and Timeline -->
            <div class="row mb-4">
                <div class="col-md-6" data-aos="fade-right">
                    <div class="card">
                        <div class="card-header pb-0">
                            <h6>شريط التقدم</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="text-sm">تطوير الموقع</span>
                                    <span class="text-sm">75%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-gradient-primary" style="width: 75%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="text-sm">اختبار النظام</span>
                                    <span class="text-sm">60%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-gradient-success" style="width: 60%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="text-sm">التوثيق</span>
                                    <span class="text-sm">40%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-gradient-warning" style="width: 40%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6" data-aos="fade-left">
                    <div class="card">
                        <div class="card-header pb-0">
                            <h6>الخط الزمني</h6>
                        </div>
                        <div class="card-body">
                            <div class="timeline timeline-one-side">
                                <div class="timeline-block mb-3">
                                    <span class="timeline-step bg-gradient-success">
                                        <i class="fas fa-check"></i>
                                    </span>
                                    <div class="timeline-content">
                                        <h6 class="text-dark text-sm font-weight-bold mb-0">تم إنشاء المشروع</h6>
                                        <p class="text-secondary font-weight-bold text-xs mt-1 mb-0">22 DEC 7:20 PM</p>
                                    </div>
                                </div>
                                
                                <div class="timeline-block mb-3">
                                    <span class="timeline-step bg-gradient-info">
                                        <i class="fas fa-code"></i>
                                    </span>
                                    <div class="timeline-content">
                                        <h6 class="text-dark text-sm font-weight-bold mb-0">بدء التطوير</h6>
                                        <p class="text-secondary font-weight-bold text-xs mt-1 mb-0">21 DEC 11:00 AM</p>
                                    </div>
                                </div>
                                
                                <div class="timeline-block mb-3">
                                    <span class="timeline-step bg-gradient-warning">
                                        <i class="fas fa-bug"></i>
                                    </span>
                                    <div class="timeline-content">
                                        <h6 class="text-dark text-sm font-weight-bold mb-0">إصلاح الأخطاء</h6>
                                        <p class="text-secondary font-weight-bold text-xs mt-1 mb-0">20 DEC 2:20 PM</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </main>
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/6.0.0-beta.2/dropzone-min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="js/script.js"></script>
    
    <script>
        // Initialize advanced table
        $(document).ready(function() {
            $('#advancedTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json"
                },
                "responsive": true,
                "pageLength": 5,
                "order": [[0, "asc"]]
            });
        });
    </script>
</body>
</html>
