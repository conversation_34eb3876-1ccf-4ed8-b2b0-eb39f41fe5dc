﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة جداول المدن، وتوفير عمليات الإنشاء والقراءة والتحديث والحذف (CRUD).
    /// </summary>
    [Authorize]
    public class CitiesTablesController : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public CitiesTablesController(HalalaPlusdbContext context)
        {
            _context = context;
        }

        // GET: CitiesTables
        /// <summary>
        /// عرض قائمة بجميع المدن.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة المدن.</returns>
        public async Task<IActionResult> Index()
        {
            var halalaPlusdbContext = _context.CitiesTables.Include(c => c.CIdNavigation);
            return View(await halalaPlusdbContext.ToListAsync());
        }

        // GET: CitiesTables/Details/5
        /// <summary>
        /// عرض التفاصيل الخاصة بمدينة معينة.
        /// </summary>
        /// <param name="id">معرف المدينة المراد عرض تفاصيلها.</param>
        /// <returns>عرض يحتوي على تفاصيل المدينة، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null || _context.CitiesTables == null)
            {
                return NotFound();
            }

            var citiesTable = await _context.CitiesTables
                .Include(c => c.CIdNavigation)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (citiesTable == null)
            {
                return NotFound();
            }

            return View(citiesTable);
        }

        // GET: CitiesTables/Create
        /// <summary>
        /// عرض نموذج إنشاء مدينة جديدة.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج الإنشاء.</returns>
        public IActionResult Create()
        {
            ViewData["CId"] = new SelectList(_context.CountriesTables, "Id", "Id");
            return View();
        }

        // POST: CitiesTables/Create
        /// <summary>
        /// معالجة عملية إنشاء مدينة جديدة وحفظها في قاعدة البيانات.
        /// </summary>
        /// <param name="citiesTable">كائن المدينة الذي يحتوي على البيانات الجديدة.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة المدن؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,City,CId")] CitiesTable citiesTable)
        {
            if (ModelState.IsValid)
            {
                _context.Add(citiesTable);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            ViewData["CId"] = new SelectList(_context.CountriesTables, "Id", "Id", citiesTable.CId);
            return View(citiesTable);
        }

        // GET: CitiesTables/Edit/5
        /// <summary>
        /// عرض نموذج تعديل بيانات مدينة حالية.
        /// </summary>
        /// <param name="id">معرف المدينة المراد تعديلها.</param>
        /// <returns>عرض يحتوي على بيانات المدينة في نموذج التعديل، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null || _context.CitiesTables == null)
            {
                return NotFound();
            }

            var citiesTable = await _context.CitiesTables.FindAsync(id);
            if (citiesTable == null)
            {
                return NotFound();
            }
            ViewData["CId"] = new SelectList(_context.CountriesTables, "Id", "Id", citiesTable.CId);
            return View(citiesTable);
        }

        // POST: CitiesTables/Edit/5
        /// <summary>
        /// معالجة التعديلات المقدمة لمدينة وحفظها في قاعدة البيانات.
        /// </summary>
        /// <param name="id">معرف المدينة التي يتم تعديلها.</param>
        /// <param name="citiesTable">كائن المدينة الذي يحتوي على البيانات المحدثة.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة المدن؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,City,CId")] CitiesTable citiesTable)
        {
            if (id != citiesTable.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(citiesTable);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CitiesTableExists(citiesTable.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["CId"] = new SelectList(_context.CountriesTables, "Id", "Id", citiesTable.CId);
            return View(citiesTable);
        }

        // GET: CitiesTables/Delete/5
        /// <summary>
        /// عرض صفحة تأكيد حذف مدينة.
        /// </summary>
        /// <param name="id">معرف المدينة المراد حذفها.</param>
        /// <returns>عرض يحتوي على تفاصيل المدينة لتأكيد الحذف، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null || _context.CitiesTables == null)
            {
                return NotFound();
            }

            var citiesTable = await _context.CitiesTables
                .Include(c => c.CIdNavigation)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (citiesTable == null)
            {
                return NotFound();
            }

            return View(citiesTable);
        }

        // POST: CitiesTables/Delete/5
        /// <summary>
        /// تنفيذ عملية حذف المدينة من قاعدة البيانات بعد التأكيد.
        /// </summary>
        /// <param name="id">معرف المدينة المراد حذفها.</param>
        /// <returns>بعد إتمام العملية بنجاح، يتم عرض صفحة قائمة المدن.</returns>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            if (_context.CitiesTables == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.CitiesTables'  is null.");
            }
            var citiesTable = await _context.CitiesTables.FindAsync(id);
            if (citiesTable != null)
            {
                _context.CitiesTables.Remove(citiesTable);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// التحقق من وجود مدينة في قاعدة البيانات بالمعرف المحدد.
        /// </summary>
        /// <param name="id">معرف المدينة للتحقق منه.</param>
        /// <returns>إرجاع 'true' إذا كانت المدينة موجودة، وإلا 'false'.</returns>
        private bool CitiesTableExists(int id)
        {
            return (_context.CitiesTables?.Any(e => e.Id == id)).GetValueOrDefault();
        }
    }
}