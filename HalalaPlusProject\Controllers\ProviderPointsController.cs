﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة نظام النقاط الخاص بمقدمي الخدمة، بما في ذلك إعدادات النقاط وقواعد المبيعات.
    /// </summary>
    [Authorize]
    public class ProviderPointsController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IStringLocalizer<ProviderPointsController> _localization;

        /// <summary>
        /// إنشاء نسخة جديدة من وحدة التحكم مع حقن الخدمات المطلوبة.
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات.</param>
        /// <param name="_localization">خدمة الترجمة.</param>
        public ProviderPointsController(HalalaPlusdbContext context, IStringLocalizer<ProviderPointsController> _localization)
        {
            this._context = context;
            this._localization = _localization;
        }

        /// <summary>
        /// عرض إعدادات النقاط والمبيعات الحالية لمقدم الخدمة، بالإضافة إلى قائمة بجميع قواعد النقاط السابقة.
        /// </summary>
        /// <returns>عرض يحتوي على إعدادات النقاط والمبيعات الحالية وقائمة بالقواعد.</returns>
        public async Task<IActionResult> Index()
        {
            var temp = new ProviderPointsIndex();
            var id = _context.SystemUsers.Where(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).FirstOrDefault().Id;
            var ob = _context.PointSettings.FirstOrDefault(p => p.UserId == id);
            if (ob != null)
            {
                temp.sales = new Sales();
                temp.sales.DeservePoints = ob.Points;
                temp.sales.SalesNo = ob.Sales;
                temp.sales.Id = ob.Id;
            }
            temp.Points = new Points();
            temp.Id = id;
            temp.allPoints = await new PointsClass().retrive(id, _context);
            return View(temp);
        }

        /// <summary>
        /// معالجة عملية إضافة أو تعديل قاعدة نقاط معينة.
        /// </summary>
        /// <param name="model">البيانات الخاصة بقاعدة النقاط.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> AddEditPoints(CModels.Points model)
        {
            if (ModelState.IsValid)
            {
                PointsClass obj = new PointsClass();
                return Ok(await obj.insert(model, _context, model.userId, _localization));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// معالجة عملية إضافة أو تعديل قاعدة تحويل المبيعات إلى نقاط.
        /// </summary>
        /// <param name="model">البيانات الخاصة بقاعدة المبيعات.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> AddEditSales(CModels.Sales model)
        {
            if (ModelState.IsValid)
            {
                PointsClass obj = new PointsClass();
                return Ok(await obj.insertSales(model, _context, model.userId, _localization));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// حذف قاعدة نقاط معينة.
        /// </summary>
        /// <param name="id">معرف قاعدة النقاط المراد حذفها.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل عملية الحذف.</returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> DeletePoints(long? id)
        {
            if (id != null)
            {
                PointsClass obj = new PointsClass();
                return Ok(await obj.DeletePoint(id ?? 0, _context, _localization));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

    }
}