﻿@model HalalaPlusProject.Models.InvestorsTable

@{
    ViewData["Title"] = "Create";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Create</h1>

<h4>InvestorsTable</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="InvestorName" class="control-label"></label>
                <input asp-for="InvestorName" class="form-control" />
                <span asp-validation-for="InvestorName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="PhoneNumber" class="control-label"></label>
                <input asp-for="PhoneNumber" class="form-control" />
                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Email" class="control-label"></label>
                <input asp-for="Email" class="form-control" />
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="StocksNumber" class="control-label"></label>
                <input asp-for="StocksNumber" class="form-control" />
                <span asp-validation-for="StocksNumber" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="StocksValue" class="control-label"></label>
                <input asp-for="StocksValue" class="form-control" />
                <span asp-validation-for="StocksValue" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="TransctionId" class="control-label"></label>
                <input asp-for="TransctionId" class="form-control" />
                <span asp-validation-for="TransctionId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CreateAt" class="control-label"></label>
                <input asp-for="CreateAt" class="form-control" />
                <span asp-validation-for="CreateAt" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
