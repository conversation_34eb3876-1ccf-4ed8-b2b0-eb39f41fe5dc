﻿namespace HalalaPlusProject.CustomClasses
{
    public class EmployeeClass
    {
        Models.HalalaPlusdbContext _context;
        public EmployeeClass(Models.HalalaPlusdbContext context)
        {
            this._context = context;
        }
        public async Task< bool> Insert(CModels.EmployeesModel model, Areas.Identity.Data.HalalaPlusProjectUser user, string MasterId)
        {
            try
            {

                var ob = new Models.SystemUser();
                ob.Name = model.Name;
                ob.PhoneNo = model.PhoneNo;
                ob.Email = model.Email;
               ob.BirthDate = DateOnly.Parse(model.BirthDate.ToString());
                ob.IdentityNo = model.IdentityNo;
                ob.Nationality = model.Nationality;
                ob.Salary = model.Salary;
                ob.AspId = user.Id;
                ob.Deleted = false;
                ob.AccountType = "Employee";
                ob.DateofJoin = DateTime.Now;
                ob.MasterId = MasterId;
                _context.Add(ob);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {

                return false;
            }

        }
     public async Task< bool> InsertBusnissEmp(CModels.BusineesEmployeesModel model, Areas.Identity.Data.HalalaPlusProjectUser user, string MasterId,long orgId)
        {
            try
            {

                var ob = new Models.SystemUser();
                ob.Name = model.Name;
                ob.PhoneNo = model.PhoneNo;
                //ob.Email = model.Email;
                //if (ob.BirthDate != null) ob.BirthDate = DateOnly.Parse(model.BirthDate.Value.ToShortDateString());
                //ob.IdentityNo = model.IdentityNo;
                //ob.Nationality = model.Nationality;
                //ob.Salary = model.Salary;
                ob.AspId = user.Id;
                ob.Deleted = false;
                ob.AccountType = "BusinessEmp";
                ob.DateofJoin = DateTime.Now;
                ob.MasterId = MasterId;
                ob.OrgId = orgId;
                _context.Add(ob);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {

                return false;
            }

        }
   
    }
}
