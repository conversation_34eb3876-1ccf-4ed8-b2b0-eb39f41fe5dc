﻿@model IEnumerable<HalalaPlusProject.CModels.MarketerIndexModel>
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["customerstracking"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["customers"]</h1>


<table id="tbl1" class="table">
    <thead>
             <tr>                   
                      <th scope="col">@localizer["customername"]</th>
                      <th scope="col">@localizer["activity"]</th>
            <th scope="col"> @localizer["details"]</th>

            <th scope="col">@localizer["state"]</th>
                    </tr>
            
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Activity)
            </td>
         <td>
                    <a asp-action="CustomerDetails" asp-route-id="@item.Id">@localizer["details"]</a>
            </td>
            
            <td>
                @Html.DisplayFor(modelItem => item.state)
            </td>
           
           
          
            
        </tr>
}
    </tbody>
</table>
@section Scripts{
    <script>
  let table = new DataTable('#tbl1');
  

    </script>
}
