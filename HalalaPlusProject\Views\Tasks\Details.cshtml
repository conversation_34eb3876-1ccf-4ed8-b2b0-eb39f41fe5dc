﻿@model HalalaPlusProject.CModels.TasksDetails
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["details"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h3>@localizer["details"]</h3>

<h4>@localizer["task"]</h4>
    <hr />
         <div class="col-md-12">
         <div class="row">

     
     <div class="col-md-5">

          <div class="form-group">
                <label class="form-label">@localizer["taskname"]</label>
                <label  class="form-control">@Model.TaskName</label>               
            </div>
            <div class="form-group">
                <label class="form-label">وصف المهمة</label>
                <label class="form-control">@Model.TaskDescribe</label>
            </div>
            <div class="form-group">
                <label class="form-label">الاسم انجليزي</label>
                <label class="form-control">@Model.EnTaskName</label>
            </div>
            <div class="form-group">
                <label class="form-label">الوصف انجليزي</label>
                <label class="form-control">@Model.EnTaskDescribe</label>
            </div>
               <div class="form-group">
                <label class="form-label">@localizer["offerstartdate"]</label>
                <label  class="form-control">@Model.StartDate</label>               
            </div>
              <div class="form-group">
                <label class="form-label"> @localizer["offerenddate"]</label>
                <label  class="form-control">@Model.EndDate</label>               
            </div>
             


        </div>
         <div class="col-md-5">

             <div class="form-group">
                <label class="form-label">@localizer["note"]</label>
                <label  class="form-control">@Model.Notes</label>               
            </div>
            <div class="form-group">
                <label class="form-label"> @localizer["empname"] </label>
                <label  class="form-control">@Model.Employee</label>               
            </div>
            <div class="form-group">
                <label class="form-label"> @localizer["attatchments"]</label>
                <label  class="form-control">@Model.Files</label>               
            </div>

          </div> 
          </div>
          </div>
<div>
    <a asp-action="Edit" class="btn btn-primary px-5" asp-route-id="@Model?.Id">@localizer["edit"]</a> |
    <a asp-action="Delete" class="btn btn-primary px-5" asp-route-id="@Model?.Id">@localizer["delete"]</a> |
    <a class="btn btn-primary px-5" asp-action="Index">@localizer["backtolist"] </a>
</div>
