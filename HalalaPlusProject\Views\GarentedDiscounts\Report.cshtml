﻿@using System.Globalization
@model IEnumerable<HalalaPlusProject.CModels.GrantedDiscountReportVM>

<h2>تقرير منح النقاط</h2>

<table class="table table-bordered">
    <thead>
        <tr>
            <th>الغرض</th>
            <th>مرات المنح</th>
            <th>إجمالي النقاط الممنوحة</th>
            <th>القيمة بالريال السعودي</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.PurposeName</td>
                <td>@item.Count</td>
                <td>@item.TotalPoints</td>
                <td>@item.TotalAmount.ToString("C2", new CultureInfo("ar-SA"))</td>
            </tr>
        }
    </tbody>
</table>
