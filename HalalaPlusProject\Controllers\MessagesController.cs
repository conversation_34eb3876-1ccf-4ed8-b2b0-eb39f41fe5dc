﻿using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    public class MessagesController : Controller
    {

        private readonly HalalaPlusdbContext _context;
        private readonly IStringLocalizer<MessagesController> _localization;
        public MessagesController(HalalaPlusdbContext context, IStringLocalizer<MessagesController> _localization)
        {
            _context = context;
            this._localization = _localization; 
        }
        public IActionResult Index()
        {
            return View();
        }
        [HttpGet]
        public IActionResult Edit(long? id)
        {
            if(id== null) return NotFound();
            var data = _context.ContactUs.Find(id);                 
            return View(data);
        } 
        [HttpPost]
        public async Task< IActionResult> Edit(Models.ContactU model)
        {
            if(model== null) return Ok(new { state = 0, message = _localization["fillalldata"].Value });
            var data = _context.ContactUs.Find(model.Id);
            if (data == null) return Ok(new { state = 0, message = _localization["messagenotsent"].Value });
            if (model.Replay == null) return Ok(new { state = 0, message = _localization["fillalldata"].Value });
            try
            {
                Service.MailClass mail = new Service.MailClass();
               if(! await mail.sendImail(new CModels.MailModel() { Email = data.Email, Subject = "Replay ", body = model.Replay })) return Ok(new { state = 0, message = _localization["messagenotsent"].Value });

                data.IsReplay = true;
                data.Replay = model.Replay;
                _context.Update(data);
                _context.SaveChanges();


                return Ok(new { state = 1, message = _localization["sendsuccessfuly"].Value });
            }
            catch (Exception ex) {
                return Ok(new { state = 0, message = _localization["messagenotsent"].Value });
            }
               

            
        }


    }
}
