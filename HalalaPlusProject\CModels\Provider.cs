﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    public class Provider
    {
        public long Id { get; set; }
        //[Display(Name = "اسم مقدم الخدمة")]

        public string? Name { get; set; }
        //[Display(Name = "اسم المستخدم")]
        public string? UserName { get; set; }
        //[Display(Name = "المدينة")]
        public string? City { get; set; }
        [Required]
        //[Display(Name = "النشاط")]
        public string? ActivityName { get; set; }
        [Required]
        //[Display(Name = "اسم الممثل")]
        public string? ServiceProviderRepresent { get; set; }
        //[Display(Name = "رقم الجوال")]
        public string? PhoneNumber { get; set; }
        //[Display(Name = "الايميل")]
        //[RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }
        //public string AspId { get; set; } = null!;
        //[Display(Name = "رقم السجل التجاري")]
        public string BusnissNo { get; set; } = null!;
        //[Display(Name = "رقم المنشأة")]
        public string EnterprisePhoneNo { get; set; } = null!;
        //[Display(Name = "الموقع")]
        public string Locatin { get; set; } = null!;
        //[Display(Name = "الشعار")]
        public string? Logo { get; set; } = null!;
     
        //[Display(Name = "الكاش باك")]
        public double? CashBack { get; set; }
        public string? startDate { get; set; }
        public string? endDate { get; set; }
        
    }
}
