﻿using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Spreadsheet;
using FirebaseAdmin.Auth;
using HalalaPlusProject.CModels;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.ReportingServices.ReportProcessing.ReportObjectModel;
using System.Security.Claims;

namespace HalalaPlusProject.CustomClasses
{
    public class UsersClass
    {
        public IOrderedEnumerable<MarketerIndexModel> retrive(string type, HalalaPlusdbContext _context )
        {
            try
            {
                var list = (from user  in _context.SystemUsers
                            where user.AccountType == type && user.Deleted != true//&& user.MasterId == System.Security.Claims.ClaimTypes.NameIdentifier  
                            select new CModels.MarketerIndexModel
                            {
                                PhoneNo = user.PhoneNo,Email = user.Email,Id = user.Id,Name = user.Name,
                                UserName = user.Asp.UserName,Nationality = _context.CountriesTables.Where(p => p.Id == user.Nationality).FirstOrDefault().Nationality,
                                Activity = _context.Activities.Where(p => p.Id == user.Activity).FirstOrDefault().Name,
                                state = (user.Status == "D") ? "تنشيط" : "ايقاف",
                                hasComplatedDaTa = (user.RegisterProgress == "0") ? false : true
                            }).ToList().OrderByDescending(o => o.Id);
                return list;
            }
            catch (Exception)
            {
                return new List<MarketerIndexModel>().OrderBy(e => e.Id);
            }

        }
        public IOrderedEnumerable<MarketerIndexModel> retriveForMarketer(string type,string masterid, HalalaPlusdbContext _context)
        {
            try
            {
                var list = (from user in _context.SystemUsers
                            where user.AccountType == type && user.Deleted != true&& user.MasterId == masterid

                            select new CModels.MarketerIndexModel
                            {
                                PhoneNo = user.PhoneNo,
                                Email = user.Email,
                                Id = user.Id,
                                Name = user.Name,
                                OrderStatus = user.OrderStatus,
                                UserName = user.Asp.UserName,
                                Nationality = _context.CountriesTables.Where(p => p.Id == user.Nationality).FirstOrDefault().Nationality,
                                Activity = _context.Activities.Where(p => p.Id == user.Activity).FirstOrDefault().Name,
                                state = (user.Status == "D") ? "تنشيط" : "ايقاف",
                                hasComplatedDaTa = (user.RegisterProgress == "0") ? false : true
                            }).ToList().OrderByDescending(o => o.Id);
                return list;

            }
            catch (Exception)
            {
                return new List<MarketerIndexModel>().OrderBy(e => e.Id);
            }

        }


        public IOrderedEnumerable<MarketerIndexModel> retriveList(string type, HalalaPlusdbContext _context)
        {
            try
            {
                var list = (from user in _context.SystemUsers
                            where( user.AccountType == type ||user.AccountType== "Orginazation") && user.Deleted != true

                            select new CModels.MarketerIndexModel
                            {
                                PhoneNo = user.PhoneNo,
                                Email = user.Email,
                                Id = user.Id,
                                aspid="https://www.halalaplus.com/"+ user.AspId+ "/RegisterEmployees",
                                Name = user.Name,
                                UserName = user.Asp.UserName,
                                Nationality = _context.CountriesTables.Where(p => p.Id == user.Nationality).FirstOrDefault().Nationality,
                                Activity = _context.Activities.Where(p => p.Id == user.Activity).FirstOrDefault().Name,
                                state = (user.Status == "D") ? "تنشيط" : "ايقاف"
                                    ,
                                hasComplatedDaTa = (user.Asp.RegisterProgress == "0") ? false : true
                            }).ToList().OrderByDescending(o => o.Id);
                return list;

            }
            catch (Exception)
            {
                return new List<MarketerIndexModel>().OrderBy(e => e.Id);
            }

        }
         public IOrderedEnumerable<MarketerIndexModel> retriveEventsList(string type, HalalaPlusdbContext _context)
        {
            try
            {
                var list = (from user in _context.SystemUsers
                            where( user.AccountType == type) && user.Deleted != true

                            select new CModels.MarketerIndexModel
                            {
                                PhoneNo = user.PhoneNo,
                                Email = user.Email,
                                Id = user.Id,
                                aspid="https://www.halalaplus.com/"+ user.AspId+ "/RegisterEmployees",
                                Name = user.Name,
                                UserName = user.Asp.UserName,
                                Nationality = _context.CountriesTables.Where(p => p.Id == user.Nationality).FirstOrDefault().Nationality,
                                Activity = _context.Activities.Where(p => p.Id == user.Activity).FirstOrDefault().Name,
                                state = (user.Status == "D") ? "تنشيط" : "ايقاف"
                                    ,
                                hasComplatedDaTa = (user.Asp.RegisterProgress == "0") ? false : true
                            }).ToList().OrderByDescending(o => o.Id);
                return list;

            }
            catch (Exception)
            {
                return new List<MarketerIndexModel>().OrderBy(e => e.Id);
            }

        }

        public List<MarketerIndexModel> retrive(string type, HalalaPlusdbContext _context, string userId )
        {
            try
            {
                var currentUser = System.Threading.Thread.CurrentPrincipal as ClaimsPrincipal;
                if (currentUser == null || !currentUser.IsInRole("Admin"))
                {
                     
                    return (from user in _context.SystemUsers
                            where user.AccountType == type && user.Deleted != true && user.MasterId == userId
                            select new CModels.MarketerIndexModel
                            {
                                PhoneNo = user.PhoneNo,
                                Email = user.Email,
                                Id = user.Id,
                                Name = user.Name,
                                UserName = user.Asp.UserName,
                                Nationality = _context.CountriesTables.Where(p => p.Id == user.Nationality).FirstOrDefault().Nationality,
                                Activity = _context.Activities.Where(p => p.Id == user.Activity).FirstOrDefault().Name
                            }).ToList();
                }
                else
                    return (from user in _context.SystemUsers
                            where user.AccountType == type && user.Deleted != true 
                            select new CModels.MarketerIndexModel
                            {
                                PhoneNo = user.PhoneNo,
                                Email = user.Email,
                                Id = user.Id,
                                Name = user.Name,
                                UserName = user.Asp.UserName,
                                Nationality = _context.CountriesTables.Where(p => p.Id == user.Nationality).FirstOrDefault().Nationality,
                                Activity = _context.Activities.Where(p => p.Id == user.Activity).FirstOrDefault().Name
                            }).ToList();

            }
            catch (Exception)
            {
                return new List<MarketerIndexModel>();
            }

        }
      
        public List<MarketerIndexModel> retriveCustomers(HalalaPlusdbContext _context, string userId = null)
        {
            try
            {
                return (from user in _context.SystemUsers
                        where user.AccountType == "Customer" && user.Deleted != true /*&& user.MasterId == userId*/
                        select new CModels.MarketerIndexModel
                        {
                            PhoneNo = user.PhoneNo,

                            Id = user.Id,
                            Name = (user.Name != null && user.Name.Length > 0) ? user.Name : (user.Email != null && user.Email.Length > 0) ? user.Email : user.PhoneNo,
                            UserName = user.Asp.UserName,
                            Nationality = (user.Nationality != null) ? _context.CountriesTables.Where(p => p.Id == user.Nationality).FirstOrDefault().Nationality : null,
                            Activity = (user.Activity != null) ? _context.Activities.Where(p => p.Id == user.Activity).FirstOrDefault().Name : null,
                            state = "نشط"
                        }).ToList();

            }
            catch (Exception)
            {
                return new List<MarketerIndexModel>();
            }

        }
        public List<MarketerIndexModel> retriveProviderss(HalalaPlusdbContext _context, string userId = null)
        {
            try
            {
                return (from user in _context.SystemUsers
                        where user.AccountType == "Provider" && user.Deleted != true && user.MasterId == userId
                        select new CModels.MarketerIndexModel
                        {
                            PhoneNo = user.PhoneNo,

                            Id = user.Id,
                            Name = (user.Name != null && user.Name.Length > 0) ? user.Name : (user.Email != null && user.Email.Length > 0) ? user.Email : user.PhoneNo,
                            UserName = user.Asp.UserName,
                            Nationality = (user.Nationality != null) ? _context.CountriesTables.Where(p => p.Id == user.Nationality).FirstOrDefault().Nationality : null,
                            Activity = (user.Activity != null) ? _context.Activities.Where(p => p.Id == user.Activity).FirstOrDefault().Name : null,
                            state = "نشط"
                        }).ToList();

            }
            catch (Exception)
            {
                return new List<MarketerIndexModel>();
            }

        }

        public CustomerModel retriveCustomerDetails(long id, HalalaPlusdbContext _context, string userId = null)
        {
            try
            {
#pragma warning disable CS8603 // Possible null reference return.
                return (from user in _context.SystemUsers
                        where user.AccountType == "Customer" && user.Deleted != true && user.Id == id /*&& user.MasterId == userId*/
                        select new CModels.CustomerModel
                        {
                            PhoneNo = user.PhoneNo,

                            Id = user.Id,
                            Name = (user.Name != null && user.Name.Length > 0) ? user.Name : (user.Email != null && user.Email.Length > 0) ? user.Email : user.PhoneNo,
                            MemberNo = user.Asp.MemberNo,
                            Nationality = (user.Nationality != null) ? _context.CountriesTables.Where(p => p.Id == user.Nationality).FirstOrDefault().Nationality : null,
                            Gender = user.Gender,
                            Birthdate = (user.BirthDate == null) ? null : DateTime.Parse(user.BirthDate.ToString()),
                            Email = user.Email,
                            IdentityNo = user.IdentityNo,
                            state = "نشط"
                        }).FirstOrDefault();
#pragma warning restore CS8603 // Possible null reference return.

            }
            catch (Exception)
            {
                return new CustomerModel();
            }

        }

        public UserDetailsModel retriveUser(long id, HalalaPlusdbContext _context)
        {
            try
            {
                var systemUser = (from user in _context.SystemUsers
                                  where user.Id == id && user.Deleted != true
                                  select new CModels.UserDetailsModel
                                  {
                                      PhoneNo = user.PhoneNo,
                                      Email = user.Email,
                                      Id = user.Id,
                                      Name = user.Name,
                                      UserName = user.Asp.UserName,

                                      Nationality = _context.CountriesTables.Where(p => p.Id == user.Nationality).FirstOrDefault().Nationality,
                                      Salary = user.Salary,
                                      BirthDate = (user.BirthDate == null) ? null : DateTime.Parse(user.BirthDate.ToString()),
                                      IdentityNo = user.IdentityNo,
                                      Amount = user.Amount,
                                      AccountNo = user.AccountNo,
                                      NationalityNo = user.Nationality,
                                      DiscountCode = user.DiscountCode,
                                      Precentage = user.Precentage,
                                      BusnissNo = user.BusinessNo,
                                      CashBack = user.CashBack,


                                  })
                .FirstOrDefault();
                return systemUser;
            }
            catch (Exception ex)
            {
                return new UserDetailsModel();
            }

        }
        public UserEmployeeModel retriveUserEmployee(long id, HalalaPlusdbContext _context)
        {
            try
            {
                var systemUser = (from user in _context.SystemUsers
                                  where user.Id == id && user.Deleted != true
                                  select new CModels.UserEmployeeModel
                                  {
                                      PhoneNo = user.PhoneNo,
                                      Email = user.Email,
                                      Id = user.Id,
                                      Name = user.Name,
                                      UserName = user.Asp.UserName,
                                      Nationality = user.Nationality,
                                      Salary = user.Salary,
                                      BirthDate = (user.BirthDate == null) ? null :  user.BirthDate  ,
                                      IdentityNo = user.IdentityNo,
                                  }).FirstOrDefault();
                return systemUser;
            }
            catch (Exception)
            {
                return new UserEmployeeModel();
            }

        }
        public UserDetails retriveUserDetails(string id, HalalaPlusdbContext _context)
        {
            try
            {
                var systemUser = (from user in _context.SystemUsers
                                  where user.AspId == id
                                  select new CModels.UserDetails
                                  {
                                      PhoneNo = user.PhoneNo,
                                      Email = user.Email,
                                      Id = user.Id,
                                      Name = user.Name,
                                      BusnissNo = user.BusinessNo,
                                      ActivityName = _context.Activities.Where(p => p.Id == user.Activity).FirstOrDefault().Name,
                                      City = _context.CitiesTables.Where(p => p.Id == user.City).FirstOrDefault().City,
                                      EnterprisePhoneNo = user.EnterPrisePhoneNo,
                                      ServiceProviderRepresent = user.ServiceProviderRepresent,
                                      Locatin = user.Location,
                                      UserName = user.Asp.UserName,
                                      Logo = "/Images/" + user.Logo,
                                      CashBack = user.CashBack,
                                      startDate = (user.ContractDate == null) ? null : user.ContractDate.Value.ToShortDateString(),
                                      endDate = (user.ContractEndDate == null) ? null : user.ContractEndDate.Value.ToShortDateString(),
                                      Salary = user.Salary,
                                      BirthDate = (user.BirthDate == null) ? null : DateTime.Parse(user.BirthDate.ToString()),
                                      IdentityNo = user.IdentityNo,
                                      GetProviderStatistics=new GetProviderStatistics()

                                  }).FirstOrDefault();
                return systemUser;
            }
            catch (Exception)
            {
                return new UserDetails();
            }

        }



    }
}
