﻿@model IEnumerable<HalalaPlusProject.Models.Package>
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["packages"];
}

<div class="row">
    <h3>@localizer["packages"]</h3>
    <a asp-action="Create" style="float:left; margin-right: 10px;">اضافة باقة</a>
</div>
<div class="tab">
    <button class="tablinks Active" onclick="openCity(event, 'ActivePackagesTab')">الباقات النشطة</button>
    <button class="tablinks" onclick="openCity(event, 'InactivePackagesTab')">الباقات الغير نشطة]</button>
</div>
<div id="ActivePackagesTab" class="tabcontent Active" style="display:block">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table id="tbl1" class="table table-striped text-center">
                            <thead>
                                <tr>
                                    <th scope="col">@localizer["name"]</th>
                                    <th scope="col">الرسومات</th>
                                    <th scope="col">السعر</th>
                                    <th scope="col">المدة</th>
                                    <th scope="col">الحالة</th>
                                    <th scope="col">@localizer["more"]</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Where(op=>op.IsActive.Value))
                                {
                                    <tr>
                                        <td>@Html.DisplayFor(modelItem => item.Name)</td>
                                        <td>@Html.DisplayFor(modelItem => item.Characters)</td>
                                        <td>@Html.DisplayFor(modelItem => item.Price)</td>
                                        <td>@Html.DisplayFor(modelItem => item.Period)</td>
                                        <td>@Html.DisplayFor(modelItem => item.State)</td>
                                        <td>
                                            <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-primary tablebtn">@localizer["edit"]</a> |
@*                                             <button onclick="setField1('/Packages/Deactivate',@item.Id)" class="btn btn-primary tablebtn">@localizer["deactivate"]</button> |
 *@                                            <button onclick="Delete('/Packages/Delete?id='+@item.Id)" class="btn btn-primary tablebtn">@localizer["delete"]</button> |
                                            <a asp-action="Details" class="btn btn-primary tablebtn" asp-route-id="@item.Id">@localizer["more"]</a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="InactivePackagesTab" class="tabcontent">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table id="tbl2" class="table table-striped text-center">
                            <thead>
                                <tr>
                                    <th scope="col">@localizer["name"]</th>
                                    <th scope="col">@localizer["characters"]</th>
                                    <th scope="col">@localizer["price"]</th>
                                    <th scope="col">@localizer["period"]</th>
                                    <th scope="col">@localizer["state"]</th>
                                    <th scope="col">@localizer["more"]</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Where(p => !p.IsActive.Value))
                                {
                                    <tr>
                                        <td>@Html.DisplayFor(modelItem => item.Name)</td>
                                        <td>@Html.DisplayFor(modelItem => item.Characters)</td>
                                        <td>@Html.DisplayFor(modelItem => item.Price)</td>
                                        <td>@Html.DisplayFor(modelItem => item.Period)</td>
                                        <td>@Html.DisplayFor(modelItem => item.State)</td>
                                        <td>
                                            <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-primary tablebtn">@localizer["edit"]</a> |
@*                                             <a onclick="setField('/Packages/Activate?id='+@item.Id)" class="btn btn-primary tablebtn">@localizer["activate"]</a> |
 *@                                            <a asp-action="Details" class="btn btn-primary" asp-route-id="@item.Id">@localizer["more"]..</a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Placeholder functions to be implemented
        function setField1(url, id) {
            // Implement this function to set field
            console.log(`Setting field with URL: ${url}, ID: ${id}`);
        }

        function Delete(url) {
            // Implement this function to delete
            console.log(`Deleting with URL: ${url}`);
        }

        function setField(url, id) {
            // Implement this function to set field
            console.log(`Setting field with URL: ${url}, ID: ${id}`);
        }

        // Initialize DataTables
        let table = new DataTable('#tbl1');
        let table2 = new DataTable('#tbl2');
    </script>
}



@* @model IEnumerable<HalalaPlusProject.Models.Package>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Characters)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Price)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Period)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.State)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.IsActive)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.CreateOn)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.MasterId)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.PackageDays)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.EnName)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.EnCharacters)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.EnPeriod)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Characters)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Price)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Period)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.State)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.IsActive)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.CreateOn)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.MasterId)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.PackageDays)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EnName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EnCharacters)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EnPeriod)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.Id">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.Id">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.Id">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
 *@