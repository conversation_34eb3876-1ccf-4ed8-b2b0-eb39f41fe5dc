﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class PlanAutoMainCommitmentType
{
    [Key]
    public int TypeId { get; set; }

    [StringLength(100)]
    public string TypeName { get; set; } = null!;

    [InverseProperty("MainCommitmentTypes")]
    public virtual ICollection<PlanAllocation> PlanAllocations { get; set; } = new List<PlanAllocation>();

    [InverseProperty("MainCommitmentTypes")]
    public virtual ICollection<PlanAutoMonthlyCommitment> PlanAutoMonthlyCommitments { get; set; } = new List<PlanAutoMonthlyCommitment>();
}
