﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("CitiesTable")]
public partial class CitiesTable
{
    [Key]
    public int Id { get; set; }

    [StringLength(250)]
    public string? City { get; set; }

    [Column("C_Id")]
    public int? CId { get; set; }

    [StringLength(250)]
    public string? EnCity { get; set; }

    [ForeignKey("CId")]
    [InverseProperty("CitiesTables")]
    public virtual CountriesTable? CIdNavigation { get; set; }
}
