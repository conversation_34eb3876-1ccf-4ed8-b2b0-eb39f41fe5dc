﻿@model IEnumerable<HalalaPlusProject.CModels.Tasks>
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["tasks"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["tasks"]</h2>


<table class="table">
    <thead>
             <tr>
            <th scope="col">@localizer["task"]</th>
            <th scope="col">@localizer["employee"]</th>
            <th scope="col">@localizer["taskstartsdate"]</th>
            <th scope="col">@localizer["taskenddate"]</th>
            <th scope="col">@localizer["more"]</th>
                    </tr>
            
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.TaskName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Employee)
            </td>
         
            <td>
                @Html.DisplayFor(modelItem => item.StartDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EndDate)
            </td>
          
           
           
          
            <td>
                    <a asp-action="Details" asp-route-id="@item.Id">@localizer["more"]</a>
            </td>
        </tr>
}
    </tbody>
</table>

