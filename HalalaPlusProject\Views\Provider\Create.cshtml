﻿@model HalalaPlusProject.CModels.EmployeesModel

@{
    ViewData["Title"] = "Create";
    Layout = "~/Views/Shared/_Layout.cshtml";
}



  <div>
        <a  asp-action="Index"><img src="../assets/img/svgs/solid/arrow-right.svg" style="width: 30px;" alt=""></a>
        <h3>إضافة موظف</h3>

      </div>
<div class="row">
    <div class="offset-md-2 col-md-8 col-sm-12">
        <form asp-action="Create" class="submitfm" novalidate>
            <div class="row">
                  <div asp-validation-summary="ModelOnly" class="text-danger"></div>
             </div>
             
         <div class="row">
           <div class="col-md-5">
          <div class="form-group">
                        <label asp-for="Name" class="control-label">الاسم        <span class="text-danger required-star">*</span></label>
                        <input asp-for="Name" class="form-control" data-val="true"
                        
                               data-val-required="الاسم مطلوب" />
                <span asp-validation-for="Name" required class="text-danger"></span>
            </div>
            
            <div class="form-group">
                        <label asp-for="Nationality" class="control-label">  الجنسية      <span class="text-danger required-star">*</span></label>
                 <select asp-for="Nationality" required class ="form-select" asp-items="ViewBag.National"></select>
                  <span asp-validation-for="Nationality" required class="text-danger"></span>
            </div>

              <div class="form-group">
                <label asp-for="IdentityNo" class="control-label">رقم الهوية</label>
                <input asp-for="IdentityNo" class="form-control" />
                <span asp-validation-for="IdentityNo" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="BirthDate" class="control-label">تاريخ الميلاد</label>
                <input asp-for="BirthDate" type="date" class="form-control" />
                <span asp-validation-for="BirthDate" class="text-danger"></span>
            </div>
              <div class="form-group">
                <label asp-for="Salary" class="control-label">الراتب </label>
                <input asp-for="Salary" placeholder="2000" class="form-control" />
                <span asp-validation-for="Salary" class="text-danger"></span>
            </div>
        

          </div>

           <div class="col-md-5">
     
         <div class="form-group">
                <label asp-for="PhoneNo" class="control-label">رقم الجوال</label>
                <input asp-for="PhoneNo" class="form-control" />
                <span asp-validation-for="PhoneNo" class="text-danger"></span>
            </div>
                    <div class="form-group">
                        <label asp-for="Email" class="control-label">البريد الالكتروني        <span class="text-danger required-star">*</span></label>
                        <input asp-for="Email" class="form-control" type="text"
                               data-val="true"
                               data-val-email="يجب إدخال بريد إلكتروني صالح"
                               data-val-required="البريد الإلكتروني مطلوب" />

                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>

              <div class="form-group">
                <label asp-for="UserName" class="control-label">اسم المستخدم</label>
                <input asp-for="UserName" class="form-control" />
                <span asp-validation-for="UserName" class="text-danger"></span>
            </div>
              <div class="form-group">
                        <label asp-for="Password" class="control-label"> كلمة المرور       <span class="text-danger required-star">*</span></label>
                        <input asp-for="Password" class="form-control" data-val="true"
                               data-val-email="يجب ان تحتوي كلمة المرور على ارقام وحروف كبيرة وصغيرة ورموز"
                               data-val-required="كلمة السر مطلوبة" />
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>
                    <div class="clear"></div>

          </div>
                <div class="clear"></div>
            </div>

            <div class="clear"></div><br />
           
           
            
           
          
       
       
            <div class="form-group mt-2">
                <button type="submit" value="Create" class="btn btn-primary" >إنشاء</button>
            </div>
        </form>
    </div>
</div>



@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
