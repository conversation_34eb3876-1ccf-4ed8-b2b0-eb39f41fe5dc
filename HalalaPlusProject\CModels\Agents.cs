﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل معلومات المستخدم الأساسي للوكيل.
    /// </summary>
    public class AgentUser
    {
        /// <summary>
        /// معرف المستخدم.
        /// </summary>
        public long? Id { get; set; }

        /// <summary>
        /// اسم المستخدم.
        /// </summary>
        [Required]
        public string? Name { get; set; }

        /// <summary>
        /// معرف الجنسية.
        /// </summary>
        [Required]
        public int? Nationality { get; set; }

        /// <summary>
        /// رقم الهوية.
        /// </summary>
        [Required]
        public string? IdentityNo { get; set; }

        /// <summary>
        /// تاريخ الميلاد.
        /// </summary>
        [Required]
        public DateTime? BirthDate { get; set; }

        /// <summary>
        /// رقم الهاتف.
        /// </summary>
        [Required]
        public string? PhoneNo { get; set; }

        /// <summary>
        /// البريد الإلكتروني.
        /// </summary>
        [Required]
        [RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }

        /// <summary>
        /// اسم المستخدم للنظام.
        /// </summary>
        [Required]
        public string? UserName { get; set; }

        /// <summary>
        /// رقم الحساب المرتبط.
        /// </summary>
        [Required(ErrorMessage = "يجب تحديد الكمية")]
        public string? AccountNo { get; set; }
    }


    public class Agents : AgentUser
    {
        /// <summary>
        /// كلمة المرور.
        /// </summary>
        [Required]
        [RegularExpression(@"^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{8,}$", ErrorMessage = "يجب ان تحتوي كلمة المرور على ارقام وحروف كبيرة وصغيرة ورموز")]
        public string? Password { get; set; }
    }
}
