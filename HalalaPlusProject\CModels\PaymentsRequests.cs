﻿namespace HalalaPlusProject.CModels
{
    public class PaymentsRequests
    {
       
            public string Id { get; set; }              // رقم العملية
            public string UserName { get; set; }        // اسم المستخدم
            public string UserPhone { get; set; }       // رقم هاتف المستخدم
            public double? Amount { get; set; }         // المبلغ
            public bool? IsVerified { get; set; }       // هل تم التحقق؟
            public string Status { get; set; }          // حالة العملية (CAPTURED أو غيرها)
            public string EntityId { get; set; }        // نوع العملية (pm = منتج، m = شحن حصالة)
            public DateTime? CreateAt { get; set; }     // تاريخ إنشاء العملية
      

    }
}
