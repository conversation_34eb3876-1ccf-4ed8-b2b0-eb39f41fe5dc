﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Index("NormalizedEmail", Name = "EmailIndex")]
public partial class AspNetUser
{
    [Key]
    public string Id { get; set; } = null!;

    public string? RefreshToken { get; set; }

    public DateTime RefreshTokenExpiryTime { get; set; }

    public string? FullName { get; set; }

    public string? ShortScrap { get; set; }

    public string? Image { get; set; }

    public bool? Type { get; set; }

    [StringLength(256)]
    public string? UserName { get; set; }

    [StringLength(256)]
    public string? NormalizedUserName { get; set; }

    [StringLength(256)]
    public string? Email { get; set; }

    [StringLength(256)]
    public string? NormalizedEmail { get; set; }

    public bool EmailConfirmed { get; set; }

    public string? PasswordHash { get; set; }

    public string? SecurityStamp { get; set; }

    public string? ConcurrencyStamp { get; set; }

    public string? PhoneNumber { get; set; }

    public bool PhoneNumberConfirmed { get; set; }

    public bool TwoFactorEnabled { get; set; }

    public DateTimeOffset? LockoutEnd { get; set; }

    public bool LockoutEnabled { get; set; }

    public int AccessFailedCount { get; set; }

    public bool Deleted { get; set; }

    public string? MemberNo { get; set; }

    [StringLength(50)]
    public string? RegisterProgress { get; set; }

    public string? FacePrint { get; set; }

    public bool HasCustomBrand { get; set; }

    public string? NotificationToken { get; set; }

    public bool EnableNotification { get; set; }

    [Column("PIN")]
    public int? Pin { get; set; }

    public bool FirstLogin { get; set; }

    public bool NeedOtp { get; set; }

    public string? DeleteReason { get; set; }

    [StringLength(50)]
    public string? DeleteEmail { get; set; }

    [InverseProperty("User")]
    public virtual ICollection<AspNetUserClaim> AspNetUserClaims { get; set; } = new List<AspNetUserClaim>();

    [InverseProperty("User")]
    public virtual ICollection<AspNetUserLogin> AspNetUserLogins { get; set; } = new List<AspNetUserLogin>();

    [InverseProperty("User")]
    public virtual ICollection<AspNetUserToken> AspNetUserTokens { get; set; } = new List<AspNetUserToken>();

    [InverseProperty("User")]
    public virtual ICollection<ProvidersCustomBrand> ProvidersCustomBrands { get; set; } = new List<ProvidersCustomBrand>();

    [InverseProperty("Asp")]
    public virtual ICollection<ServiceProvider> ServiceProviders { get; set; } = new List<ServiceProvider>();

    [InverseProperty("Asp")]
    public virtual ICollection<SystemUser> SystemUsers { get; set; } = new List<SystemUser>();

    [InverseProperty("Asp")]
    public virtual ICollection<TaskesTable> TaskesTables { get; set; } = new List<TaskesTable>();

    [ForeignKey("UserId")]
    [InverseProperty("Users")]
    public virtual ICollection<AspNetRole> Roles { get; set; } = new List<AspNetRole>();
}
