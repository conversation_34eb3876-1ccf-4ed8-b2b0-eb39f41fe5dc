﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class ActivitiesCitiesView
{
    public int? Activity { get; set; }

    public int? City { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    [StringLength(20)]
    public string? AccountType { get; set; }

    [StringLength(1)]
    [Unicode(false)]
    public string? Status { get; set; }

    public int Id { get; set; }

    [StringLength(250)]
    public string? EnName { get; set; }

    [Column("image")]
    [StringLength(500)]
    public string? Image { get; set; }
}
