﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    public class Tasks
    {
        public int Id { get; set; }
        [Required]
        //[Display(Name = "أسم المهمة")]
        public string? TaskName { get; set; }
        public string? EnTaskName { get; set; }
        //[Display(Name = "وصف المهمة")]
        public string? TaskDescribe { get; set; }
        public string? EnTaskDescribe { get; set; }
        //[Display(Name = "تاريخ التعاقد")]
        //public DateTime? ConractDate { get; set; }
        [Required]
        //[Display(Name = "بداية المهمة")]
        public DateTime? StartDate { get; set; }
        [Required]
        //[Display(Name = "نهاية المهمة")]
        public DateTime? EndDate { get; set; }
        //[Display(Name = "ملاحظات")]
        public string? Notes { get; set; }
        [Required]
        //[Display(Name = "الموظف")]
        public string? Employee { get; set; }
        public string? status { get; set; }
       
    }
    public class TasksDetails : Tasks
    {
        public List<string>? Files { get; set; }
    }
    public class TasksCreate : Tasks
    {
        //[Display(Name = "المرفقات")]
        public List<IFormFile>? Files { get; set; }
       
    }

}
