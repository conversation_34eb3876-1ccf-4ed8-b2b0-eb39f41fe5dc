﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("Achievement")]
public partial class Achievement
{
    [Key]
    public int Id { get; set; }

    [StringLength(500)]
    public string? Title { get; set; }

    [StringLength(500)]
    public string? EnTitle { get; set; }

    [StringLength(2500)]
    public string? Details { get; set; }

    [StringLength(2500)]
    public string? EnDetails { get; set; }

    [StringLength(500)]
    public string? Image { get; set; }
}
