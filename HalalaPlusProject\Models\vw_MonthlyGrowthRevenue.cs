﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models
{
    [Keyless]

    public partial class vw_MonthlyGrowthRevenue
    {
        public string YearMonth { get; set; } = string.Empty;
        public int OrdersCount { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AvgOrderValue { get; set; }
        public int NewSubscriptions { get; set; }
        public int NewCustomers { get; set; }
        public int NewProviders { get; set; }
    }

    }

