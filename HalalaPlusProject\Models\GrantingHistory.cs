﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class GrantingHistory
{
    [StringLength(50)]
    public string? Purpose { get; set; }

    public int? PointsCount { get; set; }

    public int? GrantTimes { get; set; }
}
