﻿using System;
using System.Collections.Generic;

namespace HalalaPlusProject.Models;

public partial class MonthlyCommitment
{
    public int CommitmentId { get; set; }

    public string? Name { get; set; }

    public int? MainCommitmentTypesId { get; set; }

    public decimal? Amount { get; set; }

    public byte? DueDay { get; set; }

    public string? CalendarType { get; set; }

    public bool? Reminder { get; set; }

    public bool? AutoRepeat { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? CreatedAt { get; set; }

    public string? UpdatedBy { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public bool? Deleted { get; set; }

    public string? DeletedBy { get; set; }

    public DateTime? DeletedAt { get; set; }

    public virtual MainCommitmentType? MainCommitmentTypes { get; set; }
}
