﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("Order")]
public partial class Order
{
    [Key]
    public long Id { get; set; }

    [Column("Order_Date", TypeName = "datetime")]
    public DateTime? OrderDate { get; set; }

    public long? UserId { get; set; }

    [Column("totalPrice")]
    public double? TotalPrice { get; set; }

    public int? ProductId { get; set; }

    [StringLength(100)]
    public string? OrderId { get; set; }

    [Column("resellerRefNumber")]
    [StringLength(100)]
    public string? ResellerRefNumber { get; set; }

    [StringLength(50)]
    public string? ZoneId { get; set; }

    [StringLength(50)]
    public string? ServerId { get; set; }

    [StringLength(50)]
    public string? UserName { get; set; }

    [Column("appUserId")]
    [StringLength(50)]
    public string? AppUserId { get; set; }

    [StringLength(50)]
    public string? PackageId { get; set; }

    public string? Response { get; set; }

    [StringLength(20)]
    public string? OrderStatus { get; set; }

    public int? Quantiy { get; set; }

    public string? Codes { get; set; }

    [StringLength(50)]
    public string OrderType { get; set; } = null!;

    [InverseProperty("Order")]
    public virtual ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();

    [ForeignKey("UserId")]
    [InverseProperty("Orders")]
    public virtual SystemUser? User { get; set; }
}
