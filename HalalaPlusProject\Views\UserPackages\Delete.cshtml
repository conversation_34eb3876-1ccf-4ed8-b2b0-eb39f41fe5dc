﻿@model HalalaPlusProject.Models.Package

@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Package</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Name)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Characters)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Characters)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Price)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Price)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Period)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Period)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.State)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.State)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.IsActive)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.IsActive)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.CreateOn)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CreateOn)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.MasterId)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.MasterId)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.PackageDays)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.PackageDays)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.EnName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.EnCharacters)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnCharacters)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.EnPeriod)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnPeriod)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
