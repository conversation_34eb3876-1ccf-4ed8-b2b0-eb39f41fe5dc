﻿@model IEnumerable<HalalaPlusProject.Models.BanksAccount>

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.BankName)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.ConnectionCode)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.AccountNumber)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.BankName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.ConnectionCode)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.AccountNumber)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.Id">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.Id">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.Id">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
