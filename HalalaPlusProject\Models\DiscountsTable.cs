﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class DiscountsTable
{
    [Key]
    public long Id { get; set; }

    [StringLength(250)]
    public string? DisCountName { get; set; }

    public double? Discount { get; set; }

    public string? Conditions { get; set; }

    public DateOnly? StartDate { get; set; }

    public DateOnly? EndDate { get; set; }

    public double? CashBack { get; set; }

    public long? UserId { get; set; }

    public bool? Deleted { get; set; }

    [Column("isActive")]
    public bool? IsActive { get; set; }

    [Column("orderstate")]
    [StringLength(50)]
    public string? Orderstate { get; set; }

    public bool? IsOrder { get; set; }

    [Column("orderdate", TypeName = "datetime")]
    public DateTime Orderdate { get; set; }

    [Column("masterId")]
    public long? MasterId { get; set; }

    public bool GrantPoints { get; set; }

    public bool GrantDiscount { get; set; }

    [StringLength(50)]
    public string? GrantType { get; set; }

    [Column("discImg")]
    [StringLength(250)]
    public string? DiscImg { get; set; }

    [StringLength(250)]
    public string? DiscountCode { get; set; }

    [StringLength(250)]
    public string? EnDiscountName { get; set; }

    public string? EnConditions { get; set; }

    [InverseProperty("Discount")]
    public virtual ICollection<CustomerDiscount> CustomerDiscounts { get; set; } = new List<CustomerDiscount>();

    [InverseProperty("DiscountNavigation")]
    public virtual ICollection<GrantedDiscount> GrantedDiscounts { get; set; } = new List<GrantedDiscount>();

    [ForeignKey("MasterId")]
    [InverseProperty("DiscountsTableMasters")]
    public virtual SystemUser? Master { get; set; }

    [InverseProperty("Discount")]
    public virtual ICollection<PointsTable> PointsTables { get; set; } = new List<PointsTable>();

    [ForeignKey("UserId")]
    [InverseProperty("DiscountsTableUsers")]
    public virtual SystemUser? User { get; set; }
}
