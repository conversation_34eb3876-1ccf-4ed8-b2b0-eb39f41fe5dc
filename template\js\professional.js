/**
 * HalalaPlus Professional Template - JavaScript
 * Ultra Modern & Responsive Dashboard
 */

class ProfessionalDashboard {
    constructor() {
        this.sidebar = document.getElementById('sidebar');
        this.header = document.getElementById('header');
        this.mainContent = document.getElementById('mainContent');
        this.mobileOverlay = document.getElementById('mobileOverlay');
        this.langToggle = document.getElementById('langToggle');
        
        this.isMobile = window.innerWidth <= 768;
        this.isRTL = document.documentElement.getAttribute('dir') === 'rtl';
        
        this.init();
    }
    
    init() {
        this.initializeComponents();
        this.bindEvents();
        this.handleResize();
        this.initializeAnimations();
        this.initializeNotifications();
        
        // Welcome message
        setTimeout(() => {
            this.showNotification('success', 'مرحباً بك!', 'تم تحميل النظام بنجاح');
        }, 1000);
    }
    
    initializeComponents() {
        // Initialize AOS animations
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                offset: 100,
                disable: 'mobile'
            });
        }
        
        // Initialize tooltips
        this.initializeTooltips();
        
        // Initialize search functionality
        this.initializeSearch();
        
        // Initialize theme
        this.initializeTheme();
    }
    
    initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[title]');
        tooltipElements.forEach(element => {
            if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                new bootstrap.Tooltip(element);
            }
        });
    }
    
    initializeSearch() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            let searchTimeout;
            
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                const query = e.target.value.trim();
                
                if (query.length > 2) {
                    searchTimeout = setTimeout(() => {
                        this.performSearch(query);
                    }, 300);
                }
            });
            
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch(e.target.value.trim());
                }
            });
        }
    }
    
    performSearch(query) {
        console.log('البحث عن:', query);
        this.showNotification('info', 'البحث', `جاري البحث عن: ${query}`);
        
        // Simulate search API call
        setTimeout(() => {
            this.showNotification('success', 'البحث', `تم العثور على نتائج للبحث: ${query}`);
        }, 1000);
    }
    
    initializeTheme() {
        // Check for saved theme preference
        const savedTheme = localStorage.getItem('dashboard-theme');
        if (savedTheme) {
            document.body.setAttribute('data-theme', savedTheme);
        }
    }
    
    initializeAnimations() {
        // Add smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Add loading states to buttons
        document.querySelectorAll('.btn-primary-professional, .btn-outline-professional').forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (btn.getAttribute('href') === '#') {
                    e.preventDefault();
                    this.simulateLoading(btn);
                }
            });
        });
    }
    
    initializeNotifications() {
        // Configure Toastr
        if (typeof toastr !== 'undefined') {
            toastr.options = {
                "closeButton": true,
                "debug": false,
                "newestOnTop": true,
                "progressBar": true,
                "positionClass": this.isRTL ? "toast-top-right" : "toast-top-left",
                "preventDuplicates": true,
                "onclick": null,
                "showDuration": "300",
                "hideDuration": "1000",
                "timeOut": "4000",
                "extendedTimeOut": "1000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut",
                "rtl": this.isRTL
            };
        }
    }
    
    bindEvents() {
        // Sidebar toggle
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }
        
        // Mobile menu toggle
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        if (mobileToggle) {
            mobileToggle.addEventListener('click', () => this.toggleMobileSidebar());
        }
        
        // Mobile overlay
        if (this.mobileOverlay) {
            this.mobileOverlay.addEventListener('click', () => this.closeMobileSidebar());
        }
        
        // Language toggle
        if (this.langToggle) {
            this.langToggle.addEventListener('click', () => this.toggleLanguage());
        }
        
        // Window resize
        window.addEventListener('resize', () => this.handleResize());
        
        // Navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => this.handleNavigation(e));
        });
        
        // User dropdown
        const userTrigger = document.querySelector('.user-trigger');
        if (userTrigger) {
            userTrigger.addEventListener('click', () => this.toggleUserDropdown());
        }
        
        // Header actions
        document.querySelectorAll('.header-action').forEach(action => {
            action.addEventListener('click', (e) => this.handleHeaderAction(e));
        });
        
        // Escape key handling
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeMobileSidebar();
                this.closeUserDropdown();
            }
        });
    }
    
    toggleSidebar() {
        this.sidebar.classList.toggle('collapsed');
        this.header.classList.toggle('sidebar-collapsed');
        this.mainContent.classList.toggle('sidebar-collapsed');
        
        // Save state
        const isCollapsed = this.sidebar.classList.contains('collapsed');
        localStorage.setItem('sidebar-collapsed', isCollapsed);
        
        // Refresh AOS animations
        if (typeof AOS !== 'undefined') {
            setTimeout(() => AOS.refresh(), 300);
        }
    }
    
    toggleMobileSidebar() {
        this.sidebar.classList.toggle('mobile-open');
        this.mobileOverlay.classList.toggle('active');
        document.body.style.overflow = this.sidebar.classList.contains('mobile-open') ? 'hidden' : '';
    }
    
    closeMobileSidebar() {
        this.sidebar.classList.remove('mobile-open');
        this.mobileOverlay.classList.remove('active');
        document.body.style.overflow = '';
    }
    
    toggleLanguage() {
        const html = document.documentElement;
        const currentDir = html.getAttribute('dir');
        const currentLang = html.getAttribute('lang');
        
        if (currentDir === 'rtl') {
            // Switch to LTR (English)
            html.setAttribute('dir', 'ltr');
            html.setAttribute('lang', 'en');
            this.langToggle.textContent = 'AR';
            this.updateContentForLTR();
        } else {
            // Switch to RTL (Arabic)
            html.setAttribute('dir', 'rtl');
            html.setAttribute('lang', 'ar');
            this.langToggle.textContent = 'EN';
            this.updateContentForRTL();
        }
        
        this.isRTL = html.getAttribute('dir') === 'rtl';
        
        // Update toastr position
        if (typeof toastr !== 'undefined') {
            toastr.options.positionClass = this.isRTL ? "toast-top-right" : "toast-top-left";
            toastr.options.rtl = this.isRTL;
        }
        
        // Save language preference
        localStorage.setItem('dashboard-language', currentDir === 'rtl' ? 'en' : 'ar');
        
        this.showNotification('success', 'Language Changed', 'تم تغيير اللغة بنجاح');
    }
    
    updateContentForLTR() {
        // Update navigation
        const navTexts = {
            'لوحة التحكم': 'Dashboard',
            'المستخدمون': 'Users',
            'المنتجات': 'Products',
            'الطلبات': 'Orders',
            'التقارير': 'Reports',
            'الإعدادات': 'Settings'
        };
        
        document.querySelectorAll('.nav-text').forEach(text => {
            const arabicText = text.textContent.trim();
            if (navTexts[arabicText]) {
                text.textContent = navTexts[arabicText];
            }
        });
        
        // Update page title and breadcrumb
        const pageTitle = document.querySelector('.page-title');
        if (pageTitle) pageTitle.textContent = 'Main Dashboard';
        
        const breadcrumbHome = document.querySelector('.breadcrumb-item a');
        if (breadcrumbHome) breadcrumbHome.textContent = 'Home';
        
        const breadcrumbActive = document.querySelector('.breadcrumb-item.active');
        if (breadcrumbActive) breadcrumbActive.textContent = 'Dashboard';
        
        // Update search placeholder
        const searchInput = document.querySelector('.search-input');
        if (searchInput) searchInput.placeholder = 'Search in system...';
    }
    
    updateContentForRTL() {
        // Update navigation
        const navTexts = {
            'Dashboard': 'لوحة التحكم',
            'Users': 'المستخدمون',
            'Products': 'المنتجات',
            'Orders': 'الطلبات',
            'Reports': 'التقارير',
            'Settings': 'الإعدادات'
        };
        
        document.querySelectorAll('.nav-text').forEach(text => {
            const englishText = text.textContent.trim();
            if (navTexts[englishText]) {
                text.textContent = navTexts[englishText];
            }
        });
        
        // Update page title and breadcrumb
        const pageTitle = document.querySelector('.page-title');
        if (pageTitle) pageTitle.textContent = 'لوحة التحكم الرئيسية';
        
        const breadcrumbHome = document.querySelector('.breadcrumb-item a');
        if (breadcrumbHome) breadcrumbHome.textContent = 'الرئيسية';
        
        const breadcrumbActive = document.querySelector('.breadcrumb-item.active');
        if (breadcrumbActive) breadcrumbActive.textContent = 'لوحة التحكم';
        
        // Update search placeholder
        const searchInput = document.querySelector('.search-input');
        if (searchInput) searchInput.placeholder = 'البحث في النظام...';
    }
    
    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;
        
        if (wasMobile !== this.isMobile) {
            if (!this.isMobile) {
                // Desktop mode
                this.closeMobileSidebar();
            }
        }
        
        // Refresh AOS on resize
        if (typeof AOS !== 'undefined') {
            AOS.refresh();
        }
    }
    
    handleNavigation(e) {
        // Remove active class from all nav links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        // Add active class to clicked link
        e.currentTarget.classList.add('active');
        
        // Close mobile sidebar if open
        if (this.isMobile) {
            this.closeMobileSidebar();
        }
        
        // Prevent default if href is #
        if (e.currentTarget.getAttribute('href') === '#') {
            e.preventDefault();
            this.showNotification('info', 'Navigation', `تم النقر على: ${e.currentTarget.querySelector('.nav-text').textContent}`);
        }
    }
    
    handleHeaderAction(e) {
        const action = e.currentTarget;
        const icon = action.querySelector('i').textContent;
        
        let message = '';
        switch (icon) {
            case 'notifications':
                message = 'عرض الإشعارات';
                break;
            case 'mail':
                message = 'عرض الرسائل';
                break;
            default:
                message = 'إجراء الهيدر';
        }
        
        this.showNotification('info', 'Header Action', message);
    }
    
    toggleUserDropdown() {
        // Placeholder for user dropdown functionality
        this.showNotification('info', 'User Menu', 'قائمة المستخدم');
    }
    
    closeUserDropdown() {
        // Placeholder for closing user dropdown
    }
    
    simulateLoading(button) {
        const originalText = button.innerHTML;
        const originalClass = button.className;
        
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
        button.disabled = true;
        button.style.opacity = '0.7';
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
            button.style.opacity = '1';
            button.className = originalClass;
            
            this.showNotification('success', 'تم!', 'تم تنفيذ العملية بنجاح');
        }, 2000);
    }
    
    showNotification(type, title, message) {
        if (typeof toastr !== 'undefined') {
            toastr[type](message, title);
        } else if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info',
                title: title,
                text: message,
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: this.isRTL ? 'top-end' : 'top-start'
            });
        } else {
            console.log(`${type.toUpperCase()}: ${title} - ${message}`);
        }
    }
    
    // Utility methods
    formatNumber(number) {
        return new Intl.NumberFormat(this.isRTL ? 'ar-SA' : 'en-US').format(number);
    }
    
    formatCurrency(amount, currency = 'SAR') {
        return new Intl.NumberFormat(this.isRTL ? 'ar-SA' : 'en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    }
    
    formatDate(date) {
        return new Intl.DateTimeFormat(this.isRTL ? 'ar-SA' : 'en-US').format(new Date(date));
    }
    
    // Theme methods
    setTheme(theme) {
        document.body.setAttribute('data-theme', theme);
        localStorage.setItem('dashboard-theme', theme);
    }
    
    getTheme() {
        return document.body.getAttribute('data-theme') || 'light';
    }
    
    // Loading overlay
    showLoading() {
        const overlay = document.createElement('div');
        overlay.id = 'loading-overlay';
        overlay.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 2rem; border-radius: 1rem; text-align: center;">
                    <i class="fas fa-spinner fa-spin fa-2x mb-3" style="color: var(--primary-color);"></i>
                    <div>جاري التحميل...</div>
                </div>
            </div>
        `;
        document.body.appendChild(overlay);
    }
    
    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.dashboard = new ProfessionalDashboard();
});

// Export for global access
window.ProfessionalDashboard = ProfessionalDashboard;
