﻿@model HalalaPlusProject.CModels.UserEmployeeModel

@{
    ViewData["Title"] = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>تعديل</h1>

<h4>مسوق</h4>
<hr />
<div class="row">
    <div class="col-md-8">
        <form asp-action="Edit" class="submitfm">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
             
         <div class="row">
           <div class="col-md-5">
          <div class="form-group">
                <label asp-for="Name" class="control-label">الأسم</label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" required class="text-danger"></span>
            </div>
            
            <div class="form-group">
                <label asp-for="Nationality" class="control-label">الجنسية</label>
                 <select asp-for="Nationality" required  class="form-select" asp-items="ViewBag.National"></select>
                  <span asp-validation-for="Nationality" required class="text-danger"></span>
            </div>
            @* <div class="form-group">
                   <label asp-for="Nationality" class="control-label">الدولة</label>
                    
                <select asp-for="Nationality" s class="form-select" >
                    @foreach(var item in ViewBag.National){
                                @if (@item.Value == Model.NationalityNo.ToString())
                                {
                                    <option value="@item.Value" selected>@item.Text </option>
                                }
                                else { <option value="@item.Value" >@item.Text </option>
                                }
                            }
                </select>
            <span asp-validation-for="Nationality" class="text-danger"></span>
               
            </div>*@
              <div class="form-group">
                <label asp-for="IdentityNo" class="control-label">رقم الهوية</label>
                <input asp-for="IdentityNo" class="form-control" />
                <span asp-validation-for="IdentityNo" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="BirthDate" class="control-label">تاريخ الميلاد</label>
                <input asp-for="BirthDate" type="date" class="form-control" />
                <span asp-validation-for="BirthDate" class="text-danger"></span>
            </div>
     <div class="form-group">
                <label asp-for="Salary" class="control-label">الراتب </label>
                <input asp-for="Salary" placeholder="5000" class="form-control" />
                <span asp-validation-for="Salary" class="text-danger"></span>
            </div>

          </div>

           <div class="col-md-5">
         
         <div class="form-group">
                <label asp-for="PhoneNo" class="control-label">رقم الجوال</label>
                <input asp-for="PhoneNo" class="form-control" />
                <span asp-validation-for="PhoneNo" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Email" class="control-label">البريد الالكتروني</label>
                <input asp-for="Email" readonly class="form-control" />
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>
              <div class="form-group">
                <label asp-for="UserName" class="control-label">اسم المستخدم</label>
                <input asp-for="UserName" readonly class="form-control" />
                <span asp-validation-for="UserName" class="text-danger"></span>
            </div>
              
            

          </div>
             </div>
 
        
         
        
         
     
           
            <div class="form-group">
                <button type="submit" value="Save" class="btn btn-primary" >حـفــظ</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Employees">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
