﻿@model HalalaPlusProject.Entities.CreateEventModel
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["addnew"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div>
    <a href="~/ServiceProvider/index"><img src="../assets/img/svgs/solid/arrow-right.svg" style="width: 40px;" alt=""></a>
    <h3> الفعاليات</h3>

</div>
<div class="row">
    <div class="col-md-12">
        <form asp-action="Create" enctype="multipart/form-data" id="ProvidersFrm">
            <div class="row">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            </div>
            <div class="row">
                <div class="col-md-3">
                  
                    <div class="form-group">
                        <label asp-for="Name" class="control-label">اسم الفعالية عربي <span style="font-size:20pt; color: red;">&nbsp*</span></label>
                        <input asp-for="Name" required class="form-control" />
                        <span asp-validation-for="Name" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="EnName" class="control-label">اسم الفعالية انجليزي <span style="font-size:20pt; color: red;">&nbsp*</span></label>
                        <input asp-for="EnName" required required class="form-control" />
                        <span asp-validation-for="EnName" class="text-danger"></span>
                    </div>




                 
                   

                    <div class="form-group">
                        <label asp-for="City" class="control-label">@localizer["thecity"]</label>
                        <select asp-for="City" class="form-select" asp-items="ViewBag.City"></select>
                        <span asp-validation-for="City" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="PhoneNumber" class="control-label">@localizer["phoneno"]<span style="font-size:20pt; color: red;">&nbsp*</span></label>
                        <input asp-for="PhoneNumber" required class="form-control" />
                        <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Password" class="control-label">@localizer["password"]<span style="font-size:20pt; color: red;">&nbsp*</span></label>
                        <input asp-for="Password" required class="form-control" />
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Logo" class="control-label">@localizer["logo"]<span style="font-size:20pt; color: red;">&nbsp*</span> </label>
                        <input asp-for="Logo" accept="image/*" required class="form-control" />
                        <span asp-validation-for="Logo" class="text-danger"></span>
                    </div>



                </div>


                <div class="col-md-3">
                  
                 
              

                  @*   <div class="form-group">
                        <label asp-for="ContractDate" class="control-label">@localizer["contractstartdate"]</label>
                        <input asp-for="ContractDate" class="form-control" />
                        <span asp-validation-for="ContractDate" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ContractEndDate " class="control-label">@localizer["contractenddate"]</label>
                        <input asp-for="ContractEndDate" class="form-control" />
                        <span asp-validation-for="ContractEndDate" class="text-danger"></span>
                    </div> *@
                    <div class="form-group">
                        <label asp-for="Files" class="control-label">@localizer["attatchments"]</label>
                        <input asp-for="Files" multiple class="form-control" />
                        <span asp-validation-for="Files" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Images" class="control-label">@localizer["images"]</label>
                        <input asp-for="Images" accept="image/*" multiple class="form-control" />
                        <span asp-validation-for="Images" class="text-danger"></span>
                    </div>

                  
                    <div class="form-group">
                        <label asp-for="StoreLink" class="control-label">@localizer["websiteurl"]</label>
                        <input asp-for="StoreLink" class="form-control" />
                        <span asp-validation-for="StoreLink" class="text-danger"></span>
                    </div>
                    @*
                    <div class="form-group">
                    <label asp-for="Email" class="control-label">@localizer["email"]</label>
                    <input asp-for="Email" required class="form-control" />
                    <span asp-validation-for="Email" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                    <label asp-for="UserName" class="control-label">@localizer["username"]</label>
                    <input asp-for="UserName" required class="form-control" />
                    <span asp-validation-for="UserName" class="text-danger"></span>
                    </div> *@

                </div>

                <div class="col-md-6">
                    


                    <div class="form-group">
                        <label asp-for="Lat" class="control-label">@localizer["lat"]<span style="font-size:20pt; color: red;">&nbsp*</span></label>
                        <input asp-for="Lat" required class="form-control" />
                        <span asp-validation-for="Lat" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="lng" class="control-label">@localizer["lng"]<span style="font-size:20pt; color: red;">&nbsp*</span></label>
                        <input asp-for="lng" required class="form-control mb-3" />
                        <span asp-validation-for="lng" class="text-danger"></span>
                    </div>
                    <div style="height: 400px; max-width: 700px;" id="map">
                    </div>

                </div>


            </div>








            <div class="mt-2">
                <button type="submit" value="Create" class="btn btn-primary">@localizer["save"]</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>
@* <script src="https://polyfill.io/v3/polyfill.min.js?features=default"></script> *@
<script>
    (g => { var h, a, k, p = "The Google Maps JavaScript API", c = "google", l = "importLibrary", q = "__ib__", m = document, b = window; b = b[c] || (b[c] = {}); var d = b.maps || (b.maps = {}), r = new Set, e = new URLSearchParams, u = () => h || (h = new Promise(async (f, n) => { await (a = m.createElement("script")); e.set("libraries", [...r] + ""); for (k in g) e.set(k.replace(/[A-Z]/g, t => "_" + t[0].toLowerCase()), g[k]); e.set("callback", c + ".maps." + q); a.src = `https://maps.${c}apis.com/maps/api/js?` + e; d[q] = f; a.onerror = () => h = n(Error(p + " could not load.")); a.nonce = m.querySelector("script[nonce]")?.nonce || ""; m.head.append(a) })); d[l] ? console.warn(p + " only loads once. Ignoring:", g) : d[l] = (f, ...n) => r.add(f) && u().then(() => d[l](f, ...n)) })
        ({ key: "AIzaSyBRDq842x31n-rAmjPQ7hZotsZaGvbjI_U", v: "weekly" });</script>

@* <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBRDq842x31n-rAmjPQ7hZotsZaGvbjI_U&language=ar&callback=initMap&libraries=&v=weekly" async></script>
 *@
@* <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyB7hT8IIEb4bAxpdczoZqy_Ld_IBofuZwo&language=ar&callback=initMap&libraries=&v=weekly"async></script> *@
@*
    <script>  function initMap() {
        const infowindow = new google.maps.InfoWindow();
        infowindow.setContent('اختار الموقع الذي تريد توصيل الطلب الية');
        const myLatlng = {
            lat: 24.623265,
            lng: 46.5399469
        };
        const map = new google.maps.Map(document.getElementById("map"), {
            zoom: 11,
            center: myLatlng,
        });
        marker = new google.maps.Marker({
            position: myLatlng,
            map: map,
            title: 'الموقع'
        });
        infowindow.open(map, marker);
        map.addListener("click", (mapsMouseEvent) => {
            // console.log(mapsMouseEvent.latLng.lat());
            var result = {
                lat: mapsMouseEvent.latLng.lat(),
                lng: mapsMouseEvent.latLng.lng()
            };
            $('.title').empty();
            $('input[name="lat"]').val(mapsMouseEvent.latLng.lat());
            $('input[name="lng"]').val(mapsMouseEvent.latLng.lng());
            $('input[name="address"]').val('العنوان');
            marker.setPosition(result);

        });

    }
    </script> *@
@* <script src="https://polyfill.io/v3/polyfill.min.js?features=default"></script> *@
@*
    <script> async function initMap() {

  // Request needed libraries.
  const { Map } = await google.maps.importLibrary("maps");
  const myLatlng = { lat:  20.99265387585728, lng:40.54420880475375};

  const map = new google.maps.Map(document.getElementById("map"), {
    zoom: 6,
    center: myLatlng,
  });

        // Create the initial InfoWindow.
  let infoWindow = new google.maps.InfoWindow({
    // content: "Click the map to get Lat/Lng!",
    position: myLatlng,
    name:'KSA'
  });
  var marker = new google.maps.Marker({
                   position: myLatlng,
                   map: map,
                   title: 'Halala Plus'
               });

        infoWindow.open(map, marker);
  // Configure the click listener.
  map.addListener("click", (mapsMouseEvent) => {
      document.getElementById('Lat').value =mapsMouseEvent.latLng.lat();
            document.getElementById('lng').value = mapsMouseEvent.latLng.lng();
    // console.log("clicked:",myLatlng);
    // Close the current InfoWindow.
            // var result = {
            //     lat: mapsMouseEvent.latLng.lat(),
            //     lng: mapsMouseEvent.latLng.lng()
            // };

            infoWindow.close();

    infoWindow.open(map);
  });
}

initMap();
  </script> *@

<script>
    async function initMap() {
        // Request needed libraries.
        const { Map } = await google.maps.importLibrary("maps");
        const myLatlng = { lat: 20.99265387585728, lng: 40.54420880475375 };
        const map = new google.maps.Map(document.getElementById("map"), {
            zoom: 6,
            center: myLatlng,
        });
        // Create the initial InfoWindow.
        let infoWindow = new google.maps.InfoWindow({
            content: "Click the map to get Lat/Lng!",
            position: myLatlng,
            name: 'KSA'
        });
        var marker = new google.maps.Marker({
            position: myLatlng,
            map: map,
            title: 'Halala'
        });
        infoWindow.open(map, marker);
        // Configure the click listener.
        map.addListener("click", (mapsMouseEvent) => {
            $('input[name="Lat"]').val(mapsMouseEvent.latLng.lat());
            $('input[name="lng"]').val(mapsMouseEvent.latLng.lng());
            var myLatlng1 = { lat: mapsMouseEvent.latLng.lat(), lng: mapsMouseEvent.latLng.lng() };
            marker = new google.maps.Marker({
                position: myLatlng1,
                map: map,
                title: 'Halala'
            });
            console.log("clicked:", myLatlng);
            // Close the current InfoWindow.
            infoWindow.close();
            // Create a new InfoWindow.
            infoWindow = new google.maps.InfoWindow({
                position: mapsMouseEvent.latLng,
            });
            infoWindow.setContent(
                JSON.stringify(mapsMouseEvent.latLng.toJSON(), null, 2),
            );
            infoWindow.open(map);
        });
    }

    initMap();
</script>
