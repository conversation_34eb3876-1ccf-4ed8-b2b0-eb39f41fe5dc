﻿@model IEnumerable<HalalaPlusProject.Models.CitiesTable>
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["citieslist"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["citieslist"]</h1>

<p>
    <a asp-action="Create">@localizer["addcity"]</a>
</p>
<table id="tbl1" class="table">
    <thead>
        <tr>
            <th>
                @localizer["thecity"]
            </th> <th>
                @localizer["thecity"]
            </th>
            <th>
                @localizer["thecountry"]
            </th>
            <th>
               
            </th>
            <th>@localizer["options"]</th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.City)
            </td> <td>
                @Html.DisplayFor(modelItem => item.EnCity)
            </td>
            <td>
               @Html.DisplayFor(modelItem => item.CIdNavigation.Country)
            </td>
            <td>
                
            </td>
            <td>
                    <a asp-action="Edit" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id">@localizer["edit"]</a> |
                    <a asp-action="Details" class="btn btn-outline-info tablebtn" asp-route-id="@item.Id">@localizer["details"]</a> |
                    <a asp-action="Delete" class="btn btn-outline-danger tablebtn" asp-route-id="@item.Id">@localizer["delete"]</a>
            </td>
        </tr>
}
    </tbody>
</table>
@section Scripts{
    <script>
  let table = new DataTable('#tbl1');
 
    </script>
}