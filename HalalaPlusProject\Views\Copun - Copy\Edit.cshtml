﻿@model HalalaPlusProject.CModels.CoupunModel

@{
    ViewData["Title"] = "تعديل عرض ";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div>
    <a href="~/SpecialOffers/Index"><img src="../assets/img/svgs/solid/arrow-right.svg" style="width: 40px;" alt=""></a>
    <h3>اكواد الخصم </h3>

</div>
<div class="row">
    <div class="col-md-12">
        <form asp-action="Edit" enctype="multipart/form-data" class="submitfm" >
            <div class="row">
                  <input type="hidden" asp-for="Id" />
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label asp-for="Name" class="control-label"></label>
                        <input asp-for="Name" class="form-control" />
                        <span asp-validation-for="Name"  class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="StartDate" class="control-label"></label>
                        <input asp-for="StartDate"  class="form-control" />
                        <span asp-validation-for="StartDate" class="text-danger"></span>
                    </div>


                     <div class="form-group">
                        <label asp-for="EndDate" class="control-label"></label>
                        <input asp-for="EndDate"  class="form-control" />
                        <span asp-validation-for="EndDate" class="text-danger"></span>
                    </div>

            
          </div>

          
          <div class="col-md-3">
               <div class="form-group">
                        <label asp-for="StoreLink" class="control-label"></label>
                        <input asp-for="StoreLink"  class="form-control" />
                        <span asp-validation-for="StoreLink" class="text-danger"></span>
                    </div>
                      <div class="form-group">
                        <label asp-for="OverView" class="control-label"></label>
                        <textarea asp-for="OverView"  class="form-control" ></textarea>
                        <span asp-validation-for="OverView" class="text-danger"></span>
                    </div>
       
          
           
                    <div class="form-group">
                        <label asp-for="Img" class="control-label"></label>
                        <input asp-for="Img" required class="form-control" />
                        <span asp-validation-for="Img" class="text-danger"></span>
                    </div>
        
            </div>    
            <div class="col-md-3">
               <div class="form-group">
                        <label asp-for="CoupunCode" class="control-label"></label>
                        <input asp-for="CoupunCode"  class="form-control" />
                        <span asp-validation-for="CoupunCode" class="text-danger"></span>
                    </div>
                   
        
            </div>
            </div>
           
            <div class="mt-2">
                <button type="submit" value="Create" class="btn btn-primary"  >حفظ</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">الرجوع الى القائمة</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
