﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.HalalaClass;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Metadata.Internal;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة طلبات بطاقات العضوية، مثل عرض المستخدمين وإعادة إرسال رسائل التفعيل لهم.
    /// </summary>
    [Authorize]
    public class CardOrderController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IConfiguration _configuration;
        public CardOrderController(HalalaPlusdbContext context, IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
        }

        /// <summary>
        /// عرض قائمة بطلبات البطاقات للمستخدمين الذين لم يكملوا عملية التسجيل.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة بطلبات البطاقات.</returns>
        public IActionResult Index()
        {
            var temp = _context.AspNetUsers.Where(o => o.RegisterProgress == "-1").Select(e => new CardOrders { Name = e.FullName, MemberNo = e.MemberNo, PhoneNo = e.PhoneNumber, Id = e.Id }).ToList();
            return View(temp);
        }

        /// <summary>
        /// إعادة إرسال رسالة نصية تحتوي على رقم العضوية إلى مستخدم معين.
        /// </summary>
        /// <param name="model">البيانات التي تحتوي على معرف المستخدم لإعادة إرسال الرسالة إليه.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل عملية الإرسال.</returns>
        public IActionResult SendMessage(CardOrdersResend model)
        {

            var temp = _context.AspNetUsers.Where(o => o.RegisterProgress == "-1" && o.Id == model.Id).FirstOrDefault();
            if (temp?.Id == null) { return Ok(new { state = 0, message = "خطاء لم يتم ارسال الرسالة" }); }
            var key = _configuration["SmsToken"];
            new SMSHandler().SendHelloSMS(key, temp.PhoneNumber, string.Format(" شكراَ لإنضمامك لعالم هللة بلس رقم عضويتك هو {0} توفيرك يبدأ بهللة... تصفح خصومات هللة في الموقع الالكتروني(https://www.halalaplus.com) ولا تنسى ابراز رقم عضويتك عند التاجر لكي تحصل على الخصم المدون", temp.MemberNo), temp.MemberNo, -1);
            return Ok(new { state = 1, message = "تم ارسال الرسالة" });
        }
    }
}