﻿using System;
using System.Collections.Generic;

namespace HalalaPlusProject.Models
{
    public partial class UserMonyBoxsChargeGiftPoint
    {
        public int Id { get; set; }
        public long? UserId { get; set; }
        public long? MonyBoxId { get; set; }
        public DateTime? CreateAt { get; set; }
        public double? Amount { get; set; }
        public int? PointsId { get; set; }
        public long IsReplaced { get; set; }
        public long? ProviderId { get; set; }
        public int? Points { get; set; }

        public virtual UsersMonyBox? MonyBox { get; set; }
        public virtual MonyBoxsPoint? PointsNavigation { get; set; }
        public virtual SystemUser? User { get; set; }
    }
}
