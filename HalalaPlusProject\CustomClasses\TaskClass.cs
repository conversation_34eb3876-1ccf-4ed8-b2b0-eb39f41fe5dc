﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Models;
using System.Threading.Tasks;

namespace HalalaPlusProject.CustomClasses
{
    public class TaskClass
    {

        public  List<Tasks> retrive( HalalaPlusdbContext _context)
        {
            try
            {
                return (from task in _context.TaskesTables
                        //where task.d != true
                        select new CModels.Tasks
                        {
                            TaskName = task.TaskName,
                            StartDate =DateTime.Parse( task.StartDate.ToString()),
                            Id = task.Id,
                            EndDate = DateTime.Parse(task.EndDate.ToString()),
                            Employee = task.Asp.FullName,
                            Notes = task.Notes,
                            EnTaskDescribe = task.EnTaskDescribe,
                            EnTaskName= task.EnTaskName,
                            TaskDescribe = task.TaskDescribe,
                            status=(task.Complated==true) ?"أكتملت":"لم تكتمل"
                        }).OrderByDescending(p => p.Id).ToList();
            }
            catch (Exception)
            {
                return new List<Tasks>();
            }
        }
        public List<Tasks> retrive(HalalaPlusdbContext _context,string userId)
        {
            try
            {
                return (from task in _context.TaskesTables
                        where/* task.Deleted != true &&*/ task.AspId==userId || task.MasterId==userId
                        select new CModels.Tasks
                        {
                            TaskName = task.TaskName,
                            StartDate =DateTime.Parse( task.StartDate.ToString()),
                            Id = task.Id,
                            EnTaskDescribe = task.EnTaskDescribe,
                            EnTaskName = task.EnTaskName,
                            EndDate = DateTime.Parse(task.EndDate.ToString()),
                            Employee = task.Asp.FullName,
                            Notes = task.Notes,
                            TaskDescribe = task.TaskDescribe,
                            status = (task.Complated == true) ? "أكتملت" : "لم تكتمل"
                        }).OrderByDescending(p => p.Id).ToList();
            }
            catch (Exception)
            {
                return new List<Tasks>();
            }
        }
        public TasksDetails retriveDetails(long id,HalalaPlusdbContext _context)
        {
            try
            {
                return (from task in _context.TaskesTables
                        where /*task.Deleted != true &&*/ task.Id == id
                        select new CModels.TasksDetails
                        {
                            TaskName = task.TaskName,
                            StartDate = DateTime.Parse(task.StartDate.ToString()),
                            Id = task.Id,
                            EnTaskDescribe = task.EnTaskDescribe,
                            EnTaskName = task.EnTaskName,
                            EndDate = DateTime.Parse(task.EndDate.ToString()),
                            Employee = task.Asp.FullName,
                            Notes = task.Notes,
                            TaskDescribe = task.TaskDescribe,
                             status = (task.Complated == true) ? "أكتملت" : "لم تكتمل",
                            //ConractDate = task.ConractDate,

                            Files = task.FilesTables.Select(p => p.FileLink).ToList()
                        }).FirstOrDefault();
            }
            catch (Exception)
            {
                return new TasksDetails();
            }
        }
        public async Task<bool> insert(CModels.TasksCreate model, HalalaPlusdbContext _context, IWebHostEnvironment _hosting, string userId,bool state)
        {
            try
            {
                    var ob = new Models.TaskesTable();
                    ob.TaskDescribe = model.TaskDescribe;
                    ob.TaskName = model.TaskName;
                    ob.AspId = model.Employee;
                    ob.MasterId = userId;
                    ob.Notes = model.Notes;
                ob.EnTaskName = model.EnTaskName;
                ob.EnTaskDescribe= model.EnTaskDescribe;
                    ob.StartDate = DateOnly.Parse(model.StartDate.ToString());
                //ob.ConractDate = model.ConractDate;
                ob.EndDate = DateOnly.Parse(model.StartDate.ToString());
                //ob.Deleted = false;
                _context.TaskesTables.Add(ob);
                    await _context.SaveChangesAsync();
                    if(model.Files!=null)
                    new FilesClass().insertOrdersFiles(model.Files, ob.Id, "Images", _hosting, _context, 1);
                    return true;              
            }
            catch (Exception)
            {
                return false;
            }
        }
        public async Task<bool> update(CModels.TasksCreate model, HalalaPlusdbContext _context, IWebHostEnvironment _hosting)
        {
            try
            {              
                    var ob = _context.TaskesTables.Find(model.Id);
                    if (ob == null /*|| ob.Deleted == true*/) return false;
                    ob.TaskDescribe = model.TaskDescribe;
                    ob.TaskName = model.TaskName;
                ob.EndDate = DateOnly.Parse(model.StartDate.ToString());
                ob.EnTaskName = model.EnTaskName;
                ob.EnTaskDescribe = model.EnTaskDescribe;
                //ob.ConractDate = model.ConractDate;
                ob.StartDate =DateOnly.Parse( model.StartDate.ToString());
                    ob.Notes = model.Notes;
                    ob.AspId = model.Employee;
                    _context.Update(ob);
                if (model.Files != null)
                    new FilesClass().insertOrdersFiles(model.Files, ob.Id, "Images", _hosting, _context, 1);
                await _context.SaveChangesAsync();
                    return true;                
            }
            catch (Exception)
            {
                return false;
            }
        }
        public async Task<bool> close(int Id, HalalaPlusdbContext _context)
        {
            try
            {
                var ob = _context.TaskesTables.Find(Id);
                if (ob == null /*|| ob.Deleted == true*/) return false;
                ob.Complated = true;
                _context.Update(ob);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

    }
}
