﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("Webhook")]
public partial class Webhook
{
    [Key]
    public int Id { get; set; }

    [StringLength(250)]
    public string SessionId { get; set; } = null!;

    [StringLength(100)]
    public string? CheckoutSessionId { get; set; }

    public string? Payload { get; set; }

    public DateTime Timestamp { get; set; }

    [ForeignKey("CheckoutSessionId")]
    [InverseProperty("Webhooks")]
    public virtual Checkout? CheckoutSession { get; set; }
}
