﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("AnonymousUserActivity")]
public partial class AnonymousUserActivity
{
    [Key]
    public int Id { get; set; }

    [StringLength(450)]
    public string SessionId { get; set; } = null!;

    [Column(TypeName = "datetime")]
    public DateTime LoginTime { get; set; }

    [StringLength(45)]
    public string? UserIpAddress { get; set; }
}
