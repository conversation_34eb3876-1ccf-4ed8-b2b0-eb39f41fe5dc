﻿@model HalalaPlusProject.Models.CitiesTable
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["delete"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["delete"]</h1>

<h3>@localizer["deletecitymsg"]</h3>
<div>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @localizer["thecity"]
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.City)
        </dd>
        <dt class = "col-sm-2">
            @localizer["thecity"]
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnCity)
        </dd>
        <dt class = "col-sm-2">
            @localizer["thecountry"]
        </dt>
         
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CIdNavigation.Id)
        </dd>
      
       
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-danger">@localizer["delete"]</button> |
        <a asp-action="Index">@localizer["backtolist"]</a>
    </form>
</div>
