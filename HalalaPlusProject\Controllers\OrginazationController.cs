﻿using HalalaPlusProject.Areas.Identity.Data;
using HalalaPlusProject.CModels;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Entities;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    /// <summary>
    /// إدارة المؤسسات، وتوفير عمليات الإنشاء والقراءة والتحديث والحذف (CRUD) لبياناتها.
    /// </summary>
    public class OrginazationController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly UserManager<HalalaPlusProjectUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IWebHostEnvironment _hosting;
        private readonly IEmailSender _emailSender;
        private readonly IUserStore<HalalaPlusProjectUser> _userStore;
        private readonly SignInManager<HalalaPlusProjectUser> _signInManager;
        private readonly IStringLocalizer<ServiceProviderController> _localization;
        //private readonly IUserEmailStore<HalalaPlusProjectUser> _emailStore;
        public OrginazationController(HalalaPlusdbContext context, IStringLocalizer<ServiceProviderController> _localization,/*IUserEmailStore<HalalaPlusProjectUser> emailStore,*/ IUserStore<HalalaPlusProjectUser> userStore, UserManager<HalalaPlusProjectUser> userManager, IWebHostEnvironment hosting,
              IEmailSender emailSender,
               SignInManager<HalalaPlusProjectUser> signInManager)
        {
            _context = context;
            _userManager = userManager;
            _hosting = hosting;
            _emailSender = emailSender;
            _signInManager = signInManager;
            _userStore = userStore;
            this._localization = _localization;
            //_emailStore = emailStore;
        }

        /// <summary>
        /// عرض قائمة بجميع المؤسسات.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة المؤسسات.</returns>
        public IActionResult Index()
        {
            var temp = new UsersClass().retrive("Orginazation", _context);
            return View(temp);
        }

        /// <summary>
        /// عرض نموذج إنشاء مؤسسة جديدة.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج الإنشاء.</returns>
        [HttpGet]
        public IActionResult Create()
        {
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name");
            ViewData["City"] = new SelectList(_context.CitiesTables.Where(p => p.CId == 1), "Id", "City");
            return View(new CreateOrginazationModel());
        }

        /// <summary>
        /// معالجة عملية إنشاء مؤسسة جديدة، بما في ذلك إنشاء حساب لها في نظام الهوية.
        /// </summary>
        /// <param name="model">البيانات الخاصة بالمؤسسة الجديدة.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        //[ValidateAntiForgeryToken]
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Entities.CreateOrginazationModel model)
        {
            if (ModelState.IsValid)
            {
                var userExists = await _userManager.FindByNameAsync(model.PhoneNumber);
                if (userExists != null) return Ok(new { state = 0, message = _localization["emailused"].Value });
                userExists = await _userManager.FindByNameAsync(model.PhoneNumber);
                if (userExists != null) return Ok(new { state = 0, message = _localization["usedusername"].Value });
                string url = HandleImages.SaveImage(model.Logo, "Images", _hosting);
                HalalaPlusProjectUser user = new()
                {
                    //Email = model.Email,
                    SecurityStamp = Guid.NewGuid().ToString(),
                    PhoneNumber = model.PhoneNumber,
                    Image = url,
                    UserName = model.PhoneNumber
                };
                var result = await _userManager.CreateAsync(user, model.Password);
                if (result.Succeeded)
                {
                    try
                    {
                        await _userManager.AddToRoleAsync(user, "Orginazation");
                        OrginazationClass ob = new OrginazationClass(_context, _hosting);
                        if (!ob.Insert(model, user, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value))
                        {
                            await _userManager.DeleteAsync(user);
                            return Ok(new { state = 0, message = _localization["errorwillsaving1"].Value });

                        }

                        user.EmailConfirmed = true;
                        await _userManager.UpdateAsync(user);

                        return Ok(new { state = 7, message = _localization["addedsuccessfuly"].Value, Url = "Index" });
                    }
                    catch (Exception ex)
                    {
                        await _userManager.DeleteAsync(user);
                        return Ok(new { state = 0, message = _localization["errorwillsaving1"].Value });

                    }
                }


            }

            return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });
        }

        /// <summary>
        /// تعطيل حساب مؤسسة معينة.
        /// </summary>
        /// <param name="id">معرف المؤسسة المراد تعطيلها.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        public async Task<IActionResult> Disable(long? id)
        {
            try
            {

                if (id == null) return Ok(new { state = 0, message = _localization["errorinoperation"].Value });

                if (await new CustomClasses.ServiceProvidersClass().disable(_context, id ?? 0)) return Ok(new { state = 1, message = _localization["opsuccess"].Value });
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }

        }

        /// <summary>
        /// تعطيل خصم معين مرتبط بالمؤسسة.
        /// </summary>
        /// <param name="id">معرف الخصم المراد تعطيله.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        public async Task<IActionResult> DisableDiscount(long? id)
        {
            try
            {

                if (id == null) return Ok(new { state = 0, message = _localization["selectoffer"].Value });

                if (await new CustomClasses.DiscountsClass().disable(_context, id ?? 0)) return Ok(new { state = 1, message = _localization["opsuccess"].Value });
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }
            catch (Exception ex)
            {
                return Ok(new { state = 0, message = _localization["errorinoperation"].Value });
            }

        }

        /// <summary>
        /// عرض التفاصيل الخاصة بمؤسسة معينة.
        /// </summary>
        /// <param name="id">معرف المؤسسة المراد عرض تفاصيلها.</param>
        /// <returns>عرض يحتوي على تفاصيل المؤسسة، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Details(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }
            var systemUser = (from user in _context.SystemUsers
                              where user.Id == id && user.AccountType == "Orginazation" && user.Deleted != true
                              select new Entities.OrginazationDetailsModel
                              {
                                  PhoneNumber = user.PhoneNo,
                                  Email = user.Email,
                                  Id = user.Id,
                                  Name = user.Name,
                                  BusnissNo = user.BusinessNo,
                                  ActivityName = _context.Activities.Where(p => p.Id == user.Activity).FirstOrDefault().Name,
                                  City = _context.CitiesTables.Where(p => p.Id == user.City).FirstOrDefault().City,
                                  EnterprisePhoneNo = user.EnterPrisePhoneNo,
                                  ServiceProviderRepresent = user.ServiceProviderRepresent,
                                  Locatin = user.Location,
                                  ContractNo = user.ContractNo,
                                  CashBack = user.CashBack,
                                  lng = user.Lng ?? "46.70213857938796",
                                  Lat = user.Lat ?? "24.67592860338076",
                                  UserName = user.Asp.UserName,
                                  ContractDate = DateTime.Parse(user.ContractDate.ToString()),
                                  ContractEndDate = DateTime.Parse(user.ContractEndDate.ToString()),
                                  Logo = "/Images/" + user.Logo,
                                  EnName = user.EnName,
                                  overview = user.OverView,
                                  OffersIcon = "/Images/" + user.OffesLogo,
                                  SecondColor = user.SecondColor,
                                  HasCustomBrand = user.HasCustomBrand,
                                  FirstColor = user.FirstColor,
                                  enoverview = user.EnOverView,
                                  bnifitfrompoints = user.Benefitfrompoints,
                                  enbnifitfrompoints = user.EnBenefitfrompoints,
                                  StoreLink = user.StoreLink

                              }).FirstOrDefault();
            if (systemUser == null)
            {
                return NotFound();
            }
            systemUser.Files = _context.FilesTables.Where(p => p.FileType == 1 && p.UserId == systemUser.Id).Select(i => new Files { Id = i.Id, Name = i.FileName, Link = "/Files/" + i.FileLink }).ToList();
            systemUser.images = _context.FilesTables.Where(p => p.FileType == 5 && p.UserId == systemUser.Id).Select(i => new Files { Id = i.Id, Name = i.FileName, Link = "/images/" + i.FileLink }).ToList();
            systemUser.Accounts = await new SocialAccount().retrive(systemUser.Id, _context);
            systemUser.Points = await new PointsClass().retrive(systemUser.Id, _context);//await new PointsClass().retrive(systemUser.Id, _context);
            systemUser.sales = await new PointsClass().retriveSales(systemUser.Id, _context);
            systemUser.DiscountsList = await new DiscountsClass().retrive(systemUser.Id, _context);
            return View(systemUser);
        }

        /// <summary>
        /// عرض نموذج تعديل بيانات مؤسسة حالية.
        /// </summary>
        /// <param name="id">معرف المؤسسة المراد تعديلها.</param>
        /// <returns>عرض يحتوي على بيانات المؤسسة في نموذج التعديل، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Edit(long? id)
        {
            if (id == null || _context.SystemUsers == null)
            {
                return NotFound();
            }
            var systemUser = (from user in _context.SystemUsers
                              where user.Id == id && user.AccountType == "Orginazation" && user.Deleted != true
                              select new Entities.OrginazationgetEdit
                              {
                                  PhoneNumber = user.PhoneNo,
                                  Email = user.Email,
                                  Id = user.Id,

                                  Name = user.Name,
                                  EnName = user.EnName,
                                  BusnissNo = user.BusinessNo,
                                  Activity = user.Activity,
                                  City = user.City,
                                  EnterprisePhoneNo = user.EnterPrisePhoneNo,
                                  ServiceProviderRepresent = user.ServiceProviderRepresent,
                                  Locatin = user.Location,
                                  ContractDate = user.ContractDate,
                                  ContractEndDate = user.ContractEndDate,
                                  LogoLink = "/images/" + user.Logo,
                                  UserName = user.Asp.UserName,
                                  FirstColor = user.FirstColor,
                                  SecondColor = user.SecondColor,
                                  OffersIconLink = "/images/" + user.OffesLogo,
                                  HasCustomBrand = user.HasCustomBrand,
                                  ContractNo = user.ContractNo,
                                  lng = user.Lng ?? "46.70213857938796",
                                  Lat = user.Lat ?? "24.67592860338076",
                                  overview = user.OverView,
                                  enoverview = user.EnOverView,
                                  bnifitfrompoints = user.Benefitfrompoints,
                                  enbnifitfrompoints = user.EnBenefitfrompoints,
                                  StoreLink = user.StoreLink
                              }).FirstOrDefault();
            if (systemUser == null)
            {
                return NotFound();
            }
            systemUser.FilesList = _context.FilesTables.Where(p => p.FileType == 1 && p.UserId == systemUser.Id).Select(i => new Files { Link = "/Files/" + i.FileLink, Name = i.FileName, Id = i.Id }).ToList();
            systemUser.images = _context.FilesTables.Where(p => p.FileType == 5 && p.UserId == systemUser.Id).Select(i => new Files { Link = "images" + i.FileLink, Name = i.FileName, Id = i.Id }).ToList();
            systemUser.Accounts = await new SocialAccount().retrive(systemUser.Id, _context);
            //systemUser.Points =await new PointsClass().retrive(systemUser.Id, _context);
            systemUser.Points = new Points();
            systemUser.Sales = await new PointsClass().retriveSales(systemUser.Id, _context);
            systemUser.allPoints = await new PointsClass().retrive(systemUser.Id, _context);
            systemUser.DiscountsList = await new DiscountsClass().retrive(systemUser.Id, _context);
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name", systemUser.Activity);
            ViewData["City"] = new SelectList(_context.CitiesTables.Where(p => p.CId == 1), "Id", "City", systemUser.City);
            return View(systemUser);
        }

        /// <summary>
        /// معالجة التعديلات المقدمة لبيانات مؤسسة وحفظها.
        /// </summary>
        /// <param name="model">البيانات المحدثة للمؤسسة.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(Entities.OrginazationEdit model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var ob = _context.SystemUsers.Find(model.Id);
                    if (ob != null)
                        if (_context.SystemUsers.Where(p => (p.PhoneNo == model.PhoneNumber || p.Asp.UserName == model.UserName) && p.Id != ob.Id).Count() > 0)
                            return Ok(new { state = 0, message = _localization["usedphone"].Value });
                    ob.Name = model.Name;
                    ob.ServiceProviderRepresent = model.ServiceProviderRepresent;
                    //ob.Location = model.Locatin;
                    ob.PhoneNo = model.PhoneNumber;
                    ob.EnterPrisePhoneNo = model.EnterprisePhoneNo;
                    ob.Activity = model.Activity;
                    ob.Lat = model.Lat;
                    ob.EnName = model.EnName;
                    ob.Lng = model.lng;
                    ob.ContractDate = DateOnly.Parse(model.ContractDate.ToString());
                    ob.ContractEndDate = DateOnly.Parse(model.ContractEndDate.ToString());
                    ob.BusinessNo = model.BusnissNo;
                    ob.StoreLink = model.StoreLink;
                    ob.OverView = model.overview;
                    ob.EnOverView = model.enoverview;
                    ob.Benefitfrompoints = model.bnifitfrompoints;
                    ob.EnBenefitfrompoints = model.enbnifitfrompoints;
                    ob.ContractNo = model.ContractNo;
                    ob.FirstColor = model.FirstColor;
                    ob.SecondColor = model.SecondColor;

                    ob.City = model.City;
                    if (model.Logo != null)
                    {
                        if (ob.Logo != null)
                            HandleImages.RemoveImageRoot(ob.Logo, "Images", _hosting);
                        ob.Logo = HandleImages.SaveImage(model.Logo, "Images", _hosting);
                    }
                    if (model.OffersIcon != null)
                    {
                        if (ob.OffesLogo != null)
                            HandleImages.RemoveImageRoot(ob.OffesLogo, "Images", _hosting);
                        ob.OffesLogo = HandleImages.SaveImage(model.OffersIcon, "Images", _hosting);
                    }

                    if (model.Files != null)
                    {
                        new FilesClass().insertOrdersFiles(model.Files, ob.Id, "Files", _hosting, _context, 1);
                    }
                    _context.Update(ob);
                    _context.SaveChanges();
                    var userExists = await _userManager.FindByNameAsync(model.PhoneNumber);
                    if (userExists != null)
                    {
                        userExists.PhoneNumber = model.PhoneNumber;
                        userExists.UserName = model.PhoneNumber;
                        await _userManager.UpdateAsync(userExists);
                        await _context.SaveChangesAsync();
                    }
                }
                catch (DbUpdateConcurrencyException)
                {
                    return Ok(new { state = 0, message = _localization["errorwillsaving1"].Value });
                }
                return Ok(new { state = 7, message = _localization["modefiedsuccessfuly"].Value, Url = "/ServiceProvider/Index" });
            }
            ViewData["Activity"] = new SelectList(_context.Activities, "Id", "Name");
            ViewData["City"] = new SelectList(_context.CitiesTables.Where(p => p.CId == 1), "Id", "City");
            return Ok(new { state = 0, message = _localization["validateallparamaters"].Value });
        }

        /// <summary>
        /// حذف حساب وسائط اجتماعية مرتبط بالمؤسسة.
        /// </summary>
        /// <param name="id">معرف حساب الوسائط الاجتماعية المراد حذفه.</param>
        /// <returns>نتيجة JSON تشير إلى نتيجة عملية الحذف.</returns>
        public async Task<IActionResult> DeleteAccount(int? id)
        {
            return Ok(await new SocialAccount().delete(id ?? 0, _context, _localization));
        }

        /// <summary>
        /// حذف ملف مرتبط بالمؤسسة.
        /// </summary>
        /// <param name="id">معرف الملف المراد حذفه.</param>
        /// <returns>نتيجة JSON تشير إلى نتيجة عملية الحذف.</returns>
        public async Task<IActionResult> DeleteFile(string? id)
        {
            if (id == null) return Ok(new { state = 0, message = _localization["datanotdeleted"].Value });
            if (new FilesClass().RemoveImage(long.Parse(id), "Files", _hosting, _context))
            {
                return Ok(new { state = 1, message = _localization["deletessuccessfuly"].Value });
            }
            else
                return Ok(new { state = 0, message = _localization["datanotdeleted"].Value });

        }

        /// <summary>
        /// إنشاء رابط وسائط اجتماعية جديد للمؤسسة.
        /// </summary>
        /// <param name="model">بيانات حساب الوسائط الاجتماعية المراد إضافته.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateSite(HalalaPlusProject.CModels.socialMediaAccounts model)
        {
            if (ModelState.IsValid)
            {
                return Ok(await new SocialAccount().insert(model, model.userId, _context, _localization));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// تحديث رابط وسائط اجتماعية حالي للمؤسسة.
        /// </summary>
        /// <param name="model">بيانات حساب الوسائط الاجتماعية المراد تحديثه.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> Site(HalalaPlusProject.CModels.socialMediaAccounts model)
        {
            if (ModelState.IsValid && model.Id != null)
            {
                return Ok(await new SocialAccount().update(model, _context, _localization));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// إضافة أو تعديل إعدادات نقاط المكافآت للمؤسسة.
        /// </summary>
        /// <param name="model">بيانات النقاط المراد إضافتها أو تعديلها.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> AddEditPoints(CModels.Points model)
        {
            if (ModelState.IsValid)
            {
                PointsClass obj = new PointsClass();
                return Ok(await obj.insert(model, _context, model.userId, _localization));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// إضافة أو تعديل بيانات المبيعات للمؤسسة.
        /// </summary>
        /// <param name="model">بيانات المبيعات المراد إضافتها أو تعديلها.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> AddEditSales(CModels.Sales model)
        {
            if (ModelState.IsValid)
            {
                PointsClass obj = new PointsClass();
                return Ok(await obj.insertSales(model, _context, model.userId, _localization));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// إضافة أو تعديل عرض خصم للمؤسسة.
        /// </summary>
        /// <param name="model">بيانات الخصم المراد إضافتها أو تعديلها.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> AddEditDiscount(HalalaPlusProject.CModels.DiscountsModel model)
        {
            if (ModelState.IsValid)
            {
                DiscountsClass obj = new DiscountsClass();
                return Ok(await obj.insert(model, _localization, _context, model.userId, User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value, false));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// إضافة حساب وسائط اجتماعية جديد أو تحديث حساب قائم للمؤسسة.
        /// </summary>
        /// <param name="model">بيانات حساب الوسائط الاجتماعية المراد إضافته أو تحديثه.</param>
        /// <returns>نتيجة JSON تشير إلى نجاح أو فشل العملية.</returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> AddEditAccount(HalalaPlusProject.CModels.socialMediaAccounts model)
        {
            if (ModelState.IsValid)
            {
                if (model.Id == null || model.Id == 0)
                    return Ok(await new SocialAccount().insert(model, model.userId, _context, _localization));
                else
                    return Ok(await new SocialAccount().update(model, _context, _localization));
            }
            return Ok(new { state = 0, message = _localization["fillalldata"].Value });
        }

        /// <summary>
        /// حذف عرض خصم معين من ملف تعريف المؤسسة.
        /// </summary>
        /// <param name="Id">معرف الخصم المراد حذفه.</param>
        /// <returns>نتيجة JSON تشير إلى نتيجة عملية الحذف.</returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteDiscount(long Id)
        {
            if (ModelState.IsValid)
            {
                DiscountsClass obj = new DiscountsClass();
                return Ok(await obj.delete(Id, _context, _localization));
            }
            return Ok(new { state = 0, message = _localization["datanotdeleted"].Value });
        }

        /// <summary>
        /// عرض صفحة تأكيد حذف مؤسسة.
        /// </summary>
        /// <param name="id">معرف المؤسسة المراد حذفها.</param>
        /// <returns>عرض يحتوي على تفاصيل المؤسسة لتأكيد الحذف، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Delete(long? id)
        {
            if (id == null || _context.SystemUsers.Where(p => p.AccountType == "Provider") == null)
            {
                return NotFound();
            }
            var systemUser = await _context.SystemUsers.FindAsync(id);

            if (systemUser == null)
            {
                return NotFound();
            }
            return View(systemUser);
        }

        /// <summary>
        /// تنفيذ عملية حذف منطقي للمؤسسة عن طريق تعيين علامة 'Deleted' إلى 'true'.
        /// </summary>
        /// <param name="id">معرف المؤسسة المراد حذفها.</param>
        /// <returns>بعد إتمام العملية بنجاح، يتم عرض صفحة قائمة المؤسسات.</returns>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(long id)
        {
            if (_context.SystemUsers == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.SystemUsers'  is null.");
            }
            var systemUser = await _context.SystemUsers.FindAsync(id);
            if (systemUser != null)
            {
                systemUser.Deleted = true;
                _context.Update(systemUser);
            }
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

    }
}