﻿@model HalalaPlusProject.Models.Activity
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["delete"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["delete"]</h1>

<h3>@localizer["deleteactivity"]</h3>
<div>
    <h4>@localizer["theactivity"]</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @localizer["activityname"]
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt class = "col-sm-2">
            @localizer["activityname"]
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnName)
        </dd>
        <dt class = "col-sm-2">
            @localizer["discribe"]
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Describtion)
        </dd>
        <dt class = "col-sm-2">
            @localizer["discribe"]
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnDescribtion)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-danger" >@localizer["delete"]</button> |
        <a asp-action="Index">@localizer["backtolist"]</a>
    </form>
</div>
