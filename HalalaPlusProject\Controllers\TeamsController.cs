﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HalalaPlusProject.Models;
using HalalaPlusProject.CustomClasses;
using static System.Net.Mime.MediaTypeNames;
using Microsoft.AspNetCore.Authorization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// متحكم لإدارة أعضاء الفريق، بما في ذلك عمليات الإنشاء والعرض والتعديل والحذف.
    /// </summary>
    [Authorize]
    public class TeamsController : Controller
    {
        private readonly HalalaPlusdbContext _context;
        private readonly IWebHostEnvironment _host;

        public TeamsController(HalalaPlusdbContext context, IWebHostEnvironment _host)
        {
            _context = context;
            this._host = _host;
        }

        // GET: Teams
        /// <summary>
        /// يعرض قائمة بجميع أعضاء الفريق.
        /// </summary>
        public async Task<IActionResult> Index()
        {
            return _context.Teams != null ?
                        View(await _context.Teams.ToListAsync()) :
                        Problem("Entity set 'HalalaPlusdbContext.Teams'  is null.");
        }

        // GET: Teams/Details/5
        /// <summary>
        /// يعرض تفاصيل عضو فريق محدد بناءً على المعرّف.
        /// </summary>
        /// <param name="id">معرّف عضو الفريق.</param>
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null || _context.Teams == null)
            {
                return NotFound();
            }

            var team = await _context.Teams
                .FirstOrDefaultAsync(m => m.Id == id);
            if (team == null)
            {
                return NotFound();
            }

            return View(team);
        }

        // GET: Teams/Create
        /// <summary>
        /// يعرض صفحة إنشاء عضو فريق جديد.
        /// </summary>
        public IActionResult Create()
        {
            return View();
        }

        // POST: Teams/Create
        /// <summary>
        /// ينشئ عضو فريق جديد ويحفظ صورته في حالة إرفاقها.
        /// </summary>
        /// <param name="team">بيانات عضو الفريق الجديد.</param>
        /// <param name="Image">ملف صورة عضو الفريق.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Name,EnName,Major,EnMajor,Overview,Enoverview")] Team team, IFormFile Image)
        {
            if (ModelState.IsValid)
            {
                if (Image != null) team.Image = HandleImages.SaveImage(Image, "img", _host);
                _context.Add(team);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(team);
        }

        // GET: Teams/Edit/5
        /// <summary>
        /// يعرض صفحة تعديل بيانات عضو فريق موجود.
        /// </summary>
        /// <param name="id">معرّف عضو الفريق المراد تعديله.</param>
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null || _context.Teams == null)
            {
                return NotFound();
            }

            var team = await _context.Teams.FindAsync(id);
            if (team == null)
            {
                return NotFound();
            }
            return View(team);
        }

        // POST: Teams/Edit/5
        /// <summary>
        /// يحفظ التعديلات على عضو فريق موجود في قاعدة البيانات.
        /// </summary>
        /// <param name="id">معرّف عضو الفريق المستهدف.</param>
        /// <param name="team">بيانات عضو الفريق المحدثة.</param>
        /// <param name="Image">ملف الصورة الجديد (اختياري) لاستبدال الصورة الحالية.</param>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,EnName,Major,EnMajor,Overview,Enoverview")] Team team, IFormFile? Image)
        {
            if (id != team.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var t = _context.Teams.AsNoTracking().FirstOrDefault(p => p.Id == team.Id);
                    team.Image = t.Image;
                    string old = team.Image;
                    if (Image != null) team.Image = HandleImages.SaveImage(Image, "img", _host);

                    _context.Update(team);
                    await _context.SaveChangesAsync();
                    if (Image != null)
                        if (old != null) HandleImages.RemoveImage(old, "img", _host);
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!TeamExists(team.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(team);
        }

        // GET: Teams/Delete/5
        /// <summary>
        /// يعرض صفحة تأكيد حذف عضو الفريق.
        /// </summary>
        /// <param name="id">معرّف عضو الفريق المراد حذفه.</param>
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null || _context.Teams == null)
            {
                return NotFound();
            }

            var team = await _context.Teams
                .FirstOrDefaultAsync(m => m.Id == id);
            if (team == null)
            {
                return NotFound();
            }

            return View(team);
        }

        // POST: Teams/Delete/5
        /// <summary>
        /// يحذف عضو الفريق من قاعدة البيانات بعد التأكيد.
        /// </summary>
        /// <param name="id">معرّف عضو الفريق المطلوب حذفه.</param>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            if (_context.Teams == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.Teams'  is null.");
            }
            var team = await _context.Teams.FindAsync(id);
            if (team != null)
            {
                _context.Teams.Remove(team);
            }

            await _context.SaveChangesAsync();
            if (team.Image != null) HandleImages.RemoveImage(team.Image, "img", _host);
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// يتحقق من وجود عضو فريق بالمعرّف المحدد.
        /// </summary>
        /// <param name="id">معرّف عضو الفريق.</param>
        /// <returns>True إذا كان عضو الفريق موجودًا، وإلا False.</returns>
        private bool TeamExists(int id)
        {
            return (_context.Teams?.Any(e => e.Id == id)).GetValueOrDefault();
        }
    }
}