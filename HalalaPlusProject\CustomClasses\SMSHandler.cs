﻿using HalalaPlusProject.CModels;
using Newtonsoft.Json;
//using OTSMS;
using System.Net;
using System.Text;

namespace HalalaPlusProject.HalalaClass
{
    public class SMSHandler
    {

        public async Task<int> SendSingleSMS(string key, string number, string message, string code, long otpId)
        {
            var temp = await SendSMS(key, number, message + code);
            try
            {
                SMSModel ob = JsonConvert.DeserializeObject<SMSModel>(temp.ToString());
                var otp = new Models.Otpsm()
                {
                    MsgId = ob.data.message.id,
                    Status = ob.data.message.status,
                    SendStatus = ob.status,
                    Responce = temp.ToString(),
                    Code = code,
                    Date = DateTime.Now,
                    Message = message,
                    OtpId = otpId
                };
                return await saveOtp(otp);
            }
            catch (Exception ex)
            {
                var otp = new Models.Otpsm()
                {
                    SendStatus = ex.Message,
                    Responce = temp.ToString(),
                    Code = code,
                    Date = DateTime.Now,
                    Message = message,
                    OtpId = otpId
                };
                return await saveOtp(otp);
            }
        }
        public void SendHelloSMS(string key, string number, string message, string code, long otpId)
        {
            Task savetask = Task.Run(() => SendSMS2(key, number, message, code, otpId));
        }
        public void SendSingleSMSwithoutasync(string key, string number, string message, string code, long otpId)
        {
            Task savetask = Task.Run(() => SendSMS3(key, number, message, code, otpId));
        }
        public async Task<object> SendSMS2(string key, string number, string message, string code, long otpId)
        {
            try
            {
                var client = new HttpClient();
                var request = new HttpRequestMessage(HttpMethod.Post, "https://app.mobile.net.sa/api/v1/send");
                request.Headers.Add("Accept", "application/json");
                request.Headers.Add("Authorization", "Bearer " + key);//7T1zcxIEubpbZFSJCxrRDePhNAAwyi90aI6JNdV
                var content = new StringContent("{\n    \"number\":\"" + number + "\",\n    \"senderName\":\"halala-AD\",\n    \"sendAtOption\":\"Now\", \n    \"messageBody\":\"" + message + " \",\n    \"allow_duplicate\":true\n}", null, "application/json");
                request.Content = content;
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();
                var temp = await response.Content.ReadAsStringAsync();

                var otpob = new Models.Otpsm()
                {
                    SendStatus = temp.ToString(),
                    Responce = temp.ToString(),
                    Code = code,
                    Date = DateTime.Now,
                    Message = message,
                    OtpId = otpId
                };
                return await saveOtp(otpob);
                try
                {

                    SMSModel ob = JsonConvert.DeserializeObject<SMSModel>(temp.ToString());
                    var otp = new Models.Otpsm()
                    {
                        MsgId = ob.data.message.id,
                        Status = ob.data.message.status,
                        SendStatus = ob.status,
                        Responce = temp.ToString(),
                        Code = code,
                        Date = DateTime.Now,
                        Message = message,
                    };
                    if (otpId > -1) otp.OtpId = otpId;
                    return await saveOtp(otp);
                }
                catch (Exception ex)
                {
                    var otp = new Models.Otpsm()
                    {
                        SendStatus = ex.Message,
                        Responce = temp.ToString(),
                        Code = code,
                        Date = DateTime.Now,
                        Message = message,
                        OtpId = otpId
                    };
                    return await saveOtp(otp);
                }

            }
            catch (Exception ex)
            {
                var otp = new Models.Otpsm()
                {
                    SendStatus = ex.Message,
                    Responce = ex.Message,
                    Code = code,
                    Date = DateTime.Now,
                    Message = message,
                    OtpId = otpId
                };
                return await saveOtp(otp);
            }
        }
        public async Task<object> SendSMS3(string key, string number, string message, string code, long otpId)
        {
            try
            {
                var client = new HttpClient();
                var request = new HttpRequestMessage(HttpMethod.Post, "https://app.mobile.net.sa/api/v1/send");
                request.Headers.Add("Accept", "application/json");
                request.Headers.Add("Authorization", "Bearer " + key);//7T1zcxIEubpbZFSJCxrRDePhNAAwyi90aI6JNdV
                var content = new StringContent("{\n    \"number\":\"" + number + "\",\n    \"senderName\":\"halala\",\n    \"sendAtOption\":\"Now\", \n    \"messageBody\":\"" + message + " \",\n    \"allow_duplicate\":true\n}", null, "application/json");
                request.Content = content;
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();
                var temp = await response.Content.ReadAsStringAsync();

                var otpob = new Models.Otpsm()
                {
                    SendStatus = temp.ToString(),
                    Responce = temp.ToString(),
                    Code = code,
                    Date = DateTime.Now,
                    Message = message,
                    OtpId = otpId
                };
                return await saveOtp(otpob);
                try
                {

                    SMSModel ob = JsonConvert.DeserializeObject<SMSModel>(temp.ToString());
                    var otp = new Models.Otpsm()
                    {
                        MsgId = ob.data.message.id,
                        Status = ob.data.message.status,
                        SendStatus = ob.status,
                        Responce = temp.ToString(),
                        Code = code,
                        Date = DateTime.Now,
                        Message = message,
                    };
                    if (otpId > -1) otp.OtpId = otpId;
                    return await saveOtp(otp);
                }
                catch (Exception ex)
                {
                    var otp = new Models.Otpsm()
                    {
                        SendStatus = ex.Message,
                        Responce = temp.ToString(),
                        Code = code,
                        Date = DateTime.Now,
                        Message = message,
                        OtpId = otpId
                    };
                    return await saveOtp(otp);
                }

            }
            catch (Exception ex)
            {
                var otp = new Models.Otpsm()
                {
                    SendStatus = ex.Message,
                    Responce = ex.Message,
                    Code = code,
                    Date = DateTime.Now,
                    Message = message,
                    OtpId = otpId
                };
                return await saveOtp(otp);
            }
        }

        private async Task<int> saveOtp(Models.Otpsm model)
        {
            using (Models.HalalaPlusdbContext db = new Models.HalalaPlusdbContext())
            {
                try
                {
                    db.Add(model);
                    return await db.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    return -1;

                }
            }
        }
        public async Task<object> SendSMS(string key, string number, string message)
        {
            try
            {
                var client = new HttpClient();
                var request = new HttpRequestMessage(HttpMethod.Post, "https://app.mobile.net.sa/api/v1/send");
                request.Headers.Add("Accept", "application/json");
                request.Headers.Add("Authorization", "Bearer " + key);//7T1zcxIEubpbZFSJCxrRDePhNAAwyi90aI6JNdV
                var content = new StringContent("{\n    \"number\":\"" + number + "\",\n    \"senderName\":\"halala-AD\",\n    \"sendAtOption\":\"Now\", \n    \"messageBody\":\"" + message + " \",\n    \"allow_duplicate\":true\n}", null, "application/json");
                request.Content = content;
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        public async Task<object> sendbulkMessage()
        {

            try
            {
                var client = new HttpClient();
                var request = new HttpRequestMessage(HttpMethod.Post, "https://app.mobile.net.sa/api/v1/send-bulk");

                request.Headers.Add("Accept", "application/json");
                var content = new MultipartFormDataContent();
                request.Content = content;
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {

                return ex.Data;
            }
        }
        //public async Task<object> GetCredit()
        //{

        //    try
        //    {
        //        sms sms = new sms();

        //        var result = sms.GetCredits("+966593071778", "7T1zcxIEubpbZFSJCxrRDePhNAAwyi90aI6JNdVO").ToString();

        //        return result;
        //    }
        //    catch (Exception ex)
        //    {

        //        return ex.Data;
        //    }
        //} 

        public async Task<object> GetCreditlink()
        {

            try
            {
                var client = new HttpClient();
                var request = new HttpRequestMessage(HttpMethod.Post, "https://mobile.net.sa/sms/gw/?userName=+966593071778&userPassword=7T1zcxIEubpbZFSJCxrRDePhNAAwyi90aI6JNdVO&numbers=966577842520&userSender=Mobile.Sa&msg=1565&By=standard&infos=YES");
                request.Headers.Add("Accept", "application/json");
                var content = new MultipartFormDataContent();
                request.Content = content;
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
                //return result;
            }
            catch (Exception ex)
            {

                return ex.Data;
            }
        }
        public async Task<object> GetMessageStatus(string msgId)
        {

            try
            {
                var client = new HttpClient();
                var request = new HttpRequestMessage(HttpMethod.Post, "https://app.mobile.net.sa/api/v1/message-status/" + msgId);
                request.Headers.Add("Accept", "application/json");
                var content = new StringContent("", null, "text/plain");
                request.Content = content;
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {

                return ex.Data;
            }
        }

        public async Task<object> GetBalance()
        {

            try
            {
                var client = new HttpClient();
                var request = new HttpRequestMessage(HttpMethod.Post, "https://app.mobile.net.sa/api/v1/get-balance");
                request.Headers.Add("Accept", "application/json");
                var content = new StringContent("", null, "text/plain");
                request.Content = content;
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {

                return ex.Data;
            }
        }




    }
}
