﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class Article
{
    [Key]
    public int Id { get; set; }

    [StringLength(500)]
    public string? Title { get; set; }

    [StringLength(1500)]
    public string? OverView { get; set; }

    public string? Body { get; set; }

    [StringLength(50)]
    public string? IsActive { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? WriteDate { get; set; }

    [Column("deleted")]
    public bool Deleted { get; set; }

    [StringLength(500)]
    public string? EnTitle { get; set; }

    [StringLength(1500)]
    public string? EnOverView { get; set; }

    public string? EnBody { get; set; }

    [StringLength(500)]
    public string? Link { get; set; }
}
