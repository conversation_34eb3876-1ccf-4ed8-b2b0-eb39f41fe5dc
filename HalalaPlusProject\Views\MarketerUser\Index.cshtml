﻿@model HalalaPlusProject.CModels.UserDetails
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["details"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}


    <hr />
         <div class="col-md-12">
         <div class="row">

     
     <div class="col-md-5">
          <div class="form-group">
                <label class="form-label">@localizer["serviceprovidername"]</label>
                <label  class="form-control">@Model.Name</label>               
            </div>
               <div class="form-group">
                <label class="form-label">@localizer["nationality"]</label>
                <label  class="form-control">@Model.Nationality</label>               
            </div>
               <div class="form-group">
                <label class="form-label"> @localizer["identityno"]</label>
                <label  class="form-control">@Model.IdentityNo</label>               
            </div>
              <div class="form-group">
                <label class="form-label"> @localizer["birthdate"]</label>
                <label  class="form-control">@Model.BirthDate</label>               
            </div>

        </div>
         <div class="col-md-5">
 <div class="form-group">
                <label class="form-label">@localizer["phoneno"] </label>
                <label  class="form-control">@Model.PhoneNo</label>               
            </div>
            <div class="form-group">
                <label class="form-label"> @localizer["mail"]</label>
                <label  class="form-control">@Model.Email</label>               
            </div>
            <div class="form-group">
                <label class="form-label"> @localizer["couponcode"] </label>
                <label  class="form-control">@Model.DiscountCode</label>               
            </div>
             <div class="form-group">
                <label class="form-label">  @localizer["dealingwayamount"]</label>
                <label  class="form-control">@Model.Amount</label>               
            </div>

             <div class="form-group">
                <label class="form-label">  @localizer["dealingwayrate"]</label>
                <label  class="form-control">@Model.Precentage</label>               
            </div>




</div>
          </div>  </div>

