﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("FAQsTable")]
public partial class FaqsTable
{
    [Key]
    public int Id { get; set; }

    [StringLength(500)]
    public string? Question { get; set; }

    [StringLength(1500)]
    public string? Details { get; set; }

    [Column("note")]
    [StringLength(500)]
    public string? Note { get; set; }

    [Column("lang")]
    [StringLength(50)]
    public string? Lang { get; set; }

    [StringLength(500)]
    public string? EnQuestion { get; set; }

    [StringLength(1500)]
    public string? EnDetails { get; set; }
}
