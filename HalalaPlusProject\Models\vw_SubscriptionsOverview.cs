﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models
{
    [Keyless]

    public partial class vw_SubscriptionsOverview
    {
        public int PackageId { get; set; }
        public string PackageName { get; set; } = string.Empty;
        public int ActiveSubscribers { get; set; }
        public int NewSubscribersThisMonth { get; set; }
        public decimal TotalRevenueFromPackage { get; set; }
        public decimal ChurnRate { get; set; }
    }

    }

