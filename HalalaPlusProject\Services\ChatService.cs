﻿using Google.Cloud.Firestore;
using HalalaPlusProject.CModels;
using HalalaPlusProject.Models;
using Microsoft.Extensions.Logging;
using NuGet.Protocol.Plugins;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace HalalaPlusProject.Services
{
    public interface IChatService
    {
        Task AddMessage(string roomId, string senderId, string senderEmail, string message); 
        Task<List<MessageViewModel>> GetMessages(string roomId);
        Task<List<ChatRoomViewModel>> GetChatRooms(string? userId);
    }

    public class ChatService : IChatService
    {
        private readonly FirestoreDb _db;
        private readonly ILogger<ChatService> _logger;
        private const string ChatRoomsCollectionName = "chat_rooms";
        private const string MessagesCollectionName = "messages";
        private readonly HalalaPlusdbContext _context;
        public ChatService(HalalaPlusdbContext context, FirestoreDb db, ILogger<ChatService> logger)
        {
            _db = db;
            _logger = logger;
            _context = context;
        }

        public async Task AddMessage(string roomId, string senderId, string senderEmail, string message) //Updated parameter
        {
            try
            {
                CollectionReference chatRoomsCollection = _db.Collection(ChatRoomsCollectionName);
                DocumentReference roomDocument = chatRoomsCollection.Document(roomId);
                CollectionReference messagesCollection = roomDocument.Collection(MessagesCollectionName);

                var sId = (roomId.Split("_")[0]);
                var rId = (roomId.Split("_")[1]);

                var userData =await _context.AspNetUsers.FindAsync((senderId));
                Dictionary<string, object> data = new Dictionary<string, object>()
                {
                    { "message", message }, 
                    { "receiverId", rId }, 
                    { "senderEmail", userData.Email },
                    { "senderId", sId },
                    { "timestamp", Timestamp.FromDateTime(DateTime.UtcNow) }
                };
                await messagesCollection.AddAsync(data);
                _logger.LogInformation("Chat message added to room {RoomId}: {Message}", roomId, message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding chat message to room {RoomId}: {Message}", roomId, message);
               // throw; // Re-throw to let the controller handle it
            }
        }


        public async Task<List<MessageViewModel>> GetMessages(string roomId)
        {
            List<MessageViewModel> messageList = new List<MessageViewModel>();
            try
            {
                CollectionReference chatRoomsCollection = _db.Collection(ChatRoomsCollectionName);
                DocumentReference roomDocument = chatRoomsCollection.Document(roomId);
                CollectionReference messagesCollection = roomDocument.Collection(MessagesCollectionName);

                QuerySnapshot snapshot = await messagesCollection.OrderBy("timestamp").Limit(50).GetSnapshotAsync(); //Sort by timestamp

                foreach (DocumentSnapshot document in snapshot.Documents)
                {
                    if (document.Exists)
                    {
                        MessageViewModel message = new MessageViewModel();

                        // Safely get values, handling potential nulls
                        message.Text = document.GetValue<string>("message") ?? string.Empty;
                        message.SenderId = document.GetValue<string>("senderId") ?? string.Empty;
                        message.SenderName = document.GetValue<string>("senderEmail") ?? string.Empty;

                        // Handle timestamp separately
                        if (document.TryGetValue("timestamp", out object timestampObject))
                        {
                            if (timestampObject is Timestamp timestamp)
                            {
                                message.Timestamp = timestamp.ToDateTime();
                            }
                            else
                            {
                                // Log if timestamp is not of expected type
                                _logger.LogWarning($"Unexpected timestamp type: {timestampObject?.GetType()}");
                                message.Timestamp = DateTime.MinValue; // Assign a default value
                            }
                        }
                        else
                        {
                            // Log if timestamp field is missing
                            _logger.LogWarning("Timestamp field is missing from document");
                            message.Timestamp = DateTime.MinValue; // Assign a default value
                        }

                        message.RoomId = roomId;
                        messageList.Add(message);
                    }
                }
                return messageList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chat messages for room {RoomId}", roomId);
                return new List<MessageViewModel>();
            }
        }

        public async Task<List<ChatRoomViewModel>> GetChatRooms(string? userId)
        {
            List<ChatRoomViewModel> chatRoomList = new List<ChatRoomViewModel>();
            try
            {
                CollectionReference collection = _db.Collection(ChatRoomsCollectionName);
                CollectionReference usercollection = _db.Collection("Users");
                QuerySnapshot snapshot = await collection.GetSnapshotAsync();
                QuerySnapshot usersnapshot = await usercollection.GetSnapshotAsync();
                Console.WriteLine($"Number of chat rooms found: {snapshot.Count}");
                Console.WriteLine($"Number of users found: {usersnapshot.Count}");
                var userData = await _context.AspNetUsers.FindAsync((userId));
                //foreach (DocumentSnapshot document in snapshot.Documents.Where(d=>d.Id.Contains()))
                foreach (DocumentSnapshot document in snapshot.Documents)
                {
                    if (document.Exists)
                    {
                        Console.WriteLine($"Chat room ID: {document.Id}");
                        var senderId = (document.Id.ToString().Split("_")[0]);
                        var receverId = (document.Id.ToString().Split("_")[1]);
                        //Get the "roomName" field value
                        //  string roomName = document.GetValue<string>("roomName") ?? "Unnamed Room";
                        // string roomName = document.GetValue<string>("roomName") ?? "Unnamed Room";
                        var SenA= usersnapshot.Documents.Where(d => d.Id == senderId);
                        var SenB = usersnapshot.Documents.Where(d => d.Id == receverId);
                        var personA = SenA.FirstOrDefault().GetValue<string>("email") ?? string.Empty;
                        var personB = SenB.FirstOrDefault().GetValue<string>("email") ?? string.Empty;
                        var temp = _context.AspNetUsers.Where(d => d.Email == personA).ToList();
                        var temp2 = _context.AspNetUsers.Where(d => d.Email == personB).ToList();

                        ChatRoomViewModel chatRoom = new ChatRoomViewModel
                        {
                            RoomId = document.Id, // Use the document ID as the room ID
                            RoomName = temp[0].FullName, // Populate the room name
                        };
                        //this line of code for filltering the docs that this email owned
                       //if(!(personA!=userData.Email&&personB!=userData.Email))
                        chatRoomList.Add(chatRoom);
                    }
                }
                return chatRoomList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chat rooms");
                return new List<ChatRoomViewModel>();
            }
        }

    }
}