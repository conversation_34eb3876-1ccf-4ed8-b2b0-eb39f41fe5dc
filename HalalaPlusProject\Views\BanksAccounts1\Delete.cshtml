﻿@model HalalaPlusProject.Models.BanksAccount

@{
    ViewData["Title"] = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>BanksAccount</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.BankName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.BankName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.ConnectionCode)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.ConnectionCode)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.AccountNumber)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.AccountNumber)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
