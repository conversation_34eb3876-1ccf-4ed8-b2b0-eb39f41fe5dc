﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class VwTopCustomersReport
{
    public long CustomerId { get; set; }

    [StringLength(500)]
    public string? CustomerName { get; set; }

    [StringLength(250)]
    public string? Country { get; set; }

    public int? TotalOrders { get; set; }

    public double TotalPayments { get; set; }

    public double? AvgOrderValue { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? LastOrderDate { get; set; }

    public int? DaysSinceLastOrder { get; set; }
}
