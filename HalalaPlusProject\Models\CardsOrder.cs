﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class CardsOrder
{
    [Key]
    public long Id { get; set; }

    [StringLength(50)]
    public string? Name { get; set; }

    [StringLength(50)]
    public string? PhoneNo { get; set; }

    public long? UserId { get; set; }

    [StringLength(50)]
    public string? OrderState { get; set; }

    [Column("orderdate", TypeName = "datetime")]
    public DateTime Orderdate { get; set; }

    [Column("masterId")]
    public long? MasterId { get; set; }

    public bool? IsOrder { get; set; }
}
