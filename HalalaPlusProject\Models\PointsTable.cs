﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class PointsTable
{
    [Key]
    public long Id { get; set; }

    public double? PointCount { get; set; }

    public double? Sales { get; set; }

    public double? PointDesrve { get; set; }

    public double? Prize { get; set; }

    public string? Conditions { get; set; }

    [Column("UserID")]
    public long? UserId { get; set; }

    public long? DiscountId { get; set; }

    public bool Deleted { get; set; }

    public string? EnConditions { get; set; }

    [ForeignKey("DiscountId")]
    [InverseProperty("PointsTables")]
    public virtual DiscountsTable? Discount { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("PointsTables")]
    public virtual SystemUser? User { get; set; }
}
