﻿@model List<HalalaPlusProject.CModels.PackageFeatureViewModel>

<div class="mb-3">
    <h5>قائمة المميزات الاضافية للباقة</h5>
    <div id="features-container">
        @if (Model != null)
        {
            for (int i = 0; i < Model.Count; i++)
            {
                <div class="row feature-row mb-2">
                    <div class="col-5">
                        <input type="text" name="Features[@i].Key" value="@Model[i].Key" class="form-control" placeholder="عنوان الميزة" />
                    </div>
                    <div class="col-5">
                        <input type="text" name="Features[@i].Value" value="@Model[i].Value" class="form-control" placeholder="نص او قيمة الميزة" />
                    </div>
                    <div class="col-2">
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeFeature(this)">حذف</button>
                    </div>
                </div>
            }
        }
    </div>
    <button type="button" id="add-feature-btn" class="btn btn-success mt-2">أضـافـة مـيـزة جـديـدة</button>
</div>