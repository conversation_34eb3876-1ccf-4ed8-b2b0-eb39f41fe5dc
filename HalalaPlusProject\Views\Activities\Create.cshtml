﻿@model HalalaPlusProject.Models.Activity
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["create"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["create"]</h1>

<h4>@localizer["activity"]</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="control-label">@localizer["activityname"] </label>
                        <input asp-for="Name" class="form-control" />
                        <span asp-validation-for="Name" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label">@localizer["activityname"] </label>
                        <input asp-for="EnName" class="form-control" />
                        <span asp-validation-for="EnName" class="text-danger"></span>
                    </div>
                    </div>
                    <div class="col-md-4">
                    <div class="form-group">
                        <label class="control-label"> @localizer["discribe"]</label>
                        <input asp-for="Describtion" class="form-control" />
                        <span asp-validation-for="Describtion" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label"> @localizer["discribe"]</label>
                        <input asp-for="EnDescribtion" class="form-control" />
                        <span asp-validation-for="EnDescribtion" class="text-danger"></span>
                    </div>
                    </div>
                    <div class="col-md-4">
                    <div class="form-group">
                        <div class="row justify-content-center" style="margin-bottom:1px;">
                            <div class="circle ">
                                <img src="" id="activityicon">
                            </div>
                        </div>

                    </div>
                    <div class="form-group">
                        <label class="control-label">logo</label>
                        <input type="file" name="file" accept="image/*" onchange="ShowImagePreview(this, document.getElementById('activityicon'))" class="form-control" />
                        <span asp-validation-for="Image" class="text-danger"></span>
                    </div>
                    </div>

                    </div>
            
           
           
            <div class="form-group">
                <button type="submit" value="Create" class="btn btn-primary">@localizer["create"]</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
