﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class RetriveMonyBoxAmount
{
    [Key]
    public long Id { get; set; }

    [Column("IBAN")]
    [StringLength(50)]
    public string? Iban { get; set; }

    [StringLength(50)]
    public string? AcountOwnerName { get; set; }

    public double? Amount { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateAt { get; set; }

    [StringLength(50)]
    public string? PaymentId { get; set; }

    [StringLength(50)]
    public string? Recepit { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? PayDate { get; set; }

    public bool IsCanceled { get; set; }

    public bool Payed { get; set; }

    public long? MonyBoxId { get; set; }

    [StringLength(200)]
    public string? BankName { get; set; }

    public int? BankId { get; set; }

    public long? UserId { get; set; }

    [Column("image")]
    [StringLength(100)]
    public string? Image { get; set; }

    [Column("status")]
    [StringLength(20)]
    public string Status { get; set; } = null!;

    [ForeignKey("UserId")]
    [InverseProperty("RetriveMonyBoxAmounts")]
    public virtual SystemUser? User { get; set; }
}
