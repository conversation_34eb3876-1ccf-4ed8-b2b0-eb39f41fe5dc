﻿//using AutoMapper;


namespace Project.Extensions
{
    public static class MapperExtension
    {
        public static IServiceCollection RegisterMapperService(this IServiceCollection services)
        {

            #region Mapper

            //services.AddSingleton<IMapper>(sp => new MapperConfiguration(cfg =>
            //{
   
            //    cfg.CreateMap<CouponsOffersView, CouponsOffersView>();

            //}).CreateMapper());

            //// Register the IMapperService implementation with your dependency injection container
           
            //services.AddSingleton<IBaseMapper<Cart, CartEntity>, BaseMapper<Cart, CartEntity>>();
            
            #endregion

            return services;
        }
    }
}
