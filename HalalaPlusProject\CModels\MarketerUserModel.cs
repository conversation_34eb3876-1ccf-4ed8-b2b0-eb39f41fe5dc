﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
   
    public class MUserCreateProvider {
        public long Id { get; set; }
        [Required]
        //[Display(Name = "اسم مقدم الخدمة")]
        public string? Name { get; set; }
        //[Required]
        //[Display(Name = "المدينة")]
        public int? City { get; set; }
        //[Required]
        //[Display(Name = "النشاط")]
        public int? Activity { get; set; }
        //[Required]
        //[Display(Name = "اسم الممثل")]
        public string? ServiceProviderRepresent { get; set; }
        //[Display(Name = "رقم الجوال")]
        public string? PhoneNumber { get; set; }
        //[Display(Name = "الايميل")]
        [RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }
    }

}
