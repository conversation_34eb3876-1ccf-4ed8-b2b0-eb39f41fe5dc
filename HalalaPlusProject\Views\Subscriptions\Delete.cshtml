﻿@model HalalaPlusProject.Models.Subscription

@{
    ViewData["Title"] = "حذف";
}

<h1>حذف</h1>

<h3>هل أنت متأكد أنك تريد حذف هذا؟</h3>
<div>
    <h4>الاشتراك</h4>
    <hr />
    <dl class="row">
        <div class="col-md-3">
            <dt>مشترك</dt>
            <dd>@Html.DisplayFor(model => model.IsSubscribed)</dd>
        </div>
        <div class="col-md-3">
            <dt>الحالة</dt>
            <dd>@Html.DisplayFor(model => model.State)</dd>
        </div>
        <div class="col-md-3">
            <dt>تاريخ البدء</dt>
            <dd>@Html.DisplayFor(model => model.StartDate)</dd>
        </div>
        <div class="col-md-3">
            <dt>تاريخ الانتهاء</dt>
            <dd>@Html.DisplayFor(model => model.EndDate)</dd>
        </div>
        <div class="col-md-3">
            <dt>السعر</dt>
            <dd>@Html.DisplayFor(model => model.Price)</dd>
        </div>
        <div class="col-md-3">
            <dt>رقم العملية</dt>
            <dd>@Html.DisplayFor(model => model.ProcessNo)</dd>
        </div>
        <div class="col-md-3">
            <dt>الباقة</dt>
            <dd>@Html.DisplayFor(model => model.Pack.Id)</dd>
        </div>
        <div class="col-md-3">
            <dt>المستخدم</dt>
            <dd>@Html.DisplayFor(model => model.User.Id)</dd>
        </div>
    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="حذف" class="btn btn-danger" /> |
        <a asp-action="Index">العودة إلى القائمة</a>
    </form>
</div>
