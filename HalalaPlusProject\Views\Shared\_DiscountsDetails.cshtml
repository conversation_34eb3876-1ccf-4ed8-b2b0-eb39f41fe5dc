﻿
@model IEnumerable<HalalaPlusProject.CModels.DiscountsModel>

<div class="table-responsive p-0">
               
                <table class="table table-striped text-center">
                  <thead>
                    <tr>
                     
                      <th scope="col">اسم الخدمة(الخصم)</th>
                      <th scope="col">الخصم</th>
                      <th scope="col">بداية الخصم</th>
                      <th scope="col">نهاية الخصم</th>
                      <th scope="col">نوع الخصم</th>
                      <th scope="col">الشروط</th>
                     
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model) {
        <tr id="<EMAIL>">
            <td>
                @Html.DisplayFor(modelItem => item.DiscountName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Discount)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.StartDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EndDate)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.GrantType)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Conditions)
            </td>
           
        </tr>
}
      </tbody>
               
                </table>

              </div>