﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Controllers;
using HalalaPlusProject.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace HalalaPlusProject.CustomClasses
{
    public class SocialAccount
    {
        public SocialAccount() { 
        }  
        public async Task<Object> insert(List<socialMediaAccounts> data,long Id, Models.HalalaPlusdbContext _context)
        {
            List<socialMediaAccounts> list = new List<socialMediaAccounts>();

            if (data.Count != 0)
            {
                try
                {
                    foreach (var item in data)
                    {
                        Models.SocialMediaAccount ob = new Models.SocialMediaAccount()
                        {
                            Name = item.SiteName,
                            Link = item.Link,
                            UserId = Id,
                        };
                        _context.Add(ob);
                      await  _context.SaveChangesAsync();
                        return new { state = 5, message = "تم الحفظ بنجاح" };
                    }
                }
                catch (Exception)
                {

                    return new { state = 0, message = "خطاء " };
                }
            }
            return new { state = 0, message = "خطاء " };

        } 
        public async Task<Object> insert(List<socialMediaAccounts> data,long Id, Models.HalalaPlusdbContext _context, IStringLocalizer<ServiceProviderController> _localization)
        {
            List<socialMediaAccounts> list = new List<socialMediaAccounts>();

            if (data.Count != 0)
            {
                try
                {
                    foreach (var item in data)
                    {
                        Models.SocialMediaAccount ob = new Models.SocialMediaAccount()
                        {
                            Name = item.SiteName,
                            Link = item.Link,
                            UserId = Id,
                        };
                        _context.Add(ob);
                      await  _context.SaveChangesAsync();
                        return new { state = 5, message = _localization["savedsuccessfuly"].Value };
                    }
                }
                catch (Exception)
                {

                    return new { state = 0, message = _localization["anerroroccured"].Value };
                }
            }
            return new { state = 0, message = _localization["anerroroccured"].Value };

        }
        public async Task<Object> insert(socialMediaAccounts data, long Id, Models.HalalaPlusdbContext _context, IStringLocalizer<ServiceProviderController> _localization)
        {
            if (data != null)
            {
                try
                {
                        Models.SocialMediaAccount ob = new Models.SocialMediaAccount()
                        {
                            Name = data.SiteName,
                            Link = data.Link,
                            UserId = Id,
                        };
                        _context.Add(ob);
                        await _context.SaveChangesAsync();
                        return new { state = 1, message = _localization["savedsuccessfuly"].Value };                
                }
                catch (Exception)
                {

                    return new { state = 0, message = _localization["anerroroccured"].Value };
                }
            }
            return new { state = 0, message = _localization["anerroroccured"].Value };

        }
        public async Task<Object> update(socialMediaAccounts model, Models.HalalaPlusdbContext _context, IStringLocalizer<ServiceProviderController> _localization)
        {           
                try
                {
                    var ob = _context.SocialMediaAccounts.Find(model.Id);
                    ob.Name = model.SiteName;
                    ob.Link = model.Link;
                    _context.Update(ob);
                    await _context.SaveChangesAsync();                   
                    return new { state = 1, message = _localization["savedsuccessfuly"].Value };
                }
                catch (Exception)
                {
                    return new { state = 0, message = _localization["anerroroccured"].Value };
                }
        }
        public async Task<List<SocialAccounts>> retrive(long userId, HalalaPlusdbContext _context)
        {
            try
            {
                if (userId != 0)
                    return _context.SocialMediaAccounts.Where(p => p.UserId == userId).Select(i => new SocialAccounts { Id = i.Id, Name = i.Name, Link = i.Link }).ToList();
            }
            catch (Exception)
            {
                return new List<SocialAccounts>();
            }
            return new List<SocialAccounts>();
        }
        public async Task<Object> delete(int Id, HalalaPlusdbContext _context)
        {

            if (Id == 0 || _context.SocialMediaAccounts == null)
            {
                return new { state = 0, message = "ليس هناك حسابات" };
            }

            var systemUser = _context.SocialMediaAccounts.Find(Id);

            if (systemUser == null)
            {
                return new { state = 0, message = "ليس هناك حساب بهذا الرقم" };
            }
            try
            {
                _context.Remove(systemUser);
                await _context.SaveChangesAsync();
                return new { state = 1, message = "تم الحذف" };
            }
            catch (DbUpdateConcurrencyException)
            {
                return new { state = 0, message = "هنالك خطاء " };
            }
            return new { state = 0, message = "خطاء ليس هناك بيانات" };
        }  
        public async Task<Object> delete(int Id, HalalaPlusdbContext _context, IStringLocalizer<ServiceProviderController> _localization)
        {

            if (Id == 0 || _context.SocialMediaAccounts == null)
            {
                return new { state = 0, message = _localization["noaccounts"].Value };
            }

            var systemUser = _context.SocialMediaAccounts.Find(Id);

            if (systemUser == null)
            {
                return new { state = 0, message = _localization["noaccountwithphone"].Value };
            }
            try
            {
                _context.Remove(systemUser);
                await _context.SaveChangesAsync();
                return new { state = 1, message = _localization["deletessuccessfuly"].Value };
            }
            catch (DbUpdateConcurrencyException)
            {
                return new { state = 0, message = _localization["anerroroccured"].Value };
            }
            
        }
    }
}
