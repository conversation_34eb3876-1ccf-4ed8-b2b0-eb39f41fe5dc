﻿@model HalalaPlusProject.CModels.TasksDetails
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["details"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h4> @localizer["details"]</h4>
    <hr />
         <div class="col-md-12">
         <div class="row">

     
     <div class="col-md-5">

          <div class="form-group">
                <label class="form-label">@localizer["taskname"]</label>
                <label  class="form-control">@Model.TaskName</label>               
            </div>
               <div class="form-group">
                <label class="form-label">@localizer["taskdescribe"]</label>
                <label  class="form-control">@Model.TaskDescribe</label>               
            </div>
               <div class="form-group">
                <label class="form-label">@localizer["taskstartsdate"] </label>
                <label  class="form-control">@Model.StartDate</label>               
            </div>
              <div class="form-group">
                <label class="form-label"> @localizer["taskenddate"] </label>
                <label  class="form-control">@Model.EndDate</label>               
            </div>
             


        </div>
         <div class="col-md-5">

             <div class="form-group">
                <label class="form-label">@localizer["tasknotes"] </label>
                <label  class="form-control">@Model.Notes</label>               
            </div>
            <div class="form-group">
                <label class="form-label">  @localizer["employee"]</label>
                <label  class="form-control">@Model.Employee</label>               
            </div>
            <div class="form-group">
                <label class="form-label">@localizer["attatchments"]</label>
                <label  class="form-control">@Model.Files</label>               
            </div>

          </div> 
          </div>
          </div>
<div>
     <form asp-action="CloseTask" class="submitfm">
        <input type="hidden" asp-for="Id" />
        <button type="submit" value="Delete" class="btn btn-primary">@localizer["closetask"]</button>|
        <a asp-action="Tasks"> @localizer["backtolist"]</a>
    </form>
</div>
