# HalalaPlus Advanced Template

قالب متطور ومحسن لنظام HalalaPlus مع مكتبات حديثة ومكونات تفاعلية متقدمة.

## 🚀 المميزات

### 🎨 التصميم
- **تصميم عصري**: مبني على Soft UI Dashboard مع تحسينات مخصصة
- **دعم RTL كامل**: مصمم خصيصاً للغة العربية
- **ألوان متناسقة**: نفس الألوان المستخدمة في المشروع الأصلي
- **استجابة كاملة**: يعمل بشكل مثالي على جميع الأجهزة
- **انيميشن متقدم**: حركات ناعمة وتفاعلية

### 📚 المكتبات المدمجة
- **Bootstrap 5.3.2**: أحدث إصدار من Bootstrap
- **jQuery 3.7.1**: للتفاعل والتحكم في DOM
- **SweetAlert2**: تنبيهات جميلة وتفاعلية
- **Toastr**: إشعارات منبثقة أنيقة
- **Dropzone**: رفع الملفات بالسحب والإفلات
- **DataTables**: جداول بيانات متقدمة
- **Select2**: قوائم منسدلة محسنة
- **AOS**: انيميشن عند التمرير
- **Font Awesome & Bootstrap Icons**: مجموعة شاملة من الأيقونات

### 🛠️ المكونات المتقدمة
- **Sidebar متجاوب**: يعمل بشكل مثالي على الأجهزة المحمولة
- **Navbar ديناميكي**: مع بحث وإشعارات
- **Cards تفاعلية**: بطاقات بانيميشن hover
- **Forms ذكية**: مع validation في الوقت الفعلي
- **Tables متقدمة**: مع فلترة وترتيب
- **Progress bars**: أشرطة تقدم متحركة
- **Timeline**: خط زمني تفاعلي

## 📁 هيكل الملفات

```
template/
├── index.html              # الصفحة الرئيسية
├── components.html         # صفحة عرض المكونات
├── css/
│   └── style.css          # الأنماط المخصصة
├── js/
│   └── script.js          # الوظائف JavaScript
├── assets/
│   └── img/
│       └── placeholder.svg # صورة توضيحية
└── README.md              # هذا الملف
```

## 🎨 نظام الألوان

القالب يستخدم نفس الألوان من المشروع الأصلي:

```css
--bs-primary: #cb0c9f      /* اللون الأساسي */
--bs-secondary: #8392AB    /* اللون الثانوي */
--bs-success: #82d616      /* لون النجاح */
--bs-info: #17c1e8         /* لون المعلومات */
--bs-warning: #fbcf33      /* لون التحذير */
--bs-danger: #ea0606       /* لون الخطر */
--bs-dark: #344767         /* اللون الداكن */
```

## 🚀 كيفية الاستخدام

### 1. فتح الملفات
- افتح `index.html` للصفحة الرئيسية
- افتح `components.html` لعرض جميع المكونات

### 2. تخصيص الألوان
يمكنك تعديل الألوان في ملف `css/style.css`:

```css
:root {
    --bs-primary: #your-color;
    --bs-secondary: #your-color;
    /* ... باقي الألوان */
}
```

### 3. إضافة مكونات جديدة
استخدم الكلاسات الجاهزة:

```html
<!-- بطاقة بسيطة -->
<div class="card">
    <div class="card-header">
        <h6>عنوان البطاقة</h6>
    </div>
    <div class="card-body">
        محتوى البطاقة
    </div>
</div>

<!-- زر بتدرج لوني -->
<button class="btn bg-gradient-primary">زر جميل</button>

<!-- أيقونة مع خلفية -->
<div class="icon icon-lg bg-gradient-success">
    <i class="fas fa-check"></i>
</div>
```

## 📱 الاستجابة للأجهزة

القالب مصمم ليعمل بشكل مثالي على:
- **Desktop**: شاشات كبيرة (1200px+)
- **Tablet**: أجهزة لوحية (768px - 1199px)
- **Mobile**: هواتف ذكية (أقل من 768px)

### نقاط التوقف (Breakpoints):
```css
/* Mobile First */
@media (min-width: 576px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 992px) { /* lg */ }
@media (min-width: 1200px) { /* xl */ }
@media (min-width: 1400px) { /* xxl */ }
```

## 🔧 الوظائف JavaScript

### إشعارات Toastr
```javascript
// إشعار نجاح
showNotification('success', 'تم!', 'تم حفظ البيانات بنجاح');

// إشعار خطأ
showNotification('error', 'خطأ!', 'فشل في حفظ البيانات');
```

### تنبيهات SweetAlert2
```javascript
// تنبيه بسيط
showAlert('success', 'تم!', 'تم تنفيذ العملية بنجاح');

// تنبيه تأكيد
showAlert('question', 'تأكيد', 'هل أنت متأكد؟', function(result) {
    if (result.isConfirmed) {
        // تنفيذ العملية
    }
});
```

### جداول البيانات
```javascript
$('#myTable').DataTable({
    "language": {
        "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json"
    },
    "responsive": true,
    "pageLength": 10
});
```

## 🎯 أمثلة الاستخدام

### 1. إنشاء صفحة جديدة
انسخ هيكل `index.html` وعدل المحتوى:

```html
<!-- نسخ الـ head والـ sidebar -->
<!-- تعديل المحتوى في main -->
<main class="main-content">
    <div class="container-fluid py-4">
        <!-- المحتوى الجديد هنا -->
    </div>
</main>
```

### 2. إضافة نموذج
```html
<form id="myForm">
    <div class="input-group input-group-outline mb-3">
        <label class="form-label">اسم الحقل</label>
        <input type="text" class="form-control" required>
    </div>
    <button type="submit" class="btn bg-gradient-primary">حفظ</button>
</form>
```

### 3. إضافة جدول
```html
<div class="table-responsive">
    <table class="table table-striped" id="myTable">
        <thead>
            <tr>
                <th>العمود 1</th>
                <th>العمود 2</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <!-- البيانات -->
        </tbody>
    </table>
</div>
```

## 🔄 التحديثات المستقبلية

- [ ] إضافة Dark Mode
- [ ] مكونات Chart.js للرسوم البيانية
- [ ] نظام إشعارات في الوقت الفعلي
- [ ] مكونات تقويم متقدمة
- [ ] نظام رفع ملفات محسن
- [ ] مكونات خرائط تفاعلية

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع ملف `components.html` لأمثلة شاملة
- تحقق من console المتصفح للأخطاء
- تأكد من تحميل جميع المكتبات بشكل صحيح

## 📄 الترخيص

هذا القالب مبني على:
- Bootstrap (MIT License)
- Soft UI Dashboard (MIT License)
- مكتبات أخرى مفتوحة المصدر

---

**تم تطويره خصيصاً لمشروع HalalaPlus** 🚀
