﻿@model IEnumerable<HalalaPlusProject.Models.Team>

@{
    ViewData["Title"] = "الفريق";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<p>
    <a asp-action="Create">انشاء عضو </a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
               الاسم
            </th>
            <th>
               الاسم انجليزي
            </th>
            <th>
                صورة
            </th>
            <th>
               الوظيفة
            </th>
            <th>
               الوظيفة انجليزي
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EnName)
            </td>
            <td>
                <img width="100px" src="~/img/@item.Image" />
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Major)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EnMajor)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.Id">تعديل</a> |
                <a asp-action="Delete" asp-route-id="@item.Id">حـذف</a>
            </td>
        </tr>
}
    </tbody>
</table>
