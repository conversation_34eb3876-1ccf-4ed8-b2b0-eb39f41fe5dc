﻿@model HalalaPlusProject.Models.Achievement

@{
    ViewData["Title"] = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Details</h1>

<div>
    <h4>Achievement</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Title)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Title)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.EnTitle)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnTitle)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Details)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Details)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.EnDetails)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EnDetails)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Image)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Image)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
