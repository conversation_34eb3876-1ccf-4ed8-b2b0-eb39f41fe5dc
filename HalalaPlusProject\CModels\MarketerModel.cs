﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    public class MarketerUserModel
    {
        public long? Id { get; set; }
        [Required]
        public string? Name { get; set; }
        //[Required]
        public int? Nationality { get; set; }
        
        public string? IdentityNo { get; set; }
       
        public DateOnly? BirthDate { get; set; }
        
        public string? PhoneNo { get; set; }
        [Required]
        [RegularExpression(@"\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b", ErrorMessage = " يجب اضافة ايميل صالح")]
        public string? Email { get; set; }
        [Required]
        public string? UserName { get; set; }
    
        
        public string? DiscountCode { get; set; }
        //[Required(ErrorMessage = "يجب تحديد الكمية")]
        public double? Amount { get; set; }
        
        public double? Precentage { get; set; }



    }
    public class MarketerModel: MarketerUserModel
    {

        [Required]
        [RegularExpression(@"^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{8,}$", ErrorMessage = "يجب ان تحتوي كلمة المرور على ارقام وحروف كبيرة وصغيرة ورموز")]
        public string? Password { get; set; }
      
       


    }


    public class MarketerIndexModel:UsersModel
    {
        public string state { get; set; }
        public string OrderStatus { get; set; }
        public string aspid { get; set; }
        public bool hasComplatedDaTa { get; set; }
    }
    public class BusinessIndexModel:UsersModel
    {
        public string state { get; set; }
        public bool hasComplatedDaTa { get; set; }

    }

    public class MarketerDetailsModel
    {
       


    }
}
