﻿using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    public class MarketerFinanceModel
    {
        //[Display(Name = "المبلغ المستحق")]
        public string? Due { get; set; }
        public long? Id { get; set; }
        //[Display(Name = "المبلغ المصروف")]
        public double  Outcome { get; set; }
        //[Display(Name = "المتبقي")]
      
        public string? Remain { get; set; }
        //[Display(Name = "رقم السجل")]
        public string? TransNo { get; set; }
      

    }
}
