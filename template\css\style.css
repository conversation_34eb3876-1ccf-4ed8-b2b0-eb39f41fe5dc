/* HalalaPlus Advanced Template - Custom Styles */

/* CSS Variables - Matching Project Colors */
:root {
    /* Primary Colors from Project */
    --bs-primary: #cb0c9f;
    --bs-secondary: #8392AB;
    --bs-success: #82d616;
    --bs-info: #17c1e8;
    --bs-warning: #fbcf33;
    --bs-danger: #ea0606;
    --bs-light: #e9ecef;
    --bs-dark: #344767;
    --bs-white: #fff;
    
    /* Gradient Colors */
    --primary-gradient: linear-gradient(310deg, #7928CA 0%, #FF0080 100%);
    --secondary-gradient: linear-gradient(310deg, #627594 0%, #A8B8D8 100%);
    --info-gradient: linear-gradient(310deg, #2152ff 0%, #21d4fd 100%);
    --success-gradient: linear-gradient(310deg, #17ad37 0%, #98ec2d 100%);
    --danger-gradient: linear-gradient(310deg, #ea0606 0%, #ff667c 100%);
    --warning-gradient: linear-gradient(310deg, #f53939 0%, #fbcf33 100%);
    --dark-gradient: linear-gradient(310deg, #141727 0%, #3A416F 100%);
    
    /* Soft UI Colors */
    --soft-background-color: #ffffff;
    --font-color: #67748e;
    --h-color: #344767;
    
    /* Shadows */
    --box-shadow: 0 20px 27px 0 rgba(0, 0, 0, 0.05);
    --box-shadow-lg: 0 20px 40px 0 rgba(0, 0, 0, 0.1);
    --navbar-box-shadow: 0 20px 27px 0 rgba(0, 0, 0, 0.05);
    
    /* Border Radius */
    --border-radius: 0.75rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 1.5rem;
    
    /* Transitions */
    --transition: all 0.15s ease-in;
    --transition-slow: all 0.3s ease-in-out;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', 'Open Sans', sans-serif;
    background-color: #f8f9fa;
    color: var(--font-color);
    line-height: 1.6;
    overflow-x: hidden;
}

/* RTL Support */
.rtl {
    direction: rtl;
    text-align: right;
}

/* Sidebar Styles */
.sidenav {
    background: var(--dark-gradient);
    box-shadow: var(--box-shadow-lg);
    border-radius: var(--border-radius-xl);
    transition: var(--transition-slow);
    z-index: 990;
    max-width: 15.625rem;
    transform: translateX(0);
}

.sidenav .navbar-brand {
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidenav .navbar-brand img {
    max-height: 3rem;
    filter: brightness(0) invert(1);
}

.sidenav .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.675rem 1rem;
    margin: 0 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    display: flex;
    align-items: center;
}

.sidenav .navbar-nav .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.sidenav .navbar-nav .nav-link.active {
    background: var(--primary-gradient);
    color: #fff;
    box-shadow: var(--box-shadow);
    font-weight: 600;
}

.sidenav .navbar-nav .nav-link i {
    font-size: 1rem;
    margin-left: 0.5rem;
    width: 1.5rem;
    text-align: center;
}

/* Navbar Styles */
.navbar-main {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--navbar-box-shadow);
    transition: var(--transition);
}

.navbar-main .navbar-nav .nav-link {
    color: var(--h-color);
    transition: var(--transition);
}

.navbar-main .navbar-nav .nav-link:hover {
    color: var(--bs-primary);
}

/* Breadcrumb Styles */
.breadcrumb-item a {
    color: var(--font-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    color: var(--bs-primary);
}

/* Card Styles */
.card {
    background: #fff;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--box-shadow);
    border: none;
    transition: var(--transition-slow);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background: transparent;
    border-bottom: none;
    padding: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    text-transform: none;
    transition: var(--transition);
    border: none;
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background: var(--primary-gradient);
    color: #fff;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.bg-gradient-primary {
    background: var(--primary-gradient) !important;
}

.bg-gradient-secondary {
    background: var(--secondary-gradient) !important;
}

.bg-gradient-info {
    background: var(--info-gradient) !important;
}

.bg-gradient-success {
    background: var(--success-gradient) !important;
}

.bg-gradient-warning {
    background: var(--warning-gradient) !important;
}

.bg-gradient-danger {
    background: var(--danger-gradient) !important;
}

.bg-gradient-dark {
    background: var(--dark-gradient) !important;
}

/* Icon Styles */
.icon {
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-xl);
    color: #fff;
    font-size: 1.25rem;
}

.icon-lg {
    width: 4rem;
    height: 4rem;
    font-size: 1.5rem;
}

/* Progress Bar Styles */
.progress {
    height: 0.375rem;
    border-radius: var(--border-radius);
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: var(--border-radius);
    transition: var(--transition-slow);
}

/* Table Styles */
.table {
    color: var(--font-color);
}

.table thead th {
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(203, 12, 159, 0.05);
}

/* Avatar Styles */
.avatar {
    border-radius: 50%;
    transition: var(--transition);
}

.avatar:hover {
    transform: scale(1.1);
}

.avatar-xs {
    width: 1.5rem;
    height: 1.5rem;
}

.avatar-sm {
    width: 2rem;
    height: 2rem;
}

/* Timeline Styles */
.timeline {
    position: relative;
    padding: 0;
}

.timeline-block {
    position: relative;
    padding-left: 3rem;
}

.timeline-step {
    position: absolute;
    left: 0;
    top: 0;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--success-gradient);
    color: #fff;
    font-size: 0.875rem;
}

/* Dropdown Styles */
.dropdown-menu {
    border: none;
    box-shadow: var(--box-shadow-lg);
    border-radius: var(--border-radius-lg);
    padding: 0.5rem;
}

.dropdown-item {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: rgba(203, 12, 159, 0.1);
    color: var(--bs-primary);
}

/* Badge Styles */
.badge {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 0.375rem 0.75rem;
}

/* Form Styles */
.form-control {
    border: 1px solid #d2d6da;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background-color: #fff;
}

.form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(203, 12, 159, 0.25);
}

.input-group-outline {
    position: relative;
}

.input-group-outline .form-label {
    position: absolute;
    top: 50%;
    right: 1rem;
    transform: translateY(-50%);
    color: var(--font-color);
    transition: var(--transition);
    pointer-events: none;
    background: #fff;
    padding: 0 0.5rem;
}

.input-group-outline .form-control:focus + .form-label,
.input-group-outline .form-control:not(:placeholder-shown) + .form-label {
    top: 0;
    font-size: 0.75rem;
    color: var(--bs-primary);
}

/* Dropzone Styles */
.dropzone {
    border: 2px dashed #d2d6da;
    border-radius: var(--border-radius-xl);
    background: #f8f9fa;
    padding: 3rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.dropzone:hover {
    border-color: var(--bs-primary);
    background: rgba(203, 12, 159, 0.05);
}

.dropzone .dz-message {
    color: var(--font-color);
}

.dropzone .dz-message i {
    color: var(--bs-primary);
}

/* Responsive Styles */
@media (max-width: 1199.98px) {
    .sidenav {
        transform: translateX(-100%);
    }
    
    .g-sidenav-pinned .sidenav {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0 !important;
    }
}

/* Mobile Sidebar Toggle */
.sidenav-toggler-inner {
    width: 20px;
    height: 15px;
    position: relative;
    transform: rotate(0deg);
    transition: 0.5s ease-in-out;
    cursor: pointer;
}

.sidenav-toggler-line {
    display: block;
    position: absolute;
    height: 2px;
    width: 100%;
    background: var(--h-color);
    border-radius: 2px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: 0.25s ease-in-out;
}

.sidenav-toggler-line:nth-child(1) {
    top: 0px;
}

.sidenav-toggler-line:nth-child(2) {
    top: 6px;
}

.sidenav-toggler-line:nth-child(3) {
    top: 12px;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--bs-primary);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a0096b;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(203, 12, 159, 0.3);
    border-radius: 50%;
    border-top-color: var(--bs-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Toastr Custom Styles */
.toast-success {
    background: var(--success-gradient) !important;
}

.toast-error {
    background: var(--danger-gradient) !important;
}

.toast-info {
    background: var(--info-gradient) !important;
}

.toast-warning {
    background: var(--warning-gradient) !important;
}

/* Additional Advanced Components */

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    border: none;
    box-shadow: var(--box-shadow-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: var(--transition);
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 25px 50px rgba(203, 12, 159, 0.3);
}

/* Advanced Card Hover Effects */
.card-hover-lift {
    transition: var(--transition-slow);
}

.card-hover-lift:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

/* Glassmorphism Effect */
.glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Gradient Text */
.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

/* Advanced Buttons */
.btn-glow {
    box-shadow: 0 0 20px rgba(203, 12, 159, 0.5);
    transition: var(--transition);
}

.btn-glow:hover {
    box-shadow: 0 0 30px rgba(203, 12, 159, 0.8);
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Advanced Form Styles */
.form-floating-custom {
    position: relative;
}

.form-floating-custom input {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem;
}

.form-floating-custom label {
    position: absolute;
    top: 0;
    right: 0.75rem;
    height: 100%;
    padding: 1rem 0.75rem;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: var(--transition);
}

.form-floating-custom input:focus ~ label,
.form-floating-custom input:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Status Indicators */
.status-indicator {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    display: inline-block;
    margin-left: 0.5rem;
}

.status-online { background-color: #82d616; }
.status-offline { background-color: #ea0606; }
.status-away { background-color: #fbcf33; }
.status-busy { background-color: #cb0c9f; }

/* Advanced Tooltips */
.tooltip-custom {
    position: relative;
    cursor: pointer;
}

.tooltip-custom::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    right: 50%;
    transform: translateX(50%);
    background: var(--h-color);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.tooltip-custom::after {
    content: '';
    position: absolute;
    bottom: 115%;
    right: 50%;
    transform: translateX(50%);
    border: 5px solid transparent;
    border-top-color: var(--h-color);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.tooltip-custom:hover::before,
.tooltip-custom:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -0.5rem;
    left: -0.5rem;
    background: var(--danger-gradient);
    color: white;
    border-radius: 50%;
    width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    animation: pulse 2s infinite;
}

/* Advanced Search */
.search-advanced {
    position: relative;
}

.search-advanced input {
    padding-right: 3rem;
}

.search-advanced .search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--font-color);
}

.search-results {
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-result-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: var(--transition);
}

.search-result-item:hover {
    background-color: rgba(203, 12, 159, 0.05);
}

.search-result-item:last-child {
    border-bottom: none;
}

/* Utility Classes */
.rotate-180 { transform: rotate(180deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-45 { transform: rotate(45deg); }

.scale-hover:hover { transform: scale(1.05); }
.scale-sm { transform: scale(0.9); }
.scale-lg { transform: scale(1.1); }

.blur-sm { filter: blur(2px); }
.blur-md { filter: blur(4px); }
.blur-lg { filter: blur(8px); }

.grayscale { filter: grayscale(100%); }
.sepia { filter: sepia(100%); }

/* Print Styles */
@media print {
    .sidenav,
    .navbar-main,
    .fab,
    .btn,
    .no-print {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
