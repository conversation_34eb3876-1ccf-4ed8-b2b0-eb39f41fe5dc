﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using HalalaPlusProject.Models;
using HalalaPlusProject.CModels;
using Microsoft.AspNetCore.Authorization;

namespace HalalaPlusProject.Controllers
{
    /// <summary>
    /// إدارة شركات الدفع، وتوفير عمليات الإنشاء والقراءة والتحديث والحذف (CRUD).
    /// </summary>
    [Authorize]
    public class PayingCompaniesController : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public PayingCompaniesController(HalalaPlusdbContext context)
        {
            _context = context;
        }

        // GET: PayingCompaniesTables
        /// <summary>
        /// عرض قائمة بجميع شركات الدفع.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة شركات الدفع.</returns>
        public async Task<IActionResult> Index()
        {
            return _context.PayingCompaniesTables != null ?
                        View(await _context.PayingCompaniesTables.Select(p => new PayingCompaniesModel { Id = p.Id, ConnectionCode = p.ConnectionCode, Name = p.Name, OperationDetils = p.OperationDetils }).ToListAsync()) :
                        Problem("Entity set 'HalalaPlusdbContext.PayingCompaniesTables'  is null.");
        }

        // GET: PayingCompaniesTables/Details/5
        /// <summary>
        /// عرض التفاصيل الخاصة بشركة دفع معينة.
        /// </summary>
        /// <param name="id">معرف شركة الدفع المراد عرض تفاصيلها.</param>
        /// <returns>عرض يحتوي على تفاصيل شركة الدفع، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null || _context.PayingCompaniesTables == null)
            {
                return NotFound();
            }

            var payingCompaniesTable = await _context.PayingCompaniesTables.Select(p => new PayingCompaniesModel { Id = p.Id, ConnectionCode = p.ConnectionCode, Name = p.Name, OperationDetils = p.OperationDetils })
                .FirstOrDefaultAsync(m => m.Id == id);

            if (payingCompaniesTable == null)
            {
                return NotFound();
            }

            return View(payingCompaniesTable);
        }

        // GET: PayingCompaniesTables/Create
        /// <summary>
        /// عرض نموذج إنشاء شركة دفع جديدة.
        /// </summary>
        /// <returns>عرض يحتوي على نموذج الإنشاء.</returns>
        public IActionResult Create()
        {
            return View();
        }

        // POST: PayingCompaniesTables/Create
        /// <summary>
        /// معالجة عملية إنشاء شركة دفع جديدة وحفظها في قاعدة البيانات.
        /// </summary>
        /// <param name="model">البيانات الخاصة بشركة الدفع الجديدة.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة الشركات؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(PayingCompaniesModel model)
        {
            if (ModelState.IsValid)
            {
                var temp = new PayingCompaniesTable()
                {
                    ConnectionCode = model.ConnectionCode,
                    OperationDetils = model.OperationDetils,
                    Name = model.Name,
                    EnName = model.EnName,

                };

                _context.Add(temp);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(model);
        }

        // GET: PayingCompaniesTables/Edit/5
        /// <summary>
        /// عرض نموذج تعديل بيانات شركة دفع حالية.
        /// </summary>
        /// <param name="id">معرف شركة الدفع المراد تعديلها.</param>
        /// <returns>عرض يحتوي على بيانات الشركة في نموذج التعديل، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null || _context.PayingCompaniesTables == null)
            {
                return NotFound();
            }

            var payingCompaniesTable = await _context.PayingCompaniesTables.FindAsync(id);
            if (payingCompaniesTable == null)
            {
                return NotFound();
            }

            return View(new PayingCompaniesModel { Id = payingCompaniesTable.Id, ConnectionCode = payingCompaniesTable.ConnectionCode, Name = payingCompaniesTable.Name, OperationDetils = payingCompaniesTable.OperationDetils });
        }

        // POST: PayingCompaniesTables/Edit/5
        /// <summary>
        /// معالجة التعديلات المقدمة لشركة دفع وحفظها في قاعدة البيانات.
        /// </summary>
        /// <param name="id">معرف شركة الدفع التي يتم تعديلها.</param>
        /// <param name="model">البيانات المحدثة لشركة الدفع.</param>
        /// <returns>إذا نجحت العملية، يتم عرض قائمة الشركات؛ وإلا، يتم إعادة عرض النموذج مع الأخطاء.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, PayingCompaniesModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var temp = _context.PayingCompaniesTables.Find(id);
                    temp.Name = model.Name;
                    temp.ConnectionCode = model.ConnectionCode;
                    temp.OperationDetils = model.OperationDetils;
                    temp.EnName = model.EnName;
                    _context.Update(temp);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PayingCompaniesTableExists(model.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(model);
        }

        // GET: PayingCompaniesTables/Delete/5
        /// <summary>
        /// عرض صفحة تأكيد حذف شركة دفع.
        /// </summary>
        /// <param name="id">معرف شركة الدفع المراد حذفها.</param>
        /// <returns>عرض يحتوي على تفاصيل الشركة لتأكيد الحذف، أو نتيجة `NotFound`.</returns>
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null || _context.PayingCompaniesTables == null)
            {
                return NotFound();
            }

            var payingCompaniesTable = await _context.PayingCompaniesTables
                .FirstOrDefaultAsync(m => m.Id == id);
            if (payingCompaniesTable == null)
            {
                return NotFound();
            }

            return View(new PayingCompaniesModel { Id = payingCompaniesTable.Id, ConnectionCode = payingCompaniesTable.ConnectionCode, Name = payingCompaniesTable.Name, OperationDetils = payingCompaniesTable.OperationDetils });
        }

        // POST: PayingCompaniesTables/Delete/5
        /// <summary>
        /// تنفيذ عملية حذف شركة الدفع من قاعدة البيانات بعد التأكيد.
        /// </summary>
        /// <param name="id">معرف شركة الدفع المراد حذفها.</param>
        /// <returns>بعد إتمام العملية بنجاح، يتم عرض صفحة قائمة الشركات.</returns>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            if (_context.PayingCompaniesTables == null)
            {
                return Problem("Entity set 'HalalaPlusdbContext.PayingCompaniesTables'  is null.");
            }
            var payingCompaniesTable = await _context.PayingCompaniesTables.FindAsync(id);
            if (payingCompaniesTable != null)
            {
                _context.PayingCompaniesTables.Remove(payingCompaniesTable);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        /// <summary>
        /// التحقق من وجود شركة دفع في قاعدة البيانات بالمعرف المحدد.
        /// </summary>
        /// <param name="id">معرف الشركة للتحقق منه.</param>
        /// <returns>إرجاع 'true' إذا كانت الشركة موجودة، وإلا 'false'.</returns>
        private bool PayingCompaniesTableExists(int id)
        {
            return (_context.PayingCompaniesTables?.Any(e => e.Id == id)).GetValueOrDefault();
        }
    }
}