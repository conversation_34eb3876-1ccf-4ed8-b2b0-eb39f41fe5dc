﻿using HalalaPlusProject.Models;

namespace HalalaPlusProject.CustomClasses
{
    public class ServiceProvidersClass
    {
        public async Task<bool> disable(HalalaPlusdbContext _context, long id)
        {
            try
            {
                var temp = _context.SystemUsers.Find(id);
                if (temp == null) return false;
                if (temp.Status == "A") temp.Status = "D";
                else temp.Status = "A";
                _context.Update(temp);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
        public async Task<bool> disables(HalalaPlusdbContext _context, long id)
        {
            try
            {
                var temp = _context.DiscountsTables.Find(id);
                if (temp == null) return false;
                if (temp.IsActive == true) temp.IsActive =false;
                else temp.IsActive = true;
                _context.Update(temp);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
