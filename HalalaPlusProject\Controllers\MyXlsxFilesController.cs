﻿using ClosedXML.Excel;
using HalalaPlusProject.Areas.Identity.Data;
using HalalaPlusProject.Controllers;
using HalalaPlusProject.CustomClasses;
using HalalaPlusProject.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Data;

[Authorize]
public class MyXlsxFilesController : Controller
{
    private readonly HalalaPlusProject.Models.HalalaPlusdbContext _context;
    private readonly UserManager<HalalaPlusProjectUser> _userManager;
    private readonly IWebHostEnvironment _hosting;
    private readonly IStringLocalizer<ServiceProviderController> _localization;
    public MyXlsxFilesController(HalalaPlusProject.Models.HalalaPlusdbContext context, IStringLocalizer<ServiceProviderController> _localization,UserManager<HalalaPlusProjectUser> userManager, IWebHostEnvironment hosting)
    {
        _context = context;
        _userManager = userManager;
        _hosting = hosting;  
        this._localization = _localization;
    }

    public IActionResult Index()
    {
        return View();
    }

    [HttpPost]
    public async Task<IActionResult> ImportExcel(IFormFile file, IFormFile logo)
    {
        if (file == null || file.Length == 0 || !file.FileName.EndsWith(".xlsx"))
        {
            TempData["Message"] = "Invalid or empty Excel file.";
            return RedirectToAction("Index");
        }
        using var stream = new MemoryStream();
        await file.CopyToAsync(stream);

        var table = new DataTable();
        using var workbook = new XLWorkbook(stream);
        var worksheet = workbook.Worksheet(1);
        var firstRow = worksheet.FirstRowUsed();
        var columnCount = firstRow.CellsUsed().Count();

        // Create columns dynamically
        foreach (var cell in firstRow.CellsUsed())table.Columns.Add(cell.GetString());

        foreach (var row in worksheet.RowsUsed().Skip(1))
        {
            var dataRow = table.NewRow();
            for (int i = 0; i < columnCount; i++)
                dataRow[i] = row.Cell(i + 1).GetString();
            table.Rows.Add(dataRow);
             updateDiscount(dataRow);
        }
        TempData["Message"] = $"Imported {table.Rows.Count} rows with {table.Columns.Count} columns.";
        ViewBag.Table = table;
        return View("Index");
    }
    public static DateOnly ParseToDateOnly(object contractDate)
    {

        var date = DateTime.Parse(contractDate.ToString());
        try
        {
            var t = date.ToString("yyyy-MM-dd");
            return DateOnly.Parse(t);
        }
        catch (Exception ex)
        {
            TelegramHandler1.Instance.SendSingleSMSwithoutasync(ex.Message + "\tStack Trace:\t" + ex.StackTrace);
            return DateOnly.Parse("1970-01-01");
        }

    }
    private void updateDiscount(DataRow row)
    {
        try
        {
            var mobile = row.ItemArray[10]?.ToString();
            mobile = (mobile.StartsWith("05")) ? "966" + mobile.Substring(1) : (mobile.StartsWith("5")) ? "966" + mobile : mobile;
            string name = row.ItemArray[3]?.ToString();
            var pr = _context.SystemUsers.Where(o => o.PhoneNo == mobile && o.Name == name).FirstOrDefault();
            if (pr == null)
            {
                TelegramHandler1.Instance.SendSingleSMSwithoutasync($"record not found : phone:{row.ItemArray[10]?.ToString()} ==name:{row.ItemArray[3]?.ToString()}");
                return;
            }
            var all = _context.DiscountsTables.Where(oo => oo.UserId == pr.Id ).ToList();
            if (all == null)
            {
                TelegramHandler1.Instance.SendSingleSMSwithoutasync($"record not found in discount table : phone:{row.ItemArray[10]?.ToString()} ==name:{row.ItemArray[3]?.ToString()}");
                return;
            }
            foreach (var item in all)
            {
                var discountPercentage = decimal.TryParse(row.ItemArray[19]?.ToString(), out var discount) ? discount : 0;
                if (discountPercentage < 1) item.Discount = (double)discountPercentage * 100;
                else item.Discount = (double)discountPercentage;
                _context.DiscountsTables.Update(item);
                _context.SaveChanges();
            }
            
        }
        catch (Exception ex)
        {
            TelegramHandler1.Instance.SendSingleSMSwithoutasync(ex.Message + "\tStack Trace:\t" + ex.StackTrace);
        }
    }
    private async Task<(bool success, string message)> SaveRowToDatabase(DataRow row, IFormFile logo)
    {
        try
        {
            var mobile = row.ItemArray[10]?.ToString();
            var discountPercentage = decimal.TryParse(row.ItemArray[19]?.ToString(), out var discount) ? discount : 0;
            var terms = row.ItemArray[20]?.ToString();
            mobile = (mobile.StartsWith("05")) ? "966" + mobile.Substring(1) : (mobile.StartsWith("5")) ? "966" + mobile : mobile;
            var userExists = await _userManager.FindByNameAsync(mobile);
            if (userExists != null)
            {
                ProvidersClass ob = new ProvidersClass(_context, _hosting);
                var creatorId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                if (! await ob.Insert2(getObject(row), (int)discountPercentage, terms, userExists, creatorId))
                    return (false, _localization["errorwillsaving1"].Value);
                TelegramHandler1.Instance.SendSingleSMSwithoutasync($"added half : phone:{row.ItemArray[10]?.ToString()} ==name:{row.ItemArray[3]?.ToString()}");

                return (true, _localization["addedsuccessfuly"].Value);
            }
            else
            {
                HalalaPlusProjectUser user = new()
                {
                    Email = row.ItemArray[14]?.ToString(),
                    SecurityStamp = Guid.NewGuid().ToString(),
                    PhoneNumber = mobile,
                    //Image = url,
                    FullName = row.ItemArray[3]?.ToString(),
                    UserName = mobile
                };
                var result = await _userManager.CreateAsync(user, "User@12345");
                if (!result.Succeeded) return (false, _localization["errorwillsaving1"].Value);
                try
                {
                    string url = HandleImages.SaveImage(logo, "images1", _hosting);
                    user.Image = url;
                    await _userManager.UpdateAsync(user);
                    await _userManager.AddToRoleAsync(user, "Provider");
                    ProvidersClass ob = new ProvidersClass(_context, _hosting);
                    var creatorId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                    if (!await ob.Insert2(getObject(row), (int)discountPercentage, terms, user, creatorId))
                    {
                        await _userManager.DeleteAsync(user);
                        return (false, _localization["errorwillsaving1"].Value);
                    }
                    TelegramHandler1.Instance.SendSingleSMSwithoutasync($"added full : phone:{row.ItemArray[10]?.ToString()} ==name:{row.ItemArray[3]?.ToString()}");

                    return (true, _localization["addedsuccessfuly"].Value);
                }
                catch (Exception ex)
                {
                    HandleImages.RemoveImage(user.Image, "images1", _hosting);
                    TelegramHandler1.Instance.SendSingleSMSwithoutasync(ex.Message + "\tStack Trace:\t" + ex.StackTrace);
                    await _userManager.DeleteAsync(user);
                    return (false, _localization["errorwillsaving1"].Value);
                }
            }
        }
        catch (Exception ex)
        {
            TelegramHandler1.Instance.SendSingleSMSwithoutasync(ex.Message + "\tStack Trace:\t" + ex.StackTrace);
            TelegramHandler1.Instance.SendSingleSMSwithoutasync($"record : phone:{row.ItemArray[10]?.ToString()} ==name:{row.ItemArray[3]?.ToString()}");

            return (false, "error");
        }




    }

    private HalalaPlusProject.CModels.ServiceProvidersCreate getObject(DataRow row)
    {
        var mobile = row.ItemArray[10]?.ToString();
        mobile = (mobile.StartsWith("05")) ? "966" + mobile.Substring(1) : (mobile.StartsWith("5")) ? "966" + mobile : mobile;
        var model = new HalalaPlusProject.CModels.ServiceProvidersCreate
        {
            PhoneNumber = mobile,
            Email = row.ItemArray[14]?.ToString(),
            ContractNo = row.ItemArray[0]?.ToString(),
            Activity = (!string.IsNullOrWhiteSpace(row.ItemArray[5]?.ToString())) ? int.Parse(row.ItemArray[5]?.ToString()) : null,
            Name = row.ItemArray[3]?.ToString(),
            EnName = row.ItemArray[3]?.ToString(),
            BusnissNo = row.ItemArray[4]?.ToString(),
            ServiceProviderRepresent = row.ItemArray[7]?.ToString(),
            City = (!string.IsNullOrWhiteSpace(row.ItemArray[8]?.ToString())) ? int.Parse(row.ItemArray[8]?.ToString()) : null,
            EnterprisePhoneNo = mobile,
            overview = row.ItemArray[13]?.ToString(),
            enoverview = row.ItemArray[24]?.ToString(),
            bnifitfrompoints = row.ItemArray[22]?.ToString(),
            enbnifitfrompoints = row.ItemArray[23]?.ToString(),
        };
        model.ContractDate = (!string.IsNullOrWhiteSpace(row.ItemArray[1].ToString())) ? ParseToDateOnly(row.ItemArray[1].ToString()) : null;
        model.ContractEndDate = (!string.IsNullOrWhiteSpace(row.ItemArray[2].ToString())) ? ParseToDateOnly(row.ItemArray[2].ToString()) : null;
        return model;

    }
}
