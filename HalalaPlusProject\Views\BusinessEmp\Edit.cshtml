﻿@model HalalaPlusProject.CModels.BusineesEmployeesModel
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["edit"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["edit"]</h1>

<h4>@localizer["theemployee"]</h4>
<hr />
<div class="row">
    <div class="col-md-8">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
             
         <div class="row">
           <div class="col-md-5">
          <div class="form-group">
                        <label asp-for="Name" class="control-label">@localizer["name"]</label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" required class="text-danger"></span>
            </div>
            
           @*  <div class="form-group">
                        <label asp-for="Nationality" class="control-label">@localizer["nationality"]</label>
                 <select asp-for="Nationality" required  class="form-select" asp-items="ViewBag.National"></select>
                  <span asp-validation-for="Nationality" required class="text-danger"></span>
            </div>

              <div class="form-group">
                        <label asp-for="IdentityNo" class="control-label">@localizer["identityno"]</label>
                <input asp-for="IdentityNo" class="form-control" />
                <span asp-validation-for="IdentityNo" class="text-danger"></span>
            </div>
            <div class="form-group">
                        <label asp-for="BirthDate" class="control-label">@localizer["birthdate"]</label>
                <input asp-for="BirthDate" type="date" class="form-control" />
                <span asp-validation-for="BirthDate" class="text-danger"></span>
            </div>
     <div class="form-group">
                        <label asp-for="Salary" class="control-label">@localizer["salary"] </label>
                <input asp-for="Salary" placeholder="2000" class="form-control" />
                <span asp-validation-for="Salary" class="text-danger"></span>
            </div> *@

          </div>

           <div class="col-md-5">
         
         <div class="form-group">
                        <label asp-for="PhoneNo" class="control-label">@localizer["phoneno"]</label>
                <input asp-for="PhoneNo" class="form-control" />
                <span asp-validation-for="PhoneNo" class="text-danger"></span>
            </div>
          @*   <div class="form-group">
                        <label asp-for="Email" class="control-label">@localizer["mail"]</label>
                <input asp-for="Email" readonly class="form-control" />
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>
              <div class="form-group">
                        <label asp-for="UserName" class="control-label">@localizer["username"] </label>
                <input asp-for="UserName" readonly class="form-control" />
                <span asp-validation-for="UserName" class="text-danger"></span>
            </div>
               *@
            

          </div>
             </div>
 
        
         
        
         
     
           
            <div class="form-group">
                <button type="submit" value="Save" class="btn btn-primary">@localizer["save"]</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
