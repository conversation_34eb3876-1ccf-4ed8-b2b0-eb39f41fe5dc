﻿@model IEnumerable<HalalaPlusProject.CModels.CustomerViewModel>

@{
    ViewData["Title"] = "Index";
}

<h1>العملاء والحصالات</h1>


<table class="table">
    <thead>
        <tr>
            <th>
                الاسم
            </th>
            <th>
               الجوال
            </th>
            <th>
                البريد الالكتروني
            </th>
            <th>
                عدد الحصالات
            </th>
            <th>
                اجمالي مبالغ الحصالات
            </th>

            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.CustomerName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.CustomerPhone)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.CustomerEmail)
            </td>
            <td>
                    @Html.DisplayFor(modelItem => item.CustomerMonyBoxsCount)
           </td>
            <td>
                    @Html.DisplayFor(modelItem => item.CustomerTotalAmount)
            </td>

            <td>        
                    <a asp-action="Details" class="btn btn-outline-info tablebtn" asp-route-id="@item.CustomerId">استعراض الحصالات</a>
            </td>
        </tr>
}
    </tbody>
</table>

