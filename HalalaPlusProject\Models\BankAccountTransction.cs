﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

public partial class BankAccountTransction
{
    [Key]
    public long Id { get; set; }

    [Column("Acc_Id")]
    public long? AccId { get; set; }

    [Column("DateOfOP", TypeName = "datetime")]
    public DateTime? DateOfOp { get; set; }

    public double? Amount { get; set; }

    [StringLength(50)]
    public string? OpType { get; set; }

    [StringLength(1500)]
    public string? Note { get; set; }

    public long? UserId { get; set; }

    public long? DistAccId { get; set; }

    public long? DistUserId { get; set; }

    [StringLength(1500)]
    public string? Purpose { get; set; }

    [Column("state")]
    [StringLength(50)]
    public string? State { get; set; }

    [StringLength(250)]
    public string? DistAccNo { get; set; }

    [Column("temp1")]
    [StringLength(250)]
    public string? Temp1 { get; set; }

    [Column("temp2")]
    [StringLength(250)]
    public string? Temp2 { get; set; }

    [ForeignKey("Id")]
    [InverseProperty("BankAccountTransction")]
    public virtual UserBanksAccount IdNavigation { get; set; } = null!;
}
