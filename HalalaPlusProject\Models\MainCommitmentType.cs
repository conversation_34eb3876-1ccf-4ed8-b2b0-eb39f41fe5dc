﻿using System;
using System.Collections.Generic;

namespace HalalaPlusProject.Models;

public partial class MainCommitmentType
{
    public int TypeId { get; set; }

    public string TypeName { get; set; } = null!;

    public virtual ICollection<MonthlyCommitment> MonthlyCommitments { get; set; } = new List<MonthlyCommitment>();

    public virtual ICollection<PlanAllocation> PlanAllocations { get; set; } = new List<PlanAllocation>();
}
