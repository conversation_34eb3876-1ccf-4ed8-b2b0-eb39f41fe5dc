﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Data;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    /// <summary>
    /// إدارة عرض بيانات العملاء، وتحديداً المعلومات المتعلقة بحصالاتهم (صناديق الأموال).
    /// </summary>
    public class CustomersController : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public CustomersController(HalalaPlusdbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// استرداد وعرض قائمة بالعملاء الذين لديهم حصالات، مع تفاصيل كل حصالة ومجموع المبالغ فيها.
        /// </summary>
        /// <returns>عرض يحتوي على قائمة من نماذج العملاء مع بيانات حصالاتهم.</returns>
        public async Task<IActionResult> Index()
        {
            //var systemUsers = await _context.SystemUsers
            //     .Where(op => op.AccountType == "Customer" && op.UsersMonyBoxes.Any())
            //     .ToListAsync();
            var systemUsers = await _context.SystemUsers
                .Where(op => op.AccountType == "Customer" && op.UsersMonyBoxes.Any())
                .Include(op => op.UsersMonyBoxes).Include(op => op.MonyBoxTransactions)
                .ToListAsync();

            List<CustomerViewModel> listCustomers = new List<CustomerViewModel>();
            foreach (var customer in systemUsers)
            {
                var totalAmount = customer.UsersMonyBoxes.Sum(op => op.Amount);
                var temp = new CustomerViewModel
                {
                    CustomerId = customer.Id,
                    CustomerEmail = customer.Email,
                    CustomerName = customer.Name,
                    CustomerPhone = customer.PhoneNo,
                    CustomerMonyBoxsCount = customer.UsersMonyBoxes.Count(),
                    CustomerTotalAmount = "" + ((totalAmount == null) ? (00) : (totalAmount.Value)).ToString("N2")
                };
                //  temp.MonyBoxes=await _context.UsersMonyBoxs.Where(op=>op.UserId==temp.CustomerId&&op.MonyBoxTransactions.Any()).ToListAsync();
                temp.MonyBoxes = customer.UsersMonyBoxes.ToList();
                //foreach (var item in temp.MonyBoxes)
                //{
                //    //  item.MonyBoxTransactions = await _context.MonyBoxTransactions.Where(op => op.MonyBoxId == item.Id).ToListAsync();
                //    item.MonyBoxTransactions = item.MonyBoxTransactions.ToList();
                //}

                listCustomers.Add(temp);

            }
            return View(listCustomers);
        }
        //public async Task<IActionResult> Index()
        //{
        //    return _context.CustomerMonyBoxTransactions != null ?
        //                View(await _context.CustomerMonyBoxTransactions.ToListAsync()) :
        //                Problem("Entity set 'HalalaPlusdbContext.CustomerMonyBoxTransactions'  is null.");
        //}
    }


}