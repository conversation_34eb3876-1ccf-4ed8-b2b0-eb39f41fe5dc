﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;  
using System;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
[Authorize]

public class PaymentsRequestsController : Controller
{
    private readonly HalalaPlusdbContext _context;

    public PaymentsRequestsController(HalalaPlusdbContext context)
    {
        _context = context;
    }

    public async Task<IActionResult> Index(string search)
    {
        ViewBag.Search = search;

        var query = _context.PaymentsRequests
            .Include(p => p.User)  
            .AsQueryable();

        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(p =>
                p.User.Name.Contains(search) ||
                p.User.PhoneNo.Contains(search));
        }

        var model = await query
            .Select(p => new PaymentsRequests
            {
                Id = p.Id,
                UserName = p.User.Name,
                UserPhone = p.User.PhoneNo,
                Amount = p.Amount,
                IsVerified = p.IsVerified,
                Status = p.Status,
                EntityId = p.EntityType,
                CreateAt = p.CreateAt
            })
            .OrderByDescending(p => p.CreateAt)
            .ToListAsync();

        return View(model);
    }

    [HttpGet]
    public IActionResult ExportToExcel(string search)
    {
        ExcelPackage.License.SetNonCommercialOrganization("halalplus");

        var payments = _context.PaymentsRequests
            .Include(p => p.User)
            .AsQueryable();

        if (!string.IsNullOrEmpty(search))
        {
            payments = payments.Where(p =>
                p.User.Name.Contains(search) ||
                p.User.PhoneNo.Contains(search));
        }

        var list = payments.ToList();

        using (var package = new ExcelPackage())
        {
            var worksheet = package.Workbook.Worksheets.Add("Payments");

             
            worksheet.Cells[1, 1].Value = "المستخدم";
            worksheet.Cells[1, 2].Value = "رقم الهاتف";
            worksheet.Cells[1, 3].Value = "المبلغ";
            worksheet.Cells[1, 4].Value = "نوع العملية";
            worksheet.Cells[1, 5].Value = "الحالة";
            worksheet.Cells[1, 6].Value = "تحقق";
            worksheet.Cells[1, 7].Value = "تاريخ الإنشاء";

            
            int row = 2;
            foreach (var item in list)
            {
                
                worksheet.Cells[row, 1].Value = item.User?.Name;
                worksheet.Cells[row, 2].Value = item.User?.PhoneNo;
                worksheet.Cells[row, 3].Value = item.Amount;
                worksheet.Cells[row, 4].Value = item.EntityType == "pr" ? "دفع منتج" :
                                                item.EntityType == "m" ? "شحن حصالة" : item.EntityType;
                worksheet.Cells[row, 5].Value = item.Status == "CAPTURED" ? "تمت" : "لم تتم";
                worksheet.Cells[row, 6].Value = item.IsVerified == true ? "نعم" : "لا";
                worksheet.Cells[row, 7].Value = item.CreateAt?.ToString("yyyy-MM-dd HH:mm");

                row++;
            }

            worksheet.Cells.AutoFitColumns();

            var stream = new MemoryStream(package.GetAsByteArray());
            var fileName = $"Payments_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
            return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        }
    }

}
