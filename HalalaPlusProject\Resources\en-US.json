{
  "welcome": "Welcome to the Halala Plus platform",
  "appname": "Halala Plus",
  "copyright": "All rights reserved",
  "theactivity": "Activity",
  "home": "Home",
  "home1": "Home Page",
  "privacy": "Privacy Policy",
  "vision": "Vision",
  "discounts": "Discounts",
  "coupouns": "Coupons",
  "offers": "Offers",
  "images": "Images",
  "DiscountsOrder": "Discounts Order",
  "Phonenumber": "Phone Number",
  "statistics": "statistics",
  "displayimages": "Display Image",
  "serviceprovider": "Service Provider",
  "joinwaitinglist": "Join Waiting List",
  "contactus": "Contact Us",
  "articles": "Articles",
  "userterms": "Terms of Use",
  "EnDiscountName": "English Discount Name ",
  "login": "Log in",
  "customer": "Customers",
  "cities": "Cities",
  "experts": "Experiences",
  "PaymentsRequests": "Payments Requests",
  "fininacalTranscations": "Fininacal Transcations",
  "GrantedTranscations": "Granted Transcations",
  "investmentround": "Investment round",
  "ClientsCollections": "Clients & collections",
  "joinindvisuals": "Join Hilala Plus Individuals",
  "joinservice": "Join Halala Plus Services",
  "joinwithoffers": "Join Halala Plus and enjoy the strongest discounts",
  "joinwithservice": "Join Halala Plus as a service provider",
  "registernow": "Register Vow",
  "ourvision": "Our Vision",
  "downloadapp": "Download Our Ap",
  "yourmsg": "Your Message",
  "subject": "Subject",
  "phoneno": "Phone Number",
  "email": "Email",
  "name": "Name",
  "sendbtn": "Send",
  "cardorder": "Order Card",
  "fullname": "Full Name",
  "subscribebtn": "Subscribe",
  "commercialactive": "Commercial Active",
  "chose": "choose",
  "city": "City",
  "serviceprovidername": "Service Provider Name",
  "Providerstatistics": "Provider statistics",
  "Statisticsnumbers": "Statistics in numbers",
  "Totalvalueofdiscounts": "Total value of discounts",
  "Totalvalueofoffers": "Total value of offers",
  "Totalvalueofdiscountcodes": "Total value of discount codes",
  "Totalpointsawarded": "Total points awarded",
  "Totaldiscountsactuallygranted": "Total discounts actually granted",
  "search": "Search",
  "Grant": "Grant",
  "Areyousureyouwanttograntthisdiscount": "Are you sure you want to grant this discount?",
  "registerserviceprovider": "Register a Service Provider",
  "activity": "Activity",
  "representname": "Actor Name",
  "register": "Register",
  "subscribe": "Subscribe",
  "validatephonemsg": "The Phone Number Must Be 9 Digits Starting With 5 Plus the Country Code",
  "validatephonemsg1": "Your Phone Number Must Start With the Number 5",
  "continue": "Continue",
  "saving": "Saving...",
  "complatealldata": "Complete all Data",
  "sendsuccess": "Sent successfully",
  "senderror": "An Error Occurred While Sending Your Message",
  "emailused": "This Email is already in use",
  "usedusername": "The username is already in use",
  "validateallparamaters": "All fields must be verified",
  "ordersuccess": "The order was successful",
  "anerroroccured": "An error has occurred",
  "validatephone": "Error, mobile number must be verified",
  "welcomemsg": "Thank you for joining the world of Halala Plus. Your membership number is {0}. Your savings begin with Halala... Browse Halala discounts on the website (https://www.halalaplus.com) and do not forget to show your membership number to the merchant in order to get the discount listed.",
  "thankmsg": "We thank you for your interest and congratulate you on obtaining the discount membership for free.",
  "registredmsg": "You have already subscribed to this number. Your membership number is ",
  "tryagainerror": "An error occurred, try again.",
  "waitingorder": "The order has already been ordered and is currently on hold",
  "nopointstoreplaced": "There are no points to be redeemed",
  "orderdone": "The order has been placed",
  "ordererror": "An error occurred while executing the order or there are no points.",
  "ordererror1": "An error occurred while executing the order",

  "create": "Create",
  "delete": "Delete",
  "edit": "Edit",
  "activities": "Activities",
  "addactivity": "Add activity",
  "activityname": "Activity name",
  "discribe": "Description",
  "backtolist": "Back to list",
  "add": "Add",
  "save": "Save",
  "cancel": "Cancel",
  "details": "Details",
  "date": "Date",
  "deleteactivity": "Are you sure you want to delete this activity?",
  "Currentpassword": "Current password",
  "Newpassword": "New password",
  "Confirmnewpassword": "Confirm the new password",
  "charactersremaining": "500 characters remaining",
  "options": "Options",
  "more": "More...",
  "addemp": "Add an employee",
  "theemployees": "Employees",
  "theemployee": "Employee",
  "nationality": "Nationality",
  "Enserviceprovidername": "English Service Provider name",
  "Editdata": "Edit data",
  "Changepassword": "Change password",
  "identityno": "Identity number",
  "birthdate": "BirthDate",
  "salary": "Salary",
  "password": "password",
  "mail": "e-mail",
  "enddateofdiscounts": "End date of discounts",
  "username": "User name",
  "deleteempmsg": "Are you sure you want to delete this Employee?",
  "Startdateofdiscounts": "Start date of discounts",
  "Cashbackforpiggybank": "Cashback for piggy bank",
  "addagent": "Create Agent",
  "agent": "The Agent",
  "agentname": "Agent Name",
  "agentphone": "Agent Phone No",

  "agentindex": "Agent List",
  "accountno": "Account No",
  "deleteagentmsg": "Are you sure you want to delete this Agent?",


  "Numberofdiscounts": "Number of discounts",
  "Valueofdiscounts": "Value of discounts",
  "Numberofoffers": "Number of offers",
  "valueofoffers": "Value of offers",
  "Numberofdiscountcodes": "Number of discount codes",
  "Valueofdiscountcodes": "The value of discount codes",
  "Numberofcodesused": "Number of codes used",
  "Pointsawarded": "Points awarded",
  "Actualdiscounts": "Actual discounts",




  "bank": "Bank",
  "bankindex": "Bank List",
  "bankname": "Bank Name",
  "connectioncode": "Connection Code",
  "deletebanktmsg": "Are you sure you want to delete this Bank?",
  "bulkaccountsettings": "Bulk account settings",
  "bankaccountsettings": "Bank account settings",
  "addbank": "Create New Bank",
  "serviceproviders": "Service Providers",
  "waitinglist": "Waiting List",
  "memberno": "Membership No",

  "thecity": "City",
  "addcity": "Create New City",
  "citieslist": "The Cities",
  "showcities": "Show The Cities",
  "thecountry": "Country",
  "country": "Country",
  "countries": "Countries",
  "countryname": "Country Name",
  "countrynationality": "Country Nationality",
  "deletecitymsg": "Are you sure you want to delete this City?",
  "deletecountrymsg": "Are you sure you want to delete this Country?",

  "coupon": "Coupon",
  "addcoupon": "Create New Coupon",
  "coupons": "Coupons",
  "currentcoupons": "Current Coupons",
  "expiredcoupons": "Expired Coupons",
  "couponcode": "Coupon Code",
  "coupondetails": "Coupon Details",
  "createcoupon": "Create Coupon",
  "storename": "Store Name",
  "storedetails": "Store Details",
  "storelink": "Store Link",
  "storeimg": "Store Image",
  "discount": "Discount",
  "couponstart": "Coupon Start Date",
  "couponends": "Coupon End Date",
  "storeoverview": "Overview about store",
  "offerimage": "Offer Image",
  "offerdetails": "Offer Details",
  "offerstart": "Offer Start Date",
  "offerends": "Offer End Date",
  "offername": "Offer Name",
  "offerno": "Offer No",
  "state": "State",
  "stop": "Stop",
  "activate": "Activate",
  "usedphone": "Phone Number already used!",



  "grantdiscount": "Grant a discount",
  "grant": "Grant",
  "customerno": "Customer number",
  "customerphoneno": "Customer phone number",
  "amount": "Amount",
  "rate": "Ratio",
  "createmarkter": "Add Marketer",
  "marktername": "Marketer's name",
  "dealingway": "Way of dealing",
  "customersname": "customer name",
  "customername": "How to deal",
  "customerstracking": "customer tracking",
  "customers": "Customers",
  "deletemarktermsg": "Are you sure you want to delete this marketer?",
  "markter": "Marketer",
  "dealingwayamount": "dealing method/quantity",
  "dealingwayrate": "dealing method/ratio",
  "markters": "Marketers",
  "createprovider": "Create a provider",
  "logo": "Logo",
  "busnissno": "Commercial Registration No",
  "enterprisephoneno": "Rnterprise No",
  "cashback": "Cashback",
  "contractno": "Contract No",
  "contractenddate": "End of contract",
  "contractstartdate": "Aontract start",
  "attachments": "Attachments",
  "serviceproviderrepresent": "Name of actor",
  "lat": "Latitude",
  "lng": "Longitude",
  "taskname": "Task Name",
  "taskdescribe": "Task Description",
  "taskstartsdate": "Task Start",
  "taskstracking": "Task Sracking",
  "taskenddate": "End of Sask",
  "tasknotes": "Notes",
  "employee": "Employee",
  "closetask": "Close Task",
  "financialtransactions": "Financial Transactions",
  "deservedamount": "Amount Due",
  "amountspent": "Amount Spent",
  "remain": "Remaining",
  "tasks": "Tasks",
  "addtasks": "Add Tasks",
  "task": "Task",
  "msgdetails": "Message details",
  "sendername": "Sender name",
  "senderemail": "Sender's email",
  "senderphoneno": "Sender's phone number",
  "msgdate": "Message Date",
  "message": "Message",
  "replay": "Replay",
  "send": "Send",
  "countriescities": "Countries and Cities",
  "orderstracking": "Tracking Orders",
  "bankaccount": "Bank Account",
  "payingcom": "Payment companies",
  "websitecontrol": "External site control",
  "addcoupons": "Add Discount",
  "pointssettings": "Points Settings",
  "chargewallet": "Charge Wallet",
  "pointsreplaceorders": "Points Replace Orders",
  "returnremain": "Return the Remainder",

  "payingcompany": "Payment company",
  "addpayingcompany": "Create a payment company",
  "acceptedorders": "Accepted Orders",
  "rejectedorders": "Rejected Orders",
  "neworders": "New Orders",
  "orderno": "Order Number",
  "ordertype": "Order Type",
  "orderowner": "Orderer",
  "orderdetails": "Order Details",
  "proce": "Action",
  "orderdate": "Order Date",
  "conditions": "Conditions",
  "startdate": "Start of Discount",
  "enddate": "End of Discount",
  "compname": "Company Name",
  "opdetails": "Operation Details",
  "deletepaycommsg": "Are you sure you want to delete this payment company?",
  "adddiscount": "Add discount",
  "alldiscount": "All discounts",
  "discountname": "Discount Name",
  "onlydiscount": "Only Discount",
  "onlypoints": "Only points",
  "discountandypoints": "Discount and points",
  "discounttype": "Discount type",
  "points": "Points",
  "sales": "Sales",
  "salesno": "Number of sales",
  "pointsno": "Number of points",
  "deservpoints": "Deserved points",
  "prize": "The reward due",
  "no": "Number",
  "amountdue": "Amount Due",
  "servicename": "Name of the service (discount)",
  "addserviceprovider": "Add a service provider",
  "deleteserviceprovider": "Delete a service provider",
  "deleteserprovidermsg": "Are you sure you want to delete the service provider?",
  "personaldata": "Personal data",
  "socialaccounts": "Social accounts",
  "platformname": "Platform name",
  "platformlink": "Platform link",
  "filename": "File name",
  "displayfile": "Display file",
  "display": "Display",
  "displayactivities": "Display activities",
  "addoffer": "Create Offer",
  "offerstartdate": "Offer start",
  "offerenddate": "End of offer",
  "editoffer": "Edit display",
  "currentoffers": "Current offers",
  "endedoffers": "Expired offers",
  "img": "Image",
  "note": "Notes",
  "deletetaskmsg": "Are you sure you want to delete this task?",
  "empname": "Employee name",
  "websitesettings": "website settings",
  "faqs": "Frequently Asked Questions",
  "monyboxicons": "Monybox icons",
  "showdetails": "Show details",
  "content": "Content",
  "encontent": "Contectin English",
  "place": "Address",
  "appapplelink": "Apple link",
  "youtubelink": "YouTube link",
  "whatsappurl": "WhatsApp link",
  "instagramlink": "Instagram link",
  "xlink": "x-link",
  "question": "Question",
  "enquestion": "Question In English",
  "questionno": "Question number",
  "answer": "Answer",
  "enanswer": "Answer in English",
  "iconname": "Icon Name",


  "culture": "Culture",
  "uiCulture": "UI Culture",
  "currency": "Currency",
  "numbers": "Numbers",

  "envision": "Our Vision (En)",
  "overview": "OverView",
  "doverview": "OverView",
  "endoverview": "OverView (En)",
  "features": "Features",
  "blog": "Blog",


  "yourfullname": "Your Full Name",
  "yourphoneno": "Your Phone Number",
  "youremail": "Your Email",
  "noitems": "No Items",
  "loadmore": "Load More...",
  "required": "Required field",
  "logout": "LogOut",
  "paymentcom": "Payment Companies",
  "subscriptions": "Subscribtions",
  "Reports": "Reports",
  "SettingsHalaCoinz": "Settings Halaa Coinz",
  "overallperformance": "overall performance",
  "Monthlyrevenue": "Monthly revenue",
  "GrantingpointsReport": "Granting points Report",
  "MostEngagedCustomers": "Most Engaged Customers",
  "MostEngagedDistributors": "Most Engaged Distributors",
  "Recurringsubscriptions": "Recurring subscriptions",
  "Customersavingspoints": "Customer savings points",
  "packages": "Packages",
  "pointreplacepartners": "Points Replace Partners",
  "financeeducation": "Finance Education",
  "courses": "Courses",
  "mainsections": "Main Sections",

  "news": "News",
  "websiteparam": "Website Parameters",
  "mesgs": "Messages",
  "mannageaccount": "Account Management",
  "digitalwalet": " Digital wallet",
  "openbanking": "Open Banking",
  "sender": "Sender",
  "contentlang": "content Language",
  "linkedinurl": "Linkedin Url",
  "facbookurl": "Facbook Url",

  "name1": "Name",
  "phone": "Phone Number",
  "sex": "Sex",

  "coursecount": "Corses Count",
  "subscount": "Subscribtions Count",
  "totalpayment": "Total Payed Amouunt",
  "comname": "Company Name",
  "comenname": "Company Name (En)",
  "addpaycom": "Add Paying Company",
  "deletepaycom": "Delete Paying Company",
  "editpaycom": "Edit Paying Company",
  "detailspaycom": "Details Paying Company",
  "deletepaycomconf": "Are You Sure To Delete This Company?",
  "packagename": "Package Name",
  "packageenname": "Package Name (En)",
  "packagprice": " Package Price",
  "packagperiod": "Package Period",
  "packagenperiod": "Package Period (En)",
  "packagdays": "Package Days",

  "newpackag": "New Packages",
  "packagdata": "Package Deta",
  "packagenfeatures": "Package Features (En)",
  "createdate": "Create Date",
  "deletepackagemsg": "Are You Sure you want to delete this?",
  "deletepackage": "Delete Package",

  "editpackage": "Edit Package",
  "packagedetails": "Package Details",


  // partner data 
  "newpartner": "Add New Partner",
  "partners": "Replace Points Partners",
  "partnername": "Store Name",
  "partnerenname": "Store Name (EN)",
  "aboutstore": "About the store",
  "enaboutstore": "About the store in English",
  "bnifitfmpoints": "Ways to benefit from points",
  "enbnifitfmpoints": "Ways to benefit from points in English",
  "partnerdetails": "Partner Details",
  "editpartner": "Edit Store",
  "icon": "Icon",
  "deletepartner": "Delete Partner ",
  "deletepartnermsg": "Are You Sure you want to delete this Partner?",


  "newcourse": "New Course",
  "maintitle": "Main Title",
  "courseoverview": "Course Overview",
  "coursetrainer": "Course Trainer",
  "courselanguage": "Course Language",
  "courseprice": "Course Price",
  "coursecoupon": "Course Coupon",
  "coursetopics": "Course Topics",
  "deletecourse": "Delete Course",
  "editcourse": "Edit Course",
  "coursedetails": "Course Detils",
  "coursedata": "Course Data",
  "coursepart": "Course Parts",
  "deletecoursemsg": "Are You Sure you want to delete this Course?",


  "newtopic": "New Topic",
  "newsection": "New Section",
  "coursename": "Course Name",
  "coursesections": "Course Sections",
  "coursessections": "Courses Sections",
  "topic": "Topic",
  "section": "Section",
  "sections": "Sections",
  "sectionname": "Section Name",
  "sectiondetails": "Section Details",
  "editsection": "Edit Section",
  "sectiondata": "Section Data",
  "uploadvideo": "Uploade Video",
  "deletetopic": "Delete Topic",
  "deletesection": "Delete Section",
  "edittopic": "Edit Topic",
  "topicdetails": "Topic Details",
  "deletetopicmsg": "Are You Sure you want to delete this Topic?",
  "deletesectionmsg": "Are You Sure you want to delete this Section?",

  "feature": "Feature",
  "enfeature": "Feature (En)",
  "newfeature": "New Feature",
  "deletefeature": "Delete Feature ",
  "editfeature": "Edit Feature",
  "featuredetails": "Feature Details",
  "featuredata": "Feature Data ",
  "deletefeaturemsg": "Are You Sure you want to delete this Feature?",


  "newarticle": "New Article",
  "articletext": " Article Text",
  "articletitle": "Article Title",
  "articleentitle": "Article English Title",
  "articleoverview": "Article OverView",

  "selectmultiimages": "You Can Choose More Than one image",
  "articledata": "Article Data",
  "articledetails": "Article Details",
  "editarticle": "Edit Article",
  "deletearticle": "Delete Article",
  "deletearticlemsg": "Are You Sure you want to delete this Article?",






  "newnews": "New News",
  "newstitle": "News Title",
  "newstext": "News Text",
  "deletenews": "Delete News",
  "deletenewsmsg": "Are You Sure you want to delete this News?",
  "editenews": "Edit News",
  "newsdata": "News Data",
  "newsdetails": "News Details",


  "savedsuccessfuly": "Saved successfully",
  "deletessuccessfuly": "Deleted successfully",
  "addedsuccessfuly": "Added successfully",
  "modifiedsuccessfuly": "Modified successfully",
  "datanotsaved": "Data not saved",
  "errorwillsaving": "An error occurred while adding",
  "errorwillsaving1": "An error occurred while saving",
  "errorwillsditing": "An error occurred while editing",
  "errorwillsdeleting": "An error occurred while deleting",
  "errorinoperation": "An error occurred during the operation",
  "articlenotfont": "Article not found",
  "newsnotfount": "News not found",
  "coursenotfound": "This course is not available",
  "recordnotfound": "This record is not available",
  "sendsuccessfuly": "Sent successfully",
  "errorwillsendingmsg": "An error occurred while sending your message",
  "fillalldata": "All fields must be filled in",
  "messagenotsent": "An error occurred. The message was not sent",
  "selectpackage": "You must select the package number",
  "packagenotfound": "Package not found",
  "sectionnotfound": "This section is not found",



  "selectoffer": "You must select an offer",
  "opsuccess": "The operation was completed successfully",
  "nodata": "There is no data",
  "tasknotstoped": "The task has not been stopped",
  "taskstoped": "The task was stopped successfully",
  "requestaccepted": "The request has been accepted",
  "requestnotaccepted": "The request was not accepted",
  "requestrejected": "The request was rejected",
  "requestnotrejected": "Request rejected failed",
  "datanotdeleted": "Not deleted",
  "noaccounts": "There are no accounts",
  "noaccountwithphone": "There is no account with this number",
  "cates": "Categories",
  "products": "Products",
  "bnifitfrompoints": "How to benefit from Discounts",
  "enbnifitfrompoints": "How to benefit from Discounts English",
  "enstoreoverview": "Store overview English",
  "chooseprovider": "Choose Service Provider",
  "provider": "Service Provider",
  "providername": "Store Name",
  "providerenname": "Store En Name",
  "endetails": "En Details",


  "addorginazation": "Add Orginazation",
  "FirstColor": "First Color",
  "SecondColor": "Second Color",
  "OffersIcon": "Offers Icon For App",
  "Orginazation": "Business",
  "OrginazationName": "Name In Arabic",
  "notification": "messages",
  "websiteurl": "WebSit URL",

  "Business": "Bussiness",
  "BusinessName": "Customer Name",
  "addnew": "Add New",
  "businessWelmsg": "Because you deserve it ({0}) Congratulations on joining the world of Halala Plus. Your membership number is {1}. Your savings start with a Halala... Download the Halala Plus app. 3000 discounts await you.",
  "businessWellongmsg": "Dear Employee, We are pleased to inform you that you have been subscribed to the Halala Plus service under the ({0}) initiatives to support employees, allowing you to enjoy exclusive offers and discounts at a wide range of stores and services. Your subscription number: [{1}] To take advantage of the discounts, please download the Halala Plus app from the following links: 📱 For Android devices: [Coming soon on Google Play] Website: (https://www.halalaplus.com/) 📱 For iPhone devices: [https://apps.apple.com/app/id6670226235] We hope you enjoy a rewarding and beneficial experience. Best regards"


}
