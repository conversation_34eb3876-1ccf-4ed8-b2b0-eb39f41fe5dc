﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Keyless]
public partial class OffersActivitiesView
{
    [Column(TypeName = "datetime")]
    public DateTime? StartDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? EndDate { get; set; }

    public bool Deleted { get; set; }

    [Column("isActive")]
    public bool? IsActive { get; set; }

    public long Id { get; set; }

    public int? Activity { get; set; }

    [StringLength(500)]
    public string? Name { get; set; }

    [StringLength(250)]
    public string? EnName { get; set; }

    [Column("image")]
    [StringLength(500)]
    public string? Image { get; set; }

    [Column("recType")]
    [StringLength(20)]
    public string? RecType { get; set; }
}
