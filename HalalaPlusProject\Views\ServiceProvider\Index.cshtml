﻿@model IOrderedEnumerable<HalalaPlusProject.CModels.MarketerIndexModel>
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["serviceproviders"];
}

<div class="row">
    <h3>@localizer["serviceproviders"]</h3>
    
                  @*<a href="add-consol.html" class="btn btn-primary px-5">إضافة مستشار</a>*@
                </div>


<div class="row">
        <div class="col-12">
          <div class="card mb-4">
            <div class="card-header pb-0">
                 @*  <div  style="display:10px">
    <h3 style="display:inline;">الأنشطة</h3>*@
                <a asp-controller="Activities" asp-action="Index" style="float:left;     margin-right: 10px;" class="btn btn-primary  px-5"> @localizer["displayactivities"]</a>
                <a asp-controller="Activities" asp-action="Create" style="float:left;     margin-right: 10px;" class="btn btn-primary  px-5">@localizer["addactivity"]</a>
                <a asp-action="Create" style="float:left; margin-right: 10px; " class="btn btn-primary px-5">@localizer["addserviceprovider"]</a>
                <a asp-controller="MyXlsxFiles" asp-action="Index" style="float:left; margin-right: 10px; " class="btn btn-primary px-5">استيراد من اكسل</a>
                  
                @*</div>*@
               @*<h3>مقدمي الخدمات</h3>*@
                 @*<a asp-action="Create">اضافة مقدم خدمة</a>*@
            </div>
            <div class="card-body px-0 pt-0 pb-2">
                <table class="table" id="tbl1">
                  <thead>
                    <tr>

                                <th scope="col">#</th>
                                <th scope="col">@localizer["name"]</th>
                                <th scope="col">@localizer["activity"]</th>
                                <th scope="col">@localizer["busnissno"]</th>
                                <th scope="col">@localizer["mail"]</th>
                                <th scope="col">@localizer["phoneno"]</th>
                                <th scope="col">@localizer["options"]</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.OrderByDescending(o=>o.Id)) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Id)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Activity)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.BusnissNo)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Email)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.PhoneNo)
            </td>
            
            
            <td>

                                        <a asp-action="Details" class="btn btn-primary tablebtn" asp-route-id="@item.Id">@localizer["more"]..</a>
                                    <a onclick="setField('/ServiceProvider/Disable?id=@item.Id')" class="btn btn-primary tablebtn">@item.state</a>
                                    </td>
        </tr>
}

             
             

                  


                    
                  </tbody>
               
                </table>

              </div>
              
            </div>
          </div>
        </div>
      </div>
    
                @section Scripts{
    <script>
  let table = new DataTable('#tbl1');
        table.order([0, 'desc']).draw();

    </script>
}
