﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("CustomerDiscount")]
public partial class CustomerDiscount
{
    [Key]
    public long Id { get; set; }

    public long? UserId { get; set; }

    public long? DiscountId { get; set; }

    [StringLength(50)]
    public string? DiscountCode { get; set; }

    public double? Amount { get; set; }

    public double? DiscountRate { get; set; }

    public double? DiscountAmount { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateOn { get; set; }

    public long? ProviderId { get; set; }

    public long? DiscountOfferId { get; set; }

    [StringLength(20)]
    public string CustomerStatus { get; set; } = null!;

    public bool IsReplaced { get; set; }

    [ForeignKey("DiscountId")]
    [InverseProperty("CustomerDiscounts")]
    public virtual DiscountsTable? Discount { get; set; }

    [ForeignKey("DiscountOfferId")]
    [InverseProperty("CustomerDiscounts")]
    public virtual OffersAndCopun? DiscountOffer { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("CustomerDiscounts")]
    public virtual SystemUser? User { get; set; }
}
