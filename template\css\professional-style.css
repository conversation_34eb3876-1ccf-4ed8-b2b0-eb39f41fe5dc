/* HalalaPlus Professional Template - Ultra Modern Design */

/* CSS Variables */
:root {
    /* Primary Colors */
    --primary-color: #cb0c9f;
    --secondary-color: #8392AB;
    --success-color: #82d616;
    --info-color: #17c1e8;
    --warning-color: #fbcf33;
    --danger-color: #ea0606;
    --dark-color: #344767;
    --light-color: #f8f9fa;
    --white-color: #ffffff;
    
    /* Gradients */
    --primary-gradient: linear-gradient(135deg, #cb0c9f 0%, #7928CA 100%);
    --secondary-gradient: linear-gradient(135deg, #8392AB 0%, #627594 100%);
    --success-gradient: linear-gradient(135deg, #82d616 0%, #17ad37 100%);
    --info-gradient: linear-gradient(135deg, #17c1e8 0%, #2152ff 100%);
    --warning-gradient: linear-gradient(135deg, #fbcf33 0%, #f53939 100%);
    --danger-gradient: linear-gradient(135deg, #ea0606 0%, #ff667c 100%);
    --dark-gradient: linear-gradient(135deg, #344767 0%, #141727 100%);
    
    /* Layout */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --header-height: 80px;
    --content-padding: 2rem;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Typography */
    --font-family: 'Tajawal', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Colors */
    --text-primary: #1a202c;
    --text-secondary: #718096;
    --text-muted: #a0aec0;
    --bg-primary: #ffffff;
    --bg-secondary: #f7fafc;
    --bg-tertiary: #edf2f7;
    --border-color: #e2e8f0;
    
    /* Transitions */
    --transition-fast: all 0.15s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Reset & Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* RTL/LTR Support */
[dir="rtl"] {
    text-align: right;
}

[dir="ltr"] {
    text-align: left;
}

/* Professional Sidebar */
.professional-sidebar {
    position: fixed;
    top: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: var(--dark-gradient);
    backdrop-filter: blur(20px);
    border-radius: 0 var(--radius-2xl) var(--radius-2xl) 0;
    box-shadow: var(--shadow-xl);
    z-index: var(--z-fixed);
    transition: var(--transition-normal);
    overflow: hidden;
}

[dir="rtl"] .professional-sidebar {
    right: 0;
    border-radius: var(--radius-2xl) 0 0 var(--radius-2xl);
}

[dir="ltr"] .professional-sidebar {
    left: 0;
    border-radius: 0 var(--radius-2xl) var(--radius-2xl) 0;
}

.professional-sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

/* Sidebar Header */
.sidebar-header {
    padding: 2rem 1.5rem;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    color: white;
    text-decoration: none;
    transition: var(--transition-fast);
}

.sidebar-logo:hover {
    transform: scale(1.05);
}

.sidebar-logo img {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
}

.sidebar-logo-text {
    font-size: var(--font-size-xl);
    font-weight: 700;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sidebar-toggle {
    position: absolute;
    top: 1rem;
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: var(--radius-md);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

[dir="rtl"] .sidebar-toggle {
    left: 1rem;
}

[dir="ltr"] .sidebar-toggle {
    right: 1rem;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: 1rem 0;
    height: calc(100vh - 200px);
    overflow-y: auto;
    overflow-x: hidden;
}

.sidebar-nav::-webkit-scrollbar {
    width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
}

.nav-item {
    margin: 0.25rem 1rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0;
    transition: var(--transition-fast);
    z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
    opacity: 1;
}

.nav-link:hover,
.nav-link.active {
    color: white;
    transform: translateX(5px);
}

[dir="rtl"] .nav-link:hover,
[dir="rtl"] .nav-link.active {
    transform: translateX(-5px);
}

.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    margin-inline-end: 1rem;
    transition: var(--transition-fast);
}

.nav-text {
    font-weight: 500;
    font-size: var(--font-size-sm);
    white-space: nowrap;
    opacity: 1;
    transition: var(--transition-fast);
}

.professional-sidebar.collapsed .nav-text {
    opacity: 0;
    width: 0;
}

.professional-sidebar.collapsed .nav-icon {
    margin-inline-end: 0;
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-user {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: white;
    text-decoration: none;
    padding: 0.75rem;
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.05);
    transition: var(--transition-fast);
}

.sidebar-user:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    font-size: var(--font-size-sm);
    line-height: 1.2;
    margin-bottom: 0.125rem;
}

.user-role {
    font-size: var(--font-size-xs);
    opacity: 0.7;
    line-height: 1;
}

.professional-sidebar.collapsed .user-info {
    display: none;
}

/* Professional Header */
.professional-header {
    position: fixed;
    top: 0;
    height: var(--header-height);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    z-index: var(--z-sticky);
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    padding: 0 2rem;
}

[dir="rtl"] .professional-header {
    right: var(--sidebar-width);
    left: 0;
}

[dir="ltr"] .professional-header {
    left: var(--sidebar-width);
    right: 0;
}

.professional-header.sidebar-collapsed {
    left: var(--sidebar-collapsed-width);
    right: 0;
}

[dir="rtl"] .professional-header.sidebar-collapsed {
    right: var(--sidebar-collapsed-width);
    left: 0;
}

/* Header Content */
.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-toggle {
    display: none;
    width: 40px;
    height: 40px;
    border: none;
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    cursor: pointer;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.mobile-menu-toggle:hover {
    background: var(--primary-color);
    color: white;
}

.breadcrumb-container {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin: 0;
    padding: 0;
    list-style: none;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin: 0 0.5rem;
    color: var(--text-muted);
}

.breadcrumb-item a {
    color: var(--text-muted);
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-primary);
    font-weight: 500;
}

.page-title {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Header Search */
.header-search {
    position: relative;
    width: 300px;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    background: var(--bg-primary);
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

[dir="rtl"] .search-input {
    padding: 0.75rem 3rem 0.75rem 1rem;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(203, 12, 159, 0.1);
}

.search-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 1rem;
}

[dir="rtl"] .search-icon {
    right: 1rem;
}

[dir="ltr"] .search-icon {
    left: 1rem;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-action {
    position: relative;
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.header-action:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.notification-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: var(--danger-color);
    border-radius: 50%;
    border: 2px solid var(--bg-primary);
}

[dir="rtl"] .notification-badge {
    right: auto;
    left: 8px;
}

/* User Dropdown */
.user-dropdown {
    position: relative;
}

.user-trigger {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border: none;
    background: transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-fast);
}

.user-trigger:hover {
    background: var(--bg-tertiary);
}

.user-avatar-header {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-xs);
    color: white;
}

.user-name-header {
    font-weight: 600;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

/* Main Content */
.main-content {
    transition: var(--transition-normal);
    min-height: 100vh;
    padding-top: var(--header-height);
}

[dir="rtl"] .main-content {
    margin-right: var(--sidebar-width);
}

[dir="ltr"] .main-content {
    margin-left: var(--sidebar-width);
}

.main-content.sidebar-collapsed {
    margin-left: var(--sidebar-collapsed-width);
    margin-right: var(--sidebar-collapsed-width);
}

[dir="rtl"] .main-content.sidebar-collapsed {
    margin-right: var(--sidebar-collapsed-width);
    margin-left: 0;
}

[dir="ltr"] .main-content.sidebar-collapsed {
    margin-left: var(--sidebar-collapsed-width);
    margin-right: 0;
}

.content-wrapper {
    padding: var(--content-padding);
    max-width: 100%;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-search {
        width: 200px;
    }
    
    .user-name-header {
        display: none;
    }
}

@media (max-width: 768px) {
    :root {
        --sidebar-width: 100%;
        --content-padding: 1rem;
    }
    
    .professional-sidebar {
        transform: translateX(-100%);
    }
    
    [dir="rtl"] .professional-sidebar {
        transform: translateX(100%);
    }
    
    .professional-sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .professional-header {
        left: 0;
        right: 0;
        padding: 0 1rem;
    }
    
    .main-content {
        margin-left: 0;
        margin-right: 0;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .header-search {
        display: none;
    }
    
    .breadcrumb-container {
        display: none;
    }
}

@media (max-width: 480px) {
    .header-actions {
        gap: 0.25rem;
    }
    
    .header-action {
        width: 36px;
        height: 36px;
    }
    
    .user-avatar-header {
        width: 28px;
        height: 28px;
    }
}
