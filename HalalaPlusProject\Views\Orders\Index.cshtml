﻿@*@model IEnumerable<HalalaPlusProject.CModels.OrdersModel>*@
@model HalalaPlusProject.CModels.OrdersIndexModel

    @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] =localizer["orderstracking"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h3>@localizer["orderstracking"]</h3>

<!-- Tab links -->
<div class="tab">
    <button class="tablinks active" onclick="openCity(event, 'NewOrdersTab')">@localizer["neworders"]</button>
    <button class="tablinks" onclick="openCity(event, 'AcceptOrdersTab')">@localizer["acceptedorders"]</button>
    <button class="tablinks" onclick="openCity(event, 'rejectedOrdersTab')">@localizer["rejectedorders"]</button>

</div>

<div id="rejectedOrdersTab" class="tabcontent"  style="display:none">

<table class="table" id="rejecttbl">
    <thead>
             <tr>
                <th scope="col">  @localizer["orderno"] </th>
                <th scope="col">  @localizer["ordertype"]</th>
                <th scope="col">  @localizer["orderowner"]</th>
                <th scope="col">@localizer["phoneno"] </th>

                <th scope="col">@localizer["orderdetails"] </th>
                      
                        
                    </tr>
            
    </thead>
    <tbody>
@foreach (var item in Model.rejectedOrders) {

        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.OrderNo)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.OrderType)
            </td>
         
            <td>
                @Html.DisplayFor(modelItem => item.UserOrdered)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.PhoneNo)
            </td>               
            <td>
                        <a asp-action="@item.DetailLink" style="text-decoration: underline;" asp-route-id="@item.Id"> @localizer["orderdetails"]</a>
            </td>
            

        </tr>
}
    </tbody>
</table>
</div>

<div id="AcceptOrdersTab" class="tabcontent"  style="display:none">
    <div class="table-responsive">
<table class="table table-striped" id="accpttbl">
    <thead>
             <tr>                   
                   
                    <th scope="col">  @localizer["orderno"] </th>
                    <th scope="col">  @localizer["ordertype"]</th>
                    <th scope="col">  @localizer["orderowner"]</th>
                    <th scope="col">@localizer["phoneno"] </th>

                    <th scope="col">@localizer["orderdetails"] </th>
                        
                    </tr>
            
    </thead>
    <tbody>
@foreach (var item in Model.acceptedOrders) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.OrderNo)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.OrderType)
            </td>
         
            <td>
                @Html.DisplayFor(modelItem => item.UserOrdered)
            </td>    
                 <td>
                @Html.DisplayFor(modelItem => item.PhoneNo)
            </td> 
            <td>
                            <a asp-action="@item.DetailLink" style="text-decoration: underline;" asp-route-id="@item.Id"> @localizer["orderdetails"]</a>
            </td>
            

        </tr>
}
    </tbody>
</table>
</div>
</div>


<div id="NewOrdersTab" class="tabcontent"  style="display:block">
      <div class="table-responsive" style="/*max-height:550px !important*/">
<table class="table table-striped" id="tbl1">
    <thead>
             <tr>
                    <th scope="col">  @localizer["orderno"] </th>
                    <th scope="col">  @localizer["ordertype"]</th>
                    <th scope="col">  @localizer["orderowner"]</th>
                    <th scope="col">@localizer["phoneno"] </th>

                    <th scope="col">@localizer["orderdetails"] </th>
                    <th scope="col">@localizer["proce"]</th>
                        
                    </tr>
            
    </thead>
    <tbody>
@foreach (var item in Model.newOrders) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.OrderNo)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.OrderType)
            </td>
         
            <td>
                @Html.DisplayFor(modelItem => item.UserOrdered)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.PhoneNo)
            </td> 
            <td>
                            <a asp-action="@item.DetailLink" style="text-decoration: underline;" asp-route-id="@item.Id"> @localizer["orderdetails"]</a>
            </td>
            <td>
               <a  class="tablebtn" onclick="AcceptOrder(@item.Id,'@item.OrderTypeNo',this)"><img style="width:35px" src="/img/true.png" class=""/></a> |
               <a  class="tablebtn" onclick="RejectOrder(@item.Id,'@item.OrderTypeNo',this)"><img style="width:35px" src="/img/x.png" class=""/></a>
            </td>

        </tr>
}
    </tbody>
</table>
</div>
</div>
@section Scripts{
    <script>
  let table = new DataTable('#tbl1');
  let table1 = new DataTable('#accpttbl');
  let table2 = new DataTable('#rejecttbl');

    </script>
}