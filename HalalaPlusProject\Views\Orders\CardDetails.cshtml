﻿
@model HalalaPlusProject.CModels.CardOrdersModel
    @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = @localizer["orderdetails"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["orderdetails"]</h2>

<div>
    
    <div class="col-md-12">
             <div class="row">

     
             <div class="col-md-5">
                  <div class="form-group">
                    <label class="form-label">@localizer["orderno"] </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Id)</label>               
                    </div>
                     <div class="form-group">
                    <label class="form-label">@localizer["name"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.Name)</label>               
                    </div>
                  <div class="form-group">
                    <label class="form-label">@localizer["[phoneno]"]</label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.PhoneNo)</label>               
                    </div>
                       <div class="form-group">
                    <label class="form-label">   @localizer["orderdate"] </label>
                        <label  class="form-control"> @Html.DisplayFor(model => model.OrderDate)</label>               
                    </div>
             
            </div>
          
    </div>
    </div>

</div>
<div class="mt-2">
    <a asp-action="Index">@localizer["backtolist"]</a>
</div>
