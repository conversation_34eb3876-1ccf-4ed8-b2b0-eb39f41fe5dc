﻿using HalalaPlusProject.CustomClasses;
using Microsoft.DotNet.Scaffolding.Shared.Messaging;
using System.ComponentModel.DataAnnotations;

namespace HalalaPlusProject.CModels
{
    /// <summary>
    /// يمثل نموذج إدخال أيقونة لحصالة معينة.
    /// </summary>
    public class MonyBoxIconsModel
    {
        /// <summary>
        /// معرف الأيقونة.
        /// </summary>
        public int? Id { get; set; }

        /// <summary>
        /// اسم الأيقونة.
        /// </summary>
        [Required(ErrorMessage = "يجب اخال اسم الايقونة")]
        public string? Name { get; set; }

        // [Required(ErrorMessage = "يجب تحديد ايقونة")]
        /// <summary>
        /// ملف الأيقونة المرفوع (يجب أن يكون بصيغة jpg أو jpeg أو png).
        /// </summary>
        [AllowedExtensionsAttribute(new string[] { ".jpg", ".jpeg", ".png" })]
        public IFormFile? Icon { get; set; }
    }

    /// <summary>
    /// نموذج عرض الأيقونة مع رابطها.
    /// </summary>
    public class MonyBoxIconsViewModel : MonyBoxIconsModel
    {
        /// <summary>
        /// رابط الأيقونة.
        /// </summary>
        public string? Link { get; set; }
    }
}
