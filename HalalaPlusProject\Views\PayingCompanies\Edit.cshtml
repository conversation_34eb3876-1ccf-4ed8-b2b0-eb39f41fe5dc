﻿@model HalalaPlusProject.CModels.PayingCompaniesModel
    @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["edit"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@localizer["edit"]</h2>

<h4>@localizer["payingcompany"]</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <div class="form-group">
                <label asp-for="Name" class="control-label">@localizer["compname"]</label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div><div class="form-group">
                <label asp-for="EnName" class="control-label">اسم الشركة انجليزي</label>
                <input asp-for="EnName" class="form-control" />
                <span asp-validation-for="EnName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="ConnectionCode" class="control-label">@localizer["connectiondetails"]</label>
                <input asp-for="ConnectionCode" class="form-control" />
                <span asp-validation-for="ConnectionCode" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="OperationDetils" class="control-label">@localizer["opdetails"]</label>
                <input asp-for="OperationDetils" class="form-control" />
                <span asp-validation-for="OperationDetils" class="text-danger"></span>
            </div>
            <div class="form-group mt-2">
                <button type="submit" value="Create" class="btn btn-primary ">@localizer["edit"]</button>
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index"> @localizer["backtolist"] </a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
