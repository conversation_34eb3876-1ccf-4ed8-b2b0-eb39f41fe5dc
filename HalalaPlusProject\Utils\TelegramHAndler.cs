﻿namespace HalalaPlusProject.Utils
{
    public class TelegramHAndler
    {
        public void SendSingleSMSwithoutasync(string message)
        {
            Task savetask = Task.Run(() => SendSMS(message));

        }

        public async Task<object> SendSMS(string message)
        {
            try
            {
                var client = new HttpClient();
                var request = new HttpRequestMessage(HttpMethod.Post, "https://api.telegram.org/bot7397202525:AAF1dDzaIdN74ohoQbYCYQ8by9PTbjaJeRc/sendMessage?chat_id=@bassamexceptions&text=" + message);
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }
      public async Task<object> SendSMS1(string message,string chatid,string token)
        {
            try
            {
                var client = new HttpClient();
                var request = new HttpRequestMessage(HttpMethod.Post, $"https://api.telegram.org/{token}/sendMessage?chat_id=@{chatid}&text=" + message);
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }
    
    
    }
}
