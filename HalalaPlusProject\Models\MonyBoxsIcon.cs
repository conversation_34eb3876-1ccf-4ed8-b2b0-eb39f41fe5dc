﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Models;

[Table("MonyBoxsICon")]
public partial class MonyBoxsIcon
{
    [Key]
    public int Id { get; set; }

    [Column("name")]
    [StringLength(150)]
    public string? Name { get; set; }

    [Column("link")]
    [StringLength(250)]
    public string? Link { get; set; }

    [Column("isActive")]
    public bool IsActive { get; set; }

    [Column("deleted")]
    public bool Deleted { get; set; }
}
