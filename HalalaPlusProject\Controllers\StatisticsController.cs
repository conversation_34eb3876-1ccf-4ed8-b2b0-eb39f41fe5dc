﻿using HalalaPlusProject.CModels;
using HalalaPlusProject.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HalalaPlusProject.Controllers
{
    [Authorize]
    /// <summary>
    /// متحكم مسؤول عن عرض الإحصائيات المتعلقة بالنظام.
    /// </summary>
    public class StatisticsController : Controller
    {
        private readonly HalalaPlusdbContext _context;

        public StatisticsController(HalalaPlusdbContext context)
        {
            _context = context;
        }

        //public IActionResult Index()
        //{
        //    var totalAmount = _context.UsersMonyBoxs.Sum(op => op.Amount);
        //    var totalDiscounts=_context.DiscountsTables.Sum(op => op.Discount);
        //    var stats = new UserStatistic
        //    {
        //        TotalUsersMonyBoxs = _context.UsersMonyBoxs.Count(),
        //        TotalAmount = (totalAmount != null&& totalAmount.Value>0) ?totalAmount.Value:00,
        //        TotalDiscount= (totalDiscounts != null && totalDiscounts.Value > 0) ? totalDiscounts.Value : 00,
        //        //ActiveUsers = _context.Users.Count(u => u.IsActive),
        //        //InactiveUsers = _context.Users.Count(u => !u.IsActive)
        //    };
        //    return View(stats);
        //}

        /// <summary>
        /// يعرض صفحة الإحصائيات الخاصة بمقدم الخدمة المسجل دخوله حاليًا.
        /// </summary>
        /// <param name="providerId">معرّف مقدم الخدمة (ملاحظة: يتم تجاهل هذا الوسيط حاليًا ويتم استخدام معرّف المستخدم المسجل دخوله).</param>
        /// <returns>عرض يحتوي على نموذج إحصائيات مقدم الخدمة.</returns>
        public async Task<IActionResult> Index(long providerId)
        {


            var data = await _context.GetProviderStatistics
                    .FromSqlRaw("EXEC GetProviderStatistics @ProviderId = {0}", _context.SystemUsers.FirstOrDefault(p => p.AspId == User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier).Value).Id)
                    .ToListAsync();

            var result = data.FirstOrDefault();

            return View(result);

            //return View();
        }
    }

}