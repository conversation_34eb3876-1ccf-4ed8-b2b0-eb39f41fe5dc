﻿@model HalalaPlusProject.Models.Subscription

@{
    ViewData["Title"] = "التفاصيل";
}

<h1>التفاصيل</h1>

<div>
    <h4>الاشتراك</h4>
    <hr />
    <dl class="row">
        <div class="col-md-3">
            <dt>مشترك</dt>
            <dd>@Html.DisplayFor(model => model.IsSubscribed)</dd>
        </div>
        <div class="col-md-3">
            <dt>الحالة</dt>
            <dd>@Html.DisplayFor(model => model.State)</dd>
        </div>
        <div class="col-md-3">
            <dt>تاريخ البدء</dt>
            <dd>@Html.DisplayFor(model => model.StartDate)</dd>
        </div>
        <div class="col-md-3">
            <dt>تاريخ الانتهاء</dt>
            <dd>@Html.DisplayFor(model => model.EndDate)</dd>
        </div>
        <div class="col-md-3">
            <dt>السعر</dt>
            <dd>@Html.DisplayFor(model => model.Price)</dd>
        </div>
        <div class="col-md-3">
            <dt>رقم العملية</dt>
            <dd>@Html.DisplayFor(model => model.ProcessNo)</dd>
        </div>
        <div class="col-md-3">
            <dt>الباقة</dt>
            <dd>@(Model.Pack != null ? Model.Pack.Id.ToString() : "—")</dd>
        </div>
        <div class="col-md-3">
            <dt>المستخدم</dt>
            <dd>@(Model.User != null ? Model.User.Id.ToString() : "—")</dd>
        </div>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id">تعديل</a> |
    <a asp-action="Index">العودة إلى القائمة</a>
</div>